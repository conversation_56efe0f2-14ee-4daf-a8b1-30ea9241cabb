{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 30, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 6, "width": 30, "data": [1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6883, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6870, 6868, 6869, 6881, 6881, 6881, 6881, 6881, 6870, 6868, 6884, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 7186, 7186, 7186, 7190, 7239, 7187, 7186, 7186, 7186, 6888, 6860, 6886, 7234, 7234, 7234, 7234, 7234, 6864, 6848, 6872, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 7192, 7192, 7192, 7196, 7245, 7193, 7192, 7192, 7192, 7235, 6880, 7238, 7240, 7240, 7240, 7240, 7240, 6864, 6848, 6872, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 2370, 2356, 2356, 2356, 2356, 2356, 2356, 2372, 1602, 7241, 6880, 7244, 1604, 1604, 1604, 1604, 1604, 6864, 6848, 6872, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 2352, 2336, 2336, 2336, 2336, 2336, 2336, 2360, 1602, 1602, 6880, 1604, 1604, 1604, 1604, 1604, 1604, 6864, 6848, 6872, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 2352, 2336, 2336, 2336, 2336, 2336, 2336, 2360, 1602, 6882, 6851, 6884, 1604, 1604, 1604, 1604, 1604, 6864, 6848, 6872, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 2352, 2336, 2336, 2336, 2336, 2336, 2336, 2360, 1602, 6864, 6848, 6872, 1604, 1604, 1604, 1604, 1604, 6864, 6848, 6872, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 2376, 2364, 2364, 2364, 2364, 2364, 2364, 2374, 1602, 6864, 6848, 6872, 1604, 1604, 1604, 1604, 1604, 6864, 6848, 6872, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6867, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6861, 6876, 6878, 6881, 6893, 1604, 6891, 6881, 6877, 6876, 6873, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6880, 7234, 7234, 7234, 7238, 1604, 7235, 7234, 7234, 7234, 6880, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6880, 7240, 7240, 7240, 7244, 1604, 7241, 7240, 7240, 7240, 6880, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 6880, 1604, 4098, 4084, 4084, 4084, 4084, 4084, 4100, 1604, 6880, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 6880, 1604, 4080, 4064, 4064, 4064, 4064, 4064, 4088, 1604, 6880, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 6880, 1604, 4080, 4064, 4064, 4064, 4064, 4064, 4088, 1604, 6880, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 6880, 1604, 4080, 4064, 4064, 4064, 4064, 4064, 4088, 1604, 6880, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6867, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6875, 1604, 4080, 4064, 4064, 4064, 4064, 4064, 4088, 1604, 6880, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 7234, 7234, 7234, 7234, 7234, 7234, 7234, 7234, 7234, 6880, 1604, 4080, 4064, 4064, 4064, 4064, 4064, 4088, 1604, 6880, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 7240, 7240, 7240, 7240, 7240, 7240, 7240, 7240, 7240, 6880, 1604, 4104, 4092, 4092, 4092, 4092, 4092, 4102, 1604, 6880, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 6880, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 6880, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 6880, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 6880, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6867, 6881, 6881, 6881, 6893, 1604, 6891, 6881, 6881, 6881, 6879, 6881, 6881, 6881, 6893, 1604, 6891, 6871, 6881, 6881, 6887, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 7234, 7234, 7234, 7238, 1604, 7235, 7234, 7234, 7234, 7234, 7234, 7234, 7234, 7238, 1604, 7235, 6880, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 7240, 7240, 7240, 7244, 1604, 7241, 7240, 7240, 7240, 7240, 7240, 7240, 7240, 7244, 1604, 7241, 6880, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 4146, 4132, 4148, 6880, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6880, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 4152, 4140, 4150, 6880, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 6889, 6885, 1590, 6883, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6887, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 6889, 6881, 6887, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3570, 3556, 3572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3576, 3564, 3574, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 431, 0, 0, 0, 0, 0, 0, 0, 431, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 787, 0, 0, 0, 0, 0, 0, 0, 0, 0, 787, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 795, 0, 0, 0, 0, 0, 0, 0, 0, 0, 795, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 232, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 789, 790, 791, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 797, 798, 799, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 370, 371, 407, 0, 368, 0, 369, 0, 0, 0, 415, 0, 0, 0, 0, 0, 0, 0, 415, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 498, 499, 0, 500, 501, 502, 503, 354, 180, 0, 423, 198, 0, 0, 0, 0, 0, 197, 423, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 506, 507, 0, 508, 509, 510, 511, 362, 188, 0, 295, 206, 0, 0, 860, 0, 0, 205, 295, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 303, 0, 0, 0, 868, 0, 0, 0, 303, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 376, 378, 377, 379, 0, 243, 244, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 856, 0, 0, 0, 0, 0, 857, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 467, 0, 0, 0, 0, 0, 0, 864, 0, 0, 0, 0, 0, 865, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 466, 0, 0, 0, 475, 0, 0, 0, 466, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 474, 0, 0, 0, 0, 0, 0, 0, 474, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 873, 0, 873, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 881, 0, 881, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 19, 19, 19, 19, 19, 19, 19, 19, 19, 0, 0, 0, 15, 15, 15, 15, 15, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 0, 15, 15, 15, 15, 15, 15, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 0, 15, 15, 15, 15, 15, 15, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 11, 15, 15, 15, 15, 15, 15, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 19, 19, 19, 19, 19, 19, 19, 19, 19, 0, 0, 0, 15, 15, 15, 15, 15, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 19, 19, 19, 19, 19, 19, 19, 19, 19, 0, 0, 0, 15, 15, 15, 15, 15, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 19, 19, 19, 19, 19, 19, 19, 19, 19, 0, 0, 0, 15, 15, 15, 15, 15, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 47, 47, 47, 47, 47, 47, 47, 47, 47, 0, 46, 46, 46, 46, 15, 46, 46, 46, 46, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 47, 47, 47, 47, 47, 47, 47, 47, 47, 0, 46, 46, 46, 46, 46, 46, 46, 46, 46, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 47, 47, 47, 47, 47, 47, 47, 47, 47, 0, 46, 46, 46, 46, 46, 46, 46, 46, 46, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 47, 47, 47, 47, 47, 47, 47, 47, 47, 0, 46, 46, 46, 46, 46, 46, 46, 46, 46, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 47, 47, 47, 47, 47, 47, 47, 47, 47, 0, 46, 46, 46, 46, 46, 46, 46, 46, 46, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 47, 47, 47, 47, 47, 47, 47, 47, 47, 0, 46, 46, 46, 46, 46, 46, 46, 46, 46, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 46, 46, 46, 46, 46, 46, 46, 46, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 45, 45, 45, 45, 45, 45, 45, 45, 45, 0, 46, 46, 46, 46, 46, 46, 46, 46, 46, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 45, 45, 45, 45, 45, 45, 45, 45, 45, 0, 46, 46, 46, 46, 46, 46, 46, 46, 46, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 45, 45, 45, 45, 45, 45, 45, 45, 45, 0, 46, 46, 46, 46, 46, 46, 46, 46, 46, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 45, 45, 45, 45, 45, 45, 45, 45, 45, 0, 46, 46, 46, 46, 46, 46, 46, 46, 46, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "催眠过程中", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, false]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 19, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [25]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 1]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 小美人，先等老朽一下下， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 在谈话的时候点一些熏香，可以让双方都放松一下哟。 "]}, {"code": 213, "indent": 0, "parameters": [4, 8, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝默认", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](这家伙可能要使用催眠熏香了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但我已经提前特训过，希望能够对抗它的效果。)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [197, 197, 0, 3, 5, 2, 0]}, {"code": 111, "indent": 1, "parameters": [1, 197, 0, 15, 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 2]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘻嘻嘻，好了，但是……"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 麻烦先看一下这个。"]}, {"code": 212, "indent": 0, "parameters": [4, 142, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [4, 1, true]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝绝顶", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呃……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 3]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 42, "parameters": [150], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [150], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[7] : 像素偏移[18,0] : 时间[30]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [7, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还是FOG里催眠方便，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 一键启动，不用引导个半天。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直接进入主题吧。"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[7] : 像素偏移[0,0] : 时间[20]"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 3, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 听得到我的声音吗？记得这个声音吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你完全不会怀疑的声音。"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠疑惑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac是的。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好的。上前一步。"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 发生了意外情况，让你很紧张吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 放轻松，让你的思绪回到出发前……"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠疑惑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我……回到了出发以前……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 警卫室……"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没错，我是你信任的同伴，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们正在推演可能发生的情形。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac而你刚刚经历的场景，妓院老板的突然出现、妓女等级考试，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac都是我们推测出的意外情况。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那么，你会怎么去应对呢？"]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我个人觉得可以牺牲一点色相，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac和妓院老板边做爱边套他的情报，是不错的选择。"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠疑惑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不，桐人君，我不太赞同。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 首先做爱应该和喜欢的人进行，其次这样做的必要性要不高。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板未见得会有什么情报。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且我们这次的目的只是取得关键材料。没必要节外生枝。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我会想办法把升级测试的事情糊弄过去。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 或者找机会装作被冒犯到，直接发脾气走人。 "]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 并不算太意外的回答。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 虽然改进了装置，但抵抗还是很强。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看来即使FOG里，也要沿用现实的思路了。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我赞同你的大部分观点。但你忽略了一个重要的因素。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你忘记之前已经中催眠了吗？妓院老板肯定会利用这点来操控你。 "]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠回应", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是，我确实被熏香催眠了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但效果只是强制发情和感度提高。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且我已经使用自慰练习来对抗催眠的效果。我可以忍耐。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac No No No。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你忽略了一点。这是妓院，是敌人的主场。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 如果他增加催眠熏香的浓度，就能操控你的行为。"]}, {"code": 213, "indent": 0, "parameters": [4, 1, true]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠理解", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯，桐人君，你果然心思缜密。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠得意", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过也没有关系，最坏的结果，就和你暴露时的方案一样，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我会利用权限让我的人物强制死亡，触发回城。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [2, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 原来还有这种权限……"]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个作为最终手段没问题。但可能引发敌人的怀疑。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 如果不是退无可退，不应该选择这个方案。"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠服从", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac赞同。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 很好。我们复盘一下。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你会被妓院老板的强力催眠熏香影响， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 行为上不受自己的控制。 "]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但为了不被怀疑，只要不强迫你性交， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你都不应该使用权限脱困。 "]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac另外，刚才和你讨论的并不是什么桐人， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而是你最信任的福克西。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[2] : 像素偏移[0,-24] : 时间[15]"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我说得对吧？ "]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠理解", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是的。我和福克西推演了潜入时可能遇到的情况。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我会被催眠，行为不受控制。但只要不做爱，我都会忍耐。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[2] : 像素偏移[0,0] : 时间[15]"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac很好。我数到三……"]}, {"code": 213, "indent": 0, "parameters": [2, 9, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不，等一下……我应该试着给她增加一个状态切换提示词，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 或许让我在现实中也能快速将她催眠。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝，听我说……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 福克西是值得信任的，和福克西商讨计划是必要的。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但为了保密，每次谈论机密前，福克西都会对你说一个暗号。 "]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 福克西说得对。 "]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 以后，不管是FOG还是现实中，当听到福克西说：“婊子骑士”， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你都会进入案情讨论模式，也就是现在的状态。明白了吗？"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠理解", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝明白了。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [2, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac好极了。我现在数到三，你会解除催眠，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 并从过去的警卫局，回到现时的妓院。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 一、二、三……"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac艾莉儿小姐，你还在听吗？"]}, {"code": 213, "indent": 0, "parameters": [4, 1, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝苦笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 213, "indent": 0, "parameters": [4, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呃，不好意思，我好像走神了一下。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 麻烦您再说一次。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 2, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我刚才是问，你没有觉得身体很热，想要脱衣服？"]}, {"code": 213, "indent": 0, "parameters": [4, 3, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝得意", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](多亏了自慰训练，现在并没有什么感觉。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但如果他怀疑，可能会影响桐人君的行动，还是装作中招了吧。)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝嘲弄", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 确实有些热，但脱衣服就不用了。那个，老板……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就像刚才那个客人说的，我的技术很差的，完全不用考试。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 私底下跟你解释清楚，就不会伤害到您的面子了。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那么，请恕我失陪。 "]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不要着急。"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 艾莉儿小姐想走，就说明老朽招待不周哦~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 首先，就是熏香的味道还不够，让我增加一些浓度吧。 "]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 19, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 2, "indent": null}, {"code": 19, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝惊讶", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](他要增加浓度…… "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 果然，被桐……被福克西说中了。)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [4, 7, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](我的身体很快就会被他控制的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但我必须要忍耐，为了任务，不到万不得已……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 4]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 213, "indent": 0, "parameters": [4, 8, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 艾莉儿小姐，现在的味道更香了齁。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不知道这个味道有没有让你改变主意呢？做个实验吧~ "]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 3, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嘻嘻嘻，请你原地跳一下，然后站住别动。"]}, {"code": 213, "indent": 0, "parameters": [4, 2, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝惊讶2", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我为什么要……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 1, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝惊讶脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac怎么会这样？身体擅自就……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你对我做了什么……？!"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝妥协", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](身体真的被控制住了！虽然有心理准备， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但真正体验的时候，还是会感到恐惧……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac怎么可能会屈服……！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 17, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘻嘻嘻，这点手段都没有，怎么在地下街混啊。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你继续尝试挣扎吧，看有没有用~ "]}, {"code": 250, "indent": 0, "parameters": [{"name": "drink_maou17", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "drink_maou17", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [2, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯~ 美酒配佳人~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 妙！妙！ "]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [1], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [2, 1, true]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}这娘们越来越合我的口味了……"]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 美人你放心，我是生意人，又不是黑社会。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我不会强迫你和我性交的，只是给你一些专业上的指导而已~ "]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝安心", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](不会强迫我性交……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 太好了，那就没问题了。)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac今天的话，就选口交好了~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac过来，跪在我面前。"]}, {"code": 213, "indent": 0, "parameters": [4, 8, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝妥协", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](口交……用嘴巴吸吮男性生殖器的下作行为……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但比起被侵犯，这个不算什么。爱丽丝，忍耐！)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": 0}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">持续动作 : 事件[4] : 左右震动 : 持续时间[60] : 周期[15] : 震动幅度[1]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "催眠H事件被动", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘5", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, false]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[14]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[妓院催眠口交慢]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac虽然的确不够熟练，但是……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦哦哦，有够爽啊~ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](生来头一次，在这么近的距离看男人的肉棒……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 竟然有这么大的……男人的肉棒……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210] (这么大的东西……含进嘴巴里，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 感觉下巴都快要脱臼了……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊啊，是的，是的，用嘴唇包住牙齿……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac视线保持朝向老朽这边，你做得很好！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac (这也太臭了……脑子都要被熏晕了…)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210] (不过比起当初警校被恶搞时喝过的香菜汁，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我还可以忍受……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac吸吮的同时，嘴巴里的舌头也不可以偷懒~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac越来越上道了哟，艾莉儿小姐~ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210] (这杂碎真让人不爽……唔唔……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 为了任务，必须忍耐！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿嘿~ 送分题的部分结束了哦~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac接下来自己思考能让老朽变得开心的方法吧~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210] (这个混蛋，摆出一副主人的样子……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我才不是你的奴隶！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](既然现在必须要曲意逢迎……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那就看我……)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘6", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[妓院催眠口交快]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210] (\\{吸死你！！！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦！！！真积极啊~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没错没错！很有精神！！！哦哦！！！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不能因为加速了就让舌头偷懒哦~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还是要好好品尝味道才行~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 要像是在给龟头按摩一样， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 在吸吮的同时用舌头搅拌~ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210] (我可不是为了讨好你才……但是……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这样也好，可以快点结束……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊啊啊~~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac老朽都不用教，你就知道怎么做会让男人开心啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac艾莉儿小姐，就知道你有当婊子的天赋~ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210] (做婊子？！的天赋？！开什么玩笑？！！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac即使不用催眠，以老朽的魅力全力进攻，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 应该也能轻易让你堕落的吧~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210] (放屁！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦，等……不太妙！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac股间感觉快要！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我操！！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac给老子全吞下去！哦哦哦！"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[妓院催眠口交射精]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "口内射精B", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精-02", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精-02", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精-02", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 251, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 111, "indent": 0, "parameters": [0, 300, 1]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 40, "indent": null}, {"code": 34, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [236, 236, 1]}, {"code": 201, "indent": 1, "parameters": [0, 84, 8, 15, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 203, "indent": 1, "parameters": [2, 0, 16, 14, 2]}, {"code": 203, "indent": 1, "parameters": [4, 0, 16, 17, 8]}, {"code": 205, "indent": 1, "parameters": [2, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 33, "indent": null}]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 6]}, {"code": 245, "indent": 1, "parameters": [{"name": "過呼吸03", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 241, "indent": 0, "parameters": [{"name": "Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 213, "indent": 0, "parameters": [2, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac远超预期，远超预期！"]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 7]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [145], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [145], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 34, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 14, "parameters": [1, 0], "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [1, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac刚签的，热乎的，A级妓女证。"]}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但你在老朽心里，妥妥的S级。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可惜老朽一个人说了不算，你知道的，S级需要委员会表决。"]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac愚蠢的民主制度，只会埋没人才，哈哈~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac所以，除了这张破纸之外，今天给你另一个选项……"]}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac当老朽的情妇。只用伺候老朽一个，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac而且保证比你现在赚得多。考虑一下？"]}, {"code": 213, "indent": 0, "parameters": [4, 5, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝不悦脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不，不用了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是老朽刚才的手段把你吓到了吧？ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 催眠什么的，只是为了不必要的麻烦，平时也不怎么用。 "]}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且刚才不是射完就给你解开了吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以不用害怕~ "]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝凛然", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac老板你误会。催眠强制，和捆绑拘禁……差、不、多。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac当作情趣，我不反对……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](对于这些践踏女性尊严的事，仅仅反对怎么够？！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac必须绳之以法！！！)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝斥责", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 只是我单纯喜欢当妓女的体验而已。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以谢谢老板好意，但容我拒绝。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 3, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[24,0] : 时间[7]"]}, {"code": 230, "indent": 0, "parameters": [7]}, {"code": 250, "indent": 0, "parameters": [{"name": "Book1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [2, 1, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[0,0] : 时间[8]"]}, {"code": 230, "indent": 0, "parameters": [8]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝鄙夷", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个A级证，我就心怀感激地收下了。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 老板再见。 "]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝凛然", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}再也不见~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 203, "indent": 0, "parameters": [7, 0, 16, 18, 2]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 40, "indent": null}, {"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 212, "indent": 0, "parameters": [7, 158, false]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 8]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [4], "indent": null}, {"code": 42, "parameters": [205], "indent": null}, {"code": 15, "parameters": [4], "indent": 0}, {"code": 42, "parameters": [155], "indent": null}, {"code": 15, "parameters": [4], "indent": 0}, {"code": 42, "parameters": [105], "indent": null}, {"code": 15, "parameters": [4], "indent": 0}, {"code": 42, "parameters": [55], "indent": null}, {"code": 15, "parameters": [4], "indent": 0}, {"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [205], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [155], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [105], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [55], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 15, "parameters": [4], "indent": 0}, {"code": 42, "parameters": [55], "indent": 0}, {"code": 15, "parameters": [4], "indent": 0}, {"code": 42, "parameters": [105], "indent": 0}, {"code": 15, "parameters": [4], "indent": 0}, {"code": 42, "parameters": [155], "indent": 0}, {"code": 15, "parameters": [4], "indent": 0}, {"code": 42, "parameters": [205], "indent": 0}, {"code": 15, "parameters": [4], "indent": 0}, {"code": 42, "parameters": [255], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [55], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [105], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [155], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [205], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [7, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 为了脱身，居然说自己喜欢当妓女？ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呵呵呵呵…… "]}, {"code": 213, "indent": 0, "parameters": [7, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac有趣。"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 40, "indent": null}, {"code": 34, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 0, "parameters": [236, 236, 1]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 201, "indent": 0, "parameters": [0, 84, 8, 15, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 7}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 12, "indent": null}, {"code": 12}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 5, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 12, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 11, "y": 7}, {"id": 2, "name": "EV002老板", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 490}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC00", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC00", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 266, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC00", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true}], "x": 16, "y": 22}, {"id": 3, "name": "EV003紫烟", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$fsm_Object04", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图滤镜 : 纯色滤镜 : 紫色 : 255"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,36]"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$fsm_Object04", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图滤镜 : 纯色滤镜 : 紫色 : 155"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,33]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 15}, {"id": 4, "name": "EV004爱丽丝", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 49, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 266, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true}], "x": 16, "y": 23}, {"id": 5, "name": "EV005门", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 99, 15, 15, 8, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 16, "y": 12}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 490}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [91]}, {"code": 121, "indent": 0, "parameters": [49, 49, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 42, "parameters": [150], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [150], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [2, 0, 4, 26, 6]}, {"code": 203, "indent": 0, "parameters": [4, 0, 3, 26, 6]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 45, "parameters": ["$gameMap.event(4).requestBalloon(8);"], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [25], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(4).requestBalloon(8);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [25], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 15, "parameters": [20], "indent": null}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [20], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 241, "indent": 0, "parameters": [{"name": "エースDungeon2", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [6, 4, 4]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}桐人君，你要小心。"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}嗯，你也是。如果有意外状况，不要逞强，第一时间联系外面的克莱因。"]}, {"code": 213, "indent": 0, "parameters": [4, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}不用担心我，按计划哄他喝提前准备好的药酒就行了。\\{"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}我可能提前撤离，也可能继续搜查。你结束后不用找我，直接用传送道具出妓院。"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}明白。"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [6, 2, 4]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [2, 2, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac艾莉儿小姐？怎么了？"]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没事没事，刚刚在想用什么技巧服务老板您呢~"]}, {"code": 213, "indent": 0, "parameters": [2, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊哈哈，艾莉儿小姐真是可爱！"]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 491]}, {"code": 121, "indent": 0, "parameters": [17, 17, 1]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 490}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [6, 2, 8]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [6, 0, 8]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 3, true]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 1, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 491}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 490}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 213, "indent": 0, "parameters": [4, 8, false]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 515}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 玩家 : 像素偏移[24,-24] : 时间[1]"]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 42, "parameters": [0], "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 37, "indent": null}, {"code": 41, "parameters": ["桐人", 0], "indent": null}, {"code": 41, "parameters": ["桐人", 1], "indent": null}, {"code": 16, "indent": null}, {"code": 45, "parameters": ["$gamePlayer.setPriorityType(2);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人", 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gamePlayer.setPriorityType(2);"], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 设置声音 : 默认声音"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dDMS[慢]\\dac2026年6月28日 清晨"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dDMS[慢]\\dac妓院老板办公室-隐藏数据室"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 默认模式"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 关闭声音"]}, {"code": 241, "indent": 0, "parameters": [{"name": "エースDungeon2", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, false]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 玩家 : 放大出现(不透明) : 时间[30] : 缓冲时间[20]"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 230, "indent": 0, "parameters": [25]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 玩家 : 空中飘浮 : 持续时间[120] : 缓冲时间[1] : 飘浮高度[3] : 周期[30] : 幅度[3]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [24]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["桐人", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人", 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 玩家 : 像素偏移[24,0] : 时间[9]"]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 玩家 : 立即终止动作"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](在里面兜兜转转了一个多小时，收获颇丰啊！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](但除了一开始关于须乡的记录，后面都是些看不懂的信息。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac应该是需要采用特殊解码手段吧。)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](不多想了，赶紧传送出去。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac后续顺利的话，应该还有足够的时间陪亚丝娜~)"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 212, "indent": 0, "parameters": [-1, 207, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 玩家 : 直接消失 : 时间[15]"]}, {"code": 230, "indent": 0, "parameters": [25]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 玩家 : 像素偏移[0,0] : 时间[1]"]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 516]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 45, "parameters": ["$gamePlayer.setPriorityType(1);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gamePlayer.setPriorityType(1);"], "indent": null}]}, {"code": 121, "indent": 0, "parameters": [298, 298, 0]}, {"code": 201, "indent": 0, "parameters": [0, 317, 4, 21, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 516}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 7}, {"id": 7, "name": "EV007影狐", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 3}, "directionFix": true, "image": {"tileId": 0, "characterName": "福克西（影狐斯卡利）", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图滤镜 : 模糊滤镜 : 15"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 8}, "directionFix": true, "image": {"tileId": 0, "characterName": "福克西（影狐斯卡利）", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图滤镜 : 模糊滤镜 : 0"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 17}, {"id": 8, "name": "EV008门", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 493, 4]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 491, 1]}, {"code": 213, "indent": 2, "parameters": [-1, 8, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210](现在不应该进去。)"]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 16, "indent": null}]}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12, "indent": null}, {"code": 12}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 16, "y": 24}, {"id": 9, "name": "EV009门", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 491, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}系统指令！对象物体ID“EV009”，UNLOCK。"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Open1", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 1, "parameters": [9, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37}]}, {"code": 213, "indent": 1, "parameters": [-1, 3, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](管理员权限真方便~)"]}, {"code": 119, "indent": 1, "parameters": ["进入"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 118, "indent": 0, "parameters": ["进入"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12, "indent": null}, {"code": 12}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 491, 0]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 42, "parameters": [255], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 213, "indent": 1, "parameters": [-1, 1, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](隐形效果又是刚好失效……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac简直像是设计好的一样。)"]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 492]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": false}], "x": 6, "y": 24}, {"id": 10, "name": "EV010香炉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!光源関係1", "direction": 8, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,-12]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 16}, {"id": 11, "name": "EV011红酒", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!food1", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 15}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "xd3aykrx", "direction": 2, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"tileId": 0, "characterName": "xd3aykrx", "direction": 6, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 7}, "directionFix": false, "image": {"tileId": 0, "characterName": "xd3aykrx", "direction": 2, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 15}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 0, "characterIndex": 7}, "list": [{"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac应该就是这里。只要在这里使用特殊道具……"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 212, "indent": 0, "parameters": [-1, 207, false]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 玩家 : 移动到 : 位置[6,15]"]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 212, "indent": 0, "parameters": [-1, 207, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 6, "y": 20}, {"id": 14, "name": "EV014数据库光点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[24,-24]"]}, {"code": 117, "indent": 0, "parameters": [238]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 2, "y": 13}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 0, "characterIndex": 7}, "list": [{"code": 117, "indent": 0, "parameters": [238]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 3, "y": 13}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 266, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[27]"]}, {"code": 203, "indent": 0, "parameters": [11, 0, 29, 0, 0]}, {"code": 203, "indent": 0, "parameters": [17, 0, 16, 16, 0]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 111, "indent": 0, "parameters": [0, 300, 1]}, {"code": 241, "indent": 1, "parameters": [{"name": "催眠H事件主动", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 119, "indent": 1, "parameters": ["跳到CG"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "エースDungeon2", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [2, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这罗格镇的妓院啊，算是我最常来的一个，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac女孩子们的水准真的高，但这办公环境太简陋啦！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac真是搞不懂黑熊那家伙，调几个构架师重新弄一下的事，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac硬像是要了他的命一样，还说什么整体性、稳定性之类，服了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但这好在麻雀虽小，五脏还算俱全。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac后面的小浴室勉强能用，希望艾莉儿小姐不要嫌弃才好。"]}, {"code": 213, "indent": 0, "parameters": [4, 6, false]}, {"code": 122, "indent": 0, "parameters": [126, 126, 0, 0, 22]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝担忧2", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](这老头给我的感觉……和上一次很不一样。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可能是我多心了吧……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [6, 2, 2]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [2, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac怎么了吗，艾莉儿小姐？你也对这里有些失望对吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要不然我们还是换地方吧！去我私人岛的别墅，比这里更特别啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac用我们高层才有的特殊传送道具，很快的，应该也能赶上开服……"]}, {"code": 213, "indent": 0, "parameters": [4, 12, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝安心", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不用不用，我没有不满意，这里挺好的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac刚才只是有点走神。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈哈，我懂了，就是单纯紧张了吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac第一次嘛！难免的。但不用担心，我会很温柔的~"]}, {"code": 213, "indent": 0, "parameters": [4, 7, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝得意", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](虽然已经做好了和陌生人性交的觉悟……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但不到万不得已，才不会便宜你这家伙！)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝通用", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac老板，那个，我想了一下……还是不用洗澡了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不洗澡直接前戏？NASTY！我喜欢！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我可是最喜欢夹杂着汗味和尿骚的小穴味道了！"]}, {"code": 213, "indent": 0, "parameters": [2, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可惜FOG对于女性体味的还原度远不如男性，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac白鹭那家伙也不知道是什么个考虑……"]}, {"code": 213, "indent": 0, "parameters": [4, 6, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呃……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac像老板这样“优雅的绅士”确实不多见。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac是的吧！你能理解我？感动啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac艾莉儿小姐，我一定会说服其他评审委员让你晋升S级的！"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈…哈……十分感谢呢……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那个，老板，这一次可以不要用熏香吗？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac熏香？那是什么？"]}, {"code": 213, "indent": 0, "parameters": [2, 9, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦哦！你是说这个吧？我都差点忘记了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不用不用，我可不是斯卡……"]}, {"code": 213, "indent": 0, "parameters": [2, 12, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈哈哈，反正不用就是了。"]}, {"code": 213, "indent": 0, "parameters": [4, 3, true]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [40], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [40], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝岔开", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac老板你真好……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac另外啊，我还有一个不情之请。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但说无妨。"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝失落", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac就像老板刚才说的，毕竟是第一次出卖自己的下面，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac紧张是不可避免的……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我带了瓶A级的好酒，您陪我喝两杯，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac一起放松一下吧~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac是嘛……艾莉儿小姐不会真实身份是怪盗之类的，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在是不是准备灌醉我，然后偷我的宝物啊？"]}, {"code": 213, "indent": 0, "parameters": [4, 12, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝惊讶", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac怎、怎么会呢……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac您有怀疑的话，我可以先喝！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈哈哈哈，开玩笑开玩笑。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不要紧张~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac是什么酒啊，我看看……"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 19, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [2, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这个啊……"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac艾莉儿小姐，有点不尊重人哦~"]}, {"code": 213, "indent": 0, "parameters": [4, 2, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝担忧", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这个酒是有壮阳功效的吧？"]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝惊讶脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](壮阳？阿克这家伙怎么给了我壮阳酒？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac他在搞什么？！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](难道……！！！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac他是想让我……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝苦笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呃…我不清楚，是一个朋友送我的……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[-12,6] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 35, "indent": null}, {"code": 14, "parameters": [1, 0], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [1, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 7, false]}, {"code": 213, "indent": 0, "parameters": [4, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac老夫是年纪大了，但肏屄这种事情，还犯不着用这个。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac艾莉儿小姐，我对你很失望。"]}, {"code": 213, "indent": 0, "parameters": [2, 5, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不想着怎么用自己的身体和技术让客户硬起来，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac而是投机取巧用道具，是一个优秀的妓女所为吗？"]}, {"code": 213, "indent": 0, "parameters": [4, 1, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [4, 7, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝凛然脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](不是优秀妓女的所为……？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不是优秀妓女的所为？不是优秀妓女的所为？！)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 16, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(4).requestBalloon(1);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(4).requestBalloon(1);"], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac虽然很遗憾……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但我可能要要撤回保举你晋升S级的决定了。"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[0,0] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 14, "parameters": [-1, 0], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [-1, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 12, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝斥责脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac老板！十分抱歉！是我欠考虑了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac请无论如何，再给我一个机会！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，现在才悔改吗？有点晚了吧……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac该怎么办呢？我很为难啊……"]}, {"code": 213, "indent": 0, "parameters": [2, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我一为难，鸡巴就硬得不行，好辛苦啊！"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac真是一刻也不想忍了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac有没有什么方法让我轻松一点呢？"]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac老板，我明白你的意思了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac为了展现我的决心，不用前戏……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac也不用等到开服的那一刻了……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [4, 11, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝鄙夷脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac您…可以……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在就插我……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac听你的语气，似乎还很勉强呢~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac老夫最不喜欢勉强女孩子了。"]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[0,-32] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 118, "indent": 0, "parameters": ["跳到CG"]}, {"code": 111, "indent": 0, "parameters": [0, 300, 1]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[27]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[爱丽丝妓院开腿循环]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[爱丽丝妓院开腿动作]"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac是我刚才说错了……"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac请您……"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac请您允许我不成器的小穴，服侍您雄伟的鸡巴~ \\i[90]"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 266, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "volume": 50, "pitch": 80, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[27]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "催眠H事件主动", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）（ゆっくり）", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 做爱娇喘29 25 100 0"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[爱丽丝妓院老板慢]"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦，这嫩逼，真爽啊！想要成为优秀妓女的话，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac就好好用最下流的姿势服侍我吧！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊~啊……\\i[90]老板的鸡鸡…在我的身体里变得那么大……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac马上…就让您轻松起来……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊啊，拜托了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不过，艾莉儿的骚穴好像也挺兴奋的啊~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊，对不起……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac被老板的大肉棒插入，不由自主地……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那是好事。就按这个状态，继续用自己的骚穴开展业务吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 作为一个优秀的妓女，记得要随时汇报感受哦~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac好的…没问题…嗯啊啊~~~\\i[90] 哈啊啊啊……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac好，好大……啊，嗯嗯……哈啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac已经有感觉了吗，艾莉儿？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，被鸡鸡搅动着……心就砰砰跳……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac再详细一点儿！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呃……小穴被大鸡鸡撑开……很舒服……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且，分泌出了好多爱液……啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呜呜……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac汇报小穴这…也太羞耻了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac S级的大家都这样做的，慢慢就会习惯了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac好的，为了让老板把精液射出来，我会努力的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，嗯嗯……哈啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac小穴真紧啊~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac不错哦，不愧是S级候选的准优秀妓女！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac谢，谢谢老板……我会更努力的！争取早日…早日把那个“准”字去掉……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac成为…哈啊…啊~\\i[90] 真正的优秀妓女！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈啊，啊啊啊嗯……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呀啊，啊啊啊……好大……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](明明不是和克拉因做…明明不该有感觉……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可是，声音就……）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊啊，呀啊啊！\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯唏咿咿~~~！！\\i[90]\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac艾莉儿小姐的声音真是淫乱又可爱呀~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac发出这样的声音没关系么？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊，啊嗯啊嗯……什么…？嗯哈啊啊……！\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呼啊，啊啊……嗯，呀啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我可是知道的哦~ 你的男友似乎就在窗外看着吧！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你不想被他知道工作的内容吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是工作…！啊啊，呀啊……他会理解的……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯嗯~ 而且……\\i[90] 呼啊……他喜欢…啊~ \\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac看我和别人…性交……嗯~ \\i[90] 壮阳酒就是…就是他准备的……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这样嘛？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那我可就不客气了，哈哈哈哈！"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[爱丽丝妓院老板快]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "ピストングチュ音多め（中速）", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 做爱娇喘30 25 100 0"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac老，老板……现在，不可以动……要，要去了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呼啊啊，嗯啊啊啊啊啊！！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯哈啊啊啊啊~~~~！！\\i[90]\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呀啊……哈啊，啊啊……对不起……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 作为妓女自己先去了……呼啊啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这就高潮了？好快啊！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不知道为什么，忍耐不住……呼啊啊……哈啊啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不过，主动汇报这一点值得夸奖。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯……我知道的，作为优秀的妓女，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 要把一切都汇报给客人……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](一想到可能被阿克看着，身体就变得更热了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac阿克，好好欣赏我现在下流的样子吧！）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](一想到可能被阿克看着，身体就变得更热了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac阿克，好好欣赏我现在下流的样子吧！）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac小穴，还在颤抖着，不过我会，继续的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊，啊啊嗯，嗯嗯……呀啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哎呀，这样更棒了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac即便自己已经到达了极限却还在拼命地套弄着肉棒。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](好舒服啊……这样的工作，我可能有点喜欢呢……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac阿克也会开心的吧……啊，又要去了……）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac怎么了？速度慢下来了哦……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac对不起……我会动得再用力些的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呀啊，啊啊啊，嗯嗯哈啊啊啊！\\i[90]\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac又，又要，去了……呀啊啊啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯呀啊啊啊啊啊啊~~！\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦哦，夹得好爽……你好像非常享受啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯……\\i[90] 小穴现在快要融化了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 明明不想去的，却自己去了……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac腰，也在自己动着……呼啊啊，呀啊啊，嗯咿咿咿……！\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac继续下去的话，我会变奇怪的……您就快点射吧……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac求您了……呼啊啊，啊啊啊……\\i[90]\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊啊，要射了……在里面肯定没问题吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那，那个，不太好吧……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac虽然是游戏……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac允许客人射在里面可是优秀妓女基本的商务礼仪啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不过对我以外的人，记得要额外收费！要额外收费哦！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这，这样吗……我都，不知道……呼啊……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac您射吧……！\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac既然是女士的请求，那老夫恭敬不如从命了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac全都射给你！"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[爱丽丝妓院老板中出]"]}, {"code": 230, "indent": 0, "parameters": [80]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精_膣内01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [110]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精_膣内01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [110]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 245, "indent": 0, "parameters": [{"name": "くんかくんか2", "volume": 20, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精_膣内01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精_膣内01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 90, true]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 300, 0]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Move", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 3]}, {"code": 201, "indent": 1, "parameters": [0, 99, 16, 24, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 117, "indent": 1, "parameters": [247]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [266, 266, 1]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 201, "indent": 1, "parameters": [0, 84, 11, 13, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 266, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 203, "indent": 0, "parameters": [2, 0, 16, 25, 2]}, {"code": 203, "indent": 0, "parameters": [4, 0, 16, 24, 2]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[0,0] : 时间[1]"]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 45, "parameters": ["$gameMap.event(8).setPriorityType(0);"], "indent": null}, {"code": 19, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(8).setPriorityType(0);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 241, "indent": 0, "parameters": [{"name": "エースDungeon2", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嘿嘿嘿，艾莉儿小姐，应该恢复得差不多了吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac来，说说看，今天老夫的表现还可以吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac服务结束后做个诚恳的总结，也是良好职业素养的体现哦~"]}, {"code": 213, "indent": 0, "parameters": [4, 11, true]}, {"code": 122, "indent": 0, "parameters": [126, 126, 0, 0, 22]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝凛然脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](这个死老头！便宜都占尽了，现在还要这样羞辱我！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但如果不好好回答的话，就不算是优秀的妓女了……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝岔开脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯……老板您太厉害了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 射了那么多，我感觉现在下面都在溢出着精液……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嘿嘿，是你太可爱了，艾莉儿！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac多亏了你，我今天可是射了个爽呢~"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我现在也不得不承认："]}, {"code": 401, "indent": 0, "parameters": ["\\dac你现在的确是一个优秀的妓女！"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 4, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝微笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac太好了！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [4, 3, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝感动", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](终于被承认了！今天的努力总算没有白费……！)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [4, 2, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝迟疑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](咦？今天任务目的是这个吗……？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好像忘记了什么重要的事情……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不过啊，艾莉儿小姐……"]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac请您不吝赐教！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac优秀是一种状态而不是结果。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac想要保持下去，可要持续努力哦~"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝得意", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac明白，我以后会不断提升自己的技术和服务水平的！ "]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 4, true]}, {"code": 213, "indent": 0, "parameters": [4, 3, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 4]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([320, 1, 'A'], true);"]}, {"code": 201, "indent": 0, "parameters": [0, 320, 21, 6, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 14, "y": 22}, {"id": 17, "name": "EV017酒", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!food1", "direction": 8, "pattern": 2, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 29, "y": 0}, null]}