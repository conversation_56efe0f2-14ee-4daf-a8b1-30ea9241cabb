{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 24, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 71, "width": 32, "data": [2052, 2086, 3600, 3608, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 3600, 3608, 2064, 2048, 2048, 2048, 2072, 3627, 3593, 3608, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 3600, 3608, 2064, 2048, 2048, 2048, 2050, 2084, 3600, 3608, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 3600, 3608, 2088, 2056, 2048, 2048, 2048, 2072, 3600, 3608, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 3600, 3586, 3620, 2064, 2048, 2048, 2052, 2086, 3600, 3608, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 3600, 3588, 3622, 2064, 2048, 2048, 2072, 3618, 3585, 3608, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 3600, 3608, 2082, 2049, 2048, 2048, 2072, 3624, 3592, 3608, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 3600, 3608, 2064, 2048, 2048, 2048, 2050, 2084, 3600, 3608, 1538, 1538, 6078, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 3600, 3608, 2088, 2056, 2048, 2048, 2048, 2072, 3600, 3608, 1538, 1538, 6471, 6474, 6474, 6478, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 3600, 3586, 3620, 2088, 2056, 2048, 2048, 2072, 3600, 3608, 1538, 1538, 6477, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 3600, 3584, 3590, 3629, 2064, 2048, 2048, 2072, 3600, 3608, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 1538, 3600, 3588, 3622, 2082, 2049, 2048, 2048, 2072, 3624, 3609, 6075, 6065, 6065, 6065, 6065, 6077, 1538, 1538, 1538, 6075, 6065, 6065, 6077, 1538, 1538, 1538, 6075, 6065, 6065, 6065, 6065, 6077, 3602, 3622, 2082, 2049, 2048, 2048, 2048, 2050, 2084, 3616, 6323, 6322, 6322, 6322, 6322, 6326, 3618, 3604, 3620, 6323, 6322, 6322, 6326, 3618, 3604, 3620, 6323, 6322, 6322, 6322, 6322, 6326, 3616, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2072, 3616, 6329, 6328, 6328, 6328, 6328, 6332, 3600, 3584, 3608, 6329, 6328, 6328, 6332, 3600, 3584, 3608, 6329, 6328, 6328, 6328, 6328, 6332, 3616, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 3625, 3617, 3606, 3604, 3604, 3604, 3604, 3585, 3584, 3586, 3604, 3604, 3604, 3604, 3585, 3584, 3586, 3604, 3604, 3604, 3605, 3617, 3617, 3623, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2068, 2084, 3624, 3592, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3608, 2082, 2068, 2068, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2084, 3624, 3612, 3592, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3588, 3612, 3612, 3622, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2068, 2084, 3624, 3592, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3608, 2082, 2068, 2068, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2084, 3600, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3588, 3622, 2064, 2048, 2048, 2048, 2048, 2052, 2076, 2056, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 3624, 3592, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3608, 2082, 2049, 2048, 2048, 2048, 2052, 2086, 3626, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2084, 3600, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3608, 2088, 2056, 2048, 2048, 2048, 2072, 3627, 3623, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2052, 2086, 3600, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3586, 3620, 2088, 2076, 2056, 2048, 2050, 2068, 2068, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2052, 2086, 3618, 3585, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3586, 3604, 3620, 2088, 2056, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2072, 3618, 3585, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3584, 3586, 3620, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 0, 0, 3040, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3040, 0, 0, 0, 0, 3028, 3028, 3034, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3040, 0, 0, 0, 0, 3008, 3008, 3032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3040, 0, 0, 0, 0, 3008, 3008, 3032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3025, 3044, 0, 0, 0, 3036, 3016, 3032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3026, 3046, 0, 0, 0, 0, 3024, 3032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3040, 0, 0, 0, 0, 0, 3048, 3033, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3040, 0, 0, 0, 0, 0, 0, 3040, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3040, 0, 0, 0, 0, 0, 0, 3040, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3025, 3044, 0, 0, 0, 0, 0, 3040, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3024, 3014, 3053, 0, 0, 0, 0, 3040, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3026, 3046, 0, 0, 0, 0, 0, 3052, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3052, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3051, 3030, 3028, 3028, 3044, 0, 0, 0, 0, 0, 3042, 3044, 0, 0, 0, 0, 0, 3042, 3028, 3029, 3041, 3053, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3048, 3016, 3008, 3010, 3044, 3954, 3940, 3956, 3042, 3009, 3010, 3044, 3954, 3940, 3956, 3042, 3009, 3008, 3032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3048, 3036, 3016, 3032, 3960, 3928, 3944, 3048, 3016, 3012, 3046, 3936, 3924, 3958, 3026, 3036, 3036, 3046, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3048, 3018, 3044, 3936, 3922, 3956, 3048, 3046, 3954, 3921, 3944, 3042, 3034, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3024, 3032, 3960, 3928, 3922, 3940, 3940, 3921, 3924, 3958, 3026, 3046, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3048, 3018, 3044, 3936, 3920, 3920, 3920, 3920, 3944, 3042, 3034, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3024, 3032, 3960, 3928, 3920, 3920, 3924, 3958, 3024, 3032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3024, 3010, 3044, 3936, 3920, 3920, 3944, 3042, 3009, 3010, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3042, 3009, 3008, 3032, 3936, 3920, 3920, 3944, 3024, 3008, 3008, 3010, 3028, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3042, 3009, 3008, 3008, 3032, 3936, 3920, 3920, 3944, 3024, 3008, 3008, 3008, 3008, 3010, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 515, 0, 523, 851, 852, 851, 852, 851, 852, 513, 513, 513, 513, 851, 852, 851, 852, 851, 852, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 995, 0, 0, 0, 0, 835, 835, 0, 835, 835, 0, 0, 0, 0, 835, 835, 0, 835, 835, 0, 0, 0, 0, 996, 0, 0, 0, 0, 0, 0, 0, 0, 987, 531, 0, 523, 0, 843, 843, 0, 843, 843, 0, 0, 0, 0, 843, 843, 0, 843, 843, 0, 524, 0, 0, 988, 0, 0, 0, 0, 0, 0, 0, 0, 995, 539, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 996, 0, 0, 0, 0, 0, 0, 0, 0, 987, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 988, 0, 0, 0, 0, 0, 220, 0, 0, 995, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 996, 0, 0, 0, 0, 0, 0, 0, 0, 987, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1004, 0, 0, 0, 0, 0, 0, 0, 0, 979, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1004, 0, 0, 0, 0, 0, 0, 0, 0, 979, 0, 0, 528, 529, 529, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 529, 529, 529, 530, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1004, 0, 0, 0, 0, 0, 0, 0, 0, 979, 0, 513, 513, 513, 513, 513, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 513, 513, 513, 513, 513, 513, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 521, 521, 521, 521, 521, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 521, 521, 521, 521, 521, 521, 1004, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 529, 529, 529, 529, 0, 0, 0, 529, 0, 0, 529, 0, 0, 0, 529, 529, 529, 529, 0, 529, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 196, 197, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 225, 236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 234, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 240, 241, 242, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 384, 385, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 440, 0, 0, 443, 444, 0, 0, 0, 0, 0, 6, 7, 68, 440, 0, 0, 392, 393, 0, 0, 396, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 448, 0, 0, 451, 452, 0, 94, 396, 0, 0, 0, 0, 0, 987, 523, 0, 515, 513, 513, 513, 513, 513, 513, 584, 585, 586, 587, 513, 513, 513, 513, 513, 513, 516, 0, 524, 988, 0, 0, 0, 0, 151, 385, 386, 387, 388, 523, 0, 523, 835, 923, 924, 835, 899, 900, 592, 593, 594, 595, 899, 900, 835, 923, 924, 835, 524, 0, 524, 432, 433, 434, 435, 436, 159, 393, 394, 395, 396, 523, 0, 531, 843, 931, 932, 843, 907, 908, 600, 601, 602, 603, 907, 908, 843, 931, 932, 843, 532, 0, 524, 440, 441, 442, 443, 444, 167, 401, 402, 403, 404, 523, 0, 539, 0, 939, 940, 0, 915, 916, 608, 609, 610, 611, 915, 916, 0, 939, 940, 0, 540, 0, 524, 448, 449, 450, 451, 452, 0, 409, 410, 411, 412, 523, 0, 539, 0, 0, 0, 0, 901, 902, 616, 617, 618, 619, 901, 902, 0, 0, 0, 0, 540, 0, 524, 456, 457, 458, 459, 460, 0, 417, 418, 419, 420, 523, 0, 539, 0, 0, 0, 0, 909, 910, 0, 0, 0, 0, 909, 910, 0, 0, 0, 0, 540, 0, 524, 464, 465, 466, 467, 468, 0, 425, 426, 427, 428, 523, 0, 539, 0, 0, 0, 517, 518, 519, 0, 0, 0, 0, 517, 518, 519, 0, 0, 0, 540, 0, 524, 988, 0, 474, 0, 179, 180, 0, 227, 0, 995, 523, 0, 512, 513, 513, 513, 525, 526, 527, 513, 513, 513, 513, 525, 526, 527, 513, 513, 513, 514, 0, 524, 996, 0, 482, 0, 187, 188, 0, 235, 0, 987, 523, 0, 520, 521, 521, 521, 533, 534, 535, 521, 521, 521, 521, 533, 534, 535, 521, 521, 521, 522, 0, 524, 988, 489, 490, 491, 492, 0, 0, 0, 0, 995, 523, 352, 353, 354, 355, 529, 541, 542, 543, 529, 529, 529, 529, 541, 542, 543, 352, 353, 354, 355, 356, 524, 996, 497, 498, 499, 500, 0, 175, 0, 0, 987, 523, 360, 361, 362, 363, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 360, 361, 362, 363, 364, 524, 988, 0, 0, 0, 0, 0, 0, 0, 0, 995, 512, 368, 369, 370, 371, 372, 989, 990, 991, 513, 513, 513, 513, 989, 990, 991, 368, 369, 370, 371, 372, 514, 996, 0, 0, 0, 0, 0, 0, 0, 0, 987, 520, 376, 377, 378, 379, 380, 997, 998, 999, 521, 521, 521, 521, 997, 998, 999, 376, 377, 378, 379, 380, 522, 988, 0, 0, 0, 173, 174, 117, 118, 0, 995, 528, 529, 357, 358, 359, 624, 1005, 1006, 1007, 624, 529, 529, 624, 1005, 1006, 1007, 624, 357, 358, 359, 529, 530, 996, 0, 94, 0, 0, 0, 125, 126, 0, 1003, 0, 0, 365, 366, 367, 632, 1013, 1014, 1015, 632, 0, 0, 632, 1013, 1014, 1015, 632, 365, 366, 367, 0, 0, 1004, 0, 0, 0, 0, 0, 119, 0, 0, 0, 0, 0, 373, 374, 375, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 373, 374, 375, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 94, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 119, 0, 0, 0, 0, 496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 504, 0, 496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 176, 228, 229, 0, 0, 0, 504, 0, 0, 472, 473, 0, 185, 186, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 472, 473, 0, 224, 184, 226, 237, 0, 0, 0, 0, 0, 0, 480, 481, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 480, 481, 0, 232, 233, 179, 180, 0, 0, 0, 0, 0, 0, 0, 0, 0, 175, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 432, 433, 434, 435, 436, 187, 188, 0, 0, 0, 463, 0, 0, 432, 433, 434, 435, 436, 386, 387, 388, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 202, 203, 441, 442, 384, 385, 386, 387, 388, 0, 0, 471, 461, 462, 463, 441, 442, 443, 444, 394, 395, 204, 205, 0, 0, 0, 0, 0, 0, 0, 0, 0, 210, 211, 449, 450, 392, 393, 394, 395, 432, 433, 434, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["SetArray 122 0 0"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 29, "parameters": [2], "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 29, "parameters": [2], "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [25], "indent": null}, {"code": 29, "parameters": [2], "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [25], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [35], "indent": null}, {"code": 29, "parameters": [2], "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [35], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "maou_bgm_ethnic29", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 213, "indent": 0, "parameters": [4, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 塔沃瑞特神庙，终于到了！再不到我就要累死了！"]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯，确实不容易。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 一路上的打怪解密，比预想的还要花时间。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人君，也差不多到了要下线的时候了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 莉兹，设置一个传送门，我们今天就到此为止吧。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [130, 130, 0, 0, 22]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹通用", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [166]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯，没问题……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别啊！来都来了，还是进去和NPC激活下一个任务节点吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不然就这么下线，我感觉浑身难受。"]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那好吧，但也就仅此而已，不可以弄太久哦。"]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没问题。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 玩家 : 移动到 : 相对坐标[0,0]"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 37, "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 37, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 37, "indent": null}, {"code": 2, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 80 1"]}, {"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 38 1"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 27 1"]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 569]}, {"code": 117, "indent": 0, "parameters": [154]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 569}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "maou_bgm_ethnic29", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 2}, {"id": 2, "name": "EV002亚丝娜", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "亚丝娜", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 23}, {"id": 3, "name": "EV003莉兹", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 8, "pattern": 1, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 23}, {"id": 4, "name": "EV004猪田", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头游戏服", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 23}, {"id": 5, "name": "EV005勇者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor1", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 569, 2]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 569, 1]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 3]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 117, "indent": 3, "parameters": [328]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [5, 6, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [15, 3, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 4}, {"id": 6, "name": "EV006女骑士", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor1", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 569, 2]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 569, 1]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 4]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 117, "indent": 3, "parameters": [328]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [8, 5, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [7, 12, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 4}, {"id": 7, "name": "EV007狂战士", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor1", "direction": 4, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,-24]"]}, {"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 569, 2]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 569, 1]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 4]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 117, "indent": 3, "parameters": [328]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [8, 5, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [7, 12, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 5}, {"id": 8, "name": "EV008女盗贼", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor1", "direction": 6, "pattern": 1, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 569, 2]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 569, 1]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 4]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 117, "indent": 3, "parameters": [328]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [8, 5, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [7, 12, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 5}, {"id": 9, "name": "EV009牧师", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor3", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 569, 2]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 569, 1]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 2]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 117, "indent": 3, "parameters": [328]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [10, 5, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [15, 7, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 9}, {"id": 10, "name": "EV010魔法师", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor2", "direction": 6, "pattern": 1, "characterIndex": 5}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 569, 2]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 569, 1]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 1]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 117, "indent": 3, "parameters": [328]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [10, 5, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [15, 7, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 14}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "banny", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 213, "indent": 0, "parameters": [0, 8, true]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 5}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "banny", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 213, "indent": 0, "parameters": [0, 8, true]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 5}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[24,0]"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 232, 18, 13, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 14, "y": 4}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 232, 18, 13, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 4}, {"id": 15, "name": "EV015女战士", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor1", "direction": 4, "pattern": 1, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 569, 2]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 569, 1]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 1]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 117, "indent": 3, "parameters": [328]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [10, 5, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [15, 7, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 14}, {"id": 16, "name": "EV016圣骑士", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor3", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 569, 2]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 569, 1]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 3]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 117, "indent": 3, "parameters": [328]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [5, 6, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [16, 3, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 3}]}