{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "Grassland", "battleback2Name": "<PERSON>", "bgm": {"name": "ayato_02_Stopped clock", "pan": 0, "pitch": 100, "volume": 30}, "bgs": {"name": "雷暴雨-电闪雷鸣-持续性大雨-自然天气(thundersto_爱给网_aigei_com", "pan": 0, "pitch": 100, "volume": 40}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 28, "note": "", "parallaxLoopX": true, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 1, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 57, "width": 35, "data": [2936, 2848, 3216, 3200, 3224, 2904, 2892, 2889, 3624, 3592, 3608, 1557, 2856, 2844, 2844, 2844, 2824, 2816, 2840, 1590, 1566, 1567, 2832, 2816, 2816, 2816, 2820, 2844, 2844, 2844, 2824, 2816, 0, 0, 0, 2936, 2860, 3216, 3200, 3224, 3626, 2858, 2905, 2909, 3624, 3622, 1565, 1566, 1566, 1566, 1589, 2856, 2844, 2854, 1559, 1569, 1570, 2832, 2820, 2844, 2844, 2854, 2907, 2897, 2901, 2832, 2816, 0, 0, 0, 2914, 2948, 3216, 3200, 3224, 3628, 2833, 2836, 2836, 2836, 2852, 1568, 1569, 1569, 1569, 1565, 1566, 1566, 1566, 1567, 1569, 1570, 2832, 2840, 2898, 2884, 2900, 2850, 2852, 2896, 2856, 2824, 0, 0, 0, 2916, 2950, 3240, 3228, 3210, 3236, 2856, 2844, 2824, 2816, 2840, 1568, 1569, 1569, 1569, 1568, 1569, 1569, 1569, 1570, 1569, 1570, 2856, 2841, 2880, 2864, 2888, 2856, 2841, 2905, 2901, 2856, 0, 0, 0, 2936, 2850, 2836, 2852, 3240, 3210, 3220, 3236, 2856, 2824, 2840, 1568, 1569, 1569, 1569, 1568, 1569, 1569, 1569, 1570, 1577, 1575, 2906, 2860, 2882, 2892, 2894, 2901, 2857, 2861, 2881, 2884, 0, 0, 0, 2936, 2834, 2844, 2826, 2852, 3240, 3228, 3210, 3236, 2856, 2854, 1573, 1569, 1569, 1569, 1568, 1569, 1569, 1569, 1570, 2898, 2884, 2871, 2897, 2891, 2850, 2852, 2905, 2897, 2897, 2893, 2872, 0, 0, 0, 2936, 2848, 3246, 2832, 2822, 2849, 2853, 3240, 3210, 3220, 3236, 2859, 2849, 2849, 2853, 1573, 1577, 1577, 1577, 1575, 2882, 2892, 2902, 2862, 2908, 2856, 2826, 2836, 2836, 2836, 2852, 2880, 0, 0, 0, 2950, 2833, 2836, 2817, 2840, 3246, 2833, 2852, 3216, 3200, 3202, 3220, 3220, 3236, 2857, 2849, 2838, 2836, 2852, 2907, 2903, 2850, 2852, 2906, 2862, 2910, 2832, 2816, 2816, 2816, 2840, 2904, 0, 0, 0, 2836, 2821, 2844, 2844, 2826, 2836, 2817, 2840, 3216, 3200, 3200, 3204, 3228, 3230, 3222, 3236, 2832, 2816, 2818, 2836, 2836, 2821, 2854, 2881, 2900, 2850, 2817, 2816, 2820, 2844, 2826, 2836, 0, 0, 0, 2820, 2854, 2898, 2900, 2832, 2816, 2820, 2854, 3240, 3228, 3228, 3225, 2859, 2853, 3240, 3225, 2832, 2816, 2816, 2816, 2816, 2840, 2907, 2873, 2888, 2832, 2816, 2816, 2840, 2906, 2856, 2844, 0, 0, 0, 2840, 2907, 2893, 2902, 2832, 2816, 2840, 2954, 2859, 2849, 2861, 3241, 3237, 2833, 2852, 3244, 2856, 2844, 2824, 2820, 2844, 2826, 2852, 2904, 2902, 2832, 2816, 2816, 2840, 2905, 2897, 2897, 0, 0, 0, 2818, 2836, 2836, 2836, 2817, 2816, 2840, 2953, 2934, 2932, 2948, 2858, 3232, 2856, 2826, 2852, 3243, 3237, 2832, 2840, 1589, 2856, 2846, 2849, 2849, 2825, 2816, 2820, 2846, 2849, 2849, 2849, 0, 0, 0, 2844, 2828, 2844, 2844, 2844, 2828, 2854, 1590, 2952, 2940, 2950, 2860, 3241, 3245, 2834, 2846, 2861, 3232, 2856, 2841, 1565, 1566, 1566, 1566, 1589, 2856, 2844, 2854, 1590, 1566, 1566, 1567, 0, 0, 0, 1589, 2860, 1590, 1566, 1589, 2860, 1590, 1567, 2850, 2836, 2852, 2907, 2909, 2850, 2842, 3243, 3222, 3203, 3236, 2860, 1568, 1569, 1569, 1569, 1565, 1566, 1566, 1566, 1567, 1569, 1569, 1570, 0, 0, 0, 1565, 1566, 1567, 1569, 1565, 1566, 1567, 1570, 2832, 2816, 2818, 2836, 2836, 2817, 2822, 2861, 3240, 3228, 3210, 3236, 1568, 1569, 1569, 1592, 1568, 1569, 1569, 1569, 1570, 1592, 1569, 1570, 0, 0, 0, 1568, 1569, 1570, 1569, 1568, 1569, 1570, 1570, 2832, 2816, 2820, 2844, 2824, 2816, 2840, 2954, 2850, 2852, 3216, 3224, 1568, 1569, 1569, 1569, 1568, 1569, 1604, 1569, 1570, 1569, 1569, 1570, 0, 0, 0, 1568, 1592, 1570, 1569, 1568, 1569, 1570, 1570, 2832, 2816, 2840, 1589, 2856, 2824, 2840, 2956, 2832, 2840, 3216, 3224, 1573, 1569, 1612, 1569, 1568, 1569, 1569, 1569, 1570, 1577, 1577, 1578, 0, 0, 0, 1568, 1594, 1570, 1577, 1568, 1569, 1570, 1575, 2832, 2820, 2854, 1565, 1566, 2856, 2830, 2849, 2829, 2854, 3216, 3224, 2850, 2836, 2836, 2852, 1573, 1577, 1577, 1597, 1575, 2850, 2836, 2836, 0, 0, 0, 1573, 1577, 1575, 2954, 1573, 1577, 1575, 2850, 2817, 2840, 3242, 1568, 1569, 1589, 2860, 2910, 2860, 1590, 3216, 3224, 2832, 2820, 2844, 2826, 2836, 2836, 2836, 2836, 2836, 2817, 2816, 2816, 0, 0, 0, 2836, 2836, 2852, 2956, 2851, 2849, 2838, 2817, 2816, 2840, 3232, 1568, 1569, 1565, 1566, 1566, 1566, 1567, 3216, 3224, 2832, 2840, 1589, 2856, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 0, 0, 0, 2816, 2816, 2818, 2836, 2842, 2910, 2832, 2820, 2844, 2841, 3232, 1568, 1569, 1568, 1569, 1569, 1569, 1570, 3216, 3224, 2832, 2840, 1565, 1566, 1589, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 0, 0, 0, 2816, 2820, 2844, 2844, 2846, 2849, 2845, 2854, 1590, 2848, 3232, 1573, 1577, 1568, 1592, 1569, 1594, 1570, 3216, 3224, 2832, 2840, 1568, 1569, 1557, 2856, 2844, 2844, 2844, 2828, 2844, 2844, 0, 0, 0, 2844, 2854, 1590, 1566, 1566, 1566, 1566, 1566, 1567, 2848, 3217, 3220, 3236, 1568, 1569, 1606, 1607, 1570, 3216, 3224, 2832, 2840, 1568, 1569, 1565, 1566, 1566, 1566, 1589, 2860, 1590, 1566, 0, 0, 0, 1566, 1566, 1567, 1569, 1569, 1569, 1592, 1569, 1570, 2848, 3240, 3208, 3224, 1573, 1577, 1614, 1615, 1575, 3216, 3224, 2832, 2840, 1568, 1569, 1568, 1569, 1569, 1569, 1565, 1566, 1567, 1569, 0, 0, 0, 1569, 1569, 1570, 1569, 1569, 1569, 1569, 1569, 1570, 2833, 2852, 3240, 3214, 3233, 3222, 3220, 3220, 3220, 3201, 3224, 2832, 2840, 1573, 1577, 1568, 1569, 1593, 1569, 1568, 1569, 1570, 1569, 0, 0, 0, 1569, 1593, 1570, 1569, 1569, 1569, 1569, 1569, 1570, 2832, 2818, 2852, 3244, 2858, 3240, 3208, 3204, 3228, 3228, 3238, 2832, 2818, 2836, 2852, 1568, 1569, 1569, 1605, 1568, 1569, 1570, 1569, 0, 0, 0, 1569, 1569, 1570, 1577, 1577, 1577, 1605, 1577, 1575, 2834, 2844, 2826, 2836, 2819, 2852, 3240, 3238, 2850, 2836, 2836, 2817, 2816, 2816, 2840, 1573, 1577, 1577, 1613, 1568, 1569, 1570, 1577, 0, 0, 0, 1577, 1577, 1575, 2850, 2836, 2836, 2836, 2836, 2836, 2842, 1590, 2832, 2816, 2816, 2818, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2836, 2852, 1573, 1577, 1575, 2850, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3867, 3861, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3870, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3090, 3092, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3072, 3080, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3096, 3066, 3092, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3072, 3080, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3090, 3061, 3094, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3072, 3080, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3090, 3076, 3061, 3094, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3072, 3060, 3094, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3072, 3080, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3096, 3081, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3124, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3132, 3134, 3137, 3149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3437, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3090, 3076, 3092, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3096, 3084, 3066, 3092, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3072, 3058, 3092, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3147, 3149, 3072, 3056, 3080, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 38, 39, 39, 39, 39, 39, 38, 39, 0, 0, 0, 0, 304, 305, 306, 0, 0, 0, 0, 304, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 38, 39, 38, 39, 47, 38, 39, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 298, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 299, 364, 46, 47, 46, 47, 38, 39, 47, 0, 0, 0, 0, 0, 0, 0, 298, 0, 0, 0, 0, 0, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 39, 38, 39, 46, 46, 47, 39, 39, 0, 0, 0, 0, 0, 0, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 341, 332, 364, 46, 47, 38, 39, 38, 39, 47, 0, 0, 0, 0, 0, 0, 296, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 38, 39, 264, 364, 0, 46, 47, 46, 47, 0, 0, 0, 0, 0, 0, 0, 304, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 46, 47, 7, 264, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 0, 304, 0, 0, 0, 274, 264, 364, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 306, 0, 0, 304, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 274, 374, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 0, 0, 274, 374, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 305, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 46, 0, 0, 0, 274, 274, 265, 264, 264, 0, 0, 0, 0, 0, 0, 0, 0, 198, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 32, 0, 0, 0, 0, 0, 0, 67, 0, 166, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 304, 305, 0, 0, 0, 40, 0, 0, 0, 46, 47, 282, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 0, 0, 0, 0, 0, 0, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 298, 0, 0, 22, 280, 281, 282, 0, 0, 0, 0, 22, 0, 0, 190, 0, 0, 0, 0, 291, 363, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 280, 281, 282, 0, 0, 0, 288, 289, 280, 281, 282, 0, 0, 0, 0, 0, 198, 20, 0, 0, 68, 291, 363, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 288, 289, 290, 0, 0, 0, 296, 297, 288, 289, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 291, 363, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 296, 297, 280, 281, 282, 0, 304, 305, 296, 297, 298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 299, 363, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 304, 305, 288, 289, 290, 0, 0, 0, 304, 305, 306, 0, 0, 159, 0, 0, 0, 0, 0, 0, 280, 281, 282, 374, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 280, 281, 282, 297, 298, 280, 281, 282, 0, 0, 0, 0, 0, 167, 0, 0, 0, 0, 0, 0, 288, 289, 290, 332, 374, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 288, 289, 290, 305, 306, 288, 289, 290, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 37, 0, 296, 297, 298, 274, 332, 374, 364, 364, 364, 364, 364, 364, 0, 0, 0, 296, 297, 298, 280, 281, 282, 297, 298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 280, 281, 282, 305, 306, 0, 341, 332, 374, 364, 364, 364, 364, 364, 0, 0, 0, 280, 281, 282, 288, 289, 290, 305, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 288, 289, 290, 280, 281, 282, 0, 299, 363, 364, 364, 364, 364, 364, 0, 0, 0, 288, 289, 290, 296, 297, 298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 298, 288, 289, 290, 39, 341, 332, 372, 372, 372, 372, 372, 0, 0, 0, 296, 297, 298, 304, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 306, 296, 297, 298, 47, 0, 275, 276, 276, 329, 330, 331, 0, 0, 0, 304, 305, 306, 0, 288, 289, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 304, 305, 306, 38, 39, 12, 13, 14, 274, 274, 274, 0, 0, 0, 0, 19, 0, 0, 296, 297, 298, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 47, 19, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 306, 0, 59, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 68, 178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 59, 178, 0, 0, 0, 38, 39, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 280, 281, 282, 59, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 59, 178, 0, 0, 0, 46, 47, 288, 289, 290, 0, 0, 0, 0, 0, 280, 281, 282, 0, 288, 289, 290, 59, 0, 0, 0, 0, 0, 0, 0, 280, 281, 282, 158, 0, 59, 67, 0, 0, 0, 0, 0, 296, 297, 298, 0, 0, 0, 0, 0, 288, 289, 290, 0, 296, 297, 298, 280, 281, 282, 0, 0, 0, 38, 39, 288, 289, 290, 280, 281, 282, 0, 0, 0, 0, 0, 0, 304, 305, 306, 0, 170, 0, 0, 0, 296, 297, 298, 280, 281, 282, 306, 288, 289, 290, 0, 0, 0, 280, 281, 296, 297, 298, 288, 289, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 178, 0, 0, 0, 304, 305, 306, 288, 289, 290, 280, 281, 282, 298, 0, 0, 0, 288, 289, 304, 305, 306, 296, 297, 298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 178, 0, 0, 0, 0, 0, 0, 296, 297, 298, 288, 289, 290, 306, 0, 0, 0, 296, 297, 298, 12, 0, 304, 305, 306, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 178, 0, 0, 0, 0, 0, 0, 304, 305, 306, 296, 297, 298, 0, 0, 0, 0, 304, 305, 306, 0, 0, 0, 0, 0, 0, 178, 0, 0, 0, 0, 0, 0, 0, 0, 178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 306, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 178, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 288, 289, 290, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 59, 158, 0, 0, 0, 0, 178, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 298, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 59, 166, 0, 0, 0, 0, 67, 0, 0, 0, 7, 0, 0, 0, 35, 0, 0, 0, 304, 305, 306, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 37, 67, 0, 3, 0, 0, 0, 0, 0, 0, 0, 29, 0, 0, 0, 43, 0, 0, 0, 0, 22, 0, 17, 0, 0, 921, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": false, "variableValue": 220}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 223, "indent": 0, "parameters": [[-68, -68, 0, 68], 1, false]}, {"code": 356, "indent": 0, "parameters": ["particle set 雨 weather"]}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 修改聚焦偏移 : 位置[384,0]"]}, {"code": 231, "indent": 0, "parameters": [60, "白色线条864-576x1008", 0, 0, 864, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [48, "黑幕", 0, 0, 864, 0, 40, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [49, "", 0, 0, 864, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [49, 0, 0, 0, 864, 0, 100, 100, 255, 0, 30, false]}, {"code": 231, "indent": 0, "parameters": [50, "", 0, 0, 864, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 播放简单状态元集合 : 集合[木屋素股影绘]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 播放简单状态元集合 : 集合[木屋素股]"]}, {"code": 356, "indent": 0, "parameters": [">菜单GIF : GIF[21] : 隐藏"]}, {"code": 356, "indent": 0, "parameters": [">菜单GIF : GIF[23] : 显示"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 233, 1]}, {"code": 122, "indent": 1, "parameters": [31, 31, 1, 0, 3]}, {"code": 122, "indent": 1, "parameters": [33, 33, 1, 0, 1]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 102], 30, false]}, {"code": 356, "indent": 1, "parameters": ["infotext : 11 : 1 : 590 : 140 : 28 : 150 : 0 : 1 : 0 : 亚  丝  娜  信  息  更  新"]}, {"code": 356, "indent": 1, "parameters": ["addLog 亚丝娜的经验似乎发生了一些变化..."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 12, "y": 18}, {"id": 2, "name": "空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 11}, {"id": 3, "name": "空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 19}, {"id": 4, "name": "空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 17}, {"id": 5, "name": "空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 18}, {"id": 6, "name": "空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 7}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": false, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([277, 9, 'A'], false);"]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 279, 2, 4, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 15, "y": 27}, {"id": 8, "name": "空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 8}, {"id": 9, "name": "打雷特效", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 10]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 60) + 120;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 102], 10, false]}, {"code": 232, "indent": 1, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 255, 0, 10, false]}, {"code": 356, "indent": 1, "parameters": ["particle set 闪电 screen 闪电 above"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 30) + 1;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 1, "parameters": ["particle clear 闪电"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 15, false]}, {"code": 232, "indent": 1, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 0, 0, 15, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 90) + 240;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 7, 2]}, {"code": 111, "indent": 1, "parameters": [1, 77, 0, 2, 1]}, {"code": 223, "indent": 2, "parameters": [[0, 0, 0, 102], 3, false]}, {"code": 356, "indent": 2, "parameters": ["particle set 闪电 screen 闪电 above"]}, {"code": 232, "indent": 2, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 255, 0, 3, false]}, {"code": 355, "indent": 2, "parameters": ["var waitFrames = Math.floor(Math.random() * 2) + 3;"]}, {"code": 655, "indent": 2, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 2, "parameters": ["particle clear 闪电"]}, {"code": 230, "indent": 2, "parameters": [15]}, {"code": 223, "indent": 2, "parameters": [[-68, -68, 0, 68], 10, false]}, {"code": 232, "indent": 2, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 0, 0, 15, false]}, {"code": 355, "indent": 2, "parameters": ["var waitFrames = Math.floor(Math.random() * 90) + 180;"]}, {"code": 655, "indent": 2, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 7, 3]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 102], 3, false]}, {"code": 356, "indent": 1, "parameters": ["particle set 闪电 screen 闪电 above"]}, {"code": 232, "indent": 1, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 255, 0, 3, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 2) + 3;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 1, "parameters": ["particle clear 闪电"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 10, false]}, {"code": 232, "indent": 1, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 0, 0, 15, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 3) + 14;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 102], 5, false]}, {"code": 356, "indent": 1, "parameters": ["particle set 闪电 screen 闪电 above"]}, {"code": 232, "indent": 1, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 255, 0, 5, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 5) + 5;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 1, "parameters": ["particle clear 闪电"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 15, false]}, {"code": 232, "indent": 1, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 0, 0, 15, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 120) + 180;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 12, "y": 19}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": false, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([277, 9, 'A'], false);"]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 279, 2, 4, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 14, "y": 27}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": false, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([277, 9, 'A'], false);"]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 279, 2, 4, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 16, "y": 27}, null, null, null, null, null, {"id": 17, "name": "空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 9}, {"id": 18, "name": "空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 25}, null, null, null, null, null, null, null, null, null, null, null]}