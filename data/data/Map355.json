{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "教会2", "pan": 0, "pitch": 100, "volume": 35}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 35, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "!教堂", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 28, "width": 39, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5923, 5921, 5921, 5921, 5921, 5911, 5921, 5921, 5921, 5921, 5925, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 5929, 5921, 5933, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 5923, 5921, 5933, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5907, 5933, 0, 5931, 5910, 5891, 5909, 5933, 0, 5931, 5915, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 5904, 5888, 5912, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 5904, 5888, 5912, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 5904, 5888, 5912, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 5904, 5888, 5912, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5907, 5933, 0, 5931, 5917, 5916, 5918, 5933, 0, 5931, 5915, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5929, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5927, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 41, 41, 41, 42, 43, 43, 43, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 41, 41, 41, 42, 43, 43, 43, 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 41, 41, 41, 42, 43, 43, 43, 43, 43, 0, 0, 0, 5, 0, 5, 5, 0, 0, 0, 5, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 41, 41, 41, 42, 43, 43, 43, 43, 43, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 41, 41, 41, 42, 43, 43, 43, 43, 43, 11, 5, 5, 5, 5, 5, 5, 0, 0, 0, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 41, 41, 41, 42, 43, 43, 43, 43, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 0, 0, 0, 0, 0, 43, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 41, 44, 0, 0, 0, 45, 43, 45, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 44, 44, 0, 0, 0, 45, 45, 45, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 44, 44, 0, 0, 0, 45, 45, 45, 0, 0, 0, 5, 0, 0, 0, 5, 5, 5, 5, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 44, 44, 0, 0, 0, 45, 45, 45, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 0, 0, 0, 0, 0, 45, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 44, 46, 46, 46, 46, 46, 45, 46, 0, 0, 0, 5, 0, 0, 0, 5, 5, 5, 5, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 46, 46, 46, 46, 46, 46, 46, 46, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 46, 46, 46, 46, 46, 46, 46, 46, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 46, 46, 46, 46, 46, 46, 46, 46, 0, 0, 0, 5, 0, 0, 0, 5, 5, 5, 5, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 46, 46, 46, 46, 46, 46, 46, 46, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 46, 46, 46, 46, 46, 46, 46, 46, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 46, 46, 46, 46, 46, 46, 46, 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 579}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 355 2 教堂-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 355 3 教堂-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 579}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 355 2 教堂-夜晚光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 355 3 教堂-夜晚阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 298, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 121, "indent": 0, "parameters": [298, 298, 1]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 25, "y": 6}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame01_pink", "direction": 2, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame01_pink", "direction": 4, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 24}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame01_pink", "direction": 2, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame01_pink", "direction": 4, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 27}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame01_pink", "direction": 2, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame01_pink", "direction": 4, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 27}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame01_pink", "direction": 2, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame01_pink", "direction": 4, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 24}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Door11", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 14, "y": 22}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Door11", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 8, "y": 22}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Door11", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Key", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 14, "y": 17}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Door11", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Key", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 8, "y": 17}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame02", "direction": 8, "pattern": 0, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame02_pink", "direction": 8, "pattern": 0, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 17}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame02", "direction": 8, "pattern": 0, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame02_pink", "direction": 8, "pattern": 0, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 21}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame02", "direction": 8, "pattern": 0, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame02_pink", "direction": 8, "pattern": 0, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 25}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame02", "direction": 8, "pattern": 0, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame02_pink", "direction": 8, "pattern": 0, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 27, "y": 25}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame02", "direction": 8, "pattern": 0, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame02_pink", "direction": 8, "pattern": 0, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 27, "y": 21}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame02", "direction": 8, "pattern": 0, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Flame02_pink", "direction": 8, "pattern": 0, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 27, "y": 17}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 202, 5, 27, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 214, 5, 27, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 86, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](现在不该离开……)"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 24, "y": 26}, {"id": 17, "name": "EV017", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 202, 5, 27, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 214, 5, 27, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 86, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](现在不该离开……)"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 25, "y": 26}, {"id": 18, "name": "EV018", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 202, 5, 27, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 214, 5, 27, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 86, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](现在不该离开……)"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 26, "y": 26}, {"id": 19, "name": "EV019牧师", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 385}, "directionFix": false, "image": {"tileId": 0, "characterName": "绿帽癖牧师", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 579}, "directionFix": false, "image": {"tileId": 0, "characterName": "绿帽癖牧师", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 117, "indent": 0, "parameters": [323]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 牧师，现在忏悔室有人吗？"]}, {"code": 213, "indent": 0, "parameters": [19, 11, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<牧师>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没太注意那边，其实我在教堂值班的时候，大部时间都在用插件炒股票来的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 等我查看一下忏悔室的状态……"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [0, 8, true]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [0, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<牧师>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 忏悔室内屋是修女在岗状态，外屋是空闲状态。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 应该可以进去，你敲敲门就知道了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好的，多谢。"]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 580]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 374, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 579}, "directionFix": false, "image": {"tileId": 0, "characterName": "绿帽癖牧师", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 117, "indent": 0, "parameters": [323]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 374, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 580}, "directionFix": false, "image": {"tileId": 0, "characterName": "绿帽癖牧师", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 117, "indent": 0, "parameters": [323]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 274, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 579}, "directionFix": false, "image": {"tileId": 0, "characterName": "绿帽癖牧师", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 12}, {"id": 20, "name": "EV020", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 39, "indent": null}, {"code": 12, "indent": null}, {"code": 12, "indent": null}, {"code": 40, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 11, "y": 14}, {"id": 21, "name": "EV021修女长", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 385}, "directionFix": false, "image": {"tileId": 0, "characterName": "banny", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 213, "indent": 0, "parameters": [0, 8, true]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 27, "y": 11}, {"id": 22, "name": "EV022", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 53, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 580}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [5]}, {"code": 117, "indent": 0, "parameters": [132]}, {"code": 203, "indent": 0, "parameters": [28, 0, 38, 0, 0]}, {"code": 203, "indent": 0, "parameters": [27, 0, 18, 13, 4]}, {"code": 213, "indent": 0, "parameters": [-1, 4, true]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 修女小姐，请问可以进来吗？"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 117, "indent": 0, "parameters": [329]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 18, "y": 13}, {"id": 23, "name": "EV023亚丝娜", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "亚丝娜修女", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 12}, {"id": 24, "name": "EV024猪田", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头游戏服", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 12}, {"id": 25, "name": "EV025", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 386}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "04-Cross-Examination-Moderato", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}, {"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["LAYER 355 2 教堂-夜晚光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 355 3 教堂-夜晚阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, false]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 模式-中"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 设置字数跳跃 : 字数[2]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\c[3]\\dVIMC[1]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac6月17日        \\.晚上7点\\."]}, {"code": 401, "indent": 0, "parameters": ["\\dac教   堂        \\. 忏悔室\\."]}, {"code": 213, "indent": 0, "parameters": [24, 8, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [24, 5, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac他当时和守门的称兄道弟，进地下街像回家一样。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac真的没想到他像会是这样的人！三观炸裂啊！"]}, {"code": 213, "indent": 0, "parameters": [24, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac如果只有这一次，还可能说是误会"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但后面我又无意间看到了好几次。"]}, {"code": 213, "indent": 0, "parameters": [24, 3, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac虽然都是距离比较远，但我敢保证一定是他！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不会有错！"]}, {"code": 250, "indent": 0, "parameters": [{"name": "物件_拾起物_3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [23, 6, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈苦笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呃，这样吗……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但感觉这并不需要你来忏悔啊？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我本来以为你要忏悔的，还是和你喜欢的那个女孩有关呢。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac比如，你对她做了一些出格的事情惹她生气了……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 250, "indent": 0, "parameters": [{"name": "LNSM_SE07_Sense7", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [24, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没有！我对喜欢的女孩只有爱护和尊重，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac最近也是想办法满足她的需求，我才不会惹她生气呢！"]}, {"code": 213, "indent": 0, "parameters": [23, 7, false]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}什么满足需求啊，嚤~ 真是的！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac今天我要忏悔的，是我的弱懦！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac在知道了这个我原本很尊敬前辈的真面目以后，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我没有勇气告诉他的女朋友，也是我的学姐，我的恩人！"]}, {"code": 213, "indent": 0, "parameters": [24, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我不敢说，因为那毕竟是我尊敬的前辈，也是我的偶像，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac因为我的告密让他们分手……我良心难受。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但不说的话，让学姐蒙在鼓里，她又太可怜了！"]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac所以修女姐姐知道我的痛苦了吧！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这件事真的快把我折磨死了！"]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [23, 8, true]}, {"code": 250, "indent": 0, "parameters": [{"name": "Flash2", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 241, "indent": 0, "parameters": [{"name": "10-Suspense", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈欲言又止", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你说的这件事……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我觉得很可能只是个误会。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [24, 12, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Damage1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊？我没有骗人……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac修女姐姐，你我都互不认识，我骗你对我有什么好处？"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不是说你骗人。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [23, 3, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你上次说过的，只有一个女孩对你好。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac今天说，前辈的女友学姐是你的恩人……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈苦笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac所以这个学姐……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我猜就是你喜欢的女孩吧，对吗？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [24, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯……嗯。"]}, {"code": 213, "indent": 0, "parameters": [23, 3, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈心事脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac有没有一种可能，你的潜意识里，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac会对她的男友产生嫉妒……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac让你不自觉地，把一些错觉、曲解以及过度的推理，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac主观地和这位前辈错误联想到了一起……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [24, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这怎么会……"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Jump1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac是有可能的，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac专业的说法叫“负性自动思维”……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac其实你仔细想想，刚才说的证词，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac是不是有很多不那么确定的地方？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [23, 3, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈岔开", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac比如说，你说看到了很多次，但距离都比较远，对吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac而FOG为了优化游戏，对远景是有虚化处理的……"]}, {"code": 250, "indent": 0, "parameters": [{"name": "物件_拾起物_3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你该对你的前辈多一些信心，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac很可能，你只是认错人了……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [216]}, {"code": 241, "indent": 0, "parameters": [{"name": "06-<PERSON>-<PERSON>-<PERSON><PERSON><PERSON>", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac绝无可能，那一身突兀的黑衣打扮，背着长剑的中二造型，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac在FOG里面，我那个前辈是独一份的！"]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac就算虚化了，也肯定不会认错！"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Slash10", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [23, 7, false]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈无奈", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唔……是嘛……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}这点确实无可反驳……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [24, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac修女姐姐，你不用为我的前辈开脱了！"]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你站在我那个学姐的立场想想，她多可怜！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac以为男友是去做任务，其实是背着她在地下街逍遥快活！"]}, {"code": 117, "indent": 0, "parameters": [217]}, {"code": 213, "indent": 0, "parameters": [23, 12, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac他…你的前辈，就算真去了去地下街，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不一定是去逍遥快活啊！也有可能是去正常办事……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [23, 9, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈焦急", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我听说，有些可以越狱困难副本的稀有道具，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac只有地下街的才能买到。也许他是为了……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 117, "indent": 0, "parameters": [216]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac什么稀有道具要去妓院买呢？"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Slash10", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [23, 5, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓！院？！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac况且也不是一次两次了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不仅如此……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac前天我还看到偶像和一个金发美女进了脱衣舞厅呢！"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Slash10", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [23, 1, false]}, {"code": 231, "indent": 0, "parameters": [2, "fog亚丝娜惊讶脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac金发美女…？！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没错！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还手挽着手，很亲密的样子！"]}, {"code": 117, "indent": 0, "parameters": [217]}, {"code": 213, "indent": 0, "parameters": [23, 12, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈逃避", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac就算是这样！肯定是有他的原因的！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [23, 11, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "物件_拾起物_3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈掩饰", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac而且再怎么说……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac也只是在游戏，不算出轨……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 117, "indent": 0, "parameters": [216]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac是！FOG里算不上出轨。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但这是问题的关键吗？！"]}, {"code": 213, "indent": 0, "parameters": [24, 5, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac关键的问题在于："]}, {"code": 401, "indent": 0, "parameters": ["\\dac前辈做这一切，都是打着做任务的幌子！他撒谎了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac也不是说一定不能撒谎，我的学姐有时候对我，对他，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac就会有些善意的谎言，这是没有问题的。"]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但为了去妓院，为了去脱衣舞厅，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac在和学姐约会的中途谎称有紧急任务，算善意谎言吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac他骗了我，骗了学姐，骗了所有人！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac既然在FOG里可以骗人，那现实里的话可以相信吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac知人知面不知心！我听说……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac越是优秀的男性，就越是喜欢开后宫！"]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac前辈表面只喜欢学姐一人，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac谁知道背地里是不是和各种女人乱搞！"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Slash10", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [23, 1, true]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [23, 5, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Jump1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你！说得太过分了！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 250, "indent": 0, "parameters": [{"name": "LNSM_SE07_Sense7", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [24, 1, true]}, {"code": 213, "indent": 0, "parameters": [23, 12, false]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈妥协", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac咳咳……我的意思是：这些都只是你的推测，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac在没有确凿证据前，不能轻易下结论。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [24, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这种事怎么可能弄到确凿证据啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac修女姐姐你还是觉得我因为嫉妒乱编的吧？"]}, {"code": 213, "indent": 0, "parameters": [23, 6, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[4]\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不是……我只是不希望你钻牛角尖，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac解决不了问题，还徒增了自己的烦恼……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [24, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac原来修女姐姐是为我考虑呀！我之前还在纳闷，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac姐姐你为什么那么袒护那个出轨的前辈。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯嗯，好吧！我听话。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这个事情我就不再深究，也不会去跟学姐说了。"]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但我还是要鼓起勇气去提醒一下那个前辈！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac让他不可以辜负我的学姐！"]}, {"code": 213, "indent": 0, "parameters": [24, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dVIMC[1]\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac谢谢你，修女姐姐，说出来我感觉心里舒服多了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还有些时间，我再跟你说些别的事情吧~"]}, {"code": 213, "indent": 0, "parameters": [23, 8, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 249, "indent": 0, "parameters": [{"name": "LNSM_ME06_Sleep2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 241, "indent": 0, "parameters": [{"name": "教会2", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [23, 0, 8, 11, 8]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 默认模式"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 设置字数跳跃 : 字数[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 关闭声音"]}, {"code": 117, "indent": 0, "parameters": [123]}, {"code": 213, "indent": 0, "parameters": [23, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田君……什么都好，就是太过情绪化了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac他说的那些，肯定是他搞错了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [23, 9, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈失望", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac和桐人确认一下吧。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "LNSM_SE08_Sense8", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 250, "indent": 0, "parameters": [{"name": "LNSM_SE08_Sense8", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 250, "indent": 0, "parameters": [{"name": "LNSM_SE08_Sense8", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 250, "indent": 0, "parameters": [{"name": "koukaongen_022", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君，你在忙吗？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，有一点，我正在做职业任务。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac一个人吗？在什么地方？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我今天修女任务做完了，要不要我来找你？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不用，我现在的位置离主城有点远。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你过来太麻烦了……"]}, {"code": 213, "indent": 0, "parameters": [23, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呃！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac紧张的女声:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哥哥！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 117, "indent": 0, "parameters": [124]}, {"code": 213, "indent": 0, "parameters": [23, 1, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈担忧", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君？！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac遇到一个棘手的特殊技能怪物，先不跟你说了！"]}, {"code": 250, "indent": 0, "parameters": [{"name": "koukaongen_004", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [123]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [23, 8, true]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈心事", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈修女.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君…肯定是不会骗我的……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 249, "indent": 0, "parameters": [{"name": "LNSM_ME06_Sleep2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 387]}, {"code": 117, "indent": 0, "parameters": [99]}, {"code": 117, "indent": 0, "parameters": [131]}, {"code": 117, "indent": 0, "parameters": [114]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 345, 19, 14, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 386}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 20, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Bow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["infotext : 8 : 5 : 240 : 320 : 68 : 270 : 0 : 2 : 1 : 心 境 重 塑"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": ["infotext : 10 : 4 : 400 : 510 : 100 : 90 : 10 : 14 : 0 : 纯  洁  坚  定  的  爱  恋"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 250, "indent": 0, "parameters": [{"name": "Up1", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 20, false]}, {"code": 356, "indent": 0, "parameters": ["infotext : 10 : 7 : 400 : 510 : 100 : 90 : 10 : 14 : 0 : 猜  阻  渐  生  的  感  情"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 387}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 274, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 578}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [8, "", 0, 0, 346, 400, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[29]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[忏悔室贴屁股]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[8] : 设置动画序列 : 动画序列[6]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[8] : 等待动画序列加载完成"]}, {"code": 203, "indent": 0, "parameters": [23, 0, 10, 14, 4]}, {"code": 203, "indent": 0, "parameters": [24, 0, 12, 14, 4]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 37, "indent": null}, {"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}, {"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["LAYER 355 2 教堂-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 355 3 教堂-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 213, "indent": 0, "parameters": [23, 6, true]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 52]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唉……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[24] : 像素偏移[-6,0] : 时间[15]"]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 33, "indent": null}, {"code": 39, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[24] : 像素偏移[6,0] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [24, 0, 10, 14, 4]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[24] : 像素偏移[0,0] : 时间[60]"]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 34, "indent": null}, {"code": 40, "indent": null}, {"code": 29, "parameters": [2], "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈心事脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](不知道猪田君今天又想要我干什么……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [24, 4, false]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 15, false]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[24] : 像素偏移[-12,0] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 34, "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呀！你干什么？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊， 修女姐姐的大屁股~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘶哈，好香啊！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田君..呜！这样太羞耻了！先放开好吗？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好的，修女姐姐。"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, true]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[24] : 像素偏移[-30,0] : 时间[1]"]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 15, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[24] : 像素偏移[0,0] : 时间[30]"]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 修女姐姐，我听话吧~"]}, {"code": 213, "indent": 0, "parameters": [23, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田君明明平常还算靠谱，但是有时候会变的像小孩子一样，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 让人头疼呢。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [23, 2, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还有，为什么从进门开始就一直叫我修女姐姐啊？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你都知道我的身份，还这么叫，感觉稍微有点不好意思……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 因为欺骗我的就是“修女姐姐”，而不是“亚丝娜学姐”，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以当然应该由“修女姐姐”来补偿这份罪孽啦！"]}, {"code": 213, "indent": 0, "parameters": [23, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 罪孽什么的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我那时也是为了更好的开导你……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [24, 5, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 修女姐姐，忏悔室明明就是用来坦白自己错误的地方，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你却这样推卸责任，似乎不太好吧！"]}, {"code": 213, "indent": 0, "parameters": [23, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唉……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 15, "parameters": [25], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [25], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": 0}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说到底，也确实是我不对，不该欺骗你。我向你道歉。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要，光是道歉可不行！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 补偿，我要修女姐姐补偿我！"]}, {"code": 213, "indent": 0, "parameters": [23, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 反正又是H的事情吧……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 算了，你想要什么样的补偿？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [24, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我想要用修女姐姐的大奶子净化我的肉棒~"]}, {"code": 213, "indent": 0, "parameters": [23, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 净化……总之是想要用胸部帮你做是嘛？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可是我不想弄脏衣服……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [24, 5, false]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我不管，我不管，衣服什么的之后用魔法一下子就能干净了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 修女姐姐就是在找借口！"]}, {"code": 213, "indent": 0, "parameters": [23, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我，我知道啦，帮你就是了……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[24] : 像素偏移[0,-24] : 时间[1]"]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 579]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 274, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 579}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[29]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[忏悔室乳交]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 245, "indent": 0, "parameters": [{"name": "bob_kosuru01", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 241, "indent": 0, "parameters": [{"name": "bensound-sexy", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [390, 390, 1]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿，修女姐姐的下流奶子真的在包裹着我的肉棒~ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔…哈啊……不要说奇怪的话……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔，硬的好难受，果然这光景不管看多少次都会这样~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哼……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 人都是喜新厌旧的，迟早……你也会失去兴趣……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 也？是指桐人学长吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 听修女姐姐这么说，我猜他很久没有享受过这对美乳了吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要在这种时候提到桐人……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯嗯，不提他！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但我肯定不会像他那样的！我永远只听修女姐姐的话！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 油嘴滑舌…就算你这么说我也不会高兴的…唔……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是真的！我会一步不离的跟着修女姐姐，决不做让姐姐伤心的事！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我要每天姐姐的大奶子贴贴！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嚤~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说什么胡话！真是的……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿~ 谁让我这么喜欢姐姐呢？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是，是……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对了，修女姐姐，待会可以有舌吻服务嘛？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不行！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 为什么啊？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 之前做的时候明明那么投入，舌头都主动伸进来了不是吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我上次就跟你说得很清楚了，那时是我一时昏头，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 也就不去追究你打破约定的事，不要得寸进尺。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好吧……姐姐，我听话，不要生气。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那不接吻，我们在这里做爱吧~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 男声:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 修女小姐，请问可以进来吗？"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 111, "indent": 0, "parameters": [0, 300, 0]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 580]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 581]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 274, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 580}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 203, "indent": 0, "parameters": [25, 0, 16, 13, 4]}, {"code": 203, "indent": 0, "parameters": [27, 0, 18, 13, 4]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.target(25,1.5,1);"]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [329]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 581}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[29]"]}, {"code": 111, "indent": 0, "parameters": [0, 274, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 0]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[忏悔室手交回想]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[忏悔室手交影绘]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[忏悔室手交影绘]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 迷途的羔羊啊，请说出自己的罪孽，神会宽恕你的。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哇哦，好有范！就和真正的神职人员一样！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 搞得我都不知道怎么回答了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 如果不喜欢太正式的说法，也可以像这样正常的闲聊。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 其实，基本上没有男玩家来忏悔，如果有做得欠缺的地方，请多多提意见。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](虽然声音被改变了，但亚丝娜这个亲切温柔的感觉，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真是藏都藏不住~)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那用就这样轻松一点的风格吧，修女小姐可以不用那么拘束。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯好，说说看，最近是有什么烦心事吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我的烦心事嘛……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 其实最近还好，烦心的事情之前都解决了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诶？那你今天来是……"]}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 122, "indent": 0, "parameters": [134, 134, 0, 0, 35]}, {"code": 102, "indent": 0, "parameters": [["\\dac 特殊服务（\\c[28]变态度\\c[0]:\\c[18]\\v[134]\\c[0]） ", "\\dac 随性聊天"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "\\dac 特殊服务（\\c[28]变态度\\c[0]:\\c[18]\\v[134]\\c[0]） "]}, {"code": 111, "indent": 1, "parameters": [1, 74, 1, 134, 4]}, {"code": 225, "indent": 2, "parameters": [5, 9, 15, false]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 0, 68], 15, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[28]变态度\\c[101]不足"]}, {"code": 119, "indent": 2, "parameters": ["非变态"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 121, "indent": 2, "parameters": [390, 390, 0]}, {"code": 250, "indent": 2, "parameters": [{"name": "Poison", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 2, "parameters": [[68, 255, 68, 170], 60, true]}, {"code": 111, "indent": 2, "parameters": [0, 389, 1]}, {"code": 122, "indent": 3, "parameters": [74, 74, 1, 0, 3]}, {"code": 356, "indent": 3, "parameters": ["addLog 变态度↑\\c[28]3"]}, {"code": 121, "indent": 3, "parameters": [389, 389, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 274, 0]}, {"code": 111, "indent": 3, "parameters": [0, 19, 0]}, {"code": 231, "indent": 4, "parameters": [90, "", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 4, "parameters": [1]}, {"code": 356, "indent": 4, "parameters": [">图片动画序列 : 图片[90] : 设置动画序列 : 动画序列[29]"]}, {"code": 356, "indent": 4, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[忏悔室手交不悦]"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 听说教堂有特殊服务，所以想来体验一下~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 特殊…服务？！"]}, {"code": 111, "indent": 2, "parameters": [0, 274, 0]}, {"code": 111, "indent": 3, "parameters": [0, 19, 1]}, {"code": 356, "indent": 4, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 155 : 1"]}, {"code": 230, "indent": 4, "parameters": [1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac \\}说的大概是特别圣礼。"]}, {"code": 111, "indent": 3, "parameters": [0, 19, 1]}, {"code": 356, "indent": 4, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 4, "parameters": [1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 哦，那个啊……"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 你说的是特别圣礼吧？那个只有修女长可以做。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 原来如此。那修女小姐知道这些特别圣礼的内容吗？"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 不太清楚。我曾经问过牧师和修女长，"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 但他们都说这是机密内容，不告诉我。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210](果然亚丝娜对这里的事情并不知情。"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 看来牧师老哥除了性癖有些奇怪，总体还算个靠谱的人。)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210](嘛，不过机会不错，再逗一下亚丝娜吧~"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 反正也算是在提醒她这里的事情。)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 其实那个圣礼……是性服务来着。"]}, {"code": 111, "indent": 2, "parameters": [0, 274, 0]}, {"code": 111, "indent": 3, "parameters": [0, 19, 0]}, {"code": 232, "indent": 4, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, false]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 什么？！"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 没错！修女小姐，今天要不要试着给我做个圣礼？"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 最低等级的就好~ "]}, {"code": 122, "indent": 2, "parameters": [100, 100, 0, 0, 582]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\dac 随性聊天"]}, {"code": 118, "indent": 1, "parameters": ["非变态"]}, {"code": 121, "indent": 1, "parameters": [390, 390, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 就是来找修女聊聊天的，难道不可以吗？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你这样应该也有经验的吧！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 虽然是这样，但光是聊天对你来说不是没什么意义吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 怎么没意义，跟你这样的温柔美女陪我聊天，就是最大的意义啊！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 怎么样，等任务结束后，一起去喝一杯吧！"]}, {"code": 111, "indent": 1, "parameters": [0, 274, 0]}, {"code": 111, "indent": 2, "parameters": [0, 19, 0]}, {"code": 232, "indent": 3, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, false]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你……！"]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 582]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 111, "indent": 0, "parameters": [0, 274, 0]}, {"code": 111, "indent": 1, "parameters": [0, 300, 0]}, {"code": 235, "indent": 2, "parameters": [99]}, {"code": 235, "indent": 2, "parameters": [90]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 223, "indent": 2, "parameters": [[-255, -255, -255, 0], 1, false]}, {"code": 232, "indent": 2, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, false]}, {"code": 232, "indent": 2, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, false]}, {"code": 122, "indent": 2, "parameters": [100, 100, 0, 0, 583]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 235, "indent": 1, "parameters": [90]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 582}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 203, "indent": 0, "parameters": [27, 0, 13, 12, 4]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[24] : 像素偏移[0,-30] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [24, 0, 9, 13, 8]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 111, "indent": 0, "parameters": [0, 274, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 0]}, {"code": 205, "indent": 2, "parameters": [24, {"list": [{"code": 42, "parameters": [100], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 42, "parameters": [100], "indent": null}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[24] : 像素偏移[0,0] : 时间[10]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[23] : 像素偏移[12,-6] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [23, 5, false]}, {"code": 111, "indent": 0, "parameters": [0, 390, 1]}, {"code": 122, "indent": 1, "parameters": [95, 95, 0, 0, 52]}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈严肃", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这位先生，请不要说这样轻薄的话！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不然我要向GM投诉了。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [27, 3, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哈哈，气得站起来了~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 果然是我太过火了吗？"]}, {"code": 213, "indent": 1, "parameters": [23, 2, true]}, {"code": 213, "indent": 1, "parameters": [23, 9, false]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈惊讶", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 诶？等一下……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人君，你是桐人君对吧！"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [27, 3, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 被发现了。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 是我，过来逗逗你，嘿嘿。"]}, {"code": 213, "indent": 1, "parameters": [23, 6, true]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嚤~ 不要这样啦，刚才我是真的有点生气哦。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [27, 12, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 对不起喽~ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊，对了，猪田今天有来找你吗？"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [95, 95, 0, 0, 52]}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 怎、怎么可能？！你太放肆了！"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [24, 8, false]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 205, "indent": 1, "parameters": [24, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 1, "parameters": [27, 3, false]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 都气得站起来了？哈哈！不要这么抗拒嘛！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 很多宗教都有这种设定，而且又是在游戏里……"]}, {"code": 213, "indent": 1, "parameters": [23, 5, false]}, {"code": 356, "indent": 1, "parameters": [">行走图额外位置偏移量 : 事件[24] : 像素偏移[-9,0] : 时间[15]"]}, {"code": 205, "indent": 1, "parameters": [24, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 356, "indent": 1, "parameters": [">行走图额外位置偏移量 : 事件[24] : 像素偏移[-9,-24] : 时间[30]"]}, {"code": 205, "indent": 1, "parameters": [24, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不要再说了！就算是游戏也不行！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我不可能跟没有感情的陌生人做这种事情！"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 205, "indent": 1, "parameters": [23, {"list": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈凛然脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 请立刻出去！而且我要向GM举报了！"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [27, 12, false]}, {"code": 205, "indent": 1, "parameters": [27, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [23, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 欸欸欸！亚丝娜，等一下！等一下！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我是桐人！"]}, {"code": 213, "indent": 1, "parameters": [23, 1, true]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈意外脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人？是桐人君？！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 那你为什么要说那种不正经的话？"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [27, 6, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 只是想戏弄你一下……"]}, {"code": 213, "indent": 1, "parameters": [23, 6, false]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈苦笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我就说嘛，教堂怎么会允许那种事情……"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [27, 11, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 那倒不是。特殊圣礼确实是性服务来着……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你先缓和一下情绪，我们坐下来说吧。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 274, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 155 : 1"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 213, "indent": 1, "parameters": [24, 4, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}都是学姐不好，刚才中途停下来，我已经忍不住了。"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [23, 2, false]}, {"code": 111, "indent": 0, "parameters": [0, 390, 0]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈欲言又止", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈惊讶2", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 什么？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 583]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 390, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 583}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 203, "indent": 0, "parameters": [27, 0, 13, 12, 4]}, {"code": 111, "indent": 0, "parameters": [0, 274, 0]}, {"code": 111, "indent": 1, "parameters": [0, 300, 1]}, {"code": 119, "indent": 2, "parameters": ["跳到CG"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[24] : 像素偏移[-6,-48] : 时间[15]"]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(24).setPriorityType(0);"], "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(24).setPriorityType(0);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[23] : 像素偏移[6,0] : 时间[15]"]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 15, false]}, {"code": 118, "indent": 0, "parameters": ["跳到CG"]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 0]}, {"code": 203, "indent": 0, "parameters": [29, 1, 197, 198, 0]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[29]"]}, {"code": 111, "indent": 0, "parameters": [0, 274, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 0]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[忏悔室骑乘回想]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[忏悔室骑乘影绘]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[忏悔室骑乘影绘]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [29, {"list": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 15, "pitch": 50, "pan": 0}], "indent": null}, {"code": 15, "parameters": [52], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 15, "pitch": 50, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [52], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 0, "parameters": [0, 390, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊！他……没有，怎么了……？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我刚才上线的时候他就在下城区，我还以为他又来忏悔了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不过这个无所谓，我想说的是，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 如果他再来，你还是告诉他你的真实身份比较好。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯嗯！好！我会的……！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 那就好。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 那个……桐人君！你不会真的要在这里……哈啊…跟我一直聊天吧？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我！毕竟还在任务中！这样感觉！不太好！嗯啊……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 也是。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 那我就去公会随便接个任务好了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯嗯！！再见！"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 1, "parameters": [29, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 111, "indent": 1, "parameters": [0, 274, 1]}, {"code": 122, "indent": 2, "parameters": [100, 100, 0, 0, 584]}, {"code": 201, "indent": 2, "parameters": [0, 345, 18, 16, 8, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 122, "indent": 2, "parameters": [100, 100, 0, 0, 586]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊？！！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我是奇怪……嗯…这里也不是地下街，哈啊……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 怎么会有这种服务……还有……桐人君，嗯，你是……怎么知道的？ "]}, {"code": 111, "indent": 1, "parameters": [0, 274, 0]}, {"code": 111, "indent": 2, "parameters": [0, 19, 1]}, {"code": 356, "indent": 3, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 155 : 1"]}, {"code": 230, "indent": 3, "parameters": [1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\}都去嫖妓了，接受个圣礼不也挺正常嘛……"]}, {"code": 111, "indent": 2, "parameters": [0, 19, 1]}, {"code": 356, "indent": 3, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 3, "parameters": [1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 387, 0]}, {"code": 118, "indent": 2, "parameters": ["接受过"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 这个嘛……"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 没有没有！"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 其实那个牧师和我有些渊源，他告诉我的。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哦~ \\i[90]可是……牧师和修女长……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 在现实里，不是，哈啊…真正的夫妻吗？怎么会……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呃，怎么说呢……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 似乎是他们夫妻俩的一种情趣。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac \\}这种事情……情趣？还和桐人交好……难道桐人也…？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac \\c[210](不知道为什么，虽然被帘子隔着，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但总感觉现在亚丝娜格外色气，是因为修女身份的加成吗？)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 那个，亚丝娜，本来只是为了逗逗你的……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但刚才说到牧师夫妇的事情，再配上你坚决的态度……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不知怎么的，让我现在有了感觉……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 所以，你看，能不能，真的也给我做个圣礼呢？"]}, {"code": 111, "indent": 1, "parameters": [0, 274, 0]}, {"code": 111, "indent": 2, "parameters": [0, 19, 1]}, {"code": 356, "indent": 3, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 155 : 1"]}, {"code": 230, "indent": 3, "parameters": [1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210](正好给我了一个起身的理由，可以让猪田君停下。"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 而且既然修女长都给桐人做过了……争夺桐人君的战斗，我可不能输啊！)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\}学姐，不要答应吧……"]}, {"code": 111, "indent": 2, "parameters": [0, 19, 1]}, {"code": 356, "indent": 3, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 3, "parameters": [1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯嗯……桐人君，你进到里屋来吧！我现在起来…！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 给你开门！"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 1, "parameters": [29, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 584]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 111, "indent": 1, "parameters": [0, 274, 0]}, {"code": 111, "indent": 2, "parameters": [0, 300, 1]}, {"code": 122, "indent": 3, "parameters": [100, 100, 0, 0, 585]}, {"code": 121, "indent": 3, "parameters": [298, 298, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 390, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 584}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 203, "indent": 0, "parameters": [27, 0, 13, 12, 4]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[24] : 像素偏移[0,0] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [24, 0, 9, 12, 6]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": 0}, {"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 37, "indent": null}, {"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 111, "indent": 0, "parameters": [0, 274, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 0]}, {"code": 205, "indent": 2, "parameters": [24, {"list": [{"code": 42, "parameters": [100], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 42, "parameters": [100], "indent": null}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [27, 11, false]}, {"code": 111, "indent": 0, "parameters": [0, 274, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [27, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 14, "parameters": [0, 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [24, 7, false]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 111, "indent": 0, "parameters": [0, 274, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[27] : 像素偏移[-6,0] : 时间[15]"]}, {"code": 205, "indent": 0, "parameters": [27, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 33, "indent": null}, {"code": 39, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[27] : 像素偏移[6,0] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [27, 0, 10, 14, 4]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[27] : 像素偏移[0,0] : 时间[60]"]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [27, {"list": [{"code": 34, "indent": null}, {"code": 29, "parameters": [2], "indent": null}, {"code": 40, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [23, 11, false]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 52]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亲爱的信徒大人，你希望我为你主持哪种……圣礼呢？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [27, 11, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 圣唇印礼……可以吗？"]}, {"code": 213, "indent": 0, "parameters": [23, 2, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈欲言又止", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那是什么…？ "]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG明日奈转移话题", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呃，我是说……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 请信徒大人先陈述仪式的流程。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [27, 11, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就是由修女大人…唔……用嘴巴，用嘴巴……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 在本人下体的…生殖器上，施加神圣的刻印……"]}, {"code": 213, "indent": 0, "parameters": [23, 9, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈沉默脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}原来是口交啊……\\{"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [27, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，其实我还想有个额外的请求……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 能不能最后喝下我的精液……"]}, {"code": 213, "indent": 0, "parameters": [27, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我，我知道你有些讨厌精液……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但是这毕竟是游戏里…"]}, {"code": 213, "indent": 0, "parameters": [23, 7, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈失望脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [27, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 实在不行，我们调低一点嗅觉和味觉参数…"]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [23, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好吧，桐人……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [24, 5, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](可恶的黑衣男！学姐的初次口交饮精啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 应该是我的才对啊！)"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈感动", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐…我是说信徒大人，请你坐在地上，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 由我为你主持圣唇印礼……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 585]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 390, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 585}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 203, "indent": 0, "parameters": [24, 0, 9, 11, 6]}, {"code": 356, "indent": 0, "parameters": ["LAYER 355 2 教堂-夜晚光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 355 3 教堂-夜晚阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 213, "indent": 0, "parameters": [-1, 7, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](今天的任务比我想象中挑战度更高，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 也不知道是不是出力过猛，感觉好困啊……)"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 111, "indent": 1, "parameters": [1, 197, 0, 18, 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](亚丝娜应该也快结束了吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 下线前最后再跟她打个招呼~ )"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "Knock", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 修女小姐，请问现在可以进来吗？"]}, {"code": 213, "indent": 0, "parameters": [24, 5, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<不耐烦的男声>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我这边才刚开始呢！等着吧！"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜的“生意”一如既往的兴隆啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但有些熬不住了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 给她留个言，下线睡觉吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 也不知道小直回去了没有……"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 586]}, {"code": 250, "indent": 0, "parameters": [{"name": "LNSM_SE08_Sense8", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 42, "parameters": [255], "indent": 0}, {"code": 39, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 129, "indent": 0, "parameters": [1, 0, false]}, {"code": 129, "indent": 0, "parameters": [4, 1, false]}, {"code": 356, "indent": 0, "parameters": [">菜单GIF : GIF[1] : 隐藏"]}, {"code": 356, "indent": 0, "parameters": [">菜单GIF : GIF[7] : 显示"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 98, 14, 14, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 390, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 585}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [2, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 设置动画序列 : 动画序列[29]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 播放简单状态元集合 : 集合[忏悔室桐人口交]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "bensound-sexy", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈呣……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊，亚丝娜温暖的口腔，好舒服……"]}, {"code": 111, "indent": 0, "parameters": [0, 274, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 155 : 1"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](还是硬得好慢……但比上次稍微好一些。)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](可恶可恶！气死我了！不听话的学姐！要受到正义大棒的制裁！)"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 0, 0, 10, false]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 231, "indent": 0, "parameters": [8, "", 0, 0, 346, 400, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">图片滤镜 : 图片[8] : 模糊滤镜 : 50 : 1"]}, {"code": 356, "indent": 0, "parameters": [">图片动态遮罩板A : 简单透视镜 : 图片[8] : 样式[20]"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动态遮罩板A : 图片[8] : 启用该插件的动态遮罩板"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[8] : 设置动画序列 : 动画序列[6]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[8] : 播放简单状态元集合 : 集合[忏悔室后入插入闪图]"]}, {"code": 231, "indent": 0, "parameters": [5, "剧情小图背景3", 0, 0, -816, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [6, "剧情小图背景1", 0, 0, -29, -7, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [7, "剧情小图背景2", 0, 0, 29, 7, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [6, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 0, "parameters": [7, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 0, "parameters": [8, 0, 0, 0, 375, 407, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 0, "parameters": [5, 0, 0, 0, -20, 0, 100, 100, 255, 0, 15, true]}, {"code": 232, "indent": 0, "parameters": [5, 0, 0, 0, 20, 0, 100, 100, 255, 0, 90, false]}, {"code": 232, "indent": 0, "parameters": [6, 0, 0, 0, 58, 14, 100, 100, 0, 0, 105, false]}, {"code": 232, "indent": 0, "parameters": [7, 0, 0, 0, -29, -7, 100, 100, 0, 0, 105, false]}, {"code": 232, "indent": 0, "parameters": [5, 0, 0, 0, 836, 0, 100, 100, 0, 0, 105, false]}, {"code": 232, "indent": 0, "parameters": [8, 0, 0, 0, 404, 414, 100, 100, 0, 0, 120, false]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "挿入する時の音3（勢いよく一気に入れる）", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 播放简单状态元集合 : 集合[忏悔室后入口交]"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘5", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [44], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 70, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [44], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 70, "pan": 0}], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔！！！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊！亚丝娜，怎么突然激烈起来了……？"]}, {"code": 111, "indent": 0, "parameters": [0, 274, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 155 : 1"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](猪田君怎么这个时候插入了？！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac真是的！！！在搞什么呀？)"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，不行，这样似乎太激烈了……"]}, {"code": 111, "indent": 0, "parameters": [0, 274, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 155 : 1"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](对不起，桐人君，但现在完全没办法控制嘴巴的力道了……)"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好爽！但我会坚持不了太久的……"]}, {"code": 111, "indent": 0, "parameters": [0, 274, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 155 : 1"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](桐人君变得好硬！这样他更喜欢吗？)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](可恶的黑衣男！看到他那张享受的脸就想吐！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我插我插我插！)"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我…我要射了！！！"]}, {"code": 111, "indent": 0, "parameters": [0, 274, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 155 : 1"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](可恶可恶可恶，我的母猪老婆凭什么吃这个混蛋的精液呀！)"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 播放动作 : 动作元[忏悔室后入口交射精]"]}, {"code": 230, "indent": 0, "parameters": [200]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 250, "indent": 0, "parameters": [{"name": "挿入03", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 250, "indent": 0, "parameters": [{"name": "あぁん！", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精･外･短い6", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [115]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[29]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[忏悔室口交后入事后]"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [5]}, {"code": 235, "indent": 0, "parameters": [6]}, {"code": 235, "indent": 0, "parameters": [7]}, {"code": 235, "indent": 0, "parameters": [8]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 245, "indent": 0, "parameters": [{"name": "1-女喘气", "volume": 5, "pitch": 110, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对不起……哈啊…桐人君，最后的时候……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯嗯…还是觉得没办法接受吃下精液，所以我才……哈啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没事没事，刚才我非常非常非常的舒服。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 吞精这种变态的要求，是我太唐突了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯！你舒服！就好！哈啊~ 嗯…以后还会继续给你做的……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…亚丝娜，抱歉，刚才射太猛，困意突然上来了…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不行了…虽然和医生约定的时限还有些时间，我可能要先下…唔……"]}, {"code": 111, "indent": 0, "parameters": [0, 274, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 155 : 1"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](呵呵，幸好是个没用的废物。终于没有人打扰我和母猪的快乐交配了。)"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯，你去吧……啊！我一会……也下线的……"]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 249, "indent": 0, "parameters": [{"name": "LNSM_ME06_Sleep2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 586]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 111, "indent": 0, "parameters": [0, 274, 1]}, {"code": 129, "indent": 1, "parameters": [1, 0, false]}, {"code": 129, "indent": 1, "parameters": [4, 1, false]}, {"code": 356, "indent": 1, "parameters": [">菜单GIF : GIF[1] : 隐藏"]}, {"code": 356, "indent": 1, "parameters": [">菜单GIF : GIF[7] : 显示"]}, {"code": 201, "indent": 1, "parameters": [0, 98, 14, 14, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 274, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 586}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 十分钟后……"]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[29]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[忏悔室抱位慢]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [44], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 50, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [44], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 50, "pan": 0}], "indent": null}]}, {"code": 241, "indent": 0, "parameters": [{"name": "bensound-sexy", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐，不要这个表情啦，从刚才就一直像看垃圾一样看着我。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 虽然也很带感就是了，但我还是更喜欢学姐开心的表情。"]}, {"code": 111, "indent": 0, "parameters": [0, 390, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不开心…猪田君……唔……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你刚才有点过分……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 怎么就是我过分？明明知道我涨得难受，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 学姐还在我的面前给学长口交，这不是故意刺激我吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人君，啊…是我的男朋友……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 给他口交有什么不对？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哼…那学姐口交的时候撅着你下流的大屁股，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不是邀请我插入是什么？"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你呀…啊嗯……总是喜欢胡闹……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 为什么要中途插进来？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 什么啊，明明是学姐只顾着聊天，完全就敷衍我这边的！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我看就是故意欲擒故纵，想要被我插入才对！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哼……我才不想…！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯，啊……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 学姐又胡说了，现在流出来那么多淫水，而且夹得那么紧，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 绝对是喜欢我的鸡巴插入！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 在教堂这种神圣的地方做这样的事，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你也是不怕天罚……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呵呵，学姐怕是不知道，这个教堂本来就是会提供性服务的！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 修女长会给信徒做的那个特殊圣礼，其实就是口交做爱这些哦~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 诶？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 是真的。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但我没有试过，比地下街妓院还贵，我哪有那个钱。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 难怪每次我问起，都说特殊圣礼是秘密……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 所以，学姐，我们一起好好享受吧~ "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 自古男女交合都算是一种神圣的仪式呢！"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 随你……怎么说吧……快点结束就是了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐还没高潮，我怎么能结束呢？不过，忍住不射也是很辛苦的呢~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 换做别人的话，就算是身经百战的老嫖客，肯定已经射几次了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 什么换做别人？还老嫖客？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 把我说得像是随便的妓女一样！我生气了别闹了快点停下来！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对不起，对不起，是我说错话！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那我只有拿出真本事了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 什么？！等，等一下！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 放心，作为道歉，绝对把学姐伺候舒服~"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 241, "indent": 0, "parameters": [{"name": "日出ズルstyle instrumental", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[忏悔室抱位快]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 啪啪啪5 50 100 0"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 二十分钟后……"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 245, "indent": 0, "parameters": [{"name": "娇喘8", "volume": 25, "pitch": 110, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊~ \\i[90] 不，不要……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要再激烈了……呼啊啊，啊啊啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要就是要，我知道我知道！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 来吧，说说哪里觉得舒服，让我好好帮学姐磨一磨～"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊啊！我不知道……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真的不要了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 骚水都流成这样了还嘴硬，看我的猪突猛进！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 欧拉欧拉！说嘛！说嘛！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯啊啊啊，呀啊啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 捅到的地方都很舒服……嗯嗯啊~~~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这就对了嘛！放松下来好好享受吧~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210] (还以为上次那种狂野性爱已经完全征服她了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没想到还是要先击穿理智防线……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210] (妈的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看来必须要再想些手段才行！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好爽……猪田君的大鸡巴真的好舒服……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呼啊，啊啊啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我也好爽！学姐，我们的身体就是相性最好的存在！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不行了……这样根本承受不住的……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐快高潮了吧？每次高潮前的收缩都让我爽到极点啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我也要忍不住了！！！今日第一发！！！"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[忏悔室抱位射精]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精-03", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "呻吟4", "volume": 35, "pitch": 110, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [160]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 三十分钟后……"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[29]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[忏悔室配种位]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "喘ぎ声とピストン　強", "volume": 45, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐，你的骚穴太爽了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我第二发也快要来了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯嗯！！太舒服了……啊啊啊~~~ \\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 射吧！快射吧！我快疯掉了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我想和学姐一起去！我想要亲学姐的小嘴！我想吸学姐的舌头！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们一边接吻一边高潮吧！好不好！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊啊~ \\i[90]\\i[90]\\i[90] 不行…只有这个…… "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我不能再犯这个错误了…！别的什么都好…哦齁哦哦~~~\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 行吧……那就结束以后给我清洁口交！再把精液全喝下去！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那我今天就不坚持亲嘴了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 随…随你……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 全都随你了……哦齁齁齁齁~\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好耶……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊！不忍了不忍了！我要射了！"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[忏悔室配种位射精]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "呻吟3", "volume": 35, "pitch": 110, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [75]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精音・中出し（短い）①", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精音・中出し（短い）①", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [275]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 片刻之后……"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[29]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[忏悔室清洁口交]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [36], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [36], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘10", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐的嘴巴好舒服！和小穴各有各的爽！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是一种更温柔的感觉~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊~ 真是过瘾啊~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐，混合着精液和骚水的鸡巴好吃吗？"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐？"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真的失神了吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 刚才调整姿势的时候还以为只是累了不想动。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可惜可惜！我还想听听学姐的感想的呢！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过你那么喜欢我给你的特制咖啡，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 肯定会很爱死这个的味道的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦哦，要来了要来了！！！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐，好好享受我的种汁特饮吧！"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[忏悔室清洁口交事后]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[忏悔室清洁口交射精]"]}, {"code": 230, "indent": 0, "parameters": [84]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘3", "volume": 35, "pitch": 110, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精音Bタイプ外出し①（勢いよくびゅるるるぅ～）", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [66]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精音Bタイプ外出し①（勢いよくびゅるるるぅ～）", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯呃…呜呜…咕嘟……"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](喉咙里好黏…我这是……？)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](呜…！头好晕……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还有这股腥臭的味道…但是为什么…我好像并不讨厌……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 在吸我尿道里的精液？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈，果然喜欢的不得了吧！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿嘿~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那么学姐，以后每次做完以后都给我清洁口交吧！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…嗯嗯……"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 129, "indent": 0, "parameters": [1, 0, false]}, {"code": 129, "indent": 0, "parameters": [4, 1, false]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 1, 82]}, {"code": 121, "indent": 0, "parameters": [390, 390, 1]}, {"code": 121, "indent": 0, "parameters": [274, 274, 1]}, {"code": 121, "indent": 0, "parameters": [300, 300, 1]}, {"code": 201, "indent": 0, "parameters": [0, 81, 23, 10, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 274, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 587}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 8}, {"id": 26, "name": "EV026", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 735, 0]}, {"code": 203, "indent": 1, "parameters": [19, 0, 27, 11, 2]}, {"code": 203, "indent": 1, "parameters": [21, 0, 28, 11, 2]}, {"code": 203, "indent": 1, "parameters": [23, 0, 25, 12, 2]}, {"code": 203, "indent": 1, "parameters": [31, 0, 20, 17, 0]}, {"code": 203, "indent": 1, "parameters": [32, 0, 21, 17, 0]}, {"code": 203, "indent": 1, "parameters": [33, 0, 22, 17, 0]}, {"code": 203, "indent": 1, "parameters": [34, 0, 28, 17, 0]}, {"code": 203, "indent": 1, "parameters": [35, 0, 29, 17, 0]}, {"code": 203, "indent": 1, "parameters": [36, 0, 30, 17, 0]}, {"code": 203, "indent": 1, "parameters": [37, 0, 20, 20, 0]}, {"code": 203, "indent": 1, "parameters": [38, 0, 21, 20, 0]}, {"code": 203, "indent": 1, "parameters": [39, 0, 28, 20, 0]}, {"code": 203, "indent": 1, "parameters": [40, 0, 29, 20, 0]}, {"code": 203, "indent": 1, "parameters": [41, 0, 30, 20, 0]}, {"code": 203, "indent": 1, "parameters": [42, 0, 20, 23, 0]}, {"code": 203, "indent": 1, "parameters": [43, 0, 21, 23, 0]}, {"code": 203, "indent": 1, "parameters": [44, 0, 22, 23, 0]}, {"code": 203, "indent": 1, "parameters": [45, 0, 28, 23, 0]}, {"code": 203, "indent": 1, "parameters": [46, 0, 29, 23, 0]}, {"code": 203, "indent": 1, "parameters": [47, 0, 30, 23, 0]}, {"code": 203, "indent": 1, "parameters": [48, 0, 21, 12, 0]}, {"code": 203, "indent": 1, "parameters": [49, 0, 29, 12, 0]}, {"code": 203, "indent": 1, "parameters": [51, 0, 21, 12, 0]}, {"code": 203, "indent": 1, "parameters": [50, 0, 22, 11, 0]}, {"code": 203, "indent": 1, "parameters": [52, 0, 23, 11, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 579, 0]}, {"code": 203, "indent": 1, "parameters": [28, 0, 17, 12, 0]}, {"code": 203, "indent": 1, "parameters": [23, 0, 9, 12, 2]}, {"code": 356, "indent": 1, "parameters": [">行走图额外位置偏移量 : 事件[24] : 像素偏移[0,-24] : 时间[1]"]}, {"code": 203, "indent": 1, "parameters": [24, 0, 9, 13, 8]}, {"code": 111, "indent": 1, "parameters": [0, 374, 0]}, {"code": 122, "indent": 2, "parameters": [100, 100, 0, 0, 580]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 25, "y": 5}, {"id": 27, "name": "EV027桐人", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "桐人", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 28, "name": "EV028星星点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 405}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : star : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 5"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 38, "y": 0}, {"id": 29, "name": "EV029声音源", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 0}, {"id": 30, "name": "EV030", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 730}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 203, "indent": 0, "parameters": [23, 0, 11, 24, 8]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.target(23,2,1);"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[29]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[23] : 像素偏移[0,12] : 时间[1]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[24] : 像素偏移[0,-12] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [24, 0, 11, 25, 8]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 100"]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这是哪里？画面好模糊，似乎是被某种魔法干扰了似的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这有两个人，是在……"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 玩屁股？！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 10 : 1"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 25 : 1"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [24, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}说是让我测试道具，塞给了我一个护符，\\{"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}但主设备临时又出故障，修了半天也没好，就先放我回过来……"]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "挿入する時の音3（勢いよく一気に入れる）", "volume": 25, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "あぁん！", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [23, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}啊……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可以听到声音，但很不清晰……"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 10, true]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.restore(1);"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 关闭滤镜"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 着色滤镜 : 关闭滤镜"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 继续观看？"]}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 122, "indent": 0, "parameters": [134, 134, 0, 0, 30]}, {"code": 102, "indent": 0, "parameters": [["\\dac 继续观察一会（\\c[28]变态度\\c[0]:\\c[18]\\v[134]\\c[0]） ", "\\dac 没有必要再看"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "\\dac 继续观察一会（\\c[28]变态度\\c[0]:\\c[18]\\v[134]\\c[0]） "]}, {"code": 111, "indent": 1, "parameters": [1, 74, 1, 134, 4]}, {"code": 225, "indent": 2, "parameters": [5, 9, 15, false]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 0, 68], 15, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[28]变态度\\c[101]不足"]}, {"code": 119, "indent": 2, "parameters": ["非变态"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 250, "indent": 2, "parameters": [{"name": "Poison", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": [">地图滤镜 : 模糊滤镜 : 500"]}, {"code": 356, "indent": 2, "parameters": [">地图滤镜 : 着色滤镜 : 黑白 : 255"]}, {"code": 224, "indent": 2, "parameters": [[68, 255, 68, 170], 60, false]}, {"code": 122, "indent": 2, "parameters": [74, 74, 1, 0, 1]}, {"code": 356, "indent": 2, "parameters": ["addLog 变态度↑\\c[28]1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 把画面拉近一点观察吧……"]}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 231, "indent": 2, "parameters": [1, "教会肛交开发-003-准备插入", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 232, "indent": 2, "parameters": [1, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 10 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 25 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}不要这么突然…！好奇怪的感觉……"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}没事没事，这就是正常的感觉啦。这是最小尺寸的一款拉珠了，你看，第一颗已经完全进去了呢！"]}, {"code": 231, "indent": 2, "parameters": [2, "教会肛交开发-005-生气", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}我怎么看呀？真是的！"]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}等等…第二颗好像更大…"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}对啊，就是要慢慢变大才有意思嘛！来，深呼吸，放松~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}唉…你轻点……"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}好啦好啦，我会很温柔的……"]}, {"code": 231, "indent": 2, "parameters": [2, "教会肛交开发-004-插入中", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 250, "indent": 2, "parameters": [{"name": "挿入する時の音3（勢いよく一気に入れる）", "volume": 25, "pitch": 90, "pan": 0}]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}呜！"]}, {"code": 231, "indent": 2, "parameters": [1, "教会肛交开发-006-脱力", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}唔……哈啊…哈啊……"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}你的身体似乎开始接受了呢~"]}, {"code": 231, "indent": 2, "parameters": [2, "教会肛交开发-003-准备插入", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}别…别说出来……"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}哈哈，害羞什么嘛！来！试试第三颗，这个大小刚刚好~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}不要…这个太大了…"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}没关系的，相信我，你完全可以接受这个尺寸！"]}, {"code": 231, "indent": 2, "parameters": [1, "教会肛交开发-004-插入中", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 250, "indent": 2, "parameters": [{"name": "挿入する時の音3（勢いよく一気に入れる）", "volume": 25, "pitch": 90, "pan": 0}]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 0, 0, 25, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}可是…啊…"]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 231, "indent": 2, "parameters": [2, "教会肛交开发-006-脱力", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}看吧，我说什么来着！你的身体已经完全放松了呢~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}你…你怎么这样……"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}嘿嘿，因为我最了解你的身体啦！要不要试试抽动的感觉？"]}, {"code": 231, "indent": 2, "parameters": [1, "教会肛交开发-005-生气", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}等…等一下…让我缓缓……"]}, {"code": 231, "indent": 2, "parameters": [2, "教会肛交开发-004-插入中", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 25, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}没关系的！相信我！"]}, {"code": 230, "indent": 2, "parameters": [15]}, {"code": 250, "indent": 2, "parameters": [{"name": "pierrot_slidedoorschool", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [15]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[教会肛交调教]"]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 44, "parameters": [{"name": "ブチュ２", "volume": 50, "pitch": 70, "pan": 0}], "indent": null}, {"code": 15, "parameters": [48], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 2, "parameters": [{"code": 44, "parameters": [{"name": "ブチュ２", "volume": 50, "pitch": 70, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [48], "indent": null}]}, {"code": 245, "indent": 2, "parameters": [{"name": "娇喘", "volume": 40, "pitch": 110, "pan": 0}]}, {"code": 232, "indent": 2, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 25, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\}唔…！哈啊…啊啊……"]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac诗乃:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 看什么看得那么起劲？"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 下面都翘得老高了，已经做好付报酬的准备了？"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["Galv.ZOOM.restore(1);"]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 235, "indent": 2, "parameters": [1]}, {"code": 235, "indent": 2, "parameters": [2]}, {"code": 235, "indent": 2, "parameters": [99]}, {"code": 356, "indent": 2, "parameters": ["stop_bgs2"]}, {"code": 122, "indent": 2, "parameters": [100, 100, 0, 0, 732]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 356, "indent": 2, "parameters": [">地图滤镜 : 模糊滤镜 : 关闭滤镜"]}, {"code": 356, "indent": 2, "parameters": [">地图滤镜 : 着色滤镜 : 关闭滤镜"]}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 201, "indent": 2, "parameters": [0, 370, 35, 29, 2, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\dac 没有必要再看"]}, {"code": 118, "indent": 1, "parameters": ["非变态"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 确实听不清。更不用说舞厅还有隔音魔法的干扰。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 看来无法通过这个水晶球进行调查。"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["Galv.ZOOM.restore(1);"]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 731]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 356, "indent": 1, "parameters": [">地图滤镜 : 模糊滤镜 : 关闭滤镜"]}, {"code": 356, "indent": 1, "parameters": [">地图滤镜 : 着色滤镜 : 关闭滤镜"]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 201, "indent": 1, "parameters": [0, 370, 35, 29, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 731}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 20}, {"id": 31, "name": "EV031", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 735}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor1", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 1}, {"id": 32, "name": "EV032", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 735}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor1", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 1}, {"id": 33, "name": "EV033", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 735}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor1", "direction": 8, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 1}, {"id": 34, "name": "EV034", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 735}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor1", "direction": 8, "pattern": 1, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 1}, {"id": 35, "name": "EV035", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 735}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor1", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 1}, {"id": 36, "name": "EV036", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 735}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor2", "direction": 8, "pattern": 1, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 1}, {"id": 37, "name": "EV037", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 735}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor1", "direction": 8, "pattern": 1, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 1}, {"id": 38, "name": "EV038", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 735}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor1", "direction": 8, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 1}, {"id": 39, "name": "EV039", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 735}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor1", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 2}, {"id": 40, "name": "EV040", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 735}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor2", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 2}, {"id": 41, "name": "EV041", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 735}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor2", "direction": 8, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 2}, {"id": 42, "name": "EV042", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 735}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor2", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 2}, {"id": 43, "name": "EV043", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 735}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor2", "direction": 8, "pattern": 1, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 2}, {"id": 44, "name": "EV044", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 735}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor2", "direction": 8, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 2}, {"id": 45, "name": "EV045", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 735}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor2", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 2}, {"id": 46, "name": "EV046", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 735}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor3", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 2}, {"id": 47, "name": "EV047", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 735}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor3", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 3}, {"id": 48, "name": "EV048", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 735}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 245, "indent": 0, "parameters": [{"name": "按摩棒2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["audiosource bgs 23"]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 事件[23] : 左右震动 : 持续时间[无限] : 周期[12] : 震动幅度[1]"]}, {"code": 205, "indent": 0, "parameters": [23, {"list": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜修女", 1], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔呼！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 今天真是怪了，这么多人……"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且亚丝娜怎么在台上演讲？"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 抱歉，请问今天是有什么特别的活动吗？"]}, {"code": 205, "indent": 0, "parameters": [38, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<信徒>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 首都来的大主教今天来视察我们教堂，而且特意举办了一场布道会，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 参加的信徒都能获得'主的恩典'buff，整整持续一个月呢！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<信徒>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你一看就不是信徒，不然也不会连这个活动都不知道。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 非信徒拿不到buff奖励的，没必要参加。"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我其实对入教也有点兴趣，可能等会还是会听一下……"]}, {"code": 213, "indent": 0, "parameters": [-1, 9, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 另外我还有个疑问，为什么不是主教自己讲，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在台上的，似乎是实习修女吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<信徒>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 轮着讲的，听众也都换了几波人了。最后才轮到她。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个实习小丫头长得挺美，但水平不行，声音一直发抖。太紧张了应该是。"]}, {"code": 213, "indent": 0, "parameters": [38, 7, false]}, {"code": 205, "indent": 0, "parameters": [38, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<信徒>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不能跟你多说啦，不然我的聆听时间要归零重算了！"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 213, "indent": 0, "parameters": [-1, 12, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦哦，好的，谢谢。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](稍微听一下亚丝娜的演讲吧~ )"]}, {"code": 213, "indent": 0, "parameters": [23, 8, true]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 55]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈叹气脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[2] : 左右震动 : 持续时间[无限] : 周期[12] : 震动幅度[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 正如圣典所说，爱与…啊…与宽恕是最重要的…品德…"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们要学会…唔…学会在困境中保持…保持信念…就像…就像…啊…"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](确实声音有些发抖。)"]}, {"code": 213, "indent": 0, "parameters": [23, 7, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈妥协", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[2] : 左右震动 : 持续时间[无限] : 周期[12] : 震动幅度[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 主曾教导我们，面对诱惑时要保持坚定…唔…内心的宁静是抵抗外界干扰的…重要武器……"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG明日奈叹气脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 10, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[3] : 左右震动 : 持续时间[无限] : 周期[12] : 震动幅度[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就像…啊…就像圣徒们展现的那样，即使…即使在最艰难的时刻…也要保持…保持纯洁的心灵……"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 4"]}, {"code": 231, "indent": 0, "parameters": [4, "FOG明日奈凛然脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [4, 0, 0, 0, 310, 20, 100, 100, 255, 0, 10, true]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[4] : 左右震动 : 持续时间[无限] : 周期[12] : 震动幅度[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们…必须学会…控制…啊…控制自己的欲…欲望…这是主赐予我们的…考验……"]}, {"code": 235, "indent": 0, "parameters": [4]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](感觉她不像是因为紧张才这样的。)"]}, {"code": 213, "indent": 0, "parameters": [23, 11, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈沉默脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[2] : 左右震动 : 持续时间[无限] : 周期[12] : 震动幅度[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 在…在生活中…唔…我们常常会遇到…各种…诱…诱惑…但是…但是要记住…啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 要…要时刻谨记…哈啊…主的教导…不要…不要屈服于…肉体的…软弱……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 7, false]}, {"code": 122, "indent": 0, "parameters": [184, 184, 0, 0, 5]}, {"code": 117, "indent": 0, "parameters": [119]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](亚丝娜的状态真的很奇怪…脸越来越红了……)"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈苦笑脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[2] : 左右震动 : 持续时间[无限] : 周期[12] : 震动幅度[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 让…让我们一起…祈祷…愿主的光辉…指引…我们…抵抗…一切…诱惑……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 245, "indent": 0, "parameters": [{"name": "按摩棒2", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 事件[23] : 左右震动 : 持续时间[无限] : 周期[9] : 震动幅度[1]"]}, {"code": 213, "indent": 0, "parameters": [23, 1, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG明日奈绝顶2", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 10, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[3] : 左右震动 : 持续时间[无限] : 周期[9] : 震动幅度[2]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…咿咿咿……！"]}, {"code": 213, "indent": 0, "parameters": [23, 12, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 4"]}, {"code": 231, "indent": 0, "parameters": [4, "FOG明日奈焦急脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [4, 0, 0, 0, 310, 20, 100, 100, 255, 0, 10, true]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[4] : 左右震动 : 持续时间[无限] : 周期[6] : 震动幅度[2]"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我是说…我们要…坚持…坚持…信仰…不要…不要…啊……"]}, {"code": 235, "indent": 0, "parameters": [4]}, {"code": 213, "indent": 0, "parameters": [-1, 9, false]}, {"code": 122, "indent": 0, "parameters": [184, 184, 0, 0, 10]}, {"code": 117, "indent": 0, "parameters": [119]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](该不会是生病了吧？那个病毒！不会吧？！)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "按摩棒2", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 事件[23] : 左右震动 : 持续时间[无限] : 周期[6] : 震动幅度[1]"]}, {"code": 213, "indent": 0, "parameters": [23, 11, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈不适", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 图片[2] : 左右震动 : 持续时间[无限] : 周期[6] : 震动幅度[2]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对…对不起…我今天可能有些…不适…讲道到此…结束…请大家自行…祈祷…！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 事件[23] : 纵向挤扁 : 时间[60] : 纵向比例[1.5]"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](亚丝娜……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 糟糕！赶紧下线跟她电话确认一下！)"]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 玩家 : 纵向挤扁 : 时间[60] : 纵向比例[1.5]"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["audiosource bgs reset"]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 736]}, {"code": 117, "indent": 0, "parameters": [151]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 10]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 6, 2]}, {"code": 355, "indent": 1, "parameters": ["$gameMap.event(23).requestBalloon(8);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 77, 0, 9, 2]}, {"code": 355, "indent": 2, "parameters": ["$gameMap.event(23).requestBalloon(11);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["$gameMap.event(23).requestBalloon(7);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["var waitFrames = Math.floor(Math.random() * 30) + 60;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 736}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 6}, {"id": 49, "name": "EV049主教", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "banny", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 3}, {"id": 50, "name": "EV050圣殿骑士", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "fsm_ex_chara04", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 3}, {"id": 51, "name": "EV051主教", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "banny", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 3}, {"id": 52, "name": "EV052大主教", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "fsm_ex_chara04", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 3}, null, {"id": 54, "name": "EV054", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 912, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 235, "indent": 0, "parameters": [5]}, {"code": 235, "indent": 0, "parameters": [6]}, {"code": 117, "indent": 0, "parameters": [23]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[29]"]}, {"code": 203, "indent": 0, "parameters": [21, 0, 26, 17, 2]}, {"code": 203, "indent": 0, "parameters": [19, 0, 25, 17, 2]}, {"code": 203, "indent": 0, "parameters": [52, 0, 25, 19, 8]}, {"code": 203, "indent": 0, "parameters": [51, 0, 24, 20, 8]}, {"code": 203, "indent": 0, "parameters": [49, 0, 26, 20, 8]}, {"code": 203, "indent": 0, "parameters": [50, 0, 25, 21, 8]}, {"code": 203, "indent": 0, "parameters": [23, 0, 11, 24, 8]}, {"code": 205, "indent": 0, "parameters": [19, {"list": [{"code": 45, "parameters": ["$gameMap.event(19).requestBalloon(8);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(52).requestBalloon(8);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(19).requestBalloon(8);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(52).requestBalloon(3);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(19).requestBalloon(8);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(52).requestBalloon(8);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(19).requestBalloon(8);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(52).requestBalloon(8);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(19).requestBalloon(8);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(52).requestBalloon(3);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(19).requestBalloon(8);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(52).requestBalloon(8);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[29]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[23] : 像素偏移[0,12] : 时间[1]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[24] : 像素偏移[0,-12] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [24, 0, 11, 25, 8]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 213, "indent": 0, "parameters": [23, 6, true]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 52]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我现在都不敢相信自己会答应你做这样的事，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还是在教堂……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [24, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 都这个时候了，还说这个干嘛？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这里多好，解决了我们的问题，又顺便做了职业任务。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 刚才已经用手指在你小菊花入口按摩了好久，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 也用了超多润滑液……"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 912, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[29]"]}, {"code": 231, "indent": 0, "parameters": [1, "教会肛交开发-001-最初", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [2, "教会肛交开发-002-抵住", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [3, "教会肛交开发-003-准备插入", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [4, "教会肛交开发-004-插入中", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [5, "教会肛交开发-005-生气", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [6, "教会肛交开发-006-脱力", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [100, "教会肛交开发-002-抵住", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在就不要想那么多了，放轻松~ "]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说的容易，这种样子怎么轻松得起来……"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯！等…等等…！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗨！分散一下注意力就好啦~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 刚才莉兹姐找你干嘛了呀？浪费了那么久的时间。"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说是让我测试道具，塞给了我一个护符，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但主设备临时又出故障，修了半天也没好，就先放我回过来……"]}, {"code": 250, "indent": 0, "parameters": [{"name": "挿入する時の音3（勢いよく一気に入れる）", "volume": 25, "pitch": 90, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [4, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 250, "indent": 0, "parameters": [{"name": "あぁん！", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊！"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, true]}, {"code": 232, "indent": 0, "parameters": [4, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要这么突然…！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好奇怪的感觉……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没事没事，这就是正常的感觉啦。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这是最小尺寸的一款拉珠了，你看，第一颗已经完全进去了呢！"]}, {"code": 232, "indent": 0, "parameters": [5, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我怎么看呀？真是的！"]}, {"code": 232, "indent": 0, "parameters": [5, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 等等…第二颗好像更大…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对啊，就是要慢慢变大才有意思嘛！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 来，深呼吸，放松~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](过一会道具的附加效果生效，你就知道爽了！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唉…你轻点……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好啦好啦，我会很温柔的……"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 250, "indent": 0, "parameters": [{"name": "挿入する時の音3（勢いよく一気に入れる）", "volume": 25, "pitch": 90, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [4, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜！"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 232, "indent": 0, "parameters": [6, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 232, "indent": 0, "parameters": [4, 0, 0, 0, 0, 0, 100, 100, 0, 0, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔……哈啊…哈啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](怎么回事，虽然这颗更大，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但异样感相比第一颗的时候，反而减弱了……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你的身体似乎开始接受了呢~"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 232, "indent": 0, "parameters": [6, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别…别说出来……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈，害羞什么嘛！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 来！试试第三颗，这个大小刚刚好~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要…这个太大了…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没关系的，相信我，你完全可以接受这个尺寸！"]}, {"code": 250, "indent": 0, "parameters": [{"name": "挿入する時の音3（勢いよく一気に入れる）", "volume": 25, "pitch": 90, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [4, 0, 0, 0, 0, 0, 100, 100, 255, 0, 25, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可是…啊…"]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 232, "indent": 0, "parameters": [6, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看吧，我说什么来着！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你的身体已经完全放松了呢~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你…你怎么这样……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿，因为我最了解你的身体啦！要不要试试抽动的感觉？"]}, {"code": 232, "indent": 0, "parameters": [4, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, false]}, {"code": 232, "indent": 0, "parameters": [5, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 232, "indent": 0, "parameters": [6, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 等…等一下…让我缓缓……"]}, {"code": 232, "indent": 0, "parameters": [6, 0, 0, 0, 0, 0, 100, 100, 0, 0, 25, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没关系的！相信我！"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "pierrot_slidedoorschool", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[教会肛交调教]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 44, "parameters": [{"name": "ブチュ２", "volume": 50, "pitch": 70, "pan": 0}], "indent": null}, {"code": 15, "parameters": [48], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ブチュ２", "volume": 50, "pitch": 70, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [48], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "娇喘", "volume": 40, "pitch": 110, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 25, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔…！哈啊…啊啊……"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 235, "indent": 0, "parameters": [4]}, {"code": 235, "indent": 0, "parameters": [5]}, {"code": 235, "indent": 0, "parameters": [6]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 十分钟后……"]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 245, "indent": 0, "parameters": [{"name": "娇喘2", "volume": 40, "pitch": 110, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "SC_click1", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 叮~~~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](哦~ 嚯嚯嚯~ )"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 感觉轻松多了吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…还…还好吧……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…啊啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](确实……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 甚至慢慢有有舒服的感觉了……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看来学姐开始享受了呢？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 才…才没有！只是…习惯了一点。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是吗？那这样呢？"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[教会肛交调教快]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 44, "parameters": [{"name": "ブチュ２", "volume": 50, "pitch": 70, "pan": 0}], "indent": null}, {"code": 15, "parameters": [36], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ブチュ２", "volume": 50, "pitch": 70, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [36], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "娇喘6", "volume": 40, "pitch": 110, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…别…别突然……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦哦……啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看来是很舒服嘛，要诚实一点哦。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你…你别说了…不过确实…还不错……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 要试试塞入更大颗的吗？我感觉你已经准备好了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我…我不知道…你来判断吧……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真可爱，明明很期待却说得这么含蓄。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我…！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜…你再这样说我就不玩了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好好好，我不逗你了。来，放松一点。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…这个大小…好像更舒服。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看吧，我就说你能行的。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别得意…啊…慢一点……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 已经完全适应了呢，我们开始正戏吧~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诶？现在吗…可是那个尺寸……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔…我还是有点害怕……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 放心啦，刚才都能完全接受那么大的拉珠了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要怕，我会很温柔的，一点点来~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…那…那你要说到做到…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 当然！当然！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我的鸡巴保证会比拉珠更舒服的！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 修女长:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜姐妹。"]}, {"code": 245, "indent": 0, "parameters": [{"name": "捂嘴娇喘", "volume": 40, "pitch": 120, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔…修女长？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}猪田，停一下，唔呃…修女长正在通过私密通讯跟我说话…马上！唔…不然我生气了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个时候联系，烦人！要干什么呀？"]}, {"code": 232, "indent": 0, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 修女长，您找我有什么事吗？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 修女长:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 需要你出来一下。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可我还在倾听忏悔……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 修女长:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我知道。告解需要提前结束。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你出来以后我再跟你解释。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这…好，好的……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 231, "indent": 0, "parameters": [2, "教会肛交开发-999-准备结束", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 232, "indent": 0, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田，我们不能再继续了。修女长有事找我。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 欸？为什么？可以拒绝她吗……？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不行！那我们不就暴露了吗？乖，我下次再补偿你。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唉，好吧。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过，我要先给你个小礼物才行~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯？"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "あえぎ2", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [24]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 0, "parameters": [912, 912, 1]}, {"code": 121, "indent": 0, "parameters": [300, 300, 1]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 81, 12, 8, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 11, "y": 21}]}