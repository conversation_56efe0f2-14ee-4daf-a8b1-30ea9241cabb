{"autoplayBgm": true, "autoplayBgs": false, "battleback1Name": "Grassland", "battleback2Name": "", "bgm": {"name": "魔王魂  ヒーリング05", "pan": 0, "pitch": 100, "volume": 35}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 48, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 35, "width": 33, "data": [1576, 1577, 1560, 1584, 1561, 1561, 1562, 1570, 1569, 1606, 1607, 1569, 1569, 1570, 1577, 1578, 2860, 1590, 1591, 1566, 1566, 1566, 1566, 1566, 1577, 1577, 1577, 1577, 1577, 6000, 5984, 5984, 5984, 1563, 2862, 1568, 1569, 1569, 1569, 1570, 1570, 1577, 1614, 1615, 1577, 1577, 1578, 1590, 1591, 1566, 1567, 1569, 1569, 1570, 1569, 1569, 1569, 1565, 1566, 1567, 1552, 2906, 6024, 5992, 5984, 5984, 1560, 1563, 1576, 1577, 1569, 1569, 1570, 1578, 2859, 2849, 2849, 2849, 2861, 1590, 1583, 1569, 1592, 1570, 1577, 1577, 1578, 2862, 2906, 2862, 1576, 1577, 1575, 1552, 2905, 2901, 6024, 6012, 5992, 1573, 1560, 1584, 1562, 1577, 1577, 1578, 2862, 1590, 1591, 1566, 1566, 1566, 1567, 1570, 1577, 1577, 1578, 2859, 2849, 2853, 2907, 2879, 2897, 2886, 2900, 2862, 1552, 2862, 2881, 2884, 2900, 6000, 1589, 1573, 1574, 1575, 1590, 1591, 1566, 1566, 1567, 1592, 1569, 1621, 1569, 1570, 1578, 2859, 2861, 2898, 2884, 2900, 2835, 2861, 2908, 2862, 2880, 2866, 2900, 1560, 1563, 2904, 2872, 2888, 6000, 1557, 2859, 2849, 2861, 1559, 1569, 1569, 1569, 1570, 1569, 1577, 1629, 1577, 1578, 2862, 2898, 2884, 2865, 2864, 2888, 2860, 2906, 4010, 2899, 2893, 2872, 2888, 1573, 1571, 2858, 2880, 2888, 6000, 1565, 1566, 1566, 1566, 1567, 1577, 1637, 1569, 1570, 1577, 1552, 2859, 2849, 2861, 2907, 2893, 2892, 2892, 2872, 2866, 2884, 2890, 4012, 2908, 2858, 2882, 2894, 2901, 1552, 2848, 2880, 2888, 6000, 1568, 1569, 1569, 1569, 1570, 2862, 1559, 1577, 2898, 2900, 1571, 4011, 4001, 3991, 4001, 4001, 4001, 4013, 2880, 2864, 2864, 2866, 2900, 4010, 2860, 2908, 2954, 2908, 1552, 2860, 2880, 2888, 6024, 1576, 1577, 1577, 1577, 1578, 1590, 1583, 2898, 2865, 2888, 1560, 1584, 1584, 4012, 2859, 2838, 2836, 2852, 2904, 2876, 2892, 2876, 2902, 4000, 2906, 2946, 2915, 2948, 1560, 1563, 2880, 2866, 2884, 2849, 2849, 2861, 1590, 1591, 1567, 1575, 2880, 2868, 2902, 1573, 1574, 1574, 1552, 4010, 2856, 2844, 2846, 2861, 2908, 4014, 2908, 4003, 4007, 2908, 2952, 2940, 2950, 1573, 1571, 2904, 2872, 2864, 1566, 1566, 1566, 1567, 1574, 1575, 2858, 2904, 2902, 2859, 2849, 2849, 2861, 1560, 4009, 4001, 4001, 4001, 4001, 4013, 2862, 4011, 4007, 2859, 2849, 2861, 2907, 2909, 2862, 1552, 2858, 2880, 2864, 1574, 1574, 1574, 1575, 2851, 2849, 2855, 4003, 4001, 4001, 4001, 4001, 4013, 1573, 1574, 1560, 1584, 1561, 1543, 1584, 1585, 1584, 1587, 1584, 1584, 1584, 1584, 1561, 1584, 1552, 2848, 2880, 2864, 2836, 2837, 2849, 2849, 2855, 4003, 4001, 4007, 2850, 2836, 2836, 2836, 2837, 2849, 2861, 1573, 1574, 1574, 1574, 1592, 1593, 1592, 1595, 1592, 1592, 1594, 1574, 1574, 1574, 1571, 2848, 2904, 2872, 2816, 2840, 4011, 4001, 4001, 4007, 2850, 2836, 2817, 2816, 2816, 2816, 2840, 2082, 2068, 2068, 2084, 2862, 2091, 2081, 2070, 2068, 2068, 2068, 2084, 2859, 2861, 2898, 2900, 1552, 2857, 2861, 2904, 2816, 2840, 2910, 2850, 2836, 2836, 2817, 2816, 2816, 2816, 2820, 2844, 2854, 2064, 2048, 2048, 2050, 2084, 2859, 2861, 2064, 2048, 2048, 2048, 2050, 2084, 2898, 2865, 2888, 1560, 1561, 1584, 1584, 2820, 2846, 2849, 2845, 2844, 2844, 2824, 2816, 2816, 2816, 2840, 2082, 2068, 2049, 2048, 2048, 2048, 2050, 2068, 2068, 2049, 2048, 2048, 2048, 2048, 2072, 2904, 2892, 2902, 1568, 1569, 1569, 1593, 2840, 2898, 2884, 2884, 2884, 2900, 2832, 2816, 2816, 2816, 2840, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2068, 2068, 2084, 1576, 1577, 1577, 1577, 2840, 2904, 2872, 2864, 2868, 2902, 2832, 2816, 2816, 2816, 2840, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2052, 2076, 2056, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2050, 2068, 2068, 2068, 2068, 2846, 2853, 2880, 2868, 2902, 2850, 2817, 2816, 2816, 2816, 2840, 2088, 2056, 2048, 2048, 2048, 2052, 2076, 2086, 4010, 2088, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2076, 2900, 2848, 2904, 2902, 2850, 2817, 2816, 2816, 2816, 2816, 2818, 2852, 2088, 2076, 2076, 2076, 2086, 2862, 4003, 4007, 2850, 2836, 2836, 2836, 2836, 2852, 1557, 2898, 2884, 2884, 2900, 2862, 1582, 2902, 2833, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2836, 2836, 2852, 4002, 3994, 2850, 2817, 2816, 2816, 2816, 2820, 2854, 1581, 2880, 2864, 2864, 2866, 2884, 2884, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 3984, 3992, 2832, 2816, 2816, 2816, 2816, 2840, 1557, 2858, 2880, 2864, 2864, 2864, 2864, 2864, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2824, 2816, 2816, 2816, 2840, 4008, 4006, 2832, 2816, 2816, 2820, 2844, 2854, 1557, 2860, 2904, 2872, 2864, 2864, 2868, 2892, 2897, 2897, 2897, 2897, 2897, 2897, 2886, 2884, 2884, 2884, 2884, 2900, 2832, 2816, 2816, 2816, 2818, 2836, 2836, 2817, 2816, 2820, 2854, 2898, 2900, 1565, 1566, 1589, 2880, 2864, 2864, 2888, 6018, 2836, 2836, 2836, 2836, 2837, 2861, 2880, 2864, 2864, 2868, 2892, 2902, 2832, 2816, 2820, 2844, 2844, 2844, 2844, 2844, 2824, 2840, 2898, 2865, 2888, 1568, 1569, 1557, 2904, 2872, 2868, 2902, 6000, 2816, 2816, 2816, 2816, 2840, 2907, 2893, 2892, 2892, 2902, 2859, 2849, 2845, 2844, 2854, 4002, 3988, 3988, 3989, 4013, 2832, 2840, 2880, 2864, 2888, 1568, 1569, 1557, 2858, 2880, 2888, 6018, 5985, 2844, 2844, 2824, 2816, 2818, 2836, 2836, 2836, 2837, 2861, 4003, 4001, 4001, 4001, 4001, 3997, 3996, 3996, 4006, 2850, 2817, 2840, 2904, 2872, 2870, 2909, 1577, 1557, 2848, 2880, 2888, 6000, 5984, 4001, 4013, 2832, 2816, 2816, 2816, 2816, 2816, 2840, 4011, 4007, 2851, 2849, 2849, 2849, 2849, 2849, 2849, 2839, 2845, 2844, 2846, 2861, 2904, 2902, 3723, 3725, 1557, 2860, 2904, 2902, 6024, 5992, 2849, 2849, 2845, 2844, 2844, 2844, 2844, 2824, 2818, 2837, 2849, 2855, 2082, 2068, 2068, 2068, 2069, 2093, 2860, 4003, 4001, 4001, 4013, 2850, 2836, 2852, 2091, 2081, 2081, 2081, 2081, 2093, 6024, 1549, 1542, 1542, 1541, 1549, 1542, 1541, 2832, 2816, 2840, 2091, 2081, 2077, 2076, 2076, 2076, 2086, 2862, 4003, 4007, 2850, 2836, 2836, 2817, 2816, 2840, 1557, 2862, 2898, 2884, 2900, 2862, 1582, 1540, 1541, 1541, 1550, 1542, 1542, 1550, 2832, 2816, 2818, 2836, 2837, 2849, 2849, 2849, 2838, 2852, 4002, 3994, 2850, 2817, 2816, 2816, 2816, 2820, 2854, 1581, 2898, 2865, 2864, 2866, 2884, 2884, 1548, 1542, 1541, 1542, 1541, 1542, 1542, 2832, 2816, 2816, 2816, 2840, 2907, 2886, 2900, 2834, 2854, 3984, 3992, 2832, 2816, 2816, 2816, 2816, 2840, 1557, 2858, 2880, 2864, 2864, 2864, 2864, 2864, 2836, 2836, 2836, 2852, 1542, 1541, 1541, 2856, 2844, 2844, 2824, 2818, 2852, 2904, 2902, 2848, 4011, 3997, 3993, 2832, 2816, 2816, 2820, 2844, 2854, 1557, 2860, 2904, 2872, 2864, 2864, 2868, 2892, 2816, 2816, 2816, 2840, 1550, 1542, 1548, 2898, 2884, 2900, 2832, 2816, 2818, 2836, 2836, 2819, 2837, 2861, 4012, 2832, 2816, 2816, 2840, 2898, 2900, 1565, 1566, 1589, 2880, 2864, 2864, 2888, 6018, 2816, 2816, 2816, 2840, 1542, 1549, 1542, 2880, 2864, 2888, 2832, 2816, 2816, 2816, 2820, 2844, 2854, 4010, 2859, 2845, 2824, 2820, 2854, 2880, 2888, 1568, 1569, 1557, 2904, 2872, 2868, 2902, 6000, 2816, 2816, 2816, 2840, 1542, 1550, 1542, 2904, 2892, 2902, 2856, 2844, 2844, 2844, 2854, 4002, 3988, 3971, 3989, 4013, 2832, 2840, 2898, 2865, 2888, 1568, 1605, 1557, 2858, 2880, 2888, 6018, 5985, 2844, 2844, 2844, 2854, 1550, 1550, 1542, 2859, 2849, 2861, 4002, 3988, 3988, 3988, 3988, 3969, 3972, 3996, 4006, 2850, 2817, 2840, 2904, 2872, 2866, 2900, 1613, 1557, 2848, 2880, 2888, 6000, 5984, 3988, 3988, 3988, 4004, 1542, 1542, 1541, 4002, 3988, 3988, 3969, 3972, 3996, 3996, 3996, 3996, 4006, 2850, 2836, 2817, 2816, 2818, 2852, 2904, 2872, 2866, 2900, 1557, 2848, 2904, 2889, 6000, 5984, 3996, 3996, 3996, 4006, 1550, 1542, 1550, 4008, 3996, 3996, 3996, 4006, 2851, 2849, 2849, 2849, 2849, 2845, 2824, 2816, 2816, 2816, 2818, 2852, 2904, 2872, 2888, 1557, 2833, 2852, 2908, 6000, 5984, 2836, 2837, 2861, 2910, 1541, 1550, 1542, 2859, 2838, 2836, 2837, 2849, 2855, 2898, 2884, 2884, 2884, 2900, 2832, 2816, 2816, 2816, 2816, 2822, 2861, 2904, 2902, 1557, 2832, 2840, 6018, 5985, 5984, 2816, 2840, 1582, 1566, 1542, 1542, 1550, 2094, 2834, 2844, 2841, 2898, 2884, 2865, 2864, 2868, 2892, 2902, 2832, 2816, 2820, 2844, 2828, 2854, 1581, 2859, 2861, 1557, 2832, 2840, 6000, 5984, 5984, 2816, 2818, 2852, 1559, 1541, 1542, 1542, 1557, 2848, 1582, 2848, 2904, 2892, 2892, 2892, 2902, 2851, 2849, 2845, 2844, 2854, 1581, 2860, 2898, 2884, 2884, 2900, 1557, 2856, 2854, 6000, 5984, 5984, 2816, 2820, 2854, 1559, 1548, 1542, 1550, 1557, 2833, 2837, 2847, 2838, 2836, 2836, 2837, 2849, 2855, 2898, 2884, 2885, 2897, 2897, 2897, 2893, 2892, 2892, 2902, 6018, 6004, 6004, 5985, 5984, 5984, 2844, 2854, 2906, 1559, 1541, 1542, 1542, 1557, 2834, 2854, 2906, 2832, 2816, 2820, 2854, 2898, 2885, 2893, 2892, 2902, 6018, 6004, 6004, 6004, 6004, 6004, 6004, 5985, 5984, 5984, 5984, 5984, 5984, 2884, 2885, 2903, 1559, 1541, 1550, 1549, 1557, 2848, 2898, 2890, 2834, 2844, 2854, 2899, 2893, 2902, 6018, 6004, 6004, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2864, 2888, 2862, 1559, 1549, 1542, 1542, 1557, 2848, 2904, 2902, 2848, 2899, 2897, 2903, 2850, 2852, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2892, 2894, 2909, 1559, 1541, 1542, 1548, 1557, 2833, 2836, 2836, 2842, 2908, 2850, 2836, 2817, 2840, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 2836, 2836, 2852, 1559, 1542, 1542, 1550, 1557, 2832, 2816, 2816, 2818, 2836, 2817, 2816, 2816, 2840, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3147, 3143, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3139, 3149, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3148, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3120, 3106, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3112, 3128, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3124, 3140, 0, 0, 0, 0, 3051, 3030, 3028, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3198, 0, 0, 3144, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3132, 3142, 0, 0, 0, 0, 0, 3048, 3036, 3038, 3053, 0, 0, 0, 0, 0, 3150, 0, 3147, 3149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3120, 3106, 3124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3112, 3104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3132, 0, 0, 3867, 3869, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3172, 3188, 0, 0, 0, 3858, 3860, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3154, 3188, 0, 3858, 3829, 3862, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3152, 3156, 3190, 0, 3864, 3862, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3156, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3918, 0, 0, 0, 0, 0, 3918, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3858, 3844, 3860, 0, 0, 3190, 3867, 3869, 0, 0, 0, 3867, 3869, 0, 0, 0, 0, 3915, 3905, 3917, 0, 0, 0, 0, 0, 0, 3138, 3124, 3140, 0, 0, 0, 3858, 3825, 3828, 3862, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3147, 3133, 3132, 3142, 0, 0, 0, 3864, 3852, 3862, 0, 3138, 3124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3109, 3132, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3124, 3125, 3149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3120, 3128, 0, 0, 0, 0, 0, 0, 0, 3138, 3124, 3109, 3132, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3109, 3142, 0, 0, 0, 0, 0, 0, 3147, 3133, 3132, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3186, 3188, 0, 0, 0, 0, 0, 3138, 3105, 3128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3154, 3188, 0, 0, 0, 0, 3120, 3104, 3128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3180, 3182, 3197, 0, 0, 0, 3144, 3132, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3124, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3918, 0, 0, 0, 0, 3138, 3105, 3108, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3915, 3905, 3917, 0, 0, 0, 0, 0, 0, 3138, 3105, 3108, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3147, 3126, 3140, 0, 0, 0, 0, 0, 3147, 3133, 3132, 3142, 0, 0, 0, 0, 0, 0, 0, 3138, 3124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3109, 3132, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3120, 3128, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3109, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3109, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3186, 3188, 0, 0, 0, 0, 0, 3138, 3105, 3128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3154, 3188, 0, 0, 0, 0, 3120, 3104, 3128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3160, 3154, 3188, 0, 0, 0, 3120, 3104, 3128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3160, 3154, 3172, 3188, 0, 3120, 3108, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3180, 3180, 3190, 0, 3120, 3128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3914, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3906, 3898, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3892, 3908, 0, 0, 0, 0, 0, 0, 0, 3912, 3910, 0, 0, 0, 0, 0, 3146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3900, 3902, 3917, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3136, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3866, 0, 0, 0, 0, 0, 0, 3136, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 141, 134, 0, 0, 0, 0, 67, 0, 0, 0, 307, 116, 117, 118, 153, 154, 146, 152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 157, 0, 150, 155, 0, 0, 0, 0, 0, 0, 0, 0, 116, 117, 118, 161, 161, 162, 160, 0, 0, 0, 0, 0, 0, 0, 144, 145, 0, 0, 0, 0, 67, 0, 0, 22, 163, 0, 0, 0, 0, 0, 296, 297, 298, 120, 0, 0, 0, 0, 0, 0, 0, 257, 0, 0, 0, 0, 0, 0, 0, 145, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 153, 154, 0, 304, 305, 306, 25, 0, 0, 25, 0, 0, 0, 3, 273, 272, 257, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 161, 161, 162, 0, 0, 296, 297, 298, 0, 0, 304, 305, 0, 0, 0, 1, 0, 273, 0, 0, 1, 0, 9, 0, 0, 0, 0, 0, 158, 0, 0, 0, 161, 280, 0, 304, 305, 306, 282, 280, 0, 0, 0, 0, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 166, 0, 0, 2, 0, 0, 289, 290, 282, 0, 290, 0, 0, 0, 0, 3, 298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 68, 58, 67, 0, 0, 0, 0, 0, 297, 298, 290, 0, 298, 0, 0, 0, 0, 0, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 305, 306, 298, 0, 0, 0, 0, 0, 0, 0, 304, 305, 0, 0, 0, 0, 0, 256, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 272, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 203, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 89, 94, 95, 0, 0, 0, 0, 0, 0, 88, 89, 84, 0, 0, 0, 0, 0, 0, 0, 0, 0, 85, 86, 89, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 102, 103, 0, 0, 0, 0, 0, 0, 0, 0, 88, 0, 89, 89, 89, 89, 89, 89, 89, 0, 90, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 661, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 669, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 85, 86, 89, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 55, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128, 129, 129, 129, 130, 0, 0, 132, 133, 288, 289, 290, 0, 304, 305, 306, 280, 281, 282, 337, 338, 339, 309, 153, 154, 147, 321, 0, 0, 0, 0, 0, 2, 136, 137, 137, 137, 138, 0, 156, 148, 149, 296, 297, 298, 67, 0, 0, 0, 288, 289, 290, 345, 346, 347, 280, 281, 282, 187, 299, 0, 0, 0, 0, 0, 280, 281, 282, 137, 137, 138, 0, 164, 165, 1, 304, 305, 306, 0, 0, 280, 281, 282, 280, 281, 282, 121, 122, 288, 289, 290, 187, 307, 294, 0, 0, 0, 0, 288, 289, 290, 68, 145, 146, 5, 4, 0, 169, 280, 281, 282, 153, 154, 288, 289, 290, 288, 289, 290, 123, 26, 296, 297, 298, 0, 315, 326, 336, 294, 0, 0, 296, 297, 298, 67, 0, 0, 0, 0, 0, 177, 288, 289, 290, 154, 162, 296, 297, 298, 280, 281, 282, 0, 280, 281, 282, 306, 0, 18, 315, 344, 318, 0, 0, 304, 305, 306, 0, 0, 159, 0, 0, 59, 185, 296, 297, 298, 162, 281, 282, 280, 281, 288, 289, 290, 0, 288, 289, 280, 281, 282, 0, 0, 0, 321, 0, 280, 281, 282, 0, 0, 0, 167, 0, 0, 59, 0, 304, 305, 306, 288, 280, 281, 288, 289, 296, 297, 298, 0, 296, 297, 288, 289, 290, 0, 38, 39, 321, 0, 288, 289, 290, 0, 0, 59, 0, 708, 709, 710, 0, 0, 0, 0, 296, 288, 289, 296, 297, 304, 305, 306, 0, 304, 305, 296, 297, 298, 0, 46, 47, 299, 0, 296, 297, 298, 0, 0, 67, 0, 732, 733, 734, 0, 108, 110, 0, 304, 296, 297, 304, 305, 306, 0, 0, 0, 0, 0, 312, 313, 314, 0, 0, 0, 307, 337, 304, 305, 306, 0, 0, 0, 0, 740, 741, 742, 0, 116, 118, 0, 0, 304, 305, 306, 0, 18, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 315, 345, 0, 0, 0, 0, 0, 0, 67, 0, 3, 0, 29, 124, 126, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 19, 0, 0, 44, 21, 0, 0, 0, 0, 280, 281, 282, 0, 67, 2, 0, 0, 0, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 288, 289, 290, 0, 0, 0, 0, 0, 0, 288, 289, 290, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 156, 0, 0, 20, 0, 296, 297, 298, 0, 0, 0, 0, 0, 0, 296, 297, 298, 2, 200, 0, 0, 202, 20, 0, 217, 0, 0, 0, 0, 202, 2, 46, 47, 164, 0, 19, 0, 12, 304, 305, 306, 0, 3, 0, 0, 0, 0, 38, 39, 306, 7, 0, 187, 0, 0, 202, 4, 4, 208, 0, 0, 0, 0, 202, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 8, 0, 46, 47, 200, 0, 0, 0, 0, 205, 0, 0, 201, 194, 0, 0, 0, 0, 210, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 204, 0, 0, 201, 201, 202, 0, 0, 183, 0, 156, 157, 0, 280, 281, 282, 0, 0, 0, 0, 4, 0, 203, 0, 0, 0, 0, 195, 0, 217, 0, 0, 0, 0, 0, 0, 0, 0, 203, 0, 0, 0, 0, 164, 165, 0, 288, 289, 290, 0, 0, 0, 0, 2, 216, 0, 0, 0, 186, 0, 217, 218, 0, 216, 0, 0, 217, 0, 0, 53, 54, 54, 54, 54, 54, 55, 0, 0, 0, 296, 297, 298, 0, 0, 0, 0, 0, 0, 216, 0, 0, 0, 218, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 38, 39, 0, 0, 0, 304, 305, 306, 0, 0, 0, 1, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 2, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 46, 47, 0, 0, 0, 0, 0, 0, 0, 0, 85, 86, 84, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 2, 0, 0, 89, 91, 92, 93, 89, 89, 89, 89, 90, 67, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 280, 281, 282, 0, 21, 0, 0, 0, 0, 283, 320, 0, 99, 100, 101, 0, 0, 0, 1, 0, 0, 288, 289, 290, 89, 112, 113, 113, 113, 113, 113, 114, 89, 288, 289, 290, 0, 0, 0, 0, 11, 0, 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 296, 297, 298, 1, 120, 121, 121, 121, 121, 121, 122, 0, 296, 297, 298, 0, 0, 0, 2, 0, 259, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 304, 305, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 37, 0, 143, 0, 0, 0, 291, 0, 0, 0, 0, 0, 0, 3, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 56, 151, 0, 0, 0, 291, 0, 0, 0, 0, 0, 0, 660, 663, 662, 659, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 321, 0, 0, 0, 0, 0, 656, 668, 671, 670, 667, 0, 0, 0, 0, 0, 0, 0, 186, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 53, 54, 54, 54, 54, 54, 55, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 280, 281, 282, 1, 38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 2, 0, 0, 0, 0, 53, 0, 288, 289, 290, 0, 46, 47, 0, 0, 0, 658, 0, 0, 0, 0, 672, 673, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 296, 297, 298, 2, 0, 0, 0, 0, 0, 666, 0, 0, 0, 0, 680, 681, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 280, 281, 282, 0, 21, 304, 305, 306, 0, 283, 320, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 288, 289, 290, 0, 0, 0, 0, 11, 0, 291, 0, 0, 0, 0, 0, 0, 0, 0, 657, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 298, 0, 0, 0, 2, 0, 259, 286, 0, 0, 0, 0, 0, 0, 0, 0, 665, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 37, 0, 0, 0, 0, 0, 291, 0, 0, 0, 0, 0, 664, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 280, 281, 282, 0, 0, 0, 45, 56, 156, 0, 0, 0, 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 280, 281, 282, 288, 289, 290, 0, 0, 0, 0, 1, 164, 0, 0, 0, 321, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 288, 289, 290, 296, 297, 298, 2, 0, 0, 0, 0, 0, 0, 0, 283, 286, 0, 0, 54, 54, 55, 0, 0, 0, 0, 0, 280, 281, 282, 0, 0, 0, 0, 296, 297, 298, 304, 305, 306, 0, 0, 0, 53, 54, 54, 0, 0, 291, 0, 0, 0, 281, 282, 0, 55, 0, 0, 0, 53, 288, 289, 290, 0, 0, 0, 2, 304, 305, 306, 0, 3, 0, 53, 54, 54, 0, 3, 18, 0, 0, 291, 0, 0, 0, 289, 290, 1, 0, 51, 0, 0, 0, 296, 297, 298, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 0, 1, 0, 0, 0, 259, 260, 284, 286, 0, 0, 0, 297, 298, 0, 0, 0, 0, 0, 0, 304, 305, 306, 0, 280, 281, 282, 3, 0, 6, 6, 283, 284, 284, 260, 320, 260, 320, 286, 0, 0, 0, 0, 0, 0, 305, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 288, 289, 290, 0, 283, 320, 284, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 298, 9, 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 306, 0, 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 9, 0, 0, 0, 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 47, 0, 0, 0, 0, 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 198, 0, 46, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["particle set 太阳白天光 weather 太阳白天光 above"]}, {"code": 356, "indent": 0, "parameters": ["particle set 城区白天光 weather 城区白天光 above"]}, {"code": 356, "indent": 0, "parameters": ["particle set 城区白天云 weather 城区白天云 above"]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 26, "y": 21}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 3, "priorityType": 0, "stepAnime": true, "through": true, "trigger": 0, "walkAnime": true}], "x": 24, "y": 17}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 6, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": 0}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": 0}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 3, "priorityType": 0, "stepAnime": true, "through": true, "trigger": 0, "walkAnime": true}], "x": 12, "y": 16}, {"id": 4, "name": "兽人5", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 6, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage3", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 174}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 8}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 20, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 22, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 21, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 20, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 22, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 21, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 刚才的是这个区域最后一只了…… "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 剩下的不会要等刷新吧？  "]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 傻等着也没有意义，去临近地图看一下吧。 "]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 112, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 43}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 65, 21, 38, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 112, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 任务还没有完成，暂时不能回去。"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 173}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 72, 19, 14, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 174}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 65, 21, 38, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 5, "y": 47}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离4 : 触发独立开关 : D"]}, {"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离7 : 离开范围时自动OFF"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 1, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 230, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [6, 0, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, 0, 4]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, 0, 6]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, 0, 8]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["开始战斗"]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [0, 1, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 15]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 6, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 212, "indent": 1, "parameters": [0, 6, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 9, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [7]}, {"code": 212, "indent": 1, "parameters": [0, 31, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 10, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 26, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 11, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 147, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 12, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 148, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 13, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 149, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 14, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 27, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 15, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 27, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["攻击结束"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [112, 112, 2, 0, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 315, "indent": 0, "parameters": [0, 4, 0, 0, 2836, false]}, {"code": 356, "indent": 0, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置-玩家 : 文本[\\c[21]EXP\\c[0]+\\c[18]2836] : 样式[1] : 弹道[1] : 持续时间[120]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 4, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 2, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage3", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 173}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 23, "y": 37}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离4 : 触发独立开关 : D"]}, {"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离7 : 离开范围时自动OFF"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 1, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 230, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [6, 0, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, 0, 4]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, 0, 6]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, 0, 8]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["开始战斗"]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [0, 1, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 15]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 6, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 212, "indent": 1, "parameters": [0, 6, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 9, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [7]}, {"code": 212, "indent": 1, "parameters": [0, 31, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 10, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 26, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 11, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 147, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 12, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 148, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 13, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 149, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 14, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 27, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 15, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 27, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["攻击结束"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [112, 112, 2, 0, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 315, "indent": 0, "parameters": [0, 4, 0, 0, 2836, false]}, {"code": 356, "indent": 0, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置-玩家 : 文本[\\c[21]EXP\\c[0]+\\c[18]2836] : 样式[1] : 弹道[1] : 持续时间[120]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 4, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 2, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage3", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 173}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 4, "y": 16}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离4 : 触发独立开关 : D"]}, {"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离7 : 离开范围时自动OFF"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 1, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 230, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [6, 0, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, 0, 4]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, 0, 6]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, 0, 8]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["开始战斗"]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [0, 1, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 15]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 6, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 212, "indent": 1, "parameters": [0, 6, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 9, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [7]}, {"code": 212, "indent": 1, "parameters": [0, 31, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 10, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 26, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 11, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 147, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 12, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 148, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 13, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 149, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 14, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 27, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 15, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 27, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["攻击结束"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [112, 112, 2, 0, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 315, "indent": 0, "parameters": [0, 4, 0, 0, 2836, false]}, {"code": 356, "indent": 0, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置-玩家 : 文本[\\c[21]EXP\\c[0]+\\c[18]2836] : 样式[1] : 弹道[1] : 持续时间[120]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 4, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 2, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage3", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 173}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 18, "y": 20}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [195, 195, 0, 3, 3, 1, 0]}, {"code": 213, "indent": 0, "parameters": [-1, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 总算是到了，这个地图真是有够偏远的。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 十只小怪一个BOSS……\\.\\.争取半个小时内搞定!"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 44}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离4 : 触发独立开关 : D"]}, {"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离7 : 离开范围时自动OFF"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 1, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 230, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [6, 0, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, 0, 4]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, 0, 6]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, 0, 8]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["开始战斗"]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [0, 1, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 15]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 6, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 212, "indent": 1, "parameters": [0, 6, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 9, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [7]}, {"code": 212, "indent": 1, "parameters": [0, 31, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 10, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 26, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 11, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 147, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 12, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 148, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 13, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 149, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 14, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 27, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 15, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 27, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["攻击结束"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [112, 112, 2, 0, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 315, "indent": 0, "parameters": [0, 4, 0, 0, 2836, false]}, {"code": 356, "indent": 0, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置-玩家 : 文本[\\c[21]EXP\\c[0]+\\c[18]2836] : 样式[1] : 弹道[1] : 持续时间[120]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 4, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 2, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage3", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 173}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 17, "y": 29}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离4 : 触发独立开关 : D"]}, {"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离7 : 离开范围时自动OFF"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 1, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 230, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [6, 0, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, 0, 4]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, 0, 6]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [6, 0, 8]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["开始战斗"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["开始战斗"]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [0, 1, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 15]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 6, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 212, "indent": 1, "parameters": [0, 6, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 9, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [7]}, {"code": 212, "indent": 1, "parameters": [0, 31, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 10, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 26, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 11, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 147, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 12, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 148, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 13, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 149, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 14, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 27, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 15, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 12, "indent": 0}, {"code": 13, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [3]}, {"code": 212, "indent": 1, "parameters": [0, 27, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["攻击结束"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [112, 112, 2, 0, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 315, "indent": 0, "parameters": [0, 4, 0, 0, 2836, false]}, {"code": 356, "indent": 0, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置-玩家 : 文本[\\c[21]EXP\\c[0]+\\c[18]2836] : 样式[1] : 弹道[1] : 持续时间[120]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 4, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 2, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage3", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 173}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 3, "y": 33}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 265, 18, 34, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 112, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 22, "y": 3}, {"id": 14, "name": "兽人2", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 4, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage3", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 174}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 8}, {"id": 15, "name": "兽人3", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 4, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage3", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 174}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 9}, {"id": 16, "name": "兽人4", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 6, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage3", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 174}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 9}, {"id": 17, "name": "兽人1", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage3", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 174}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 6}, {"id": 18, "name": "兽人首领", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 8, "pattern": 1, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage3", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 174}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 10}, {"id": 19, "name": "EV019", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [113, 113, 0, 3, 3, 4, 0]}, {"code": 111, "indent": 0, "parameters": [1, 113, 1, 195, 5]}, {"code": 122, "indent": 1, "parameters": [195, 195, 0, 3, 3, 4, 0]}, {"code": 122, "indent": 1, "parameters": [113, 113, 0, 3, 3, 4, 0]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置-玩家 : 文本[\\c[21]LevelUP] : 样式[1] : 弹道[1] : 持续时间[120]"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置-玩家 : 文本[Lv\\v[195]] : 样式[1] : 弹道[1] : 持续时间[120]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 8, "y": 42}, {"id": 20, "name": "EV020", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 8, "pattern": 1, "characterIndex": 3}, "list": [{"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 刚才的女玩家被怪物包围了，不过…… "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 她很强，应该根本不需要的我帮助。 "]}, {"code": 205, "indent": 0, "parameters": [21, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [7]}, {"code": 212, "indent": 0, "parameters": [18, 11, true]}, {"code": 213, "indent": 0, "parameters": [18, 5, false]}, {"code": 212, "indent": 0, "parameters": [18, 151, true]}, {"code": 205, "indent": 0, "parameters": [18, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[18] : 像素偏移[0,-24] : 时间[7]"]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[21] : 像素偏移[0,-36] : 时间[11]"]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[18] : 像素偏移[0,0] : 时间[7]"]}, {"code": 230, "indent": 0, "parameters": [7]}, {"code": 205, "indent": 0, "parameters": [18, {"list": [{"code": 13, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[21] : 像素偏移[0,0] : 时间[27]"]}, {"code": 205, "indent": 0, "parameters": [21, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [18, 4, false]}, {"code": 213, "indent": 0, "parameters": [21, 7, false]}, {"code": 213, "indent": 0, "parameters": [17, 4, false]}, {"code": 213, "indent": 0, "parameters": [14, 4, false]}, {"code": 213, "indent": 0, "parameters": [15, 4, false]}, {"code": 213, "indent": 0, "parameters": [16, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔，但是……弓箭手一对多还是很吃力的，而且还被拖入了近距离战斗…… "]}, {"code": 213, "indent": 0, "parameters": [21, 8, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG诗乃生气", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG诗乃.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<???>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}切，基础技能不行吗？奥义还在冷却……先想办法突破包围拉开距离吧。 "]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 很明显她就是遇到了危险，我在犹豫什么？ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怕被嫌弃就见死不救吗？先上吧，日蚀！ "]}, {"code": 213, "indent": 0, "parameters": [21, 1, false]}, {"code": 205, "indent": 0, "parameters": [21, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [6], "indent": null}, {"code": 14, "parameters": [0, 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 2], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [17, 12, false]}, {"code": 212, "indent": 0, "parameters": [17, 148, false]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([268, 17, 'A'], true);"]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 6, "indent": null}, {"code": 42, "parameters": [150], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 6, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [150], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 102], 10, false]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 玩家 : 移动到 : 位置[25,10]"]}, {"code": 205, "indent": 0, "parameters": [21, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 42, "parameters": [255], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 7, "indent": null}, {"code": 42, "parameters": [150], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 7, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [150], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 102], 10, false]}, {"code": 213, "indent": 0, "parameters": [14, 12, false]}, {"code": 212, "indent": 0, "parameters": [14, 31, false]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([268, 14, 'A'], true);"]}, {"code": 213, "indent": 0, "parameters": [15, 12, false]}, {"code": 212, "indent": 0, "parameters": [15, 31, false]}, {"code": 205, "indent": 0, "parameters": [21, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([268, 15, 'A'], true);"]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 玩家 : 移动到 : 位置[19,7]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 42, "parameters": [255], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 6, "indent": null}, {"code": 42, "parameters": [150], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 6, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [150], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 102], 10, false]}, {"code": 213, "indent": 0, "parameters": [16, 12, false]}, {"code": 212, "indent": 0, "parameters": [16, 31, false]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([268, 16, 'A'], true);"]}, {"code": 213, "indent": 0, "parameters": [4, 12, false]}, {"code": 212, "indent": 0, "parameters": [4, 31, false]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([268, 4, 'A'], true);"]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 玩家 : 移动到 : 位置[22,11]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 42, "parameters": [255], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 205, "indent": 0, "parameters": [21, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 205, "indent": 0, "parameters": [18, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [18, 1, false]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 102], 10, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 38, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 212, "indent": 0, "parameters": [18, 27, true]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [18, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([268, 18, 'A'], true);"]}, {"code": 315, "indent": 0, "parameters": [0, 4, 0, 0, 20180, false]}, {"code": 356, "indent": 0, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置-玩家 : 文本[\\c[21]EXP\\c[0]+\\c[18]20180] : 样式[1] : 弹道[1] : 持续时间[120]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 搞定。你还好吧？ "]}, {"code": 213, "indent": 0, "parameters": [21, 11, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG诗乃傲娇脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG诗乃.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<???>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 多管闲事,我可不会感谢你!"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 205, "indent": 0, "parameters": [21, {"list": [{"code": 14, "parameters": [-2, 3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [-2, 3], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [7]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [8]}, {"code": 205, "indent": 0, "parameters": [21, {"list": [{"code": 14, "parameters": [0, 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 4], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [21, {"list": [{"code": 14, "parameters": [0, 4], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈，果然是这个结果。有趣的家伙。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 反正我也不是为了她的感谢才出手的。 "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔……不过……"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 刚才第二招抬手似乎慢了些，走位也还有提升的空间。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这些问题以后再琢磨吧，刚才似乎正好完成任务，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 赶紧回公会交差，然后去找亚丝娜！"]}, {"code": 213, "indent": 0, "parameters": [-1, 4, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 9, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 刚才的兽人不会是因为我的任务才埋伏在那里的吧……？ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且她也是因为把奥义丢给我了才只能用小招……"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呃，下次见到她再跟她道歉吧…… "]}, {"code": 122, "indent": 0, "parameters": [80, 80, 0, 4, "\"回公会交任务\""]}, {"code": 122, "indent": 0, "parameters": [112, 112, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 173]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 173}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 28, "y": 8}, {"id": 21, "name": "诗乃", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": false, "image": {"tileId": 0, "characterName": "诗乃fog像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": true, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 174}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 8}, {"id": 22, "name": "EV022", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 65, 21, 38, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 112, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 任务还没有完成，暂时不能回去。"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 173}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 72, 19, 14, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 174}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 65, 21, 38, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 6, "y": 47}, {"id": 23, "name": "EV023", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 172}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 65, 21, 38, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 112, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 任务还没有完成，暂时不能回去。"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 173}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 72, 19, 14, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 174}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 65, 21, 38, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 4, "y": 47}]}