{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 24, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 71, "width": 12, "data": [2816, 2816, 2816, 2816, 2820, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2898, 2900, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2898, 2865, 2888, 2832, 2816, 2816, 2816, 2816, 2844, 2824, 2840, 3002, 2880, 2864, 2888, 2832, 2816, 2816, 2816, 2816, 2996, 2856, 2854, 2992, 2880, 2864, 2888, 2832, 2816, 2816, 2816, 2816, 2962, 2980, 2980, 2986, 2880, 2864, 2888, 2834, 2844, 2824, 2816, 2816, 2960, 2960, 2960, 2984, 2880, 2864, 2888, 2860, 3002, 2832, 2816, 2816, 2960, 2960, 2960, 2984, 2880, 2864, 2888, 2994, 2986, 2832, 2816, 2816, 2960, 2960, 2960, 2984, 2880, 2864, 2888, 3000, 2998, 2832, 2816, 2816, 2960, 2960, 2960, 2984, 2880, 2864, 2888, 2850, 2836, 2817, 2816, 2816, 2960, 2960, 2960, 2984, 2880, 2864, 2888, 2832, 2816, 2816, 2816, 2816, 2960, 2960, 2964, 2998, 2880, 2864, 2888, 2832, 2820, 2844, 2844, 2844, 0, 0, 0, 2858, 2880, 2864, 2888, 2834, 2854, 0, 0, 0, 0, 0, 0, 2848, 2880, 2864, 2888, 2848, 0, 0, 0, 0, 0, 0, 0, 2848, 2880, 2864, 2888, 2848, 0, 0, 0, 0, 0, 0, 0, 2848, 2880, 2864, 2888, 2833, 2836, 2836, 2836, 2836, 0, 0, 0, 2848, 2880, 2864, 2888, 2832, 2816, 2816, 2816, 2816, 0, 0, 0, 2848, 2880, 2864, 2888, 2832, 2816, 2816, 2816, 2816, 2836, 2836, 2836, 2842, 2880, 2864, 2888, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2880, 2864, 2888, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2880, 2864, 2888, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2880, 2864, 2888, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2880, 2864, 2888, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2880, 2864, 2888, 2832, 2816, 2816, 2816, 2816, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3028, 3028, 3028, 3044, 0, 0, 0, 0, 3042, 3028, 3028, 3028, 3008, 3008, 3008, 3032, 0, 0, 0, 3042, 3009, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 3008, 3008, 3008, 3032, 0, 0, 0, 3024, 3008, 3008, 3008, 3008, 33, 33, 33, 33, 33, 33, 33, 33, 392, 33, 394, 33, 33, 33, 33, 33, 33, 0, 0, 33, 400, 33, 402, 33, 41, 41, 41, 41, 164, 0, 0, 41, 0, 41, 41, 41, 0, 0, 0, 0, 0, 0, 0, 250, 416, 0, 200, 448, 0, 0, 420, 0, 0, 0, 0, 0, 424, 425, 0, 456, 426, 427, 428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 171, 172, 426, 427, 428, 417, 418, 419, 420, 0, 0, 0, 0, 0, 0, 0, 0, 425, 426, 427, 428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 204, 205, 0, 0, 0, 0, 0, 0, 0, 416, 417, 418, 0, 465, 0, 0, 0, 0, 0, 0, 0, 424, 425, 426, 220, 221, 474, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 482, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 489, 490, 0, 0, 0, 0, 0, 0, 0, 0, 420, 0, 497, 498, 0, 0, 0, 0, 424, 425, 426, 0, 428, 0, 0, 420, 0, 0, 0, 0, 0, 233, 234, 0, 456, 0, 0, 243, 0, 0, 0, 0, 0, 241, 242, 0, 0, 0, 0, 251, 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 386, 387, 388, 245, 147, 148, 149, 150, 33, 151, 33, 176, 394, 395, 396, 253, 155, 156, 157, 158, 33, 159, 33, 184, 402, 403, 404, 245, 163, 164, 165, 166, 41, 167, 192, 440, 410, 411, 412, 141, 0, 0, 0, 384, 385, 386, 387, 388, 418, 419, 194, 195, 0, 0, 0, 392, 393, 394, 395, 396, 385, 386, 387, 388, 0, 0, 0, 400, 401, 402, 403, 404, 393, 394, 395, 396, 0, 0, 0, 408, 409, 410, 411, 412, 401, 402, 403, 404, 0, 0, 0, 416, 417, 418, 419, 420, 409, 410, 411, 412, 0, 0, 0, 424, 425, 384, 385, 386, 432, 433, 434, 435, 436, 0, 0, 202, 203, 392, 393, 394, 440, 441, 442, 443, 444, 0, 0, 210, 211, 400, 401, 402, 448, 449, 450, 451, 452, 0, 0, 218, 219, 408, 409, 410, 456, 457, 458, 459, 460, 0, 0, 384, 385, 386, 387, 388, 212, 213, 466, 467, 0, 0, 0, 392, 393, 394, 395, 396, 386, 387, 388, 227, 0, 0, 0, 400, 401, 402, 403, 404, 394, 395, 396, 235, 0, 0, 0, 408, 409, 410, 411, 412, 402, 403, 404, 491, 492, 0, 0, 416, 417, 418, 419, 440, 410, 411, 412, 499, 500, 0, 0, 224, 225, 226, 427, 448, 418, 419, 193, 0, 0, 0, 0, 232, 384, 385, 386, 387, 426, 427, 428, 244, 0, 0, 0, 240, 392, 393, 394, 395, 385, 386, 387, 388, 0, 0, 0, 0, 400, 401, 402, 403, 393, 394, 395, 396, 0, 0, 0, 0, 408, 409, 410, 411, 401, 402, 403, 404, 0, 0, 0, 248, 416, 417, 418, 419, 409, 410, 411, 412, 0, 0, 0, 0, 424, 425, 426, 427, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 0, 0, 0, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101, 101], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 646}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">地图镜头 : 固定看向 : 事件[5]"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[24,0] : 时间[1]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 42, "parameters": [255], "indent": null}, {"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 41, "parameters": ["亚丝娜", 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}]}, {"code": 129, "indent": 0, "parameters": [4, 0, false]}, {"code": 129, "indent": 0, "parameters": [1, 1, false]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 33, "indent": null}, {"code": 4, "indent": 0}, {"code": 29, "parameters": [2], "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 33, "indent": null}, {"code": 29, "parameters": [2], "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 241, "indent": 0, "parameters": [{"name": "maou_bgm_ethnic29", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[34, -17, -17, 0], 60, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 到了，灵石洞窟。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 育灵宝石的素材就在里面。"]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [130, 130, 0, 0, 22]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹岔开", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [166]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 根据我的情报，这个任务的难度不小哦。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 有我在，多麻烦的怪物都解决给你看！"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 在桐人学长大杀四方的时候，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我会用技能好好保护亚丝娜学姐，还有莉兹姐的！"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈凛然", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别瞎闹啦！"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG明日奈严肃", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 10, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你保护好自己就谢天谢地了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可别又在关键时候踢到脚绊倒之类的。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 12, false]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 事件[4] : 左右震动 : 持续时间[30] : 周期[6] : 震动幅度[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 噗咿……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐好凶！"]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [166]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，你不要对猪田那么严格嘛~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 他也是好心。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 刚才那段对话，感觉你们俩个对猪田的态度和平时正好反过来了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈哈哈~"]}, {"code": 213, "indent": 0, "parameters": [2, 7, false]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈叹气", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哪有……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 41, "parameters": ["亚丝娜", 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [166]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就是啊！我平时对这家伙很凶吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 也还好吧！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯嗯，我和莉兹姐就是一些小误会！"]}, {"code": 213, "indent": 0, "parameters": [2, 8, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 3, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 3, true]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 本来我也以为今天会被修理一顿，但好在莉兹姐大人有大量，没跟我计较~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过也和我那天表现好有关，嘿嘿嘿！"]}, {"code": 213, "indent": 0, "parameters": [-1, 9, true]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 意思是你已经把钱给莉兹还上了，对吧？"]}, {"code": 213, "indent": 0, "parameters": [4, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 钱？什么钱？"]}, {"code": 213, "indent": 0, "parameters": [3, 7, true]}, {"code": 213, "indent": 0, "parameters": [3, 9, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [166]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊，对了，猪田，亚丝娜给你的装备你穿了吗？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [4, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个护腕？学姐给我的时候就装备上啦！属性很不错呢！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以说，别看学姐有时候对我凶，实际上是最关心我的人！"]}, {"code": 213, "indent": 0, "parameters": [2, 8, false]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呃，那其实你应该谢莉兹。"]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诶？这样吗？"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈逃避", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈回应", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田君，这是莉兹特意为你制作的，我只是转交而已。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [4, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 道具上没有莉兹姐的署名啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且莉兹姐为啥要给我制作装备？"]}, {"code": 213, "indent": 0, "parameters": [4, 9, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 难道说……"]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹坏笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [166]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好啦好啦，不要说这些有的没的，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们赶紧开始任务吧！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [4, 3, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [2, 8, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 玩家 : 移动到 : 相对坐标[0,0]"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 37, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 37, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[0,0] : 时间[15]"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 37, "indent": null}, {"code": 33, "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 80 0"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 38 1"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 27 2"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 647]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 1]}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 解除固定看向"]}, {"code": 117, "indent": 0, "parameters": [154]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 647}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [10]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 198, 0, 8, 2]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 647}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 4, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 6, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).setDirection(8);"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.followers().follower(1).setDirection(8);"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.followers().follower(2).setDirection(8);"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).setDirection(8);"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.followers().follower(1).setDirection(8);"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.followers().follower(2).setDirection(8);"]}, {"code": 355, "indent": 0, "parameters": ["$gameVariables.setValue(197, $gamePlayer.followers().follower(0).x);"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.setValue(198, $gamePlayer.followers().follower(0).y);"]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 事件[2] : 移动到 : 位置变量[197,198]"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 355, "indent": 0, "parameters": ["$gameVariables.setValue(197, $gamePlayer.followers().follower(1).x);"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.setValue(198, $gamePlayer.followers().follower(1).y);"]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 事件[3] : 移动到 : 位置变量[197,198]"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 355, "indent": 0, "parameters": ["$gameVariables.setValue(197, $gamePlayer.followers().follower(2).x);"]}, {"code": 655, "indent": 0, "parameters": ["$gameVariables.setValue(198, $gamePlayer.followers().follower(2).y);"]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 事件[4] : 移动到 : 位置变量[197,198]"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": ["NF_REM_NPC 1"]}, {"code": 356, "indent": 0, "parameters": ["NF_REM_NPC 2"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你好。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 3, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[-24,0] : 时间[15]"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们需要进入灵石洞窟……"]}, {"code": 213, "indent": 0, "parameters": [5, 7, true]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [2], "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<洞窟守卫>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你们要下矿？现在矿洞被剧毒魔物占据了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 为了大家的安全，村长已经下令封锁了这里……"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<洞窟守卫>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以，还是请回吧！"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不用为我们担心。我们是经验丰富的冒险者，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不管什么魔物，都能轻松击败！"]}, {"code": 213, "indent": 0, "parameters": [5, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<洞窟守卫>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 冒险者？大祭司前几天通知我们，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 会有冒险者来帮助我们解决魔物的问题，原来就是你们！"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯嗯，没错没错！就是我们！"]}, {"code": 213, "indent": 0, "parameters": [5, 9, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<洞窟守卫>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 大祭司还嘱咐过，要把制作育灵宝石的秘技传授给你们。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可以现在先回村子学习，也可以解决怪物以后再去。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还有学习宝石制作方法的任务，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 情报漏了这个细节，今天可能做不完第一阶段了……"]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [130, 130, 0, 0, 22]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹岔开", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [166]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要紧。我一个去学，你们继续怪物讨伐和素材收集，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这样时间就不用耽误了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 8, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<洞窟守卫>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好的，那我们走吧。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 莉兹，也不急于一时。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们还是先解决洞窟里怪物，然后再一起去村里……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹岔开", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [166]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不了，我对战斗没兴趣，你们应该也都没辅修珠宝匠吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 宝石配方的学业任务一般都很麻烦，一起行动只会拖累彼此。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 有道理。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就按莉兹说的来吧。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田，你要不要也去帮莉兹的忙？"]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诶？算了吧…不过，好像也可以……"]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 1, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹坏笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [166]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呵呵，宝石制作这种精细活，要他去干嘛？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 只会帮倒忙！如果……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [3, 3, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹嘲弄", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [166]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是奶牛挤奶、母猪配种之类的，我就捎上他了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 213, "indent": 0, "parameters": [2, 11, false]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹安心", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [166]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就这样吧，走了走了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 648]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 117, "indent": 0, "parameters": [154]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 137, 11, 26, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 650}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 41, "parameters": ["亚丝娜", 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 玩家 : 像素偏移[10,0] : 时间[1]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[2] : 像素偏移[10,0] : 时间[1]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[3,0] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [2, 0, 5, 5, 8]}, {"code": 203, "indent": 0, "parameters": [4, 0, 6, 5, 8]}, {"code": 203, "indent": 0, "parameters": [5, 0, 11, 23, 8]}, {"code": 203, "indent": 0, "parameters": [3, 0, 11, 23, 8]}, {"code": 356, "indent": 0, "parameters": ["NF_REM_NPC 2"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "maou_bgm_ethnic29", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-51, -51, 0, 34], 60, true]}, {"code": 213, "indent": 0, "parameters": [4, 5, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个任务也太烦人了吧！从来没见过扣血这么多的坏境特效！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 搞不好根本就是BUG！"]}, {"code": 213, "indent": 0, "parameters": [2, 7, true]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈沉默", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 虽然很想早点再见到结衣，但目前这个情况，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 只能先去找莉兹修戒指了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](一出来亚丝娜的气色就正常了，看来瘴气对她的影响确实大。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但我确实不怕这种环境伤。)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 其实吧……这个瘴气我是没问题的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 它扣的血都没有我自动回血多。"]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诶？！学长这么厉害的吗？你简直是怪物！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以我可以一个人下去。"]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我是想和桐人君一起的，不过……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在效率是最优先考虑的，只能全部拜托桐人君了！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 并不是全部哦，亚丝娜，我还是需要你的帮助。"]}, {"code": 213, "indent": 0, "parameters": [2, 2, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈惊讶2", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我该怎么做？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 第二层矿洞的可见度非常糟糕，而剑士职业的侦察技能简直稀烂，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以我需要你用魔法探查地形，指引我去洞窟最深处……"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 45, "parameters": ["$gamePlayer.followers().follower(0).turnTowardCharacter($gameMap.event(4));"], "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gamePlayer.followers().follower(0).turnTowardCharacter($gameMap.event(4));"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学长！学长！我来助你！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 交给我吧！侦察探索什么的，我可擅长了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我的区域扫描技能虽然每次只能看一小块，但施法范围能覆盖整个副本的！"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 行，那猪田，你负责实时扫描，把数据同步到亚丝娜那边。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，麻烦帮忙过滤下关键信息，再告诉我该怎么走。"]}, {"code": 213, "indent": 0, "parameters": [2, 3, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈得意", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈，感觉桐人君像是特工，我是指挥担当，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而猪田君，是人肉雷达，好有趣~ "]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呵呵，的确。"]}, {"code": 213, "indent": 0, "parameters": [4, 5, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}\\c[8]哼！人肉雷达……人肉马达还差不多！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我解决瘴气的问题以后，你们就可以下来，我们再一起清除怪物。"]}, {"code": 213, "indent": 0, "parameters": [2, 4, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 8, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 25, "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [75]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 玩家 : 直接消失 : 时间[10]"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [4, 9, true]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[10,0] : 时间[15]"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 2, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐，你刚才不是说我们像是指挥部吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我有个点子，可以大大增加代入感，绝对能给桐人学长一个惊喜！"]}, {"code": 213, "indent": 0, "parameters": [2, 2, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈理解", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦？说来听听。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 249, "indent": 0, "parameters": [{"name": "LNSM_ME06_Sleep2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 玩家 : 像素偏移[0,0] : 时间[1]"]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 651]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 1]}, {"code": 117, "indent": 0, "parameters": [154]}, {"code": 201, "indent": 0, "parameters": [0, 171, 26, 43, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 651}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 657}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">位置与位移 : 玩家 : 移动到 : 位置[5,3]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 40, "indent": null}, {"code": 42, "parameters": [255], "indent": null}, {"code": 16, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 41, "parameters": ["亚丝娜", 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [2, 0, 4, 5, 8]}, {"code": 203, "indent": 0, "parameters": [4, 0, 6, 5, 8]}, {"code": 203, "indent": 0, "parameters": [3, 0, 5, 21, 8]}, {"code": 203, "indent": 0, "parameters": [5, 0, 11, 23, 8]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "maou_bgm_ethnic29", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 设置声音 : 默认声音"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dDMS[慢]\\dac一个小时后……"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dDMS[慢]\\dac灵石洞窟-洞窟入口"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 默认模式"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 关闭声音"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 223, "indent": 0, "parameters": [[-51, -51, 0, 34], 30, true]}, {"code": 117, "indent": 0, "parameters": [122]}, {"code": 117, "indent": 0, "parameters": [131]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咻……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这种皮糙肉厚又会自我再生的怪物真是难搞，但总算是全部清除了。"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学长真的强！连续作战一点疲态都没有！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你也不差！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 除了一身的汗，但精神状态感觉还是很不错的。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个任务设计这么精妙，整个过程都是一种享受。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 人逢喜事精神爽啦！哈哈哈~"]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦？你也有同感？"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [10], "indent": 0}, {"code": 25, "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 25, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 虽然有些劳累，但不得不说，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个任务的设计确实很有意思……"]}, {"code": 213, "indent": 0, "parameters": [-1, 9, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，从刚才开始，你都一直没有说话，是累了吗？"]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 12, true]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诶？那个…有…有一点吧……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [3, 6, true]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [130, 130, 0, 0, 22]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹批评", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [166]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你们怎么这么慢？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我在村子里都等得不耐烦了！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个任务比预想的要复杂一些。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 宝石的材料给你，今晚可以做出来吗？"]}, {"code": 213, "indent": 0, "parameters": [3, 7, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹叹气", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [166]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那么急干嘛？你不累其他人也不用休息啊？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈苦笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯，桐人君，虽然我们都希望尽早完成任务，但也应该劳逸结合。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我其实也有点累了……不如暂且就到这里，大家回去休息吧。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好呀好呀！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我今天确实需要好好补补啦~"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [10], "indent": 0}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [166]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 素材给我，明天下班回去就弄，不做出来就不睡觉。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 保证后天可以继续后面的任务，可以了吧？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我刚才也只是问问，又没有说非要。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那今天就这样，我们先回去吧。"]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 249, "indent": 0, "parameters": [{"name": "LNSM_ME06_Sleep2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 117, "indent": 0, "parameters": [363]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 658]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 117, "indent": 0, "parameters": [154]}, {"code": 117, "indent": 0, "parameters": [96]}, {"code": 117, "indent": 0, "parameters": [151]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 658}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 0}, {"id": 2, "name": "EV002亚丝娜", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "亚丝娜", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 18}, {"id": 3, "name": "EV003莉兹", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 8, "pattern": 1, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 19}, {"id": 4, "name": "EV004猪田", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头游戏服", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 19}, {"id": 5, "name": "EV005守卫", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "BB_akunin2kuro", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : Quest_A : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 6"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 2}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 646}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 80 0"]}, {"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 38 1"]}, {"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 27 2"]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 647]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 玩家 : 像素偏移[10,0] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [5, 0, 5, 15, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 8, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [25], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [25], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [75]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 玩家 : 直接消失 : 时间[10]"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 0}, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]}