[null, {"id": 1, "autoRemovalTiming": 0, "chanceByDamage": 100, "iconIndex": 1, "maxTurns": 1, "message1": " has fallen!", "message2": " is slain!", "message3": "", "message4": " revives!", "minTurns": 1, "motion": 3, "name": "Knockout", "note": "State #1 will be automatically added when\nHP reaches 0.", "overlay": 0, "priority": 100, "releaseByDamage": false, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100, "traits": [{"code": 23, "dataId": 9, "value": 0}]}, {"id": 2, "autoRemovalTiming": 2, "chanceByDamage": 100, "description": "", "iconIndex": 81, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "Guard", "note": "", "overlay": 0, "priority": 0, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": true, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 62, "dataId": 1, "value": 0}]}, {"id": 3, "autoRemovalTiming": 0, "chanceByDamage": 100, "description": "", "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "<PERSON><PERSON><PERSON><PERSON>", "note": "", "overlay": 0, "priority": 0, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100, "traits": [{"code": 14, "dataId": 1, "value": 0}]}, {"id": 4, "autoRemovalTiming": 2, "chanceByDamage": 100, "iconIndex": 6, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 1, "name": "<PERSON><PERSON>", "note": "<Damage Color: 255, 0, 0, 128>\n<Break Popup>\n\n<All Element Damage Rate: 200%>\n<No Weak Popup>\n\nThis is for All-Out Attack only:\n<Custom Apply Effect>\nif (target.isEnemy()) {\n  var condition = true;\n  var enemies = target.friendsUnit().aliveMembers();\n  for (var i = 0; i < enemies.length; i++) {\n    var enemy = enemies[i];\n    if (!enemy.isStateAffected(4)) {\n      condition = false;\n      break;\n    }\n  }\n  if (condition && $gameParty.aliveMembers().contains($gameActors.actor(14))) {\n    for (var i = 0; i < enemies.length; i++) {\n      var enemy = enemies[i];\n      enemy._storedResult = JsonEx.makeDeepCopy(enemy._result);\n    }\n    BattleManager.queueForceAction(user, 198, -1);\n    for (var i = 0; i < enemies.length; i++) {\n      var enemy = enemies[i];\n      enemy._result = enemy._storedResult;\n      enemy._storedResult = undefined;\n    }\n  }\n}\n</Custom Apply Effect>", "overlay": 8, "priority": 100, "releaseByDamage": false, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100, "traits": [{"code": 22, "dataId": 3, "value": 10}, {"code": 22, "dataId": 1, "value": -10}, {"code": 22, "dataId": 4, "value": -10}, {"code": 22, "dataId": 6, "value": -10}]}, {"id": 5, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "<PERSON><PERSON>", "note": "<Item Seal>\n<Item Concoct Seal>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 6, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 7, "value": -0.05}], "iconIndex": 2, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 1, "name": "Poison", "note": "<Max Turns: 9>", "overlay": 1, "priority": 60, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 7, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 42, "dataId": 1, "value": 1}, {"code": 42, "dataId": 2, "value": 1}, {"code": 42, "dataId": 3, "value": 1}, {"code": 42, "dataId": 4, "value": 1}, {"code": 42, "dataId": 5, "value": 1}, {"code": 42, "dataId": 6, "value": 1}, {"code": 42, "dataId": 7, "value": 1}, {"code": 42, "dataId": 8, "value": 1}, {"code": 42, "dataId": 9, "value": 1}, {"code": 42, "dataId": 10, "value": 1}, {"code": 42, "dataId": 11, "value": 1}, {"code": 42, "dataId": 12, "value": 1}], "iconIndex": 4, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 1, "name": "Silence", "note": "<Max Turns: 9>", "overlay": 3, "priority": 60, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 8, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 0, "value": -0.5}], "iconIndex": 3, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 1, "name": "Blindness", "note": "<Max Turns: 9>", "overlay": 2, "priority": 60, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 9, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 11, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 1, "name": "Terror", "note": "<Boost Sealed>\n<Max Turns: 9>", "overlay": 10, "priority": 60, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 10, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 6, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 2, "name": "Unconscious", "note": "<Max Turns: 9>", "overlay": 8, "priority": 60, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100}, {"id": 11, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 8, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 2, "name": "Sleep", "note": "<Max Turns: 9>", "overlay": 7, "priority": 60, "removeAtBattleEnd": true, "removeByDamage": true, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100}, {"id": 12, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 22, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 1, "name": "Confusion", "note": "<Max Turns: 9>", "overlay": 5, "priority": 60, "removeAtBattleEnd": true, "removeByDamage": true, "removeByRestriction": false, "removeByWalking": false, "restriction": 2, "stepsToRemove": 100}, {"id": 13, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 6, "value": 0}], "iconIndex": 54, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 1, "name": "<PERSON><PERSON><PERSON>", "note": "<Max Turns: 9>\n<Turn Color: 2>\n\n<Image Popup Name: LegholdTrap>", "overlay": 0, "priority": 60, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 14, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 15, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 2, "value": 1.2}], "iconIndex": 42, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "攻击力上升", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 16, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 17, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 18, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 2, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "封印技能", "note": "<封印技能类:2>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 19, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 7, "value": 0.25}, {"code": 22, "dataId": 9, "value": 0.25}, {"code": 14, "dataId": 1, "value": 1}], "iconIndex": 89, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "单刀作战", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 20, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 21, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 222, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "Reflective Veil", "note": "Puts stacks on the target then each time\nmagic is reflected, a stack is removed.\n\n<Custom Apply Effect>\nvar stacks = target.getStateCounter(stateId) || 0;\nstacks += origin.bp + 1;\nstacks = stacks.clamp(0, 9);\ntarget.setStateCounter(stateId, stacks);\n</Custom Apply Effect>\n\n<Custom React Effect>\nif (this.isMagical() && value > 0) {\n  var animationId = target._lastAnimationId || this.item().animationId;\n  setTimeout(function(attacker, action, animationId) { \n    if (attacker.isAlive()) {\n        attacker.startAnimation(animationId);\n      action.apply(attacker);\n      attacker.clearResult();\n      if (attacker.isDead()) {\n        attacker.performCollapse();\n      }\n    }\n  }.bind(this, attacker, this, animationId), 1000);\n  target.startAnimation(132);\n  value = 0;\n  var stacks = target.getStateCounter(stateId) || 0;\n  stacks -= 1;\n  stacks = stacks.clamp(0, 9);\n  if (stacks < 1) {\n    target.removeState(stateId);\n  } else {\n    target.setStateCounter(stateId, stacks);\n  }\n}\n</Custom React Effect>", "overlay": 0, "priority": 100, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 22, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 109, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 0, "name": "Holy Auspices", "note": "This is a custom status effect for the \nCleric skill: Holy Auspices. It will\nmake the user cast the same skill twice.\n\n<Max Turns: 9>\n\n<Custom Action Start Effect>\nif (!$gameTemp._repeatAction && !$gameTemp._secondRepeat) {\n    var action = user.currentAction();\n    if (!!action && action.isSkill() && !action.item().note.match(/<Divine>/i) && !action.isAttack() && !action.isGuard()) {\n        $gameTemp._repeatAction = action.item().id;\n    }\n}\n</Custom Action Start Effect>\n\n<Custom Initiate Effect>\nif (!!$gameTemp._repeatAction && !$gameTemp._repeatTarget) {\n  $gameTemp._repeatActionTarget = target;\n}\n</Custom Initiate Effect>\n\n<Custom Action End Effect>\nif ($gameTemp._secondRepeat) {\n   $gameTemp._secondRepeat = false;\n} else if (!!$gameTemp._repeatAction) {\n    BattleManager.queueForceAction(user, $gameTemp._repeatAction, $gameTemp._repeatActionTarget);\n    $gameTemp._repeatAction = undefined;\n    $gameTemp._secondRepeat = true;\n}\n</Custom Action End Effect>", "overlay": 0, "priority": 80, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 23, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 108, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Enlightenment", "note": "This is a custom status effect for the \nScholar skill: Enlightenment. Skills that\ntargeted all enemies now target one enemy.\n\n<Skill Target Change Enemies: All to One>\n<Max Turns: 9>\n\n<Custom Confirm Effect>\nif (value > 0 && this.isForOne()) {\n  value *= 4;\n}\n</Custom Confirm Effect>", "overlay": 0, "priority": 80, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 24, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 82, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "Sidestep", "note": "Puts stacks on the target then each time\nattacks are dodged, a stack is removed.\n\n<Custom Apply Effect>\nvar stacks = target.getStateCounter(stateId) || 0;\nstacks += origin.bp + 1;\nstacks = stacks.clamp(0, 9);\ntarget.setStateCounter(stateId, stacks);\n</Custom Apply Effect>\n\n<Custom Select Effect>\nif (this.isPhysical() && this.isDamage()) {\n  this._formerItemSuccessRate = this.item().successRate;\n  this.item().successRate = 0;\n}\n</Custom Select Effect>\n\n<Custom Deselect Effect>\nif (this._formerItemSuccessRate !== undefined) {\n  this.item().successRate = this._formerItemSuccessRate;\n  var stacks = target.getStateCounter(stateId) || 0;\n  stacks -= 1;\n  stacks = stacks.clamp(0, 9);\n  if (stacks < 1) {\n    target.removeState(stateId);\n  } else {\n    target.setStateCounter(stateId, stacks);\n  }\n}\n</Custom Deselect Effect>", "overlay": 0, "priority": 100, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 25, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 128, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Incite", "note": "This is the status effect for the Warrior\nclass's Incite skill. It draws all attacks\nthat target one foe towards the user.\n\n<Physical Taunt>\n<Magical Taunt>\n<Certain Taunt>\n<Max Turns: 9>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 26, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 149, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Spread the Love", "note": "This is a custom status effect for the \nDancer skill: Spread the Love. Skills that\ntargeted one ally/foe now target all.\n\n<Skill Target Change Enemies: One to All>\n<Skill Target Change Allies: One to All>\n<Max Turns: 9>", "overlay": 0, "priority": 80, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 27, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 241, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 0, "name": "Immunity", "note": "This is a custom status effect for the\nApothecary class. Once on, it prevents the\naffected ally to acquire the listed states or\ndebuffs until this status effect wears off.\n\n<State Immune: 6, 7, 8, 9, 10, 11, 12, 13>\n<Debuff Immune: 0, 1, 2, 3, 4, 5, 6, 7>\n<Max Turns: 9>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 28, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 147, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Charity", "note": "This is a custom status effect for the \nApothecary skill: Charity. Items that\ntargeted one ally/foe now target all.\n\n<Item Target Change Enemies: One to All>\n<Item Target Change Allies: One to All>\n<Max Turns: 9>", "overlay": 0, "priority": 80, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 29, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 0, "value": 0.5}, {"code": 22, "dataId": 2, "value": 0.5}], "iconIndex": 225, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 0, "name": "Take Aim", "note": "This is the Hunter class's Take Aim status\neffect which raises all allies' HIT and CRI.\n\n<Max Turns: 9>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 30, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 160, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 0, "name": "BP Boost", "note": "This is the Starseer's BP Boost status effect\nthat grants an additional BP gain per turn\nif the user hasn't performed a Boost during\nthe past turn.\n\n<BP Regen: +1>\n<Max Turns: 9>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 31, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 2, "value": 1}], "iconIndex": 76, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 0, "name": "Divination", "note": "This is the Starseer's Diviniation status\neffect that gives the target the ability\nto land critical hits whenever possible.\n\n<Max Turns: 9>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 32, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 1, "value": 0.5}], "iconIndex": 30, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 0, "name": "Evasion Up", "note": "This is the Starseer's Evasion Up status\neffect that gives the target more EVA.\n\n<Max Turns: 9>\n<Turn Color: 24>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 33, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 1, "value": 0.5}], "iconIndex": 28, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 0, "name": "<PERSON>uff", "note": "This is the <PERSON>eer's Anti Buff status\neffect that prevents the target from being\nable to receive buffs.\n\n<Max Turns: 9>\n<Buff Immunity: 0, 1, 2, 3, 4, 5, 6, 7>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 34, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 1, "value": 0.5}], "iconIndex": 29, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 0, "name": "<PERSON>", "note": "This is the Starseer's Anti Debuff status\neffect that prevents the target from being\nable to receive debuffs.\n\n<Max Turns: 9>\n<Debuff Immunity: 0, 1, 2, 3, 4, 5, 6, 7>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 35, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 7, "value": 0.05}], "iconIndex": 72, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 0, "name": "HP Regen", "note": "This is the Starseer's HP Regen status effect\nthat lets the target regen HP each turn.\n\n<Max Turns: 9>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 36, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 6, "value": 1}], "iconIndex": 77, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "Eye for an Eye", "note": "Puts stacks on the target then each time\ntarget receives a physical attack, the\ntarget will counter if it is an enemy.\n\n<Counter Skills: 1>\n<Hit Counter>\n<Counter Condition>\nphysical hit\n</Counter Condition>\n\n<Custom Apply Effect>\nvar stacks = target.getStateCounter(stateId) || 0;\nstacks += origin.bp + 1;\nstacks = stacks.clamp(0, 9);\ntarget.setStateCounter(stateId, stacks);\n</Custom Apply Effect>\n\n<Custom Conclude Effect>\nif (BattleManager.isCountering()) {\n  var stacks = user.getStateCounter(stateId) || 0;\n  stacks -= 1;\n  stacks = stacks.clamp(0, 9);\n  if (stacks < 1) {\n    user.removeState(stateId);\n  } else {\n    user.setStateCounter(stateId, stacks);\n  }\n}\n</Custom Conclude Effect>", "overlay": 0, "priority": 100, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 37, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 64, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Fire Rune", "note": "Rune Lord's Fire Rune.\nFollow up physical attacks with\nFiery Pursuit\n\n<Max Turns: 9>\n<Physical Follow Up Skill: 141>\n\n<Custom Initiate Effect>\nif (this.isPhysical() && this.isForOpponent()) {\n  target._pursuit = true;\n}\n</Custom Initiate Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 38, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 65, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Ice Rune", "note": "Rune Lord's Ice Rune.\nFollow up physical attacks with\nIcy Pursuit\n\n<Max Turns: 9>\n<Physical Follow Up Skill: 142>\n\n<Custom Initiate Effect>\nif (this.isPhysical() && this.isForOpponent()) {\n  target._pursuit = true;\n}\n</Custom Initiate Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 39, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 66, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Thunder Rune", "note": "Rune Lord's Thunder Rune.\nFollow up physical attacks with\nShocking Pursuit\n\n<Max Turns: 9>\n<Physical Follow Up Skill: 143>\n\n<Custom Initiate Effect>\nif (this.isPhysical() && this.isForOpponent()) {\n  target._pursuit = true;\n}\n</Custom Initiate Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 40, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 69, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Wind Rune", "note": "Rune Lord's Thunder Rune.\nFollow up physical attacks with\nWindy Pursuit\n\n<Max Turns: 9>\n<Physical Follow Up Skill: 144>\n\n<Custom Initiate Effect>\nif (this.isPhysical() && this.isForOpponent()) {\n  target._pursuit = true;\n}\n</Custom Initiate Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 41, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 70, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Light Rune", "note": "Rune Lord's Thunder Rune.\nFollow up physical attacks with\nBright Pursuit\n\n<Max Turns: 9>\n<Physical Follow Up Skill: 145>\n\n<Custom Initiate Effect>\nif (this.isPhysical() && this.isForOpponent()) {\n  target._pursuit = true;\n}\n</Custom Initiate Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 42, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 71, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "<PERSON> Rune", "note": "Rune Lord's Thunder Rune.\nFollow up physical attacks with\nShadowy Pursuit\n\n<Max Turns: 9>\n<Physical Follow Up Skill: 146>\n\n<Custom Initiate Effect>\nif (this.isPhysical() && this.isForOpponent()) {\n  target._pursuit = true;\n}\n</Custom Initiate Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 43, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 75, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Transfer Rune", "note": "Rune Lord's Thunder Rune.\nFollow up physical attacks with\nShadowy Pursuit\n\n<Max Turns: 9>\n<Skill Target Change: Self to All>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 44, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 121, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Spellmaster", "note": "Sorcerer's Spellmaster status effect.\nThis makes magical attacks do critical\ndamage whenever possible.\n\n<Max Turns: 9>\n<Custom Confirm Effect>\nif (this.isMagical() && value > 0 && !target.result().critical) {\n  if (Math.random() > target.cev) {\n    target.result().critical = true;\n    value *= 2;\n  }\n}\n</Custom Confirm Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 45, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 61, "dataId": 0, "value": 1}], "iconIndex": 73, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 0, "name": "<PERSON><PERSON>", "note": "<PERSON> Blade's <PERSON><PERSON> skill applies this\nstatus effect.\n\n<Max Turns: 9>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 46, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 0, "name": "Jumping", "note": "<Cannot Select: All>\n\n<Custom Apply Effect>\nuser._jumpBP = user.bp;\n</Custom Apply Effect>\n\n// Buffs & States Core\n<Custom Leave Effect>\nvar skill = 219;\nvar target = -2;\nuser.setUseBP(user._jumpBP);\nBattleManager.queueForceAction(user, skill, target);\nuser._jumpBP = undefined;\n</Custom Leave Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100}, {"id": 47, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 31, "dataId": 7, "value": 1}], "iconIndex": 64, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Fire Infusion", "note": "<Max Turns: 9>\n\n<Custom Confirm Effect>\nif (this.isAttack()) {\n  target.startAnimation(3);\n}\n</Custom Confirm Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 48, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 31, "dataId": 8, "value": 1}], "iconIndex": 65, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Ice Infusion", "note": "<Max Turns: 9>\n\n<Custom Confirm Effect>\nif (this.isAttack()) {\n  target.startAnimation(4);\n}\n</Custom Confirm Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 49, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 31, "dataId": 9, "value": 1}], "iconIndex": 66, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Lightning Infusion", "note": "<Max Turns: 9>\n\n<Custom Confirm Effect>\nif (this.isAttack()) {\n  target.startAnimation(5);\n}\n</Custom Confirm Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 50, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 31, "dataId": 10, "value": 1}], "iconIndex": 69, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Wind Infusion", "note": "<Max Turns: 9>\n\n<Custom Confirm Effect>\nif (this.isAttack()) {\n  target.startAnimation(92);\n}\n</Custom Confirm Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 51, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 31, "dataId": 11, "value": 1}], "iconIndex": 70, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Light Infusion", "note": "<Max Turns: 9>\n\n<Custom Confirm Effect>\nif (this.isAttack()) {\n  target.startAnimation(97);\n}\n</Custom Confirm Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 52, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 31, "dataId": 12, "value": 1}], "iconIndex": 71, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Dark Infusion", "note": "<Max Turns: 9>\n\n<Custom Confirm Effect>\nif (this.isAttack()) {\n  target.startAnimation(101);\n}\n</Custom Confirm Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 53, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 2, "value": 0.5}], "iconIndex": 160, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Critical Booster", "note": "<BP Regen: 0%>\n<Max Turns: 9>\n\n<Custom Confirm Effect>\nif (target.result().critical) {\n    user.gainStoredBP(2);\n    user.startAnimation(135, user.isActor());\n}\n</Custom Confirm Effect>", "overlay": 0, "priority": 1, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 54, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 166, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Weakness Booster", "note": "<BP Regen: 0%>\n<Max Turns: 9>\n\n<Custom Confirm Effect>\nif (Imported.YEP_ElementCore) {\n  var elements = this.getItemElements();\n} else {\n  var elementId = this.item().damage.elementId;\n  if (elementId < 0) {\n    var elements = this.subject().attackElements();\n  } else {\n    var elements = [elementId];\n    }\n}\nfor (var i = 0; i < elements.length; i++) {\n  var elementId = elements[i];\n  if (elementId > 0 && target.elementRate(elementId) >= 1.1) {\n    user.gainStoredBP(2);\n    user.startAnimation(135, user.isActor());\n  }\n}\n</Custom Confirm Effect>", "overlay": 0, "priority": 1, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 55, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "Critical Exploited", "note": "", "overlay": 0, "priority": 1, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 56, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 2, "value": 0.5}], "iconIndex": 163, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Critical Exploiter", "note": "<Max Turns: 9>\n\n<Custom Confirm Effect>\nvar exploited = target.isStateAffected(55);\nif (target.result().critical && !exploited) {\n  target.addState(55);\n  user.otbAddActionTimes(1, true);\n  user.startAnimation(134, user.isActor());\n  BattleManager.otbNextTurnChange(user, 2000, true);\n}\n</Custom Confirm Effect>", "overlay": 0, "priority": 1, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 57, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "Weakness Exploited", "note": "", "overlay": 0, "priority": 1, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 58, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 169, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "Weakness Exploiter", "note": "<Max Turns: 9>\n\n<Custom Confirm Effect>\nvar exploited = target.isStateAffected(57);\nif (!exploited) {\n  if (Imported.YEP_ElementCore) {\n    var elements = this.getItemElements();\n  } else {\n    var elementId = this.item().damage.elementId;\n    if (elementId < 0) {\n      var elements = this.subject().attackElements();\n    } else {\n      var elements = [elementId];\n      }\n  }\n  for (var i = 0; i < elements.length; i++) {\n    var elementId = elements[i];\n    if (elementId > 0 && target.elementRate(elementId) >= 1.1) {\n      target.addState(57);\n      user.otbAddActionTimes(1, true);\n      setTimeout(user.startAnimation.bind(user, 134, user.isActor()), 100);\n      BattleManager.otbNextTurnChange(user, 2000, true);\n    }\n  }\n}\n</Custom Confirm Effect>", "overlay": 0, "priority": 2, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 59, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 162, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "BP Eater", "note": "<Max Turns: 9>\n\n<Custom Confirm Effect>\nif (this.isDamage()) {\n  var multiplier = 1 + user.bp;\n  value *= multiplier;\n}\n</Custom Confirm Effect>", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 60, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 61, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 62, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 63, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 64, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "停止援助", "note": "<封印技能类:2>", "overlay": 0, "priority": 0, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 65, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 0, "value": -10}], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "无法击中", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 66, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 14, "dataId": 1, "value": 1}], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "根性", "note": "<Custom Regenerate Effect>\nif (target.hpRate() < 0.15) {\ntarget.setHp(Math.floor(target.mhp * 0.25));\n}\n</Custom Regenerate Effect>\n", "overlay": 0, "priority": 0, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 67, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 72, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "锁血", "note": "<Custom Regenerate Effect>\nif (target.hpRate() < 0.25) {\ntarget.setHp(Math.floor(target.mhp * 0.25));\n}\n</Custom Regenerate Effect>\n", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 68, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 14, "dataId": 1, "value": 1}], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "不死之身", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 69, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 7, "value": 0.1}, {"code": 21, "dataId": 2, "value": 0.5}, {"code": 21, "dataId": 4, "value": 0.5}, {"code": 61, "dataId": 0, "value": 1}], "iconIndex": 5, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "梦境", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 70, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 41, "dataId": 3, "value": 1}, {"code": 43, "dataId": 274, "value": 1}], "iconIndex": 24, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "噩梦", "note": "<封印技能类:2>\n<封印技能类:1>\n<封印攻击>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 71, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 20, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "全域静默", "note": "<封印技能类:1>\n<封印技能类:2>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 72, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 727, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "死斗战吼", "note": "<封印技能类:1>\n<封印技能类:2>", "overlay": 0, "priority": 100, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 73, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 74, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 75, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 76, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 77, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 7, "value": -0.1}], "iconIndex": 2, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "毒", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 78, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 0, "value": -0.5}], "iconIndex": 3, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "瞎", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 79, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 42, "dataId": 1, "value": 1}, {"code": 42, "dataId": 2, "value": 1}], "iconIndex": 4, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "沉默", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 80, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 8, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "睡眠", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": true, "removeByRestriction": false, "removeByWalking": false, "restriction": 4, "stepsToRemove": 100}, {"id": 81, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [], "iconIndex": 22, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 0, "name": "混乱", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 3, "stepsToRemove": 100}, {"id": 82, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 2, "value": 0.5}], "iconIndex": 58, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "攻击力降低", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 83, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 0, "value": 0.8}], "iconIndex": 56, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "最大血量降低", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 84, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 3, "value": 0.5}], "iconIndex": 59, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "防御降低", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 85, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 6, "value": 0.5}], "iconIndex": 62, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "敏捷降低", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 86, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 7, "value": 0.5}], "iconIndex": 63, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "幸运降低", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 87, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 2, "value": -1}], "iconIndex": 87, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "暴击降低", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 88, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 89, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 90, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 91, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 92, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 93, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 1, "value": 0.5}], "iconIndex": 75, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 0, "name": "回避", "note": "This is the <PERSON>eer's Anti Buff status\neffect that prevents the target from being\nable to receive buffs.\n\n<Max Turns: 9>\n<Buff Immunity: 0, 1, 2, 3, 4, 5, 6, 7>", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 94, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 6, "value": 1}], "iconIndex": 77, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "反击", "note": "Puts stacks on the target then each time\ntarget receives a physical attack, the\ntarget will counter if it is an enemy.\n\n<Counter Skills: 1>\n<Hit Counter>\n<Counter Condition>\nphysical hit\n</Counter Condition>\n\n<Custom Apply Effect>\nvar stacks = target.getStateCounter(stateId) || 0;\nstacks += origin.bp + 1;\nstacks = stacks.clamp(0, 9);\ntarget.setStateCounter(stateId, stacks);\n</Custom Apply Effect>\n\n<Custom Conclude Effect>\nif (BattleManager.isCountering()) {\n  var stacks = user.getStateCounter(stateId) || 0;\n  stacks -= 1;\n  stacks = stacks.clamp(0, 9);\n  if (stacks < 1) {\n    user.removeState(stateId);\n  } else {\n    user.setStateCounter(stateId, stacks);\n  }\n}\n</Custom Conclude Effect>", "overlay": 0, "priority": 100, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 95, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 0, "value": 1.5}], "iconIndex": 40, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "最大HP上升", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 96, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 2, "value": 1.5}], "iconIndex": 42, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "攻击力上升", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 97, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 3, "value": 1.5}], "iconIndex": 43, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "防御上升", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 98, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 6, "value": 1.5}], "iconIndex": 46, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "敏捷上升", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 99, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 21, "dataId": 7, "value": 1.5}], "iconIndex": 46, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "幸运上升", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 100, "autoRemovalTiming": 2, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 2, "value": 1}], "iconIndex": 87, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "暴击上升", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": true, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 101, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 102, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 1, "value": 0.34}], "iconIndex": 70, "maxTurns": 2, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 2, "motion": 0, "name": "心无杂念", "note": "", "overlay": 0, "priority": 1, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 103, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 2, "value": 0.25}], "iconIndex": 67, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "明镜止水", "note": "", "overlay": 0, "priority": 2, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 104, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [{"code": 22, "dataId": 6, "value": 1}], "iconIndex": 850, "maxTurns": 3, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 3, "motion": 0, "name": "自在极意", "note": "", "overlay": 0, "priority": 3, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 105, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 106, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 107, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 108, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 109, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 110, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 111, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 112, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 113, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 114, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 115, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 116, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 117, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 118, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 119, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 120, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 121, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 122, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 123, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 124, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 125, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 126, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 127, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 128, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 129, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 130, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 131, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 132, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 133, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 134, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 135, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 136, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 137, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 138, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 139, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 140, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 141, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 142, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 143, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 144, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 145, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 146, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 147, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 148, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 149, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 150, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 151, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 152, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 153, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 154, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 155, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 156, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 157, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 158, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 159, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 160, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 161, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 162, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 163, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 164, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 165, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 166, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 167, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 168, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 169, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 170, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 171, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 172, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 173, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 174, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 175, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 176, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 177, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 178, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 179, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 180, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 181, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 182, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 183, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 184, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 185, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 186, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 187, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 188, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 189, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 190, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 191, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 192, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 193, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 194, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 195, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 196, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 197, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 198, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 199, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}, {"id": 200, "autoRemovalTiming": 0, "chanceByDamage": 100, "traits": [], "iconIndex": 0, "maxTurns": 1, "message1": "", "message2": "", "message3": "", "message4": "", "minTurns": 1, "motion": 0, "name": "", "note": "", "overlay": 0, "priority": 50, "removeAtBattleEnd": false, "removeByDamage": false, "removeByRestriction": false, "removeByWalking": false, "restriction": 0, "stepsToRemove": 100}]