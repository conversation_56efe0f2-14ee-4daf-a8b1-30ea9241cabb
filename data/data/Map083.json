{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "S - かおりとぬくもり", "pan": 0, "pitch": 100, "volume": 25}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 21, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 50, "width": 30, "data": [2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3859, 3857, 3857, 3857, 3869, 0, 3867, 3857, 3857, 3857, 3861, 0, 0, 3859, 3857, 3857, 3857, 3869, 0, 3867, 3857, 3857, 3857, 3861, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 0, 0, 0, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3868, 0, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 0, 0, 0, 0, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 0, 0, 0, 0, 0, 0, 0, 3866, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3866, 0, 0, 3866, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3866, 0, 0, 0, 0, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 3868, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3868, 0, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3866, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 0, 0, 0, 0, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3866, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3866, 0, 0, 3866, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 3865, 3857, 3857, 3857, 3869, 0, 3867, 3857, 3857, 3857, 3863, 0, 0, 3865, 3857, 3857, 3857, 3869, 0, 3867, 3857, 3857, 3857, 3863, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 79, 15, 20, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 14, "y": 20}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 79, 15, 20, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 15, "y": 20}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "直叶", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 直叶"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 19}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃校服像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 诗乃"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 19}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [91]}, {"code": 121, "indent": 0, "parameters": [21, 21, 1]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 40, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 42, "parameters": [255], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[6] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[7] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[8] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[9] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[10] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[11] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[12] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[13] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[14] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[15] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[16] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[17] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[18] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[19] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[20] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[21] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[22] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[23] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[24] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[25] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[26] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[27] : 强制刷新文本"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 11, "y": 0}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "直叶", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/1蛇岛家口交"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 5, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 5 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 5]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 5 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 125, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "直叶", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/1蛇岛家口交"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关蛇岛家口交的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [203, 203, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 86, 13, 7, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "口交+娇喘5", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 231, "indent": 2, "parameters": [100, "窗户", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 75 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[8]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直叶口交偷窥]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac蛇岛的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}噢~不错,就是得这样,作为起床后的小消遣就是得这种~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}噗嗤...噗嗤....咕噜~咕噜~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac蛇岛的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}反正你也是一个人住，干脆以后搬过来和我同居怎么样，这2天的生活还不错吧~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}唔~咕噜~谁..谁会..咕噜~和你...这种人..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac蛇岛的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}还在说这种话,这次不是你自己主动来我这边的嘛~而且这2天都做的那么开心，\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}你也能感觉到你那色情的肉体不停传来的快感吧~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}..才...没有..那种事...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac蛇岛的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}算了,嘴硬也是你这家伙吸引我的地方~如果只是一般的炮友我也早玩腻了，话说我和你说的游戏怎么样了?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}唔....我...我才对...那种咕噜~咕噜·~游戏没...没兴趣。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac蛇岛的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}别这么说嘛，游戏里能做的事可比现实还要刺激多了，你这副淫荡的肉体在游戏里可是还有更多的价值，\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嘿嘿，我会在说好的城镇等你的~"]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 125, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "直叶", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/1蛇岛家口交"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关蛇岛家口交的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [203, 203, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 86, 13, 7, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "口交+娇喘5", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 231, "indent": 2, "parameters": [100, "窗户", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 75 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[8]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直叶口交偷窥]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac蛇岛的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}噢~不错,就是得这样,作为起床后的小消遣就是得这种~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}噗嗤...噗嗤....咕噜~咕噜~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac蛇岛的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}反正你也是一个人住，干脆以后搬过来和我同居怎么样，这2天的生活还不错吧~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}唔~咕噜~谁..谁会..咕噜~和你...这种人..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac蛇岛的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}还在说这种话,这次不是你自己主动来我这边的嘛~而且这2天都做的那么开心，\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}你也能感觉到你那色情的肉体不停传来的快感吧~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}..才...没有..那种事...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac蛇岛的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}算了,嘴硬也是你这家伙吸引我的地方~如果只是一般的炮友我也早玩腻了，话说我和你说的游戏怎么样了?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}唔....我...我才对...那种咕噜~咕噜·~游戏没...没兴趣。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac蛇岛的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}别这么说嘛，游戏里能做的事可比现实还要刺激多了，你这副淫荡的肉体在游戏里可是还有更多的价值，\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嘿嘿，我会在说好的城镇等你的~"]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "直叶", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/1蛇岛家口交"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关蛇岛家口交的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [203, 203, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 86, 13, 7, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "口交+娇喘5", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 231, "indent": 2, "parameters": [100, "窗户", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 75 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[8]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直叶口交偷窥]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac蛇岛的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}噢~不错,就是得这样,作为起床后的小消遣就是得这种~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}噗嗤...噗嗤....咕噜~咕噜~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac蛇岛的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}反正你也是一个人住，干脆以后搬过来和我同居怎么样，这2天的生活还不错吧~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}唔~咕噜~谁..谁会..咕噜~和你...这种人..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac蛇岛的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}还在说这种话,这次不是你自己主动来我这边的嘛~而且这2天都做的那么开心，\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}你也能感觉到你那色情的肉体不停传来的快感吧~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}..才...没有..那种事...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac蛇岛的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}算了,嘴硬也是你这家伙吸引我的地方~如果只是一般的炮友我也早玩腻了，话说我和你说的游戏怎么样了?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}唔....我...我才对...那种咕噜~咕噜·~游戏没...没兴趣。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac蛇岛的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}别这么说嘛，游戏里能做的事可比现实还要刺激多了，你这副淫荡的肉体在游戏里可是还有更多的价值，\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嘿嘿，我会在说好的城镇等你的~"]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 16}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 127, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃校服像素", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/2文房娇吟"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 5, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 5 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 5]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 5 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 127, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃校服像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/2文房娇吟"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6.2学习室偷窥的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [204, 204, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 87, 35, 23, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 231, "indent": 1, "parameters": [100, "黑幕", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 接吻2 50 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[图书室诗乃偷窥]"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}啊~啊~嗯~嗯~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呣~嗯呜~哈啊...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac粗重的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯哼...嗯哼...怎么样舒服吗~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呣~舒....舒服....呣..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac粗重的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}那能原谅我这段时间去找别的女人而疏忽了你的事了吧~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊~嗯呜...嗯....那个女生是...工作地方...吗?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac粗重的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}原来你都已经知道了,嘿嘿那下次介绍你们互相认识吧，大家来一场激烈的混战~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊~那...那种事情....不要...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac粗重的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}别这么说嘛,对了我让你做的事情你有去尝试吗?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯~稍..稍微有尝试过...只是真的有做那种...事情的必要吗...而且也没什么人..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac粗重的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}你不是想对自己做出改变吗，那可是最好的方式，刚刚开始的话肯定会那样的，\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}之后我帮你购买一些道具，后续再来详细教你就会好起来的~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac粗重的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}话说离上课时间也没多少了,得抓紧时间在来个两发才行~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯~嗯~"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 50, "pitch": 100, "pan": 100}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 127, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃校服像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/2文房娇吟"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6.2学习室偷窥的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [204, 204, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 87, 35, 23, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 接吻2 50 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[图书室诗乃偷窥]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}啊~啊~嗯~嗯~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呣~嗯呜~哈啊...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac粗重的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯哼...嗯哼...怎么样舒服吗~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呣~舒....舒服....呣..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac粗重的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}那能原谅我这段时间去找别的女人而疏忽了你的事了吧~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊~嗯呜...嗯....那个女生是...工作地方...吗?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac粗重的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}原来你都已经知道了,嘿嘿那下次介绍你们互相认识吧，大家来一场激烈的混战~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊~那...那种事情....不要...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac粗重的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}别这么说嘛,对了我让你做的事情你有去尝试吗?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯~稍..稍微有尝试过...只是真的有做那种...事情的必要吗...而且也没什么人..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac粗重的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}你不是想对自己做出改变吗，那可是最好的方式，刚刚开始的话肯定会那样的，\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}之后我帮你购买一些道具，后续再来详细教你就会好起来的~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac粗重的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}话说离上课时间也没多少了,得抓紧时间在来个两发才行~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯~嗯~"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 50, "pitch": 100, "pan": 100}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃校服像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/2文房娇吟"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6.2学习室偷窥的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [204, 204, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 87, 35, 23, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 接吻2 50 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[图书室诗乃偷窥]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}啊~啊~嗯~嗯~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呣~嗯呜~哈啊...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac粗重的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯哼...嗯哼...怎么样舒服吗~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呣~舒....舒服....呣..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac粗重的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}那能原谅我这段时间去找别的女人而疏忽了你的事了吧~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊~嗯呜...嗯....那个女生是...工作地方...吗?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac粗重的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}原来你都已经知道了,嘿嘿那下次介绍你们互相认识吧，大家来一场激烈的混战~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊~那...那种事情....不要...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac粗重的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}别这么说嘛,对了我让你做的事情你有去尝试吗?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯~稍..稍微有尝试过...只是真的有做那种...事情的必要吗...而且也没什么人..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac粗重的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}你不是想对自己做出改变吗，那可是最好的方式，刚刚开始的话肯定会那样的，\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}之后我帮你购买一些道具，后续再来详细教你就会好起来的~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac粗重的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}话说离上课时间也没多少了,得抓紧时间在来个两发才行~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac微弱的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯~嗯~"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 50, "pitch": 100, "pan": 100}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 16}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 125, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 120}, "directionFix": true, "image": {"tileId": 0, "characterName": "直叶", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/7门的那一边"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关和直叶约会后在直叶家门口剧情的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 121, "indent": 1, "parameters": [226, 226, 0]}, {"code": 121, "indent": 1, "parameters": [153, 153, 1]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 115]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 127, 28, 33, 8, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 102, "indent": 1, "parameters": [["门的那一边", "门的里面"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "门的那一边"]}, {"code": 243, "indent": 2, "parameters": []}, {"code": 241, "indent": 2, "parameters": [{"name": "夜晚", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 2, "parameters": [{"name": "P_piston01", "volume": 15, "pitch": 100, "pan": 100}]}, {"code": 231, "indent": 2, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 2, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[8]"]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[门后隐奸慢]"]}, {"code": 231, "indent": 2, "parameters": [100, "黑幕不直视", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac哥..哥哥你怎么过来了..."]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac没什么，就是小直你把放在我那的玩偶和照片给忘记了,"]}, {"code": 401, "indent": 2, "parameters": ["\\dac抱歉，小直你这是刚刚在洗澡嘛..."]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac嗯...刚刚洗完来着...其实,不用特地跑一趟过来的..."]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac反正没什么事就送过来了,毕竟小直那么喜欢的样子。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac嗯…谢谢…呜…"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac小直?怎么感觉你有一些晃悠悠的?"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac...没什么.只是泡澡好像有点久稍微有点晕..."]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac是,是嘛。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210]（小直最近是不是身材又变好了,视线总是忍不住往那里看)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210]不行…看着地面转移一下注意力吧…"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac咳咳，那个，我是不是和母亲问声好比较好。"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 100 \\}哼!"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 224, "indent": 2, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[门后隐奸快]"]}, {"code": 245, "indent": 2, "parameters": [{"name": "P_piston03", "volume": 15, "pitch": 100, "pan": 100}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac不,不用啦,妈妈接到公司的电话出门了..呜.."]}, {"code": 401, "indent": 2, "parameters": ["\\dac不知道什么时候才会回来..."]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac这样啊,嘛，那下次再说吧。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac好...抱歉，还麻烦哥哥你特地跑一趟..."]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac那我，我先回去了,小直你快去换身衣服吧，穿成这样小心"]}, {"code": 401, "indent": 2, "parameters": ["\\dac感冒。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac...嗯,我打算去道场换练习服在锻炼会..."]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac也别太累了,毕竟今天也到处跑了一天,晚安。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac我会的..哥哥晚安..."]}, {"code": 250, "indent": 2, "parameters": [{"name": "开门", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 2, "parameters": [99]}, {"code": 235, "indent": 2, "parameters": [100]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "门的里面"]}, {"code": 243, "indent": 2, "parameters": []}, {"code": 241, "indent": 2, "parameters": [{"name": "夜晚", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 2, "parameters": [{"name": "pierrot_piston01_middle", "volume": 15, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 2, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 2, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[8]"]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[约会晚影绘]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac好像正在锻炼的样子."]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac小时候爷爷也每天让我这样做深蹲来着,真亏小直能替我"]}, {"code": 401, "indent": 2, "parameters": ["\\dac一直坚持下来呢。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac........"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac不过总感觉有点色气,明明只是在做深蹲而已...."]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac啧，又在疑神疑鬼了,今天白天不是在厕所那边就胡思乱想"]}, {"code": 401, "indent": 2, "parameters": ["\\dac闹出误会了嘛，我这坏毛病...."]}, {"code": 235, "indent": 2, "parameters": [99]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 16}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 130, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 62}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃fog像素", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/5戏猫于泉"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 5, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 5 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 5]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 5 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 130, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃fog像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/5戏猫于泉"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关从民居窗户窥视温泉的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [212, 212, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 201, "indent": 1, "parameters": [0, 116, 17, 12, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "啪啪啪1", "volume": 50, "pitch": 100, "pan": 100}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃FOG窗外]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯啊,哈啊，呜....."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}我记得现实的话你的敏感点是在这个地方吧~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}噢噢噢~~嗯啊~~呜~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈哈~不仅小穴紧致程度就是敏感点也甚至能做到和现实\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}差不多程度嘛,这种真实感真不赖~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}之前那...那个女精灵..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}很在意那孩子?既然这样的话下次在这里面带你俩见一面吧，\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}要好好相处噢，还有外面我教你做的那些效果应该出现了吧?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}啊啊~~♥嗯~人好...好像开始多起来了~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嘿嘿~听我的准没错，在等一段时间差不多就该我这个好炮友登场了~不过在那之前,\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}等做完我先带你去这个城镇有趣的好地方吧，你在意的孩子现在也正在哪里呢，嘿嘿。"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 50, "pitch": 100, "pan": 100}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 130, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃fog像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/5戏猫于泉"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关从民居窗户窥视温泉的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [212, 212, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 201, "indent": 1, "parameters": [0, 116, 17, 12, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "啪啪啪1", "volume": 50, "pitch": 100, "pan": 100}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃FOG窗外]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯啊,哈啊，呜....."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}我记得现实的话你的敏感点是在这个地方吧~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}噢噢噢~~嗯啊~~呜~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈哈~不仅小穴紧致程度就是敏感点也甚至能做到和现实\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}差不多程度嘛,这种真实感真不赖~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}之前那...那个女精灵..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}很在意那孩子?既然这样的话下次在这里面带你俩见一面吧，\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}要好好相处噢，还有外面我教你做的那些效果应该出现了吧?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}啊啊~~♥嗯~人好...好像开始多起来了~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嘿嘿~听我的准没错，在等一段时间差不多就该我这个好炮友登场了~不过在那之前,\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}等做完我先带你去这个城镇有趣的好地方吧，你在意的孩子现在也正在哪里呢，嘿嘿。"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 50, "pitch": 100, "pan": 100}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃fog像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/5戏猫于泉"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关从民居窗户窥视温泉的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [212, 212, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 201, "indent": 1, "parameters": [0, 116, 17, 12, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "啪啪啪1", "volume": 50, "pitch": 100, "pan": 100}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃FOG窗外]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯啊,哈啊，呜....."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}我记得现实的话你的敏感点是在这个地方吧~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}噢噢噢~~嗯啊~~呜~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈哈~不仅小穴紧致程度就是敏感点也甚至能做到和现实\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}差不多程度嘛,这种真实感真不赖~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}之前那...那个女精灵..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}很在意那孩子?既然这样的话下次在这里面带你俩见一面吧，\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}要好好相处噢，还有外面我教你做的那些效果应该出现了吧?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}啊啊~~♥嗯~人好...好像开始多起来了~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男???："]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嘿嘿~听我的准没错，在等一段时间差不多就该我这个好炮友登场了~不过在那之前,\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}等做完我先带你去这个城镇有趣的好地方吧，你在意的孩子现在也正在哪里呢，嘿嘿。"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 50, "pitch": 100, "pan": 100}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 16}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 125, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 120}, "directionFix": true, "image": {"tileId": 0, "characterName": "直叶", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/7虚以委蛇"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/7夜晚在直叶房间发生事情的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "暗い・憂鬱・不安【2556日間の闇】", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）（ゆっくり）", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 淫乱普通に喘ぐ1 20 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[8]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[约会之夜1]"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯~~呜啊~~哈啊~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac呼~爽~这是第几发来着?带过来的套子快用完了啊。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac噢~~~嗯啊~~~嗯~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac妈的，你真是越来越棒了，要不别做炮友了,现在的话，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我可以答应让你当我的女朋友噢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac谁会…当..嗯…你这种人的…女朋友…啊~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不要这么冷淡嘛，这可是为数不多的机会哦，错过了就没有下次了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不要….呜…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac是嘛，真可惜，不过今天最刺激的还得是当着自己暗恋的人的面的时候吧，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac都把玄关的地毯弄湿了不是嘛~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac还不是你…嗯啊~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不过真亏得他没发现呢，真是太好笑啦哈哈哈。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac啊啊~~哈啊~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac怎么，又要去了嘛？嘛，我也差不多了，就这样一起高潮吧~"]}, {"code": 356, "indent": 1, "parameters": [">地图滤镜 : 噪点滤镜 : 50"]}, {"code": 356, "indent": 1, "parameters": ["HorrorEffects Screen Create Glitch"]}, {"code": 356, "indent": 1, "parameters": ["HorrorEffects Screen Change Glitch Frequency 25"]}, {"code": 250, "indent": 1, "parameters": [{"name": "噪声", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["HorrorEffects Screen Clear"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 356, "indent": 1, "parameters": [">地图滤镜 : 噪点滤镜 : 关闭滤镜"]}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）（中くらい）", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 淫乱普通に喘ぐ3 20 100 0"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[约会之夜2]"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac呼~呼~果然还是得这个姿势~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哈啊….嗯~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac说起来你这个骚货居然穿着炮友给你买的衣服和别的男人约会,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac对我也太过分了,这个丝袜这条情趣内裤我可是精心挑选了好久的。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac可惜对于那种木头人是完全不会懂的~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我...我不知道你在说什么啊~♥♥~啊~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不过我原谅你了,这个紧致感,不愧是被我从处女开发的小穴,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac其他女人稍微玩一会早就跟死鱼一样了~而宝贝你因为学习那狗屁剑道"]}, {"code": 401, "indent": 1, "parameters": ["\\dac获得的好体力,搞不好真的拼起来,我都有可能吃不消……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac噢~~~啊~~~嗯啊~~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac把那种家伙送来的玩偶抱这么紧,看来是相当喜欢啊,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac是怕是弄脏它吗~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac男:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac唔..别突然夹那么紧、别扭……我操，又要射了…………！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac女:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哼~~~~"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 16}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 125, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 120}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃校服像素", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/7厕所惊情"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/7在学校厕所发生事件的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 121, "indent": 1, "parameters": [227, 227, 0]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 108]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 16, 18, 17, 8, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "脈打つ音単体（中くらい）５回", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[师奶小洞口]"]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[99] : 空中飘浮 : 持续时间[无限] : 缓冲时间[1] : 飘浮高度[30] : 周期[150] : 幅度[20]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac蛇岛:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac这可是经过我精心调教过的口技,除了我可是还没尝过"]}, {"code": 401, "indent": 1, "parameters": ["\\dac其他人的荤,保证瞬间能让你升天,小屌丝你只需要把你"]}, {"code": 401, "indent": 1, "parameters": ["\\dac的肉棒从这个洞里放进来,我就让她用这个口技帮你服务一下~"]}, {"code": 250, "indent": 1, "parameters": [{"name": "吞口水", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](好...好灵活的舌头,被这种东西口交....连亚丝娜"]}, {"code": 401, "indent": 1, "parameters": ["\\dac都没帮我做过的事。)"]}, {"code": 250, "indent": 1, "parameters": [{"name": "吞口水", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac蛇岛:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不要老是咽口水嘛,你只需要把肉棒伸进来,剩下的只要交给"]}, {"code": 401, "indent": 1, "parameters": ["\\dac这个骚货就行了~真的很简单,这可比偷窥带劲多了。"]}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[99] : 立即终止动作"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 16}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 125, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 176}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃fog像素", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/10奥格与珊妮"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/10所做之梦的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [231, 231, 0]}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 201, "indent": 1, "parameters": [0, 266, 22, 1, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": [">地图滤镜 : 模糊滤镜 : 200"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "怪しげな感じ", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）（中くらい）", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃猪头人]"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呃…头好昏……我怎么了……？ 身体动不了…… "]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这是……！ "]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">地图滤镜 : 模糊滤镜 : 25"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 356, "indent": 2, "parameters": [">地图滤镜 : 模糊滤镜 : 关闭滤镜"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac兽人奥格:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 咿嘻嘻……！！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac不知道名字的女弓箭手:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 咿呀……\\i[90]……啊……\\i[90]……\\i[90]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac兽人奥格:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac老大，死了，奥格，活着！奥格很强！\\i[90]"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呜咿……♪奥格很强♪奥格战胜人类！奥格是新老大！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac不知道名字的女弓箭手:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 咕，哦……嗯哦哦哦……\\i[90] "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 咕，嗯哦……嗯，呼呼……呀啊啊……！\\i[90] "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac兽人奥格:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 黑毛人类坏！奥格讨厌！蓝毛人类好，奥格喜欢！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 奥格叫蓝毛人类珊妮，珊妮是奥格的雌性！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac不知道名字的女弓箭手:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不要，啊…擅自…啊…给我…\\i[90]"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这种……啊…难听的名字…哦哦~~~\\i[90]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac兽人奥格:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 珊妮不听奥格的话！奥格生气！ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac不知道名字的女弓箭手:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊，啊……不要……不要……\\i[90] "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 快……\\}快要窒息了…… "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac兽人奥格:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 奥格要珊妮生崽子！ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac雌性兽人珊妮:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac \\}咳咳咳，啊啊……知道了……我知道……\\i[90]\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我会生……\\{珊妮要生奥格的崽子！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac兽人奥格:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 奥格高兴！奥格奖励珊妮！ "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哦……噢……奥格发射种汁♪"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac雌性兽人珊妮:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯，啊……啊……不……不行……啊……\\i[90] "]}, {"code": 401, "indent": 1, "parameters": ["\\dac :呜……噜咿…怎么…射的嗯…时候还…啊……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac兽人奥格:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 奥格很强！奥格可以干到早上！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac雌性兽人珊妮:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊……啊嗯嗯……\\I[90]兽人的肉棒，好舒服…… "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 简直像是要把我的小穴挤爆了一样…… "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 要……要去了……哈啊，呀啊啊……！ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac兽人奥格:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 人类的屄洞……好紧……爽爆了……！ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac雌性兽人珊妮:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 肉棒……在身体里面变大了……再这样插进去的话…… "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊，嗯咕……哦…真的要……在这里堕落了啊…………\\i[90]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac兽人奥格:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哦哦哦哦哦……！又要射种汁了！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 奥格舒服，奥格也要让珊妮舒服！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac雌性兽人珊妮:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哦……嗯啊……高，高潮了……！ "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这样玩弄小穴的话……又要高潮了……！\\i[90]"]}, {"code": 111, "indent": 1, "parameters": [1, 74, 0, 10, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 这也太刺激了……游戏里不知道是不是真的有这种机制？ "]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 什么乱七八糟的，FOG怎么可能设计这种剧情？肯定又是做梦。 "]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 感觉意识……又要……"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 0]}, {"code": 356, "indent": 2, "parameters": [">地图滤镜 : 模糊滤镜 : 关闭滤镜"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": [">地图滤镜 : 噪点滤镜 : 关闭滤镜"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 14}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 125}, "directionFix": true, "image": {"tileId": 0, "characterName": "直叶", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/8点燃引线"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/8直叶照顾桐人的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [221, 221, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 115, 22, 5, 2, 0]}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "甘い香り", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "bob_chikan02", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[8]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直叶手交慢]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 小小的，好可爱，和那个混蛋的完全不一样……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 虽然短短小小的，但好硬啊……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 唔………… "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](感觉好舒服…是亚丝娜吗……？)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac感冒了也可以变硬的吗？男人真是神奇。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac是因为发烧的原因吗？感觉好烫……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 和身上其他地方一样，也没有什么气味，"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 真遗憾~明明我还那么想知道哥哥的是什么气味……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 越来越硬了……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哥哥好像意识还是不太清醒……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac但是哥哥的小弟弟可不是的哦。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 要是一直这样子的话，不光没法好好休息，而且也会很难受吧？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不会是因为我所以才变得这么兴奋了吧？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 既然如此，我可要负起责任来~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 就放心交给我吧，绝对会让哥哥平静下来的。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}啊……啊啊，呼啊……唔……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呼呼，哥哥的声音听上去好像很享受的样子呢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我的手真的有这么舒服吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯嗯…………唔……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](唔，看不清……只会是亚丝娜吧……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哦？还想要吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这样的话，我就用一些技巧让哥哥比现在还舒服吧~"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直叶手交快]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 虽然不想承认，但那个混蛋确实让我成长了不少……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 如果我还是什么也不懂，现在也没办法照顾好哥哥了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 感觉哥哥的肉棒越来越大了……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哥哥这样子身体一颤一颤的真可爱。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 偶尔能像这样和哥哥增进兄妹感情，真是很不错呢~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 现在的小鸡鸡比刚才变得更大了呢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呐，哥哥，龟头这里是不是很舒服啊？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac手指每次划过这里的时候，整根小鸡鸡都在抖个不停呢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}啊……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](气上不来…头好晕，不行…意识，快要……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊，哥哥反应越来越有趣了。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 要是一直这样的话，哥哥会坏掉吗？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 坏掉也没关系哦~\\i[90]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这次……就先放过你好了……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯……差不多可以让哥哥释放出来了……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac呐，哥哥，想要什么时候射出来都看可以哦。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}莉兹，其实你不用过来的。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}我反正也没事，正好过来帮你的忙嘛。"]}, {"code": 231, "indent": 1, "parameters": [1, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 1, "parameters": [2, "直叶生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["直叶校服立绘.png"]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 怎么偏偏在这个时候？！"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 244, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 14}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 130, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 242}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃fog像素", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/12又名荆芥"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 15, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 15 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 15]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 15 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 195, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃fog像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/12又名荆芥"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关诗乃在妓院被迷奸的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [242, 242, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 240]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 242, "indent": 1, "parameters": [1]}, {"code": 201, "indent": 1, "parameters": [0, 254, 12, 5, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [242, 242, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 241]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 242, "indent": 1, "parameters": [1]}, {"code": 201, "indent": 1, "parameters": [0, 254, 12, 5, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃fog像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/12又名荆芥"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关诗乃在妓院被迷奸的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [242, 242, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 240]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 242, "indent": 1, "parameters": [1]}, {"code": 201, "indent": 1, "parameters": [0, 254, 12, 5, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [242, 242, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 241]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 242, "indent": 1, "parameters": [1]}, {"code": 201, "indent": 1, "parameters": [0, 254, 12, 5, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 14}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 226}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉法FOG(2)", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11拍摄现场"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/11克莱因观看AV拍摄现场的回想，要回顾吗？"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [245, 245, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 225]}, {"code": 129, "indent": 1, "parameters": [61, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 201, "indent": 1, "parameters": [0, 288, 10, 16, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [245, 245, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 225]}, {"code": 201, "indent": 1, "parameters": [0, 288, 10, 16, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 14}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 352}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉法FOG(2)", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/15斩断时间"]}, {"code": 111, "indent": 0, "parameters": [1, 74, 0, 15, 4]}, {"code": 225, "indent": 1, "parameters": [5, 9, 15, false]}, {"code": 224, "indent": 1, "parameters": [[255, 0, 0, 68], 15, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[28]变态度\\c[101]不足"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[18]无法解锁此录像"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [186, 186, 0, 0, 15]}, {"code": 111, "indent": 1, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 2, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 2, "parameters": [0, "是"]}, {"code": 122, "indent": 3, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 3, "parameters": [0, 139, true]}, {"code": 123, "indent": 3, "parameters": ["A", 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "否"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 226}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉法FOG(2)", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/15斩断时间"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/15莉法用特殊剑技暂停桐人时间后发生事情的回想，要回顾吗？"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 121, "indent": 1, "parameters": [249, 249, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 348]}, {"code": 201, "indent": 1, "parameters": [0, 343, 16, 6, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 121, "indent": 1, "parameters": [249, 249, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 351]}, {"code": 201, "indent": 1, "parameters": [0, 343, 16, 6, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 226}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉法FOG(2)", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/15斩断时间"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/15莉兹用特殊剑技暂停桐人时间后发生事情的回想，要回顾吗？"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 121, "indent": 1, "parameters": [249, 249, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 348]}, {"code": 201, "indent": 1, "parameters": [0, 343, 16, 6, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 121, "indent": 1, "parameters": [249, 249, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 351]}, {"code": 201, "indent": 1, "parameters": [0, 343, 16, 6, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 12}, {"id": 17, "name": "EV017", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 314}, "directionFix": true, "image": {"tileId": 0, "characterName": "工口2", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/14监控录像(一)"]}, {"code": 111, "indent": 0, "parameters": [1, 74, 0, 10, 4]}, {"code": 225, "indent": 1, "parameters": [5, 9, 15, false]}, {"code": 224, "indent": 1, "parameters": [[255, 0, 0, 68], 15, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[28]变态度\\c[101]不足"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[18]无法解锁此录像"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [186, 186, 0, 0, 10]}, {"code": 111, "indent": 1, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 2, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 2, "parameters": [0, "是"]}, {"code": 122, "indent": 3, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 3, "parameters": [0, 139, true]}, {"code": 123, "indent": 3, "parameters": ["A", 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "否"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 125}, "directionFix": true, "image": {"tileId": 0, "characterName": "直叶", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/14监控录像(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是6/14桐人公寓房间的监控录像,要查看吗?"]}, {"code": 102, "indent": 0, "parameters": [["包含录音", "仅有录像", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "包含录音"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 45, "pitch": 90, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 机器工作音 5 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 155 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[8]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直叶录像一]"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac好神奇，明明一开始小巧可爱的样子……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac稍微刺激了一下就变成帅气风格了~ \\i[90]"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不愧是哥哥，可爱和帅气兼备！"]}, {"code": 111, "indent": 1, "parameters": [0, 303, 0]}, {"code": 111, "indent": 2, "parameters": [0, 194, 0]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 3, "parameters": ["\\dac\\}莉兹……"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 3, "parameters": ["\\dac那个毛头？！"]}, {"code": 401, "indent": 3, "parameters": ["\\dac切！竞争对手又增加了吗？"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 3, "parameters": ["\\dac我不会把哥哥让个任何人的！"]}, {"code": 119, "indent": 3, "parameters": ["结束"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}亚丝娜……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不履行女友基本的义务，放任哥哥孤单寂寞；"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不敢去维护自己的爱人，纵容家人霸凌哥哥……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜姐，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我曾经觉得你是哥哥最好的选择……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac但现在……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac已经没有办法再放心把哥哥交给你了。"]}, {"code": 118, "indent": 1, "parameters": ["结束"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 关闭滤镜"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 55, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅有录像"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 155 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[8]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直叶录像一]"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 关闭滤镜"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 125}, "directionFix": true, "image": {"tileId": 0, "characterName": "直叶", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/14监控录像(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是6/14桐人公寓房间的监控录像,要查看吗?"]}, {"code": 102, "indent": 0, "parameters": [["包含录音", "仅有录像", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "包含录音"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 45, "pitch": 90, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 机器工作音 5 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 155 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[8]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直叶录像一]"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac好神奇，明明一开始小巧可爱的样子……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac稍微刺激了一下就变成帅气风格了~ \\i[90]"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不愧是哥哥，可爱和帅气兼备！"]}, {"code": 111, "indent": 1, "parameters": [0, 303, 0]}, {"code": 111, "indent": 2, "parameters": [0, 194, 0]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 3, "parameters": ["\\dac\\}莉兹……"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 3, "parameters": ["\\dac那个粉毛？！"]}, {"code": 401, "indent": 3, "parameters": ["\\dac切！竞争对手又增加了吗？"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 3, "parameters": ["\\dac我不会把哥哥让个任何人的！"]}, {"code": 119, "indent": 3, "parameters": ["结束"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}亚丝娜……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不履行女友基本的义务，放任哥哥孤单寂寞；"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不敢去维护自己的爱人，纵容家人霸凌哥哥……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜姐，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我曾经觉得你是哥哥最好的选择……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac但现在……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac已经没有办法再放心把哥哥交给你了。"]}, {"code": 118, "indent": 1, "parameters": ["结束"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 关闭滤镜"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 55, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅有录像"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 155 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[8]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直叶录像一]"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 关闭滤镜"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 14}, {"id": 18, "name": "EV018", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 364}, "directionFix": true, "image": {"tileId": 0, "characterName": "工口2", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/16监控录像(二)"]}, {"code": 111, "indent": 0, "parameters": [1, 74, 0, 15, 4]}, {"code": 225, "indent": 1, "parameters": [5, 9, 15, false]}, {"code": 224, "indent": 1, "parameters": [[255, 0, 0, 68], 15, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[28]变态度\\c[101]不足"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[18]无法解锁此录像"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [186, 186, 0, 0, 10]}, {"code": 111, "indent": 1, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 2, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 2, "parameters": [0, "是"]}, {"code": 122, "indent": 3, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 3, "parameters": [0, 139, true]}, {"code": 123, "indent": 3, "parameters": ["A", 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "否"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 125}, "directionFix": true, "image": {"tileId": 0, "characterName": "直叶", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/16监控录像(二)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是6/16桐人公寓房间的监控录像,要查看吗?"]}, {"code": 102, "indent": 0, "parameters": [["包含录音", "仅有录像", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "包含录音"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 55, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 机器工作音 5 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 155 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[8]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直叶录像二]"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哥哥，小直的屁股舒服吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac你是更喜欢小直的屁股，还是嘴巴呢？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我知道的，我都知道哦~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哥哥最想要的，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac肯定还是我的小穴吧~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不是小直不想给哥哥……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac但我想和哥哥的第一次，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac是哥哥清醒的时候……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac请哥哥原谅我这一点点的小任性吧……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac在此之前，我会用其他地方让哥哥舒服的！"]}, {"code": 118, "indent": 1, "parameters": ["结束"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 关闭滤镜"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 55, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅有录像"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 155 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[8]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直叶录像二]"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 关闭滤镜"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 125}, "directionFix": true, "image": {"tileId": 0, "characterName": "直叶", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/16监控录像(二)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是6/16桐人公寓房间的监控录像,要查看吗?"]}, {"code": 102, "indent": 0, "parameters": [["包含录音", "仅有录像", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "包含录音"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 55, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 机器工作音 5 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 155 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[8]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直叶录像二]"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哥哥，小直的屁股舒服吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac你是更喜欢小直的屁股，还是嘴巴呢？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我知道的，我都知道哦~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哥哥最想要的，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac肯定还是我的小穴吧~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不是小直不想给哥哥……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac但我想和哥哥的第一次，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac是哥哥清醒的时候……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac请哥哥原谅我这一点点的小任性吧……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac在此之前，我会用其他地方让哥哥舒服的！"]}, {"code": 118, "indent": 1, "parameters": ["结束"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 关闭滤镜"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 55, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅有录像"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 155 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[8]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直叶录像二]"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 关闭滤镜"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 12}, {"id": 19, "name": "EV019", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 500}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉法娼服", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/27各取所需"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 15]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉法娼服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/27各取所需"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/27莉法在妓院援交的回想，要回顾吗？"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [268, 268, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 93, 16, 10, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [268, 268, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 93, 16, 10, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉法娼服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/27各取所需"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/27莉法在妓院援交的回想，要回顾吗？"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [268, 268, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 93, 16, 10, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [268, 268, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 93, 16, 10, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 10}, {"id": 20, "name": "EV020", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 130, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 551}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃fog像素", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/30榨精比赛"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 15]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 130, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃fog像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/30榨精比赛"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关桐人被强制拔线之前故事的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac诗乃对桐人的好感度……"]}, {"code": 102, "indent": 1, "parameters": [["低（莉法比赛获胜）", "高（诗乃比赛获胜）"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "低（莉法比赛获胜）"]}, {"code": 121, "indent": 2, "parameters": [386, 386, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "高（诗乃比赛获胜）"]}, {"code": 121, "indent": 2, "parameters": [386, 386, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 121, "indent": 1, "parameters": [272, 272, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 550]}, {"code": 129, "indent": 1, "parameters": [8, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 201, "indent": 1, "parameters": [0, 105, 23, 17, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac比赛的结果是？"]}, {"code": 102, "indent": 1, "parameters": [["莉法胜利", "诗乃胜利"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "莉法胜利"]}, {"code": 121, "indent": 2, "parameters": [386, 386, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "诗乃胜利"]}, {"code": 121, "indent": 2, "parameters": [386, 386, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 121, "indent": 1, "parameters": [272, 272, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 551]}, {"code": 129, "indent": 1, "parameters": [8, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 201, "indent": 1, "parameters": [0, 105, 23, 17, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 14}, {"id": 21, "name": "EV021", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 364}, "directionFix": true, "image": {"tileId": 0, "characterName": "工口2", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/26监控录像(三)"]}, {"code": 111, "indent": 0, "parameters": [1, 74, 0, 15, 4]}, {"code": 225, "indent": 1, "parameters": [5, 9, 15, false]}, {"code": 224, "indent": 1, "parameters": [[255, 0, 0, 68], 15, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[28]变态度\\c[101]不足"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[18]无法解锁此录像"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [186, 186, 0, 0, 10]}, {"code": 111, "indent": 1, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 2, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 2, "parameters": [0, "是"]}, {"code": 122, "indent": 3, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 3, "parameters": [0, 139, true]}, {"code": 123, "indent": 3, "parameters": ["A", 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "否"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 125}, "directionFix": true, "image": {"tileId": 0, "characterName": "直叶", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/26监控录像(三)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是6/26桐人公寓房间的监控录像,要查看吗?"]}, {"code": 102, "indent": 0, "parameters": [["包含录音", "仅有录像", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "包含录音"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 55, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 机器工作音 5 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 155 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[27]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直叶录像三]"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哥哥，对不起……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac明明不应该妒忌的……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac明明应该为你感到高兴的……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac明明以为可以在背后默默支持你……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac但我真的受不了了！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哥哥的正妻应该是我才对！这个位置绝对不能让给其他女人！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哥哥是我的！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不能坐以待毙了，我要采取行动才可以！"]}, {"code": 118, "indent": 1, "parameters": ["结束"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 关闭滤镜"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 55, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅有录像"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 155 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[27]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直叶录像三]"]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 关闭滤镜"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 12}, {"id": 22, "name": "EV022", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 127, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 558}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃校服像素", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/30最后温柔"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 5, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 5 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 5]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 5 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 395, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃校服像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/30最后温柔"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/30诗乃在病房厕所为蛇岛口交的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [277, 277, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 98, 25, 42, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [277, 277, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 98, 26, 42, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 395, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃校服像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/30最后温柔"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/30诗乃在病房厕所为蛇岛口交的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [277, 277, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 98, 25, 42, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [277, 277, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 98, 26, 42, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃校服像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/30最后温柔"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/30诗乃在病房厕所为蛇岛口交的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [277, 277, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 98, 25, 42, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [277, 277, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 98, 26, 42, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 12}, {"id": 23, "name": "EV023", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 125, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 176}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃fog像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/3拔剑相助"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/3桐人在FOG中偶遇诗乃的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [903, 903, 0]}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 623]}, {"code": 201, "indent": 1, "parameters": [0, 319, 29, 28, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [903, 903, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 624]}, {"code": 201, "indent": 1, "parameters": [0, 319, 29, 28, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 12}, {"id": 24, "name": "EV024", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 125, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 672}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃fog像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/6鲜榨种汁"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/6桐人被诗乃榨汁的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 121, "indent": 1, "parameters": [908, 908, 0]}, {"code": 121, "indent": 1, "parameters": [415, 415, 1]}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 672]}, {"code": 201, "indent": 1, "parameters": [0, 246, 6, 6, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac 是否配合诗乃？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 121, "indent": 2, "parameters": [415, 415, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 121, "indent": 2, "parameters": [415, 415, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 121, "indent": 1, "parameters": [908, 908, 0]}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 673]}, {"code": 201, "indent": 1, "parameters": [0, 246, 6, 6, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 12}, {"id": 25, "name": "EV025", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 641}, "directionFix": true, "image": {"tileId": 0, "characterName": "直叶", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/5兄妹共浴(一)"]}, {"code": 111, "indent": 0, "parameters": [1, 74, 0, 45, 4]}, {"code": 225, "indent": 1, "parameters": [5, 9, 15, false]}, {"code": 224, "indent": 1, "parameters": [[255, 0, 0, 68], 15, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[28]变态度\\c[101]不足"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[18]无法解锁此录像"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [186, 186, 0, 0, 15]}, {"code": 111, "indent": 1, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 2, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 2, "parameters": [0, "是"]}, {"code": 122, "indent": 3, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 3, "parameters": [0, 139, true]}, {"code": 123, "indent": 3, "parameters": ["A", 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "否"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 407, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 641}, "directionFix": true, "image": {"tileId": 0, "characterName": "直叶", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/5兄妹共浴(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/5直叶和桐人一起洗澡的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [909, 909, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 641]}, {"code": 201, "indent": 1, "parameters": [0, 252, 9, 5, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [909, 909, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 641]}, {"code": 121, "indent": 1, "parameters": [431, 431, 0]}, {"code": 201, "indent": 1, "parameters": [0, 252, 9, 5, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 407, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 641}, "directionFix": true, "image": {"tileId": 0, "characterName": "直叶", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/5兄妹共浴(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/5直叶和桐人一起洗澡的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [909, 909, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 641]}, {"code": 201, "indent": 1, "parameters": [0, 252, 9, 5, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [909, 909, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 641]}, {"code": 121, "indent": 1, "parameters": [431, 431, 0]}, {"code": 201, "indent": 1, "parameters": [0, 252, 9, 5, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 83, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 641}, "directionFix": true, "image": {"tileId": 0, "characterName": "直叶", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/5兄妹共浴(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/5直叶和桐人一起洗澡的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [909, 909, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 641]}, {"code": 201, "indent": 1, "parameters": [0, 252, 9, 5, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [909, 909, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 641]}, {"code": 121, "indent": 1, "parameters": [431, 431, 0]}, {"code": 201, "indent": 1, "parameters": [0, 252, 9, 5, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 10}, {"id": 26, "name": "EV026", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 125, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 732}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃fog像素", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/8泥棒蓝猫"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/8诗乃据点中发生事情的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [915, 915, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 732]}, {"code": 201, "indent": 1, "parameters": [0, 370, 35, 29, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [915, 915, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 734]}, {"code": 201, "indent": 1, "parameters": [0, 370, 35, 29, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 10}, {"id": 27, "name": "EV027", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 471, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 842}, "directionFix": true, "image": {"tileId": 0, "characterName": "诗乃像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/14擦边直播"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/14诗乃和桐人一起直播的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [928, 928, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 842]}, {"code": 201, "indent": 1, "parameters": [0, 380, 14, 21, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [928, 928, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 843]}, {"code": 201, "indent": 1, "parameters": [0, 380, 14, 21, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 10}, {"id": 28, "name": "EV028", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 471, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 854}, "directionFix": true, "image": {"tileId": 0, "characterName": "直叶现实裸", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/14兄妹共浴(二)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/14直叶和桐人一起洗澡的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [929, 929, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 854]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 382, 15, 26, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [929, 929, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 856]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 382, 15, 26, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 10}]}