{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 32, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 76, "width": 40, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7506, 7493, 7505, 7494, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7508, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7488, 7496, 1536, 7512, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7484, 7500, 7500, 7484, 7500, 7500, 7500, 7500, 7500, 7480, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7488, 7496, 1536, 7283, 7282, 7282, 7282, 7282, 7282, 7282, 7282, 7504, 7282, 7282, 7504, 7858, 7858, 7858, 7858, 7858, 7488, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7488, 7496, 1536, 7289, 7288, 7288, 7288, 7288, 7288, 7288, 7288, 7504, 7288, 7288, 7504, 7864, 7864, 7864, 7864, 7864, 7488, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7488, 7496, 1536, 1536, 1536, 2859, 2849, 2849, 2849, 2849, 2861, 7504, 3666, 3668, 7504, 3234, 3220, 3220, 3220, 3236, 7488, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7488, 7496, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 7504, 3650, 3670, 7504, 3240, 3228, 3212, 3228, 3238, 7488, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7488, 7496, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 7504, 3664, 7515, 7503, 7505, 7517, 3232, 7515, 7505, 7481, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7488, 7496, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 7516, 3664, 7283, 7282, 7282, 7286, 3232, 7283, 7282, 7488, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7488, 7496, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 7287, 3664, 7289, 7288, 7288, 7292, 3244, 7289, 7288, 7488, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7488, 7496, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 7293, 3649, 3652, 3652, 3652, 3652, 3652, 3652, 3668, 7488, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7488, 7496, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 3672, 3660, 3660, 3660, 3660, 3660, 3660, 3670, 7488, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7488, 7474, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7473, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7512, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7510, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4202, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4204, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4302, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 410, 410, 410, 410, 275, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 469, 469, 0, 418, 418, 418, 477, 419, 0, 0, 0, 0, 984, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 319, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 573, 574, 574, 575, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 581, 582, 582, 583, 0, 0, 0, 0, 0, 0, 0, 0, 0, 864, 0, 0, 919, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 970, 0, 0, 0, 0, 0, 0, 0, 0, 927, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 761, 762, 0, 0, 604, 0, 0, 0, 0, 961, 0, 0, 850, 849, 870, 977, 978, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 782, 0, 263, 385, 363, 389, 366, 357, 0, 59, 33, 0, 858, 857, 878, 985, 986, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 453, 461, 271, 419, 419, 397, 418, 283, 0, 67, 0, 0, 895, 856, 0, 993, 994, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 309, 310, 311, 0, 0, 0, 0, 0, 0, 0, 0, 865, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 565, 566, 566, 567, 0, 317, 318, 704, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 451, 438, 439, 450, 0, 0, 0, 712, 0, 0, 0, 896, 897, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 459, 446, 447, 458, 0, 0, 0, 720, 0, 0, 35, 904, 905, 942, 0, 0, 946, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 589, 590, 590, 591, 0, 0, 0, 969, 782, 0, 0, 912, 913, 872, 902, 920, 925, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 935, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 16, 0, 18, 18, 18, 18, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 16, 0, 18, 18, 18, 18, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 15, 0, 18, 18, 17, 18, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 15, 0, 17, 17, 17, 17, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 101, 0, 0, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 101, 0, 101, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 298, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 121, "indent": 0, "parameters": [298, 298, 1]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 7}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 2, "characterIndex": 5}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 17, "y": 15}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 2, "characterIndex": 5}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 22, "y": 15}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 363, 8, 17, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 7, "y": 8}, {"id": 5, "name": "EV005亚丝娜", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "亚丝娜OL", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 699}, "directionFix": false, "image": {"tileId": 0, "characterName": "亚丝娜 居家服", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 6, "name": "EV006猪田", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 0}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 901, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 203, "indent": 0, "parameters": [5, 0, 14, 17, 6]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,-24] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [6, 0, 23, 17, 8]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [6], "indent": null}, {"code": 33, "indent": null}, {"code": 37, "indent": null}, {"code": 41, "parameters": ["猪头裸", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["猪头裸", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}, {"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [5, 5, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [6, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘶哈嘶哈！呣啊呣啊！"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 4]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田君，你在干什么呀？！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 12, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,0] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 17, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咿呃~ 呀啊！！！"]}, {"code": 213, "indent": 0, "parameters": [6, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐你吓死我了，差点阳痿啊！"]}, {"code": 213, "indent": 0, "parameters": [5, 11, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈凛然脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](阳痿个头啊，明明还翘得老高……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你，手上拿的，是什么？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐的…换洗内裤……"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈凛然脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 能不能解释一下为什么会在你手上?"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对不起学姐！现在就还给你！"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈意外脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在给我这个干嘛呀！赶紧放回脏衣篮里去！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦哦。"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 3, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 19, "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嚤~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 一瞬间还对猪田君抱有期待的我真是个傻瓜！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我错了学姐……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 实在是一时忍不住了，但是又不能麻烦学姐…所以……"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以什么？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 为什么要做这么变态的事情？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈掩饰", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 都不知道你在想些什么……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔…对不起！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但是和学姐住在一个房间里，对我来说实在是折磨啊……"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 19, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 满屋子都是学姐散发的香气，完全就像是催情药一样。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但是我又不能忤逆学姐的意愿，做出让学姐伤心的事，一直忍耐着…"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 17, "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 本来好不容易等到上线FOG，可以好好释放一下，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 结果又……"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我不想打扰学姐和学长宝贵的相处时间，不想让学姐为难，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以只好……自己处理，中途看见了放在筐里的内裤，一时糊涂才……"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐，不要讨厌我，我保证以后再也不这样了！"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈迟疑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唉……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 8, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈沉默脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](以猪田君冲动的性格，能忍耐到这种程度，已经很难得了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真放任他积攒太多，最后又情绪失控的话，反而会更麻烦也说不定。)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](可惜刚才没能在游戏里满足一下他。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但现在重新上线，可能会让桐人君觉得奇怪的……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 7, true]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈心事脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}这样，我用手帮你射出来吧……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [6, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 什么？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐你刚才说什么？!"]}, {"code": 213, "indent": 0, "parameters": [5, 11, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈欲言又止", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我是说……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我帮你，打出来。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 19, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [5], "indent": 0}, {"code": 16, "indent": null}, {"code": 15, "parameters": [5], "indent": 0}, {"code": 18, "indent": null}, {"code": 15, "parameters": [5], "indent": 0}, {"code": 19, "indent": null}, {"code": 15, "parameters": [5], "indent": 0}, {"code": 17, "indent": 0}, {"code": 15, "parameters": [5], "indent": 0}, {"code": 16, "indent": 0}, {"code": 15, "parameters": [5], "indent": 0}, {"code": 18, "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦耶，太好了！"]}, {"code": 213, "indent": 0, "parameters": [5, 6, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但仅此而已，你不要又提一些得寸进尺的要求。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不会不会！打出来就好，打出来就好！"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈失望脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 去客厅吧。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 16, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [35]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 213, "indent": 0, "parameters": [6, 9, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诶，对了！也不用学姐动手，我自己来，怎么样？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还有就是，着装上能再微调一下就更好啦~"]}, {"code": 213, "indent": 0, "parameters": [5, 2, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你想干什么？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 4, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐你信我！保证不会做出格的事情！"]}, {"code": 213, "indent": 0, "parameters": [5, 8, false]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 901, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[30]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜足交闻]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 241, "indent": 0, "parameters": [{"name": "bensound-sexy", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 44, "parameters": [{"name": "闻", "volume": 15, "pitch": 60, "pan": 0}], "indent": null}, {"code": 15, "parameters": [100], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "闻", "volume": 15, "pitch": 60, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [100], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "呻吟", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗅嗅~ 嗅嗅~ 哈咿~~~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个味道好上头，好过瘾啊~ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嚤~ 猪田君啊……可以不闻我的脚了吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你不是说要…拿我的脚摩擦那里的吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 只是一点气氛准备啦！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不可以吗学姐？这种事情会让你讨厌吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 讨厌到说不上，但……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这样好羞耻啊！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就我们两个在，有什么好羞耻的嘛！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咻咻！呜啊！色色的味道～"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 今天从外面回来到现在，都还没有洗过脚……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你不会觉得难闻吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么会呢？这种味道让我兴奋得要命啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 喜欢！太喜欢了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 随你吧……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐，我可以舔一下吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 求求你了，我饭后已经刷过牙的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 拜托拜托！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不管你了啦……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 谢谢学姐！"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 245, "indent": 0, "parameters": [{"name": "呻吟", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜足交舔]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [40], "indent": null}, {"code": 44, "parameters": [{"name": "舔", "volume": 40, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [152], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [40], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "舔", "volume": 40, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [152], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我舔我舔～"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦～啊～"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](痒酥酥的，还有点舒服……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 滋溜滋溜～"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 吸溜～ 唰啊～"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要发出那种怪声呀~ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯嗯！尽量！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 滋溜~ 滋溜……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔～ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](这种居高临下的感觉好像也不错……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯～～～"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](桐人君应该永远不可能为我做这样的事情……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯~ 嗯嗯~ 哈啊…… \\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐…虽然一开始确实是说了我自己来，但是……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我还是好想学姐主动用丝足夹住我的肉棒摩擦，可以吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诶？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 女性呼吸声 350 120 0"]}, {"code": 245, "indent": 0, "parameters": [{"name": "1-Rubbing2", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜足交慢]"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊！不仅是气味、味道，触感也都棒极啦！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](猪田君的鸡…肉棒，好精神啊……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](只是脚而已就可以兴奋成这样吗？)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐的丝足~ 摩擦着我的鸡巴！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我真是天底下最幸福的人了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 一如既往的油嘴滑舌……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我是真的觉得很幸福！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿~ 学姐你是了解我的，我从来不会骗你哟~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哼……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐，你腿会不会酸？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还好，这个动作的强度比以前练习的那些舞蹈比差远了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 应该可以坚持到你…结束，不用担心。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐，你又把我想得很自私！我是在关心你呀！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 才没有担心我能不能射精的事！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那就专心点，早点射出来，我也就能早点休息了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 其实……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 如果可以的话，我想多享受一会儿，嘿嘿~ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嚤~ "]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 十分钟后……"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 喔喔，爽死我了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](看着猪田君这副陶醉的样子，感觉……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 稍微有点开心……)"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我在胡思乱想什么？！都怪猪田君刚才又闻又舔的离谱行为，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 搞得我的大脑又变得不太正常了……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](要快点结束掉才行！)"]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 女性急促呼吸声 45 120 0"]}, {"code": 245, "indent": 0, "parameters": [{"name": "1-Rubbing3", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜足交快]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦哦！！！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐，怎么突然加速了？！！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 时间也差不多了，赶紧结束。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…嗯嗯……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](之前骚母狗给我口交的时候提过一嘴的事情，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没想到她居然记下来了！应该说真不愧是高材生吗？我操！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 快点…射出来吧……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](不行了不行了！这骚母猪的黑丝美足太极品了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 耐久蹭蹭掉啊！早知道刚才就不打飞机了！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我感觉……就快要射了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊！我要射了！我要射出好多好多精液！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我要让学姐用精液泡脚！我要让学姐的丝足怀孕了！"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[亚丝娜足交射]"]}, {"code": 230, "indent": 0, "parameters": [192]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [208]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 40, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 121, "indent": 0, "parameters": [901, 901, 1]}, {"code": 121, "indent": 0, "parameters": [300, 300, 1]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 0]}, {"code": 201, "indent": 0, "parameters": [0, 81, 26, 10, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 16, "y": 8}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 662}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [23]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 33, "indent": null}, {"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}, {"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [5, 0, 15, 12, 8]}, {"code": 203, "indent": 0, "parameters": [6, 0, 11, 15, 4]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,-30] : 时间[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 设置声音 : 默认声音"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dDMS[慢]\\dac 两个小时后……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dDMS[慢]\\dac 大阪-酒店房间"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 默认模式"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 关闭声音"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 213, "indent": 0, "parameters": [6, 7, true]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐，还没有好吗？我肚子好饿呀！"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 34, "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 19, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 4]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈回应", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 再等一下就好。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 7, true]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,0] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 1], "indent": null}, {"code": 3, "indent": 0}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐不饿吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你起那么早，怎么没提前把吃的弄好呀？"]}, {"code": 213, "indent": 0, "parameters": [5, 6, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈叹气", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我都已经吃过啦！还不是你总是睡懒觉，都不知道几点醒。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 昨天，前天，我提前弄的，后面不都冷了？"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈不悦", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以我今天等你起来再做，让你能吃上热乎的~"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [6, 4, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐对我真好……"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[30,0] : 时间[20]"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [5, 5, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 34, "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [6, 12, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,0] : 时间[10]"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈严肃", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不许动手动脚！不然……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我会生气的！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咿！！！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是……"]}, {"code": 213, "indent": 0, "parameters": [5, 8, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 19, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [6, 7, true]}, {"code": 213, "indent": 0, "parameters": [6, 9, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊！那个……"]}, {"code": 213, "indent": 0, "parameters": [5, 2, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈叹气", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 又怎么啦？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学姐，我突然想起来，昨天晚上部长有个工作交给我处理，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 需要借用一下你的电脑，可以吗？"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈通用", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可以，你去用吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 密码是我的生日。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 谢谢！谢谢！"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 249, "indent": 0, "parameters": [{"name": "LNSM_ME22_Mystery1", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 663]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [24]}, {"code": 201, "indent": 0, "parameters": [0, 334, 14, 25, 8, 0]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 663}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 7}, {"id": 9, "name": "EV009摄像头伪装花瓶", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 628}, "directionFix": false, "image": {"tileId": 519, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[15,0]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 0}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create Glitch"]}, {"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 10]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 5, 2]}, {"code": 356, "indent": 1, "parameters": ["HorrorEffects Screen Change Glitch Offset 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 77, 0, 8, 2]}, {"code": 356, "indent": 2, "parameters": ["HorrorEffects Screen Change Glitch Offset 3"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 77, 0, 10, 2]}, {"code": 356, "indent": 3, "parameters": ["HorrorEffects Screen Change Glitch Offset 5"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 10]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 7, 2]}, {"code": 356, "indent": 1, "parameters": ["HorrorEffects Screen Change Glitch Frequency 60"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 77, 0, 9, 2]}, {"code": 356, "indent": 2, "parameters": ["HorrorEffects Screen Change Glitch Frequency 45"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 77, 0, 10, 2]}, {"code": 356, "indent": 3, "parameters": ["HorrorEffects Screen Change Glitch Frequency 30"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var waitFrames = Math.floor(Math.random() * 45) + 45;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Remove Glitch"]}, {"code": 355, "indent": 0, "parameters": ["var waitFrames = Math.floor(Math.random() * 60) + 120;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 18, "y": 6}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 699}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 执行开启"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层透明度 : 200"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层过渡时间 : 1"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层颜色 : #000000"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 物体照明 : 事件[9] : 照明[34]"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 25"]}, {"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 203, "indent": 0, "parameters": [6, 0, 14, 14, 2]}, {"code": 203, "indent": 0, "parameters": [5, 0, 17, 14, 2]}, {"code": 203, "indent": 0, "parameters": [9, 0, 20, 15, 0]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [35]}, {"code": 213, "indent": 0, "parameters": [5, 3, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 45, "parameters": ["$gameMap.event(5).turnTowardCharacter($gameMap.event(6));"], "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(5).turnTowardCharacter($gameMap.event(6));"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 大懒猪终于起床啦？想吃什么，我给你做……"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [6, 5, 6]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [5, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田君之前有在白天洗过澡的吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 急匆匆的，跟他说话也不回应，感觉有点奇怪……"]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 算了，等他洗完澡再问他吧。"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 117, "indent": 0, "parameters": [405]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 297, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 699}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 35, "pitch": 100, "pan": 40}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 700}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 执行开启"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层透明度 : 200"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层过渡时间 : 1"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层颜色 : #000000"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 物体照明 : 事件[9] : 照明[34]"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 25"]}, {"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 203, "indent": 0, "parameters": [6, 0, 21, 11, 2]}, {"code": 203, "indent": 0, "parameters": [5, 0, 22, 17, 8]}, {"code": 203, "indent": 0, "parameters": [12, 0, 22, 16, 0]}, {"code": 203, "indent": 0, "parameters": [13, 0, 23, 16, 0]}, {"code": 203, "indent": 0, "parameters": [9, 0, 20, 15, 0]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田君，你还好吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你已经把自己关在里面2个多小时了。先出来吃点东西吧。"]}, {"code": 213, "indent": 0, "parameters": [6, 5, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要管我！！！我现在什么也不想吃！"]}, {"code": 213, "indent": 0, "parameters": [5, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好吧，那饭菜放在门口，我先去工作了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 如果有什么需要，随时跟我打电话。"]}, {"code": 213, "indent": 0, "parameters": [6, 8, true]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [405]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 702}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 执行开启"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层透明度 : 200"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层过渡时间 : 1"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层颜色 : #000000"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 物体照明 : 事件[9] : 照明[34]"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 25"]}, {"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 203, "indent": 0, "parameters": [6, 0, 21, 11, 2]}, {"code": 203, "indent": 0, "parameters": [5, 0, 22, 17, 8]}, {"code": 203, "indent": 0, "parameters": [9, 0, 20, 15, 0]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 饭拿进去吃了。但还是不肯出来。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 也不知道昨天晚上发生了什么。我下线以后他就已经睡了。"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 再等等吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 如果晚餐时间他还不出来……"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [405]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 709}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 执行开启"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层透明度 : 200"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层过渡时间 : 1"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层颜色 : #000000"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 物体照明 : 事件[9] : 照明[34]"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 25"]}, {"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 203, "indent": 0, "parameters": [6, 0, 21, 11, 2]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 41, "parameters": ["猪头裸", 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["猪头裸", 2], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [5, 0, 22, 17, 8]}, {"code": 203, "indent": 0, "parameters": [9, 0, 20, 15, 0]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田君，你别把自己关在里面里，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 来跟我说说，发生了什么？"]}, {"code": 213, "indent": 0, "parameters": [6, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我...我不想出去！我没办法再见人了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不出来也可以，你可以先告诉我是什么问题，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说不定我可以给出好的建议呢。"]}, {"code": 213, "indent": 0, "parameters": [6, 12, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没用的！我完了，我废了！一切都毁了！"]}, {"code": 213, "indent": 0, "parameters": [5, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田君，每个人都会遇到困难和挫折。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但我们应该勇敢面对，而不是逃避。"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 再说了，事情也许远没有你想的那么严重也说不定呢。"]}, {"code": 213, "indent": 0, "parameters": [6, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真的吗？"]}, {"code": 213, "indent": 0, "parameters": [5, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 当然。"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 37, "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [6, 8, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [6, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 先答应我，不管我变成什么样你都不会嫌弃我，可以吗？"]}, {"code": 117, "indent": 0, "parameters": [123]}, {"code": 117, "indent": 0, "parameters": [131]}, {"code": 213, "indent": 0, "parameters": [5, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嫌弃……？"]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 121, "indent": 0, "parameters": [294, 294, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好，我答应你…我永远不会嫌弃你。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不管你遇到了什么困难，我都会帮助你。全心全意，竭尽全力。"]}, {"code": 213, "indent": 0, "parameters": [6, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在，愿意出来和我谈谈吗？"]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我阳痿啦！就算用你的内裤自慰都没办法硬起来啊！"]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 213, "indent": 0, "parameters": [5, 12, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诶？诶？！！"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\{求求你不要嫌弃我！！！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 秘书:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐谷君？"]}, {"code": 121, "indent": 0, "parameters": [433, 433, 0]}, {"code": 121, "indent": 0, "parameters": [298, 298, 0]}, {"code": 232, "indent": 0, "parameters": [98, 0, 0, 0, 0, 0, 100, 100, 0, 0, 10, false]}, {"code": 117, "indent": 0, "parameters": [405]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 710}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [99, "黑幕-监控-厕所", 0, 0, 0, 0, 100, 100, 250, 0]}, {"code": 231, "indent": 0, "parameters": [90, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 设置动画序列 : 动画序列[30]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[708监控抚摸安慰]"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 25"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 等待动画序列加载完成"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "手コキ（低速）", "volume": 25, "pitch": 50, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 感觉好一些了吗……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不行啊，根本没有硬起来的迹象！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要着急。先让心情平静一些。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜呜呜，好温柔！天使！圣母！女神！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 和那个母夜叉完全不同！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 噗哧，母夜叉……不是在说我的好闺蜜吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 话说，你是不是得罪她了，怎么会弄成这样？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还不是她企图让我去当她的舔狗，可我是你的忠犬嘛，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以不仅当即拒绝还嘲讽了几句，然后就被她狠狠修理了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嚤~ 什么忠犬啊！又说这种怪话……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过，你刚才说的舔狗，是怎么回事？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 大概意思就是给我一个追她的机会，但必须先断掉和你的往来。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊？怎么会……"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}但她不是喜欢那个人吗……"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}不过，她最近也确实和你走得挺近的……"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}难怪她会因为被拒绝而那么生气……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 抱歉刚才说了什么吗？我都没听清。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过好像在说喜欢什么的，你不会是以为母夜叉喜欢我吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 虽然我也觉得意外，但她对你说的话，让你去追她，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 以女生的角度看，几乎等同向你告白了呀。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗨！这算什么告白啊？告白是直接确立恋爱关系！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而所谓给我追的机会，不过是当她的跑腿奴隶而已。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这样吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 当然啦！像我这种人，哪还有别的女生会高看一眼啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 话说回来，我觉得她也不喜欢你男友，倒是可能对你有扭曲的爱。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我..我？！什么啊！又胡言乱语！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 才没有胡说好吧！我其实早就发现了，她对你身边的男生都态度异常。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 条件好的就撩拨一下，条件差的就威胁恐吓。我不愿意，她就打击报复。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说得好像我身边男生很多似的……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 数量不是重点！重点是和你有关系的男生她才有兴趣。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还不能说明问题嘛？"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac ……………………"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别说这个话题了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你现在，觉得怎么样了？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 欸，好像开始有点感觉了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 如果能多给一点刺激的话，说不定真能恢复的。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 多一点刺激……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 比如舔一下？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 想什么呢！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这又不是在那边，谁要舔这个……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那这样吧……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 正好这里放着准备洗的新衣服，临时穿一下可以吗？"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac ……………………"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 行吧……"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [91, "708-监控-浴室门口2", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那个…你想我穿什么？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那套内衣，可以吗……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔…可以吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还以为你会选兔女郎……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那更好啊！我担心你不同意呢！"]}, {"code": 225, "indent": 0, "parameters": [9, 9, 10, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要得寸进尺！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咿！！！好的好的！"]}, {"code": 231, "indent": 0, "parameters": [92, "708-监控-浴室门口2-1", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [92, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么样？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 有感觉！很有感觉！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可以扭一下腰吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诶？"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唉……"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 0, 0, 1, true]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, true]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放动作 : 动作元[708色情舞蹈勃起]"]}, {"code": 232, "indent": 0, "parameters": [92, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 230, "indent": 0, "parameters": [280]}, {"code": 235, "indent": 0, "parameters": [90]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我恢复啦！！！\\|"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦哦哦！！！太感谢了！！！\\|"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [6, 0, 8]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 恢复了就好，赶紧把衣服穿起来吧。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那个，可不可以做完刚才没做完的事情呀？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我现在感觉涨得厉害，实在是憋的难受……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 求求了！！！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别站在这里了，去沙发那边吧。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦耶，太好了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你先去，我收拾一下马上就来！"]}, {"code": 117, "indent": 0, "parameters": [405]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 709}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [260]}, {"code": 231, "indent": 0, "parameters": [91, "708-监控-浴室门口3", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 255, 0, 24, true]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 711}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 7}, {"id": 12, "name": "EV012饭团", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!food3", "direction": 6, "pattern": 2, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-2,-3]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 0}, {"id": 13, "name": "EV013玻璃杯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!food3", "direction": 8, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-24,9]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 0}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 922, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [10, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 设置动画序列 : 动画序列[30]"]}, {"code": 203, "indent": 0, "parameters": [5, 0, 11, 15, 4]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[0,-30] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [6, 0, 8, 15, 6]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,-30] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [16, 0, 9, 14, 0]}, {"code": 203, "indent": 0, "parameters": [17, 0, 10, 15, 0]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [18, 0, 9, 15, 0]}, {"code": 205, "indent": 0, "parameters": [18, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 3], "indent": null}, {"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 2], "indent": null}, {"code": 17, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 等待动画序列加载完成"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [6, 4, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [18, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是不是觉得放松些了？味道也不错吧？"]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 104]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈回应", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……这酒还挺香的，也不太苦。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我就说吧，事前喝点红酒是很好的缓解紧张的方式。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 等会儿就不会尴尬了~"]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈心事", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac ……有个事，想跟你谈谈。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 什么事？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你不会是要反悔吧？！"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,0] : 时间[10]"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你不愿意和我肛交了？！"]}, {"code": 213, "indent": 0, "parameters": [5, 7, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 3], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈凛然脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 昨天答应的事情我会做到的！"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 2], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [420, 420, 0]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈掩饰", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我会和你……肛交……"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 3], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈叹气脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我只是想问你……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 以后FOG里的关系，你是怎么想的？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 121, "indent": 0, "parameters": [420, 420, 1]}, {"code": 213, "indent": 0, "parameters": [6, 1, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 2], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊？FOG里？"]}, {"code": 213, "indent": 0, "parameters": [6, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个……我还没怎么想过。"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[0,0] : 时间[10]"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 1], "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈严肃", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可你之前不是说，隔离结束以后，都不再见我了？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我是说现实里……但线上这个，嗯……"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 之前怎么样以后就怎么样呗……"]}, {"code": 213, "indent": 0, "parameters": [5, 6, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈逃避", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 果然……"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 121, "indent": 0, "parameters": [420, 420, 0]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈失落", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但我觉得，这次断了就该彻底一点。不光现实，游戏里也不要再见面了。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 121, "indent": 0, "parameters": [420, 420, 1]}, {"code": 213, "indent": 0, "parameters": [6, 12, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诶？诶诶诶！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别啊！游戏里没必要吧？"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你想想，游戏里的性爱能帮你缓解压力，而且……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 除了我，谁还能让你满足啊？"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈沉思", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……还有吗？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还有！FOG里我对你那么了解，你习惯的节奏、喜欢的玩法，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 都是我陪你弄出来的。换别人哪行啊？"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 3], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈沉默脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这就是全部了吗？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……差不多吧。"]}, {"code": 213, "indent": 0, "parameters": [5, 11, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 其实，因为仪式的buff还有FOG的性爱辅助系统，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人他……已经能让我满足了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 1, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": 0}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 什么？！桐人？！那家伙……？！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真的假的？！不可能吧！我觉得他肯定没我……"]}, {"code": 213, "indent": 0, "parameters": [5, 5, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 2], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田君，别说了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要破坏现在我们之间的气氛，好吗？"]}, {"code": 121, "indent": 0, "parameters": [420, 420, 0]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈心事脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们就好好享受当下吧，这最后相处的时间。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 121, "indent": 0, "parameters": [420, 420, 1]}, {"code": 213, "indent": 0, "parameters": [6, 12, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可是……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我还是觉得……"]}, {"code": 213, "indent": 0, "parameters": [5, 7, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 3], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈凛然脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 够了，别让我后悔昨晚的决定。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [6, 8, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好吧……好吧，我不说了。"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 19, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 再喝一杯吧，然后我们去卧室。"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 2], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[24,0] : 时间[15]"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": [">多帧行走图 : 事件[5] : 固定帧 : 3"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,0] : 时间[15]"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">多帧行走图 : 事件[5] : 解除固定帧"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 2, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 19, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [18, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [6, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 来，干杯~"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈失落脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……干杯。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[12,0] : 时间[15]"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">多帧行走图 : 事件[6] : 固定帧 : 1"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[-12,0] : 时间[15]"]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": [">多帧行走图 : 事件[5] : 固定帧 : 3"]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 250, "indent": 0, "parameters": [{"name": "葡萄酒杯干杯声音效", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [5]}, {"code": 235, "indent": 0, "parameters": [6]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 1]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "bob_cloth2-2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 201, "indent": 0, "parameters": [0, 365, 16, 12, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 922, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [10, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 0, "parameters": [0, 19, 1]}, {"code": 234, "indent": 1, "parameters": [10, [-51, -51, -51, 0], 1, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 设置动画序列 : 动画序列[30]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[712酒店肛交前戏待机]"]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "女性呼吸声", "volume": 40, "pitch": 110, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 感觉如何？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 肛塞插了一整天，应该已经适应了吧？  "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……还、还好……有点胀，但不难受。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我真的能做到吗？)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那我开始拔了，放松点，别夹紧。 "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……好、好的……慢、慢点……  "]}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "あえぎ6", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "肛塞拔出音", "volume": 25, "pitch": 80, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[712酒店肛交前戏指入]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-030喘ぎ小", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看，挺顺利的吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呵呵呵~ 你的小穴都湿了，是不是期待了？ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没、没有……只是、只是有点热……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这是汗来着……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](这感觉好奇怪，但好像也没那么可怕。)  "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别嘴硬了，括约肌这么软，说明你早就准备好了。  "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哎！先等等！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 让我…再平复一下心情……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还是有些害怕？不要紧张嘛！和FOG里不会有太大区别的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……啊……有点、有点奇怪……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](他的手指滑动的时候，触感有点麻。)  "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 奇怪？哪里奇怪？你在找我的茬吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我手指不比肛塞细多了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不是…哈啊……不是那种不好的…奇怪……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那一开始就把话说清楚嘛！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 接下来可要好好告诉我每一处的感觉。  "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……直、直肠里有点被撑开的感觉……但、但不疼……啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还有、还有点麻……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 麻？那是好兆头，说明你身体在回应我。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 隔壁阴道有没有感觉？  "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 有……啊……隐、隐约有快感……隔着肠壁，感觉有点、有点湿了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210]((这真的很羞耻，但也真的挺舒服的……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 很好很好，继续说！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 差不多…就是这样了……哈啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 再说…啊…就重复了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 也是。手指确实比较单调。不像直接肏的感觉丰富。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那就回忆一下，在FOG里，我肛交你的时候，你最喜欢的是什么？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这……我想想……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不着急，慢慢想~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……嗯……"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我、我想起来了……你、你的…龟头每次最深的时候…哦……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 会、会顶到我的子宫……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 顶到子宫？哈哈，感觉怎么样？是不是全身都软了？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是、是的……啊……全身酥麻……很、很舒服……还有、还有你撞击我屁股的时候……嗯……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那、那种震动也让我觉得好、好棒……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 屁股震动也舒服？哈哈哈哈，看来你天生就是被肛的料，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们应该早点开发你的后门的。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别说这种话……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这也不能说？你的下面都放松了，精神还绷着，这不好！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊！我知道一个好方法帮你彻底放松！"]}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 0, 0, 10, true]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 250, "indent": 0, "parameters": [{"name": "bob_cloth1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "AY-117嗯呃呃", "volume": 45, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[712酒店肛交前戏舔]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-031喘ぎ小", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田…脏啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 吸溜吸溜~ 我的嘴巴可不脏！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不仅刷牙漱口过，刚才喝酒更算是杀毒！干净得很！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不是…！我是说……哦…啊啊……我的那里……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要舔了……哦……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾噜~ 啾噜~ 才不脏，就要舔就要舔！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 随便你…嗯…只要你不会嫌脏……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过我也已经洗过很多次了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 滋溜滋溜~ 怎么样，我的舌头舒服吗？放松吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你、你的舌头很、很软……嗯……让我、让我觉得很、很放松……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾啾~ 吸溜~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 放松是关键~ 啾噜~ 别的都别想，好好享受吧~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好、好的……啊……我、我会放松的……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咕啾…咕啾…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……嗯…啊…感觉全身、浑身都、都软了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](他的舌头真的很厉害，我有点喘不过气。)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 吸溜~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 喜欢吗？我舔到你深处的时候？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……啊……是、是的……你、你的舌头舔到…深处的时候……哦……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我、我觉得真的、真的很、很舒服……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哧溜~ 啾噜噜…啾啾……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还有酥麻的感觉吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是、是的……啊……全身、浑身酥麻……嗯……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 和、和FOG里肛交时一样……哦……甚至、甚至更、更好……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咕啾咕啾~ 咕啾咕啾~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看来已经完全放松了！想不想我插进来？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…是、是的……我、我想…哦…放松了……嗯……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你、你可以、可以试试……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我真的准备好了吗？这感觉让我有点迷糊。)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾啾~ 好！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 再舔一会儿……吸溜……就正式开始吧~"]}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 2]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [11]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 201, "indent": 0, "parameters": [0, 365, 16, 12, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 922, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [10, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 0, "parameters": [0, 19, 1]}, {"code": 234, "indent": 1, "parameters": [10, [-51, -51, -51, 0], 1, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 设置动画序列 : 动画序列[30]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 等待动画序列加载完成"]}, {"code": 250, "indent": 0, "parameters": [{"name": "bob_cloth2-2", "volume": 90, "pitch": 70, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[712酒店肛交慢一]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [48], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston10", "volume": 50, "pitch": 100, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [48], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston10", "volume": 50, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 241, "indent": 0, "parameters": [{"name": "bensound-sexy", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_11_喘ぎ声（小）2_01", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……亚丝娜，这里真的好紧……比游戏里还要厉害！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 感觉太棒了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊……猪田……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…嗯……好胀……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦……放松点……我们已经在游戏里试过的，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不会有问题的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……嗯……游戏里是试过……但现实不一样……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好、好羞人……哈啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜害羞的样子也好可爱~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我会慢慢来的点，不要担心……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……好……慢一点……我、我怕……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……有点怪怪的……啊啊~\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](不仅是肉棒剐蹭着肠壁的刺激快感，猪田每一次撞击我的屁股，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 全身上下都会一点点地渗出酥软、甜美、舒服的幸福感……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怪怪的？但你声音听起来很舒服啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦……真紧……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别、别说这个……嗯……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我……我也不知道……哈啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿……亚丝娜，你的表情真美！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我保持现在这个节奏，没有问题吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…嗯……可、可以……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 如果要提速，要提前告诉……我……嗯~~~\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……这感觉太他妈的爽了……"]}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 二十分钟后……"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[712酒店肛交慢二]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_11_喘ぎ声（小）2_01", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊……猪田……别盯着我看……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我、我不好意思……啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不看怎么行？你现在的表情太诱人了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……别说啦……我……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……感觉……好奇怪……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 奇怪？是不是舒服的那种奇怪？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦……你夹得我好紧……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……有点……舒服……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别让我说…这么羞人的话…哈啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈，亚丝娜，你害羞的样子简直了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊……猪田……好深……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯~~~\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那是我太兴奋了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你也喜欢吧？嗯……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……喜欢……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不、不对……我、我不知道啦……嗯……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不知道也没事，反正你声音出卖你了……哦……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别、别笑我……哈啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊~~~\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，最后一次你这么配合，我真的很感动……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……最后一次……嗯……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别说这个……我、我会难过……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好，不说！我会专心肏你的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦~ 啊！好爽！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊……猪田……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我要…嗯…受不了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 受不了才好啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈……啊！最好不要突然这么夹，我可能会射的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我、我控制不住……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好舒服…不行…嗯…啊啊…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 舒服就对了！怎么是“不行”呢？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别忍着，放开点！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……放开…我……啊~~~\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我有点害怕…啊啊…自己会陷进去……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怕什么？放开才会更舒服啊？做都已经做了，不要顾虑那么多！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，放纵一点，让我看看你有多淫乱……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我、我试试……啊……好深……嗯~\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 用力插我…啊啊~~~\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呼……对，就是这样！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你天生就适合这样！来，继续！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…肏我…啊不对……猪田…你把我…啊啊啊~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我、我脑子都弄乱了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别担心，这是正常的，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 脑子一乱，就不会害羞，专注身体上的舒服就好，放开叫出来！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……叫出来……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……猪田……我……啊……好舒服……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊……亚丝娜你的浪叫太棒了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 讨厌…啊啊……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…啊啊……哦……嗯……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我想加快一些，可以吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……快点？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好吧……但你……别、别太用力……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 放心，我会小心的……"]}, {"code": 231, "indent": 0, "parameters": [11, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 0, "parameters": [0, 19, 1]}, {"code": 234, "indent": 1, "parameters": [11, [-51, -51, -51, 0], 1, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[11] : 设置动画序列 : 动画序列[30]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[11] : 播放简单状态元集合 : 集合[712酒店肛交快]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [24], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston10", "volume": 50, "pitch": 80, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [24], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston10", "volume": 50, "pitch": 80, "pan": 0}], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_14_喘ぎ声（中）1_01", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦……你的菊穴真的太棒了……"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……猪田……好舒服……\\i[90]\\i[90]\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是不是比游戏里还舒服？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……比游戏……啊……嗯嗯……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我真的要…啊啊…失控了……啊啊啊~\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那就失控！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，别想太多，享受被我肏的感觉就好！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊……享受……猪田……我……啊……真的好舒服……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我……我真的要变得奇怪了……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，这是我们最后一起的时间了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 只要舒服，变得奇怪也没什么啦！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……最后的……嗯……好……猪田……我……啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 既然这样，亚丝娜，我有个想法，咱们聊聊怎么样？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯？聊、聊什么……啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田……我……嗯……听不清了……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我说啊，隔离还有几天……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 最后的日子，我们就真的当炮友吧，随时随地都可以做的那种！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……炮友？嗯……猪田……你、你在说什么……啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 听我说完……嗯……我保证，隔离一结束，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我就彻底消失，现实里、游戏里，都不会再烦你！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊……但、但是……啊……炮友……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咱们现在不也在做着嘛？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 之前暴雨夜也做过，最后几天当炮友，又没什么差别！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…！我……我不知道……猪田……啊~\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你、你别…嗯…插这么用力…我…没办法…思考…啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，咱们就这样疯几天，每天都像现在这么爽！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 之后我就走人，干干净净！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…啊……猪田…我……嗯…好舒服……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但、但是…我……啊~~~\\i[90]\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别想太多了，一起舒服几天不好嘛？！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……猪田……你、你慢点……让我…啊…想想……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 反正已经这样了，多几天有什么区别？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…区别…嗯……我…啊……好舒服……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你说的…好像…嗯…有道理……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呼……对吧？哈哈，你也觉得爽……那就答应我，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 隔离这几天，咱们当炮友，一方生理上有要求另一方必须完全配合。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…炮友…不…猪田…我…我…啊……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你、你肏得我……嗯…好舒服……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，别再拒绝了！你看你多用力地夹我？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我知道你想要！看着你现在的样子！说出来！答应我！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊……想要……啊……猪田……我……嗯……好舒服……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我……我……啊……好…我当你的炮友！啊啊~\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈，太好了！那一言为定！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 隔离期间你是我的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……只是隔离期间……啊……猪田……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 在这个酒店房间…嗯……我是你的！啊啊啊~\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦……你这下夹得我更紧了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 等肛交结束，直接让我肏小穴吧！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…猪田…我……啊…我不管了…！都听你的……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊……好舒服……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈，好！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，咱们这几天一定要疯到底！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……疯到底……嗯……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田……我…啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 射了…！"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 设置动画序列 : 动画序列[30]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放动作 : 动作元[712酒店肛交中出]"]}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [16], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston10", "volume": 50, "pitch": 80, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [16], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston10", "volume": 50, "pitch": 80, "pan": 0}], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [140]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精音・中出し（長い）①", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 108, "indent": 0, "parameters": ["344帧"]}, {"code": 230, "indent": 0, "parameters": [272]}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 3]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 235, "indent": 1, "parameters": [10]}, {"code": 235, "indent": 1, "parameters": [11]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 201, "indent": 1, "parameters": [0, 365, 16, 12, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [922, 922, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 36, "indent": null}, {"code": 34, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 81, 9, 6, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 922, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 3}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 231, "indent": 0, "parameters": [10, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 设置动画序列 : 动画序列[30]"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 设置声音 : 默认声音"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 暂存当前的模式"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[7]"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 模式-中"]}, {"code": 241, "indent": 0, "parameters": [{"name": "<PERSON> Take Care Instrumental", "volume": 20, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [21, "712-中场-01-肛交事后", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [22, "712-中场-02-事后聊天", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [23, "712-中场-03-接吻", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [24, "712-中场-04-推开", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [25, "712-中场-05-强吻", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [26, "712-中场-06-舔阴", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [27, "712-中场-07-插入预备", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [28, "712-中场-08-插入进行", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [29, "712-中场-09-插入完成", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 0, "parameters": [0, 19, 1]}, {"code": 234, "indent": 1, "parameters": [10, [-51, -51, -51, 0], 1, false]}, {"code": 234, "indent": 1, "parameters": [21, [-51, -51, -51, 0], 1, false]}, {"code": 234, "indent": 1, "parameters": [22, [-51, -51, -51, 0], 1, false]}, {"code": 234, "indent": 1, "parameters": [23, [-51, -51, -51, 0], 1, false]}, {"code": 234, "indent": 1, "parameters": [24, [-51, -51, -51, 0], 1, false]}, {"code": 234, "indent": 1, "parameters": [25, [-51, -51, -51, 0], 1, false]}, {"code": 234, "indent": 1, "parameters": [26, [-51, -51, -51, 0], 1, false]}, {"code": 234, "indent": 1, "parameters": [27, [-51, -51, -51, 0], 1, false]}, {"code": 234, "indent": 1, "parameters": [28, [-51, -51, -51, 0], 1, false]}, {"code": 234, "indent": 1, "parameters": [29, [-51, -51, -51, 0], 1, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 等待动画序列加载完成"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 0, 0, 100, 100, 255, 0, 180, false]}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[6]\\{\\{咻……刚才真的是爽死了！\\}\\}\\."]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{\\{就算是我，也要缓一缓才能进行第二轮了。\\|\\|\\|\\^"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改速度 : 指定间隔[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 设置字数跳跃 : 字数[2]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [21, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [22, 0, 0, 0, 0, 0, 100, 100, 255, 0, 90, false]}, {"code": 230, "indent": 0, "parameters": [75]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[1]\\{刚才那个…\\.肛…肛交时说的……\\.是我一时糊涂……\\|\\|\\^"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 设置字数跳跃 : 字数[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 模式-快"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[6]\\{\\{嘛嘛！先不说炮友的事！\\}\\}\\."]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{\\{我已经恢复好了！赶紧来做爱吧~ \\|\\|\\^"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 模式-慢"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[1]\\{这个也……\\|\\|\\^"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [22, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [23, 0, 0, 0, 0, 0, 100, 100, 255, 0, 90, false]}, {"code": 230, "indent": 0, "parameters": [75]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[8]"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 模式-中"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[1]\\{唔…\\.呣…\\.啾……\\|\\|\\^"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [23, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [24, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 模式-快"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[1]\\{\\{这样不行…！\\|\\|\\^"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[6]\\{\\{但你刚才不也吻得很动情嘛！\\|\\|\\^"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 模式-中"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[1]\\{我不能对不起他……\\|\\|\\^"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 模式-慢"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[6]\\{\\{不要这么想……\\.\\.其实都是我不好！都是我强迫你的。\\}\\}\\."]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{\\{你只是被我牵着走了，不需愧疚任何事情……\\|\\|\\^"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 232, "indent": 0, "parameters": [24, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "fst_snow_light_walk_001", "volume": 90, "pitch": 80, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[7]"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 模式-中"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[1]\\{\\{嗯……\\.啾…滋…啾啾……\\|\\|\\^"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 232, "indent": 0, "parameters": [25, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 0, 0, 100, 100, 255, 0, 90, false]}, {"code": 230, "indent": 0, "parameters": [75]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 模式-快"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[1]\\{\\{不行…不行…！\\.会去的…！\\|\\|\\^"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 模式-中"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[6]\\{\\{吸溜…吸溜……\\.\\}\\}"]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[6]\\{\\{说着不行，手却按着我的头哦~ 吸溜…吸溜……\\|\\|\\^"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[1]\\{\\{啊…啊啊……\\|\\.\\^"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [26, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 232, "indent": 0, "parameters": [27, 0, 0, 0, 0, 0, 100, 100, 255, 0, 90, false]}, {"code": 230, "indent": 0, "parameters": [75]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 模式-快"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[6]\\{\\{\\{大鸡巴要插小骚穴喽！\\.是可以的吧？\\}\\}\\}\\."]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{\\{我们可是炮友！你答应过要随时配合的！\\|\\|\\^"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 模式-慢"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[1]\\{哈啊……哈啊……\\|\\|\\^"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 模式-中"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[6]\\{\\{怎么不说话呀？害羞了？默许啦？\\}\\}\\."]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[6]\\{\\{要拒绝这可就是最后机会了啊！\\|\\|\\^"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 模式-快"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[6]\\{\\{\\{\\{我要忍不住啦！ \\|\\.\\^"]}, {"code": 232, "indent": 0, "parameters": [28, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [29, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 模式-中"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[6]\\{\\{\\{\\{哦哦！\\}\\}\\}\\}\\."]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[6]\\{\\{\\{鸡巴终于又回到这个极品骚穴了！\\.爽！\\.爽啊！\\|\\|\\^"]}, {"code": 232, "indent": 0, "parameters": [29, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 恢复暂存的模式"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 关闭声音"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [21]}, {"code": 235, "indent": 0, "parameters": [22]}, {"code": 235, "indent": 0, "parameters": [23]}, {"code": 235, "indent": 0, "parameters": [24]}, {"code": 235, "indent": 0, "parameters": [25]}, {"code": 235, "indent": 0, "parameters": [26]}, {"code": 235, "indent": 0, "parameters": [27]}, {"code": 235, "indent": 0, "parameters": [28]}, {"code": 235, "indent": 0, "parameters": [29]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 4]}, {"code": 201, "indent": 0, "parameters": [0, 365, 16, 12, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 922, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [10, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [11, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [15, "712-后入-01-预备", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [16, "712-后入-02-射精", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [17, "712-后入-02-射精", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 0, "parameters": [0, 19, 1]}, {"code": 234, "indent": 1, "parameters": [10, [-51, -51, -51, 0], 1, false]}, {"code": 234, "indent": 1, "parameters": [11, [-51, -51, -51, 0], 1, false]}, {"code": 234, "indent": 1, "parameters": [15, [-51, -51, -51, 0], 1, false]}, {"code": 234, "indent": 1, "parameters": [16, [-51, -51, -51, 0], 1, false]}, {"code": 234, "indent": 1, "parameters": [17, [-51, -51, -51, 0], 1, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 设置动画序列 : 动画序列[30]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[11] : 设置动画序列 : 动画序列[30]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[712酒店本番俯视正常位慢二]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [24], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston02", "volume": 30, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [48], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 30, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [42], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [24], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston02", "volume": 30, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [48], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 30, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [42], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_10_喘ぎ声（小）1_01", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "Arctic Monkeys - Do I Wanna Know？ (Official Instrumental)", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊……嗯……\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我在…做什么…啊啊……\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 241, "indent": 0, "parameters": [{"name": "Arctic Monkeys - Do I Wanna Know？ (Official Instrumental)", "volume": 45, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_14_喘ぎ声（中）1_01", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[712酒店本番正常位慢一]"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [24], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston02", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [48], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [42], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [24], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston02", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [48], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [42], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田…！你插进来了？！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](这、这是怎么回事？我明明不想这样的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但为什么身体这么热？他的动作让我头晕目眩，我该怎么办……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 都插半天了。我当时可是征求过你意见的，你完全没反对呀！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦哦，你这极品小穴，湿得一塌糊涂，吸得我好紧~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……不、不行……嗯……我刚才……失神了……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这、这样……我不能……哈啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我记得他要插的时候自己确实没能拒绝……但那不是同意啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我不能让这件事继续……可是，我也感觉好刺激……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不能啥？都插了两分钟了……嗯……你还夹得这么用力，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘴上说不行，身体可老实得很啊！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊……不是……我……嗯……刚才答应的事……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我为什么要听他的？我明明知道这是错的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但他的声音、他的动作，都有种让我无法拒绝的魔力。)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…那是…嗯…我迷糊的时候…啊…不算数…！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我、我们不能……嗯……这样……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我在害怕什么？是在怕自己真的喜欢上这种感觉吗？)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿，不算数？现在说这个晚了吧！"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[11] : 播放简单状态元集合 : 集合[712酒店本番正常位快一]"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [36], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston01", "volume": 45, "pitch": 100, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [36], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston01", "volume": 45, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_16_喘ぎ声（大）1_01", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 255, 0, 10, true]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嚯啦嚯啦！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你看你，现在被我顶得多爽！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……别、别顶那么深……啊…！我……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](太深了……我快要崩溃了。这种感觉太强烈了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我不能再让他控制我……但为什么我还想让他继续？我是不是疯了？)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我不能……思考了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 思考啥？隔离还有几天，咱们爽完再说不行吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我保证，之后我彻底滚蛋，干干净净！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……嗯…但现在……我不行……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](他的话总能让我动摇……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](不！我真的不能再让他得逞了！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 停、停一下……让我……嗯……清醒点……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 停不了啊……你这小穴咬着我不放…哦~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，别挣扎了！对自己诚实点，才会更舒服！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……嗯……我……我不想……哈啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我不想？那是在说谎……每一次他的动作都让我失去理智。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我在抗拒什么？是在抗拒背叛桐人，还是在抗拒自己内心的欲望？)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田……你、你别……嗯……蛊惑我……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 蛊惑？哦……我这是让你享受啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你听听这水声，多下流……嗯……你早就想要了吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…别说这个……我……嗯……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](下流……是的，这很下流。但我听到这些话反而更兴奋了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我在堕落吗？还是说，这才是我真正想要的？我不能再听他的了……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我、我得坚持住……不能答应……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 坚持啥？都被我干得神魂颠倒了……哦……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，隔离这几天当我炮友，随时随地爽，之后我走人！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……我……我不想……嗯……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](炮友……多么粗鄙的词汇，可却让我有些心动："]}, {"code": 401, "indent": 0, "parameters": ["\\dac 隔离结束后他会走人，那我就可以回到正常生活……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](而且现在，我真的能拒绝吗？)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呵呵，意识都模糊了就不用想那么多了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说句痛快的，答应我，咱们一起疯到隔离结束！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…我…我怕……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这样下去……我会陷进去……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我真怕自己会彻底失去控制，怕自己会喜欢上这种感觉……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我不能再犹豫了，但他的每一次动作都在瓦解我的意志。)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 陷进去有啥不好？哦……爽就行了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，你这身子早就只认我了，换桐人那家伙能满足你吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……认你…嗯啊…不、不对……我……哈啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](不，那不可能……但为什么我现在只想到他的动作？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人…桐人…桐人……！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我不能对不起他……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦……又提桐人？可你现在被我顶得浪叫连连……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 隔离这几天当炮友而已，反正之后我离开，他不会知道的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…但我…哈啊…自己知道……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](是的，我知道……可我害怕自己会习惯这种感觉，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 害怕自己再也离不开他。我必须坚持……但我还能坚持多久？)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我、我不能…嗯…再错下去了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这叫享受人生……你看桐人那家伙不也去地下街找乐子嘛？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我……我要去了……哈啊……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac ……真的要去了…啊啊啊~~~\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](这种感觉太强烈了，我快要崩溃了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田，他真的想让我失去理智……让我还想更多……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦，要去了呀？"]}, {"code": 108, "indent": 0, "parameters": ["猪田放慢动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田放慢动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田放慢动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田放慢动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田放慢动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田放慢动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田放慢动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田放慢动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田放慢动作"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[712酒店本番俯视正常位慢一]"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 0, 0, 10, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [24], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston02", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [48], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [42], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [24], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston02", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [48], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [42], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[11] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_14_喘ぎ声（中）1_01", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…欸…？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](为什么慢下来？我明明已经快到了……他是在惩罚我吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还是在逼我低头？我不能再让他操控我……但我真的好想要……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么了嘛？亚丝娜。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 为什么……刚才都快要……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我不能再说了，这样就落入他的陷阱了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但我确实想他继续……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你不是不愿意嘛？那既然不是炮友我也没必要满足你的需求吧，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我自己舒服就好了，这个节奏也挺好。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜……"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[712酒店本番正常位慢二]"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [24], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston02", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [48], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [42], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [24], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston02", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [48], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [42], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](他在故意折磨我……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么了，为什么露出这种表情？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没…没什么…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](他对我的肉体了如指掌，他就是在利用我的欲望。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我必须坚持……但我真的快崩溃了……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不肯坦率吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 再给你点福利吧~"]}, {"code": 108, "indent": 0, "parameters": ["猪田再次恢复快动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田再次恢复快动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田再次恢复快动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田再次恢复快动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田再次恢复快动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田再次恢复快动作"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[11] : 播放简单状态元集合 : 集合[712酒店本番正常位快一]"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [36], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston01", "volume": 45, "pitch": 100, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [36], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston01", "volume": 45, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 255, 0, 10, true]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_16_喘ぎ声（大）1_01", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊……嗯……嗯啊~~那里……好深……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](这种感觉让我无法思考，我到底在坚持什么？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我是为了桐人，还是为了自己最后的底线？)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你看看，都已经这样了还在坚持什么呢？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯呜……哈啊…又，又要……啊啊啊~~~\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](不，我不能再忍了……他的每一次动作都在摧毁我。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我想要更多……只要我还爱着桐人……就算是隔离期间的炮友也……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好！福利时间结束！"]}, {"code": 108, "indent": 0, "parameters": ["猪田放慢动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田放慢动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田放慢动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田放慢动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田放慢动作"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[712酒店本番正常位慢一]"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 0, 0, 10, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [24], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston02", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [48], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [42], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [24], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston02", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [48], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [42], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_14_喘ぎ声（中）1_01", "volume": 60, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[11] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](又停？我已经受不了了……我需要他继续，我需要那种感觉。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我知道自己在堕落，但我也知道自己无法阻止了……！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯呜……哈啊……哈啊……不要停下来…！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿，想要我继续的话，那应该怎么做呢，需要我再教一遍嘛？"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[712酒店本番俯视正常位慢一]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我…我知道了……\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](他知道我无法拒绝，我已经没有退路了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 隔离结束后我可以重新开始……但现在……)"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[712酒店本番正常位慢一]"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [24], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston02", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [48], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [42], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [24], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston02", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [48], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [42], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好……好吧……只、只到隔离结束……\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说清楚呀，什么只到隔离结束，我听不明白。"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[11] : 播放简单状态元集合 : 集合[712酒店本番正常位慢二]"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [24], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston02", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [48], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [42], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [24], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston02", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [48], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [42], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 255, 0, 10, true]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 做…炮……炮友\\i[90] "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我说了……我真的说了。我背叛了桐人，但我无法抗拒这种感觉。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 隔离结束后我一定会悔过的……原谅我好吗？)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac ……到隔离结束前…人家…都是你的炮友……啊~~~\\i[90]\\i[90]"]}, {"code": 108, "indent": 0, "parameters": ["猪田再次恢复快动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田再次恢复快动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田再次恢复快动作"]}, {"code": 108, "indent": 0, "parameters": ["猪田再次恢复快动作"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[712酒店本番正常位快一]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [36], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston01", "volume": 50, "pitch": 100, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [36], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston01", "volume": 50, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 0, 0, 10, false]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_16_喘ぎ声（大）1_01", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[11] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦……这就对了！哈哈，太好了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 等等！这次不会做完以后就又赖账吧？"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊…不会！我、我不会…再反悔了…啊啊~\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](他的抽插让我失去了所有理智！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我只能接受！接受这个禁忌的快感！桐人，对不起……)"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 既然你是我的炮友，那必须随时随地满足我的需求！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好…哈啊…听你的…猪田…啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我彻底沦陷了……但我又觉得解脱了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我不再需要挣扎，我可以享受这种感觉……至少在这几天。)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac …听你的……所以……让我……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 耶！太好啦！从现在起，你就是我的骚炮友！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……啊~ \\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](骚炮友……这个词让我羞耻，但我无法否认，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我喜欢这种身份带来的刺激。我已经不是平时的我了……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 顶、顶深点……我……嗯……好想要……\\i[90]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[11] : 播放简单状态元集合 : 集合[712酒店本番正常位快二]"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [36], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston01", "volume": 50, "pitch": 100, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [36], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston01", "volume": 50, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_16_喘ぎ声（大）1_01", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 255, 0, 10, true]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好！"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…嗯…嗯嗯……\\i[90]\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac ……听听这咕啾咕啾的声音，多下流！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，告诉我，你有多想要我的鸡巴？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…想要…啊啊……我想一直要……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](好贱啊……我竟然说出这种话……承认了我想要更多，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我想要他完全占有我。这种感觉让我害怕，但我无法停止。)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 大肉棒…顶到我的…嗯啊…最里面……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈，最里面是吧？哦……我这就满足你……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你的骚穴真会讨好我，夹得我爽死了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…猪田…我…要去了……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我要去了……桐人从未让我这样……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦？又要去啦？放心，这次我不会中途停下，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 让你体验那家伙给不了你的真正高潮。"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[11] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[11] : 播放动作 : 动作元[712酒店本番正常位射精]"]}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 255, 0, 25, true]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 230, "indent": 0, "parameters": [95]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精音・中出し（長い）①", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[712酒店本番正常位事后]"]}, {"code": 230, "indent": 0, "parameters": [196]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_9_吐息2_01", "volume": 45, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你这骚货，天生就该被我干个够！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…嗯……我是骚货……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 继续干我……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈哈，没问题！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 翻过来！屁股对着我！"]}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 设置动画序列 : 动画序列[30]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[712酒店本番后入一]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [32], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 55, "pitch": 130, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [32], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 55, "pitch": 130, "pan": 0}], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_17_喘ぎ声（大）2_01", "volume": 75, "pitch": 110, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 再、再快点……我……嗯…啊啊啊…\\i[90]\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对，就这样抬高屁股，让我狠肏你！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…已经…忍不住了……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这样的…好舒服……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](好舒服啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 舒服得意识都要消散了……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这才刚开始而已哦。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 隔离这几天，我要让你天天爽到飞起！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……嗯……天天……哈啊……你……太坏了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](天天这样？我受得了吗……但这快感好强，我完全抗拒不了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 只是刚答应做炮友，就这样了，最后几天，我会堕落成什么样子……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 坏？这可是炮友的福利！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜你屁股抬得这么高，肏起来真爽！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还不是…哈啊……你要的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦……啊……好……嗯……深……啊啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](的确，是他让我抬高屁股的…但我也乖乖听话了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这太羞耻了，可我好舒服，意识都模糊了……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊！我要受不了了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这就受不了了？那可不行！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 等干完这次，得让我再射一发！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……嗯……哈啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊……随便你……嗯……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 随便我？哈哈，亚丝娜，你这炮友当得太称职了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦哦，感觉上来啦！反正后面还有一次，没必要忍着！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 再接我一发重炮轰入！"]}, {"code": 232, "indent": 0, "parameters": [15, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_8_吐息1_01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [25]}, {"code": 232, "indent": 0, "parameters": [17, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 0, "parameters": [16, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 80, "pitch": 90, "pan": 0}], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 80, "pitch": 90, "pan": 0}], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [17, 0, 0, 0, -144, -101, 120, 120, 0, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 250, "indent": 0, "parameters": [{"name": "いき9", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_8_吐息1_01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [15]}, {"code": 235, "indent": 0, "parameters": [17]}, {"code": 232, "indent": 0, "parameters": [16, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 5]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 201, "indent": 0, "parameters": [0, 365, 16, 12, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 922, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [10, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [11, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 0, "parameters": [0, 19, 1]}, {"code": 234, "indent": 1, "parameters": [10, [-51, -51, -51, 0], 1, false]}, {"code": 234, "indent": 1, "parameters": [11, [-51, -51, -51, 0], 1, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 设置动画序列 : 动画序列[30]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[11] : 设置动画序列 : 动画序列[30]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 等待动画序列加载完成"]}, {"code": 241, "indent": 0, "parameters": [{"name": "bensound-sexy", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[712酒店本番后入二慢]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [38], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 55, "pitch": 110, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [38], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 55, "pitch": 110, "pan": 0}], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_17_喘ぎ声（大）2_01", "volume": 35, "pitch": 110, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…你怎么才射完就又…啊啊…我刚高潮……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 太、太敏感了…慢一点…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我知道的啦！我这不是在慢慢肏吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我可是很温柔的~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊…嗯……但还是……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好刺激…我感觉…自己……好奇怪……啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 奇怪啥呀，就是发骚嘛！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这样难道不舒服吗？我如果停下来，你肯定又不干了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…很舒服…不要停……啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以说，女人啊！就是口是心非！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…别说这个了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…啊…啊啊……啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 行，不说这个，说别的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，你好美！我想要好好珍惜现在的每一秒！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田君…啊…这样……反而更让人害羞了…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这害羞什么？让我好好看着你的表情！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我想记住你享受时的每一个样子！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……啊……啊……你看吧……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你想怎么样都行……啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿~ 你说了多少次随便我了？刚做炮友就这么配合！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 以后每天都给我操，亚丝娜，你迟早是我的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……啊……只是隔离期间……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你答应我的！啊啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对……我顺着气氛说的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你不要扫兴嘛！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对不起……啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对不起有什么用？要接受惩罚才行！"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[712酒店本番后入二快]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [32], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 55, "pitch": 140, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [32], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 55, "pitch": 140, "pan": 0}], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_18_喘ぎ声（大）3_01", "volume": 35, "pitch": 110, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嚯啦嚯啦嚯啦！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦…啊……嗯……好快……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…不行…求你…啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呵……既然只是炮友关系……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那我也不用顾虑什么了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田君……别…这样…啊啊啊…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 刚才不是还说我想怎样都行吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在又说什么废话！骗子！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不、不是……啊……！\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 既然我们只是暂时的肉体关系……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那就拜托你这段时间好好当个称职的炮友！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜……太快了……嗯啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我……我会好好配合的……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这才像话！我们是炮友！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我操我操我操！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊……啊……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是…是的……肏我！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对啦！炮友就是要这样玩！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，说你喜欢被我操！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……嗯……我……哈啊……喜欢……被你……啊啊……操……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 接着说！我是谁？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…啊啊…是…猪田…啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田是你的谁？！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是…嗯啊…是…是我的…啊啊啊…\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 炮友……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没错没错！哈哈哈！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爽啊！我操！我操！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯嗯…啊啊…猪田君…那里…不行…太激烈了…哈啊…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你要是我老婆该多好啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我操！我操！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜…好舒服…再、再深一点…嗯啊…要坏掉了…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好想你给我生孩子！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我操！我操！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我……嗯……要去了……啊啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我操！亚丝娜，你这骚女人……！我也要……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔……呼啊……"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[11] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[11] : 播放动作 : 动作元[712酒店本番后入二射精]"]}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 230, "indent": 0, "parameters": [85]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精音・中出し（短い）①", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [80]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精音・中出し（短い）①", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [92]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精音・中出し（短い）①", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [75]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_9_吐息2_01", "volume": 25, "pitch": 110, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[712酒店本番后入二事后]"]}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 骚货……妈的，真带劲！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 吸得老子爽死了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊……哈啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜…嗯……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还想再来一发……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 算了，来日放长。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 搞肿了后面几天肏不成就不好了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我一定要用这几天的时间，让你的身体彻底离不开我！"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [11]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 0, "parameters": [922, 922, 1]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 40, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 81, 9, 6, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 20, "y": 8}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 742}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 执行开启"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层透明度 : 200"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层过渡时间 : 1"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层颜色 : #000000"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 物体照明 : 事件[9] : 照明[34]"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 25"]}, {"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 203, "indent": 0, "parameters": [6, 0, 15, 13, 8]}, {"code": 203, "indent": 0, "parameters": [5, 0, 15, 12, 8]}, {"code": 203, "indent": 0, "parameters": [9, 0, 15, 13, 0]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[0,-24] : 时间[1]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,-40] : 时间[1]"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [5, 11, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别闹…让我好好做饭。"]}, {"code": 213, "indent": 0, "parameters": [6, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我是用按摩来道歉嘛！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 昨天的事…对不起啊。"]}, {"code": 213, "indent": 0, "parameters": [5, 5, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[0,0] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 34, "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,0] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 34, "indent": null}, {"code": 14, "parameters": [-1, -1], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [-1, -1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [7]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还好意思提！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 让我在公开场合那样…你简直不可理喻！"]}, {"code": 213, "indent": 0, "parameters": [6, 5, true]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": 0}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还不是因为…明明说好了要试试新玩法，结果中途又反悔，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 害我憋得难受，我才一时没忍住的……"]}, {"code": 213, "indent": 0, "parameters": [5, 12, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 19, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这…这也不能怪我啊……"]}, {"code": 213, "indent": 0, "parameters": [6, 4, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[24,0] : 时间[30]"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我知道的啦，所以来道歉了嘛。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 最近工作还忙吗？"]}, {"code": 213, "indent": 0, "parameters": [5, 11, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…那个紧急项目总算结束了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这两天会轻松很多。"]}, {"code": 213, "indent": 0, "parameters": [6, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 太好了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对，对了，我最近听说了一部超棒的剧，要不要一起看？"]}, {"code": 213, "indent": 0, "parameters": [5, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 什么剧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 《蛾摩拉》，讲一个大都会的女教师，去一个偏远乡村支教的故事。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac  具体的我也不清楚，但是网上的评价很不错。"]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真的只是普通剧集吧？"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,0] : 时间[15]"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 才不普通！SBO出品的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 简直可以说是精品中的精品！"]}, {"code": 213, "indent": 0, "parameters": [5, 6, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 34, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我说的普通不是指那个方面……唉，算了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在你先让我好好做饭，等会下午一起看吧。"]}, {"code": 213, "indent": 0, "parameters": [6, 3, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好耶！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我去准备零食和饮料！"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 19, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [405]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 743}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 824}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 执行开启"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层透明度 : 200"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层过渡时间 : 1"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层颜色 : #000000"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 物体照明 : 事件[9] : 照明[34]"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 25"]}, {"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 121, "indent": 0, "parameters": [999, 999, 1]}, {"code": 203, "indent": 0, "parameters": [6, 0, 13, 16, 2]}, {"code": 203, "indent": 0, "parameters": [5, 0, 13, 17, 6]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 1], "indent": null}, {"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [9, 0, 20, 15, 0]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 15, "parameters": [55], "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 16, "indent": null}, {"code": 15, "parameters": [55], "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 16, "indent": null}, {"code": 15, "parameters": [55], "indent": null}, {"code": 1, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [55], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [55], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [55], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 27, "parameters": [999], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 27, "parameters": [999], "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [6, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你看你，一瘸一拐的，精神也不太好的样子，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就让我陪你一起洗澡嘛！我只是想帮忙！"]}, {"code": 213, "indent": 0, "parameters": [5, 5, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我这样怪谁？不要你帮！"]}, {"code": 213, "indent": 0, "parameters": [6, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要生气嘛！昨天晚上，我已经很克制了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 等会热水一泡，再给你搓澡放松放松，保证让你痛痛飞飞~"]}, {"code": 213, "indent": 0, "parameters": [5, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还痛痛飞飞，小学生吗你？"]}, {"code": 213, "indent": 0, "parameters": [6, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 欸嘿嘿~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这不是重点，重点是我的按摩……"]}, {"code": 213, "indent": 0, "parameters": [5, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 按摩？又在想什么歪主意吧！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别告诉我你还想在浴室里干那种事。"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 999, 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [999, 999, 1]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哎呀，被你看透了！我就是忍不住嘛！只是想起昨晚的事就硬得不行。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咱们洗个鸳鸯浴，然后再……"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 13, "indent": null}, {"code": 27, "parameters": [999], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 27, "parameters": [999], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 15, "parameters": [20], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(5).requestBalloon(7);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [20], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(5).requestBalloon(7);"], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 饶了我吧！拜托…哎啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘶……"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 999, 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 1], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": null}]}, {"code": 121, "indent": 0, "parameters": [999, 999, 1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不是谁都和你一样精力充沛！昨晚我们已经折腾了一整夜了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在我全身都酸痛得不行，连站都站不稳。你不能有点节制吗？"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且我们到现在都还没吃饭，我的工作周报也完全没动笔，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 幸好有时差，还没到妈妈查看的时间，不然我都不知道该怎么解释了。"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哎呀，洗个澡的功夫，能耽误多少事？等会我来动，也不用你费力。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 男人嘛，哪有不想要的？再说，昨晚你不是也答应了当我隔离期间的炮友吗？"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": 0}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别得寸进尺！"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唉，也不知道昨晚的我是哪根筋搭错了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 先洗澡、做饭、完成工作，这些才是正事……"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 至于你说的那种事……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 晚上再说。"]}, {"code": 213, "indent": 0, "parameters": [6, 7, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那一起洗澡……"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在我没心情，也没体力陪你胡闹，更别说一起洗澡了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你要是真在乎我的感受，就别再添乱。我去洗澡，你自己看着办。"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 12}, {"code": 15, "parameters": [10], "indent": null}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [6, 5, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}昨晚你可不是这个态度，求着我弄得你爽得都昏过去了，\\{"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}现在翻脸不认人，女人都是他妈的这样，贱！"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [6, 8, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 算了算了，就依你吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 老子就是太喜欢你了，爱让男人软弱啊，妈的……"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还剩五天……要是能隔离再久一点就好了，只要再多一个月，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 肯定能让她千依百顺！可恶！可恶……"]}, {"code": 117, "indent": 0, "parameters": [405]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 846}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 执行开启"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层透明度 : 200"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层过渡时间 : 1"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层颜色 : #000000"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 物体照明 : 事件[9] : 照明[34]"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 25"]}, {"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 203, "indent": 0, "parameters": [6, 0, 7, 9, 2]}, {"code": 203, "indent": 0, "parameters": [5, 0, 15, 12, 8]}, {"code": 203, "indent": 0, "parameters": [9, 0, 15, 13, 0]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[0,-24] : 时间[1]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,0] : 时间[1]"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 121, "indent": 0, "parameters": [999, 999, 1]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}猪田君……"]}, {"code": 213, "indent": 0, "parameters": [5, 9, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [5, 11, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 为什么会想到这个大坏蛋？！"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [6], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [5, 5, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 超级大坏蛋猪田！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}你叫我？来啦！"]}, {"code": 213, "indent": 0, "parameters": [5, 1, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [5, 12, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没…！没叫你！"]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 事件[6] : 移动显现 : 时间[15] : 方向角度[0] : 移动距离[0]"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 15, "parameters": [45], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(5).requestBalloon(11);"], "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 19, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [45], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(5).requestBalloon(11);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,-24] : 时间[45]"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": 0}, {"code": 3, "indent": 0}, {"code": 29, "parameters": [3], "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": 0}]}, {"code": 213, "indent": 0, "parameters": [6, 4, true]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[24,-24] : 时间[60]"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 27, "parameters": [999], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 27, "parameters": [999], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 宝贝~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你做饭的样子真美，一副贤妻良母的样子，看得我都要把持不住了。"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 999, 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [999, 999, 1]}, {"code": 117, "indent": 0, "parameters": [122]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你干什么？我正在忙呢！不要毛手毛脚的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还有，别那样叫我，鸡皮疙瘩都要起来了。"]}, {"code": 213, "indent": 0, "parameters": [6, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我就是看你忙得满头大汗，想帮你一下，干嘛这么大反应？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且，不是你叫我来的吗？"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 19, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 刚才没叫你，是你幻听了。你说的帮忙到底是要干什么，大家都心知肚明。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你要是闲得慌，就去客厅待着，别在这儿碍事。"]}, {"code": 213, "indent": 0, "parameters": [6, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么啦？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对我这么冷淡……"]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 213, "indent": 0, "parameters": [6, 7, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咱们现在这关系，你能不能别老把我当外人？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 抱一下，摸一下，再正常不过了，你可是答应了……"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[0,-12] : 时间[10]"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 17, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我跟你说过多少次了，答应你的事我会做。但你别老想着得寸进尺！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就算是临时炮…炮友，我也没必要事事都配合你。"]}, {"code": 213, "indent": 0, "parameters": [6, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别这么严肃嘛……"]}, {"code": 213, "indent": 0, "parameters": [6, 9, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [4], "indent": 0}, {"code": 45, "parameters": ["$gameMap.event(6).setPriorityType(0);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(6).setPriorityType(0);"], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我知道了！你现在这反应就是压力太大的表现！我们做点解压的事情就好啦！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你换上裸体围裙，我从后面抱住你，就像之前FOG那次……"]}, {"code": 213, "indent": 0, "parameters": [5, 5, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 13, "indent": 0}, {"code": 13, "indent": 0}, {"code": 13, "indent": 0}, {"code": 13, "indent": 0}, {"code": 13, "indent": null}, {"code": 27, "parameters": [999], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 27, "parameters": [999], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 15, "parameters": [6], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(6).requestBalloon(12);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(6).requestBalloon(12);"], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": 0}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你不要太过分！我不是你的玩物，更不是妓女！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你再胡说八道，就别怪我不客气！"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 999, 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [999, 999, 1]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 45, "parameters": ["$gameMap.event(6).setPriorityType(1);"], "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(6).setPriorityType(1);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈，别生气嘛，我就是开个玩笑。但有一点不假："]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你真的太美了，我总忍不住想跟你更亲密一点。"]}, {"code": 213, "indent": 0, "parameters": [5, 11, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就、就知道得意忘形……"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那个……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不穿裸体围裙呢？普通做爱我也很欢迎的！"]}, {"code": 213, "indent": 0, "parameters": [5, 8, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [6, 8, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 15, "parameters": [35], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [35], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 3, "indent": 0}, {"code": 3, "indent": null}, {"code": 27, "parameters": [999], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 27, "parameters": [999], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不行……我只有晚上我才会跟你做那种事，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 白天我需要一点私人空间。"]}, {"code": 213, "indent": 0, "parameters": [6, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你也知道我的，光靠晚上那点时间，根本不够啊。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就不能稍微体谅我一下吗？"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 999, 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [999, 999, 1]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你到底想说什么？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我的意思是，你晚上愿意和我做爱挺好的……"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可我有时候白天也憋得慌！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 能不能在我需要的时候，帮我解决一下嘛？"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": 0}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 比如用嘴或者胸什么的，拜托拜托啦！"]}, {"code": 213, "indent": 0, "parameters": [5, 6, true]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 19, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[0,-24] : 时间[30]"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [5, 11, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唉，好吧好吧，不然真的要被你烦死了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 像个蜜蜂一样一直在我耳边嗡嗡的。"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [6, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 太好了，嘿嘿，这不是你这朵花太香了嘛，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我这蜜蜂忍不住就想吸几口啦。"]}, {"code": 213, "indent": 0, "parameters": [5, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别高兴得太早，我也有条件："]}, {"code": 401, "indent": 0, "parameters": ["\\dac 只能在我有空而且愿意的时候，其他时间你不许来打扰我。"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": 0}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 行没问题，你定规矩，我都听你的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 谢谢啦，你真是太好了。"]}, {"code": 213, "indent": 0, "parameters": [5, 11, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别在这儿站着了，坐到餐桌旁边去吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 料理还有一会就要做好了。"]}, {"code": 213, "indent": 0, "parameters": [6, 3, true]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [131]}, {"code": 117, "indent": 0, "parameters": [405]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 846}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 213, "indent": 0, "parameters": [5, 7, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 34, "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,-12] : 时间[15]"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 852}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[33]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[714监控浴室乳交]"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 55"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 245, "indent": 0, "parameters": [{"name": "浴室冲水", "volume": 15, "pitch": 100, "pan": 0}]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 至少洗澡的时候让我休息一下吧…你这个性欲怪兽…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 洗澡哪有爽重要？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 都是你自己一个人爽，我下巴都疼了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗨，还不是刚才你深喉没能坚持到最后，临门一脚没能让我射出来~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过算了，我不怪你。等你洗完澡，我帮你做个脸部按摩舒缓舒缓。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我明明已经深喉很久了，舌头也……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哼，绝对是你有问题好不好？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好啦好啦，有了刚才的积累，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我肯定会很快射到你这对大白奶子上的~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 又说下流话……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯？你腿上这个印子是什么？还很新的样子……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没，没什么…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 像是指甲弄的……啊哈！我知道了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 刚才口交的时候，你为了忍住不射，所以偷偷掐自己转移注意力了，对吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我差点就真的怀疑是我自己的问题了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好啊！真是被你气死！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么会呢，你可要对自己的技术再自信一点，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 毕竟是我们一起锻炼出来的。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就是说嘛，是我们一起……是你个大头鬼啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别想转移话题。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 暴露了，哈哈……我也是为了多享受一会嘛~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 谁叫你的嘴巴太舒服了呢？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 如果不用这种盘外招，我早就被吸干了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 少来！每次都花言巧语，我听腻了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真的嘛，可是你的乳交的动作好像更卖力了？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我…我是怕你没完没了，赶紧完事而已！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好啦好啦……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还有，不许再做作弊拖延时间了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 否则被我发现的话，之前的约定就当没说过。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这好像是惩罚你自己哦，不做的话，你晚上睡得好吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你不骚扰我，我睡得才香！哼！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 晚上你自己用手处理吧！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别别别，我错了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我保证不耍花招了，也不故意忍耐了，就原谅我吧！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这还差不多~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 尽量快一点，我等会还有工作。"]}, {"code": 117, "indent": 0, "parameters": [404]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 19, "y": 7}, {"id": 16, "name": "EV016红酒", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!food1", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 0}, {"id": 17, "name": "EV017亚丝娜酒杯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!food1", "direction": 4, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-6,-24]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 0}, {"id": 18, "name": "EV018猪田酒杯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!food1", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-12,-24]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 0}, null]}