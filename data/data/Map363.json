{"autoplayBgm": false, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "PerituneMaterial_PV_Emotional", "pan": 0, "pitch": 100, "volume": 15}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 28, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 75, "width": 50, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7506, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7508, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7488, 7476, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7484, 7500, 7500, 7500, 7500, 7500, 7480, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7488, 7496, 7282, 7282, 7282, 7282, 7282, 7282, 7282, 7282, 7282, 7282, 7504, 7234, 7234, 7234, 7234, 7234, 7488, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7488, 7496, 7288, 7288, 7288, 7288, 7288, 7288, 7288, 7288, 7288, 7288, 7504, 7240, 7240, 7240, 7240, 7240, 7488, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7490, 7510, 4002, 3988, 3988, 3988, 3988, 3988, 3988, 3988, 3988, 4004, 7516, 3762, 3748, 3748, 3748, 3764, 7488, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7504, 7238, 3984, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3992, 7239, 3744, 3728, 3728, 3728, 3752, 7488, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7504, 7244, 3984, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3992, 7245, 3744, 3728, 3728, 3728, 3752, 7488, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7504, 4011, 3977, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3974, 4013, 3744, 3728, 3728, 3728, 3752, 7488, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7489, 7508, 3984, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3968, 3992, 7514, 3744, 3728, 3728, 3728, 3752, 7488, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7488, 7496, 3986, 3996, 3996, 3996, 3996, 3996, 3996, 3996, 3996, 4006, 7504, 3768, 3756, 3756, 3756, 3766, 7488, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7488, 7496, 4012, 7506, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7492, 7475, 7492, 7492, 7492, 7492, 7492, 7473, 7496, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7512, 7502, 7505, 7501, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7500, 7510, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3474, 3460, 3460, 3476, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3456, 3440, 3440, 3464, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3456, 3440, 3440, 3464, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3456, 3440, 3440, 3464, 0, 0, 3822, 0, 0, 0, 0, 3966, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3480, 3468, 3468, 3478, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3963, 3953, 3953, 3953, 3965, 0, 0, 0, 0, 3963, 3953, 3965, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 159, 159, 159, 141, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 691, 691, 693, 0, 0, 167, 167, 167, 149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 699, 700, 701, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 567, 0, 227, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 575, 0, 235, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 761, 762, 515, 516, 523, 524, 157, 157, 157, 159, 0, 761, 762, 157, 58, 137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 690, 758, 518, 0, 37, 165, 165, 165, 167, 0, 34, 901, 165, 66, 145, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 707, 708, 709, 127, 918, 919, 0, 0, 0, 0, 0, 0, 197, 198, 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 916, 422, 423, 18, 0, 0, 0, 565, 566, 205, 206, 207, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 924, 430, 431, 0, 0, 0, 0, 573, 574, 213, 214, 215, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 581, 582, 583, 0, 600, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 738, 739, 688, 689, 0, 0, 589, 590, 591, 920, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 910, 746, 747, 696, 697, 0, 0, 0, 18, 911, 0, 902, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 298, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 121, "indent": 0, "parameters": [298, 298, 1]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 8}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Hotel", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 111, "indent": 0, "parameters": [6, 0, 8]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 36, "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [3]}, {"code": 17, "indent": null}, {"code": 15, "parameters": [3]}, {"code": 16, "indent": null}, {"code": 37}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37}]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 35, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 36, "indent": null}, {"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37}]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 19, "indent": null}, {"code": 35, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": false}], "x": 23, "y": 11}, {"id": 3, "name": "EV003睡猪田", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头居家", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[24,-6]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 0}, {"id": 4, "name": "EV004亚丝娜", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "亚丝娜OL", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 5, "name": "EV005猪田", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 0}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 614}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [23]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [3, 0, 22, 13, 0]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 45, "parameters": ["$gameMap.event(3).requestBalloon(10);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(3).requestBalloon(10);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [4, 0, 16, 15, 6]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 设置声音 : 默认声音"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dDMS[慢]\\dac与此同时……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dDMS[慢]\\dac大阪-酒店房间"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 默认模式"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 关闭声音"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "2000_Village2", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈通用", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田君，我回来了。我们准备出发吧……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈回应", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田君？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [6, 4, 8]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [2, "明日奈叹气", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还在睡呀？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呼噜……"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田君，起床啦~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呼噜…呼噜……"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 懒虫！该起床啦！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 45, "parameters": ["$gameMap.event(3).requestBalloon(13);"], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(3).requestBalloon(13);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [3, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 老婆别喊啦！困死了！我还要再睡会……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呼噜…呼噜……"]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 45, "parameters": ["$gameMap.event(3).requestBalloon(10);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(3).requestBalloon(10);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 11, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈焦急脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 谁是你老婆……?！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嚤~ "]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [4, 6, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈沉思", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 精力那么旺盛的猪田君居然会这样……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看来是真困了？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈无奈", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没办法……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 晚回去几个小时应该也不会怎么样。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈默认", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 去客厅看一会儿电视吧。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 249, "indent": 0, "parameters": [{"name": "LNSM_ME06_Sleep2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 615]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [4, 0, 13, 12, 0]}, {"code": 201, "indent": 0, "parameters": [0, 363, 13, 12, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 615}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 两个小时后……"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "Bell3", "volume": 90, "pitch": 50, "pan": -20}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 模式-慢"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 设置声音 : 默认声音"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 酒店广播:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 女士们先生们，我非常抱歉地通知大家，我们酒店接待的某位非洲哈库拉国贵宾，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就在半小时之前，不幸确诊了名为玛塔塔热的当地致命性流行病。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 酒店广播:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这种病毒通过血液传播，蚊子是主要的传播媒介。感染的患者经过七至十日的潜伏期，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 便会陷入高烧昏迷，如未得到及时救治，便将在短时间内迅速死亡。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 酒店广播:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 为了避免疫情进一步扩散，保护您和其他国民健康，根据厚生劳动省的指示，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 将对酒店进行为期两周临时封锁，同时将进行定期的除虫杀蚊措施。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 默认模式"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 关闭声音"]}, {"code": 213, "indent": 0, "parameters": [4, 12, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈惊讶", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 什么？！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 模式-慢"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 设置声音 : 默认声音"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 酒店广播:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 请各位客人待在房间内，并做好个人防护。医护人员会每日定时前往客房记录大家的身体情况，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 除此之外的时间如有任何不适，也请立即与我们取得联系，我们会提供必要的医疗救助。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 酒店广播:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们理解这可能给您的行程带来一些不便，在此我们深表歉意。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 酒店广播:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 酒店全体同仁将全力以赴保护好大家的健康安全。我们相信，经过这次紧急处置，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 很快就能控制住疫情。再次感谢各位的理解与配合。"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 默认模式"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 关闭声音"]}, {"code": 213, "indent": 0, "parameters": [4, 7, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈沉思", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这下…麻烦了……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 249, "indent": 0, "parameters": [{"name": "Mystery", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [24]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 616]}, {"code": 121, "indent": 0, "parameters": [298, 298, 0]}, {"code": 201, "indent": 0, "parameters": [0, 98, 11, 17, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 616}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 6}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 365, 7, 9, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 8, "y": 18}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!笔记本", "direction": 4, "pattern": 0, "characterIndex": 5}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,-10]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 17}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 4, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 7, "y": 15}, {"id": 10, "name": "EV010亚丝娜睡眠", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "亚丝娜 居家服", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[24,-6]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 0}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 901, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[30]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 44, "parameters": [{"name": "闻", "volume": 15, "pitch": 60, "pan": 0}], "indent": null}, {"code": 15, "parameters": [100], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "闻", "volume": 15, "pitch": 60, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [100], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [10, 0, 22, 13, 0]}, {"code": 203, "indent": 0, "parameters": [4, 0, 22, 13, 4]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null}, {"code": 39, "indent": null}, {"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}, {"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [13, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 17, "indent": null}, {"code": 15, "parameters": [15], "indent": 0}, {"code": 18, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 事件[10] : 直接消失 : 时间[3]"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 4]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈回应", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田君并没有进房间。"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈安心", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看来是我误会他了。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 15, "parameters": [90], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [90], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [4, 2, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈惊讶2", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯？也不在客厅……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猥琐的男声>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}\\}嘶哈…嘶哈……学姐！哦哦～ 我忍不住了～ 我好喜欢你～"]}, {"code": 213, "indent": 0, "parameters": [4, 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, true]}, {"code": 121, "indent": 0, "parameters": [298, 298, 0]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 365, 22, 17, 6, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 8, "y": 7}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 902, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 203, "indent": 0, "parameters": [4, 0, 20, 13, 8]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}, {"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}, {"code": 33, "indent": null}, {"code": 29, "parameters": [2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [10, 0, 22, 13, 8]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[10] : 像素偏移[24,32] : 时间[1]"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "エースDungeon1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [60], "indent": 0}, {"code": 1, "indent": null}, {"code": 15, "parameters": [60], "indent": 0}, {"code": 1, "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [60], "indent": 0}, {"code": 4, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 213, "indent": 0, "parameters": [4, 7, true]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 4]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈沉思", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 虽然到现在为止猪田君的表现都很老实，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但还是有些不太放心，需要做些防护……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈逃避", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可该怎么做呢？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 枕头旁边放安全套……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [4, 12, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 16, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈焦急脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我在想什么呢！简直就像是在邀请他一样！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不行不行！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 2], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈沉默", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 以我对猪田君的了解，他再怎么情绪化，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 也不至于直接过来强行和我……"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈失望", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但是或许他会搞点恶作剧来试探。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [4, 9, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈得意", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以只要能够遏制住他的第一步，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 应该就可以避免局面失控！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, 4, 1]}, {"code": 111, "indent": 0, "parameters": [1, 198, 0, 13, 0]}, {"code": 205, "indent": 1, "parameters": [4, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 198, 0, 14, 0]}, {"code": 205, "indent": 1, "parameters": [4, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 198, 0, 15, 0]}, {"code": 205, "indent": 1, "parameters": [4, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 16, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈理解", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 用笔记本的摄像头做个简单的监控，这样在登录和睡觉的时候，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 如果猪田君有任何的逾矩行为，我都能及时发现再劝阻他。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "KOK_Systemsounds_ItemSelect_09", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 3, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac OK，设置好了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这种简单的事情，就算不用桐人帮忙，我也还是可以的~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [5, 0, 8, 17, 8]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 40, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈心事", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 要不要跟猪田君明说呢？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈嘲弄", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 今天先不说吧，正好可以当作对他的考验。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诶，学姐，你还没有上线啊？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我今天洗澡的时间算是比较久了耶。"]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈岔开", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯，刚才想了一些事情……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 正准备登录的。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯嗯~"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 37, "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[24,0] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [10, 11, false]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈心事脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯~ 呼……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 床上好像还有一些昨天猪田君留下的汗味……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈失落脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 等明天酒店管理把所有防疫的事情理顺以后，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 应该就能恢复房间的情节和床铺换洗了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [10, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈沉默脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 先这样吧，反正……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这味道我也差不多习惯了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip1", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 1, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [5, 5, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](贱人！居然还给我设置了一个陷阱！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](幸亏之前把隐藏摄像头安装在卧室而不是浴室，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 幸亏刚刚洗万澡后偶然想起来看一眼监控画面……)"]}, {"code": 213, "indent": 0, "parameters": [5, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](但母猪设置的监控要想办法化解才行，不然后续计划可就难办了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可之前我已经把母猩猩彻底得罪了，再找谁商量好呢……？)"]}, {"code": 213, "indent": 0, "parameters": [5, 12, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](说到母猩猩……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 也是个定时炸弹啊！)"]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 213, "indent": 0, "parameters": [5, 7, true]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](嗨！太麻烦！先不去想了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 赶紧登录操逼哦~)"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "KOK_Systemsounds_MenuOpen_05", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "KOK_Systemsounds_MenuOpen_01", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [298, 298, 0]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 301, 17, 8, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 9, "y": 7}, {"id": 13, "name": "EV013摄像头伪装花瓶", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 628}, "directionFix": false, "image": {"tileId": 519, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[15,-3]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 17}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 665}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 203, "indent": 0, "parameters": [13, 0, 21, 17, 0]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[13] : 像素偏移[15,-3] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [5, 0, 22, 16, 2]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 45, "parameters": ["$gameMap.event(5).requestBalloon(8);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(5).requestBalloon(8);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [4, 0, 8, 17, 8]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [15, 0, 21, 17, 0]}, {"code": 205, "indent": 0, "parameters": [15, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 执行开启"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层透明度 : 200"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层过渡时间 : 1"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层颜色 : #000000"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 物体照明 : 事件[15] : 照明[26]"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 物体照明 : 事件[15] : 修改图片层级[0]"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 25"]}, {"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](是猪田啊……这是他之前电话里说的那个亲戚家的摄像头吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 估计是没调好焦距，画面像是蒙了一层雾似的。)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](为什么要把那边的录像往这个电脑上传呢？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 搞不懂他。大概是误操作了吧。)"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 213, "indent": 0, "parameters": [5, 3, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac OK，搞定！本大爷真是天才！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿嘿嘿~"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](不仅有电流声，语音语调也全失真了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看来，不仅是焦距，收声的设置也是稀烂。)"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 话说这里网速还真是快，我设置监控的功夫，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 十几部AV就下好了~"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](还真是猪田会做的事。)"]}, {"code": 213, "indent": 0, "parameters": [5, 4, true]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 在桌面新建文件夹…改名“学习资料”……AV全放进去……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我就不信你不点进去看！嘿嘿嘿~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}猪田君，早餐好了，先过来吃吧。"]}, {"code": 213, "indent": 0, "parameters": [5, 1, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦哦！好的，马上来！"]}, {"code": 117, "indent": 0, "parameters": [404]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 679}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 203, "indent": 0, "parameters": [13, 0, 15, 17, 0]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[13] : 像素偏移[0,-3] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [5, 0, 12, 14, 6]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[0,-9] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [4, 0, 20, 15, 4]}, {"code": 203, "indent": 0, "parameters": [15, 0, 15, 17, 0]}, {"code": 205, "indent": 0, "parameters": [15, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 执行开启"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层透明度 : 200"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层过渡时间 : 1"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层颜色 : #000000"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 物体照明 : 事件[15] : 照明[26]"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 物体照明 : 事件[15] : 修改图片层级[0]"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 55"]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 33, "indent": null}, {"code": 29, "parameters": [2], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [5, 1, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[0,0] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 1], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [35]}, {"code": 213, "indent": 0, "parameters": [5, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 视频会开完啦？"]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯。"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 渴不渴？我再去给你弄点牛奶吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 暂时不渴。"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 199, 0, 2, 1]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那你累不累？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还好，不累。"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 199, 0, 3, 1]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就算不累，工作完了也应该放松放松，让我给你按摩吧~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没关系的。"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 199, 0, 4, 1]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要误会，只是捏个肩膀之类的……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真的不用啦。"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 199, 0, 5, 1]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那我……"]}, {"code": 213, "indent": 0, "parameters": [4, 7, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 15, "parameters": [3], "indent": null}, {"code": 35, "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田君啊！"]}, {"code": 213, "indent": 0, "parameters": [5, 12, true]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别、别生气好吗？"]}, {"code": 213, "indent": 0, "parameters": [4, 6, true]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没有生气，就是觉得……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你没必要一直这样嘘寒问暖，非要补偿我什么的样子，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 昨天的事情，我真的没有怪你。"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可是可是，因为我昨天晚饭以后就睡过去了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 耽误了进度，不是吗？"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 其实没有。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我也难得可以静下心陪那个人……"]}, {"code": 213, "indent": 0, "parameters": [5, 1, true]}, {"code": 213, "indent": 0, "parameters": [5, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是，是嘛……"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那是挺好的……"]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唉……"]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 其实，我刚才是故意那么说的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你的这个反应，果然和我预想的一样。"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [5, 2, true]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 近期一些事情的发展，已经有了一点失控的迹象，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以我觉得有必要和猪田君你好谈谈。"]}, {"code": 213, "indent": 0, "parameters": [5, 12, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [35], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [35], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 有，有吗，是不是太夸张了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 有，非常有。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 坐下说吧。"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[0,0] : 时间[20]"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 35, "indent": null}, {"code": 4, "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好吧……"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[0,-9] : 时间[20]"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 18, "indent": null}, {"code": 35, "indent": null}, {"code": 4, "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田君，游戏里的那些事情，你应该很清楚，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们只是暂时性的各取所需。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过FOG实在是太过真实了，这一点确实有利有弊。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 包括前两天那次例外的处理……"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可能让你潜意识里有了是不是可以更进一步的错觉……"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没有！绝对不会！你多想了！我没有任何篡越的想法！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以我们完全没有必要结束游戏里的关系！"]}, {"code": 213, "indent": 0, "parameters": [4, 12, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 也，也没有说要结束，游戏只是游戏嘛，冷静一点。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我也是只提醒猪田君一下。"]}, {"code": 213, "indent": 0, "parameters": [5, 4, true]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 至于在这里……"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我知道的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我会尽量控制自己，不到万不得已，绝对不会提过分要求的！"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……"]}, {"code": 213, "indent": 0, "parameters": [4, 9, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不对，到了万不得已也不可以啦！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 八嘎！"]}, {"code": 213, "indent": 0, "parameters": [5, 3, true]}, {"code": 213, "indent": 0, "parameters": [4, 11, true]}, {"code": 117, "indent": 0, "parameters": [404]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 1]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 13, "indent": null}, {"code": 15, "parameters": [30], "indent": 0}, {"code": 19, "indent": null}, {"code": 45, "parameters": ["$gameVariables.setValue(199, 2);"], "indent": null}, {"code": 13, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 15, "parameters": [25], "indent": null}, {"code": 2, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 19, "indent": null}, {"code": 45, "parameters": ["$gameVariables.setValue(199, 3);"], "indent": null}, {"code": 15, "parameters": [35], "indent": null}, {"code": 2, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 19, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 45, "parameters": ["$gameVariables.setValue(199, 4);"], "indent": null}, {"code": 15, "parameters": [20], "indent": null}, {"code": 2, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 19, "indent": null}, {"code": 45, "parameters": ["$gameMap.event(5).requestBalloon(8);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 4, "indent": null}, {"code": 45, "parameters": ["$gameVariables.setValue(199, 5);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameVariables.setValue(199, 2);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [25], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameVariables.setValue(199, 3);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [35], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameVariables.setValue(199, 4);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [20], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(5).requestBalloon(8);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameVariables.setValue(199, 5);"], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 37, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[0,-9] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 4, "indent": null}, {"code": 34, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 680}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 203, "indent": 0, "parameters": [13, 0, 15, 17, 0]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[13] : 像素偏移[0,-3] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [5, 0, 13, 15, 6]}, {"code": 203, "indent": 0, "parameters": [4, 0, 14, 15, 8]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[16] : 像素偏移[0,-12] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [16, 0, 14, 14, 6]}, {"code": 203, "indent": 0, "parameters": [15, 0, 15, 17, 0]}, {"code": 205, "indent": 0, "parameters": [15, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 执行开启"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层透明度 : 200"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层过渡时间 : 1"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层颜色 : #000000"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 物体照明 : 事件[15] : 照明[26]"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 物体照明 : 事件[15] : 修改图片层级[0]"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 55"]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [4, 7, true]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这送来的都是些什么衣服啊！"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别生气别生气！"]}, {"code": 213, "indent": 0, "parameters": [5, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 话说是什么衣服啊？我能看看嘛？"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 随便你。我要打个视频问问她。"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 3, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [5, 1, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [5, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}\\c[8]嚯哦！这些衣服……！母猩猩真是会挑啊！给力给力！"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 34, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}你呀你呀！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[27]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}哈哈，看你这反应，衣服收到了是吧？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 213, "indent": 0, "parameters": [4, 5, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}是啊！可都不是我要的衣服！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[27]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}当然不是你的。要跑好几个地方好麻烦的嘛！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[27]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}所以我就在店里选了一些促销的夏装，算我这个闺蜜给你的礼物，不用你给钱啦~"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 213, "indent": 0, "parameters": [4, 11, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}不是这个问题！这些衣服……\\{"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}我怎么穿啊！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[27]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}怎么就不能穿啦？现在可是2026年！而且你别忘了，\\{"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}上次我给你挑的黑色开胸那件，你男人爱死了好吧！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 213, "indent": 0, "parameters": [4, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}那兔女郎装是怎么回事？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[27]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}啊，这件怎么寄给你了？可能是我放错了……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 213, "indent": 0, "parameters": [4, 5, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}还有比基尼女仆装！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[27]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}好啦好啦！\\{"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}我承认确实有那么一两件不适合穿出门，你无视不就好了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[27]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊，欢迎光临！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[27]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}我有客人上门，就不跟你多聊啦！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 213, "indent": 0, "parameters": [4, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}喂！唉……"]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}不过既然都寄到了……"]}, {"code": 213, "indent": 0, "parameters": [4, 11, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}还是试试吧。"]}, {"code": 213, "indent": 0, "parameters": [5, 4, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田君，帮我把衣服拿到房间来，我还是稍微试一下。"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可以给我看吗？"]}, {"code": 213, "indent": 0, "parameters": [4, 5, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 当然不行！怎么可能让你看我换衣服？"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": 0}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要误会！不是说换衣服的过程！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我只是想看你换好以后的样子，这些情趣服装……"]}, {"code": 213, "indent": 0, "parameters": [4, 11, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好吧……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过，你给我去厨房等着！我换好了再叫你。"]}, {"code": 213, "indent": 0, "parameters": [5, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没问题！我先把衣服拿去卧室！"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 17, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[16] : 像素偏移[24,-12] : 时间[10]"]}, {"code": 117, "indent": 0, "parameters": [404]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 681}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 743}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [99, "7_9晚监控", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 55"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](猪田和她表姐在看电视……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 结束观看？"]}, {"code": 102, "indent": 0, "parameters": [["是", "否"], 1, 1, 2, 1]}, {"code": 402, "indent": 0, "parameters": [0, "是"]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "否"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 【电视旁白】"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 夕阳的余晖轻轻洒在静谧的站台上，温暖的光线在铁轨上镶嵌出金色的线条。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 远处，一列列车缓缓驶近，带着一丝轻微的轰鸣声，仿佛在讲述着一段未完的故事。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 【电视旁白】"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 玛丽站在站台边缘，怀里紧紧抱着杰克，目光温柔而伤感，仿佛这瞬间的静谧将永远停留在她的记忆中。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 车轮与轨道的摩擦声，仿佛在提醒着时间的流逝，而这一刻，属于她和杰克，属于这片渐渐暗下来的天空。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 电视里的女声:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 杰克，我真的要走了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 电视里的男声:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯，玛丽，去实现你的梦想吧。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 玛丽:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你说…那里会不会很艰难？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 杰克:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别怕，你是最棒的老师。那些孩子们会爱上你的。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 玛丽:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可是…我舍不得你……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 杰克:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别哭了，傻瓜，我会去看你的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 等你安顿好就去。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 玛丽:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那里…那么远…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 杰克:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 再远也阻挡不了我。你知道的，从第一次见你，我就……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 玛丽:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别说了…我怕我会后悔…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 杰克:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 去吧。记住，无论发生什么，我都在这里等你。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 等你回来，我们就结婚。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 玛丽:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 答应我…别忘了我……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔，虽然剧本有些老套，但演员和场景都选的挺好。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…拍得真美。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个男的，简直不像是在演戏，天生戏骨啊。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 确实很动人…但为什么我觉得杰克的眼神有点怪？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怪？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…说不上来，就是感觉他的温柔下面藏着什么。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个配乐…有种不祥的预感。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 有吗？反正慢慢看下去就知道了。"]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [404]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 744}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 749}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [99, "7_10中午监控", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 55"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](猪田和她表姐又在看电视……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 结束观看？"]}, {"code": 102, "indent": 0, "parameters": [["是", "否"], 1, 1, 2, 1]}, {"code": 402, "indent": 0, "parameters": [0, "是"]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "否"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 【电视旁白】"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 夜色笼罩着偏远的山村，保罗站在玛丽的窗前，月光为他圆润的身形镀上一层柔和的光晕。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 他的手中捧着一束野花，那是他走遍山野采来的。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 保罗:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 玛丽老师…我知道我配不上你。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但是，我真的很喜欢你。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 玛丽:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 保罗…我……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 保罗:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我知道自己不够帅，也不像城里人那样有品位。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可是我保证，我会用一辈子对你好。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个男的，虽然外表普通，癞蛤蟆想吃天鹅肉，做事倒是挺上道的。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…比起杰克，他确实更懂得关心人。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过我倒是比较意外，你们男生不都是都觉得这类电影很无聊的吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没有没有，其实我还挺喜欢的，就是那个，呃，看的时候容易共感角色"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还…不用动脑，咳咳咳，不用懂一堆科幻设定啥的"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 保罗:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我看得出来，你最近很难过。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 每次收到杰克的信，你的眼神都会黯淡下来。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这家伙还挺会察言观色，连这种细节都注意到了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 有些人事业有成、表面光鲜，但却根本不在乎伴侣的感受。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是啊……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 玛丽:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对不起…保罗…我还不能……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 保罗:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我知道你还放不下杰克。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但我愿意等，等到你看清真相的那一天。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 玛丽…她是不是太傻了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊，问我嘛……这个…我觉得应该不是吧，这都要怪之前那个男的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你看，如果一个人真的爱你，会让你这么痛苦吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac …不会。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真心爱一个人，应该是想让她快乐，而不是……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 保罗:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 玛丽老师，我永远都在这里。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 当你需要我的时候，我一定会出现。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对嘛对嘛…我倒是觉得这个男的比较顺眼…"]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [404]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 752}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[30]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[7-10下午监控]"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 55"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](猪田和她表姐还是在看电视……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 结束观看？"]}, {"code": 102, "indent": 0, "parameters": [["是", "否"], 1, 1, 2, 1]}, {"code": 402, "indent": 0, "parameters": [0, "是"]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "否"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 【电视旁白】"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 月光，像一位无声的见证者，悄然洒落。它穿过窗帘的缝隙，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 轻轻抚过保罗与玛丽的身影，仿佛为这一刻镀上了一层永恒的银辉。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 【电视旁白】"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 他的吻，温柔却坚定，像一场无声的誓言，试图将她所有的伤痛，融化在这片静谧的夜色中。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 时间在此刻失去了意义。他们的世界，只剩下彼此的呼吸，彼此的体温。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 【电视旁白】"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 良久，两人才依依不舍地分开，仿佛从一场梦中醒来，却又带着梦的余温。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 玛丽:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 保罗…我已经…不干净了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 保罗:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 在我眼里，你永远都是最纯洁的。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那些暴徒！那些混蛋！居然把玛丽给……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是啊，玛丽被轮奸得好惨！毒蛇帮简直太可恶了！幸好保罗最后赶到，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这家伙虽然没什么本事，但为了救玛丽，连自己受伤都不在乎。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 玛丽被…被那样对待…杰克却…唉…还在电话里自说自话……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘛，也不能怪他，毕竟他什么都不知道嘛！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不知道才不算是理由！身为恋人却一直忽视对方的需求！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 连对方受到伤害了都不知道，才会出现现在这种情况……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别，别生气嘛，至少现在她终于找到真正爱她的人了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你看保罗多温柔~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac ……唉，至少这样也好。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是啊，有时候需要经历一些事，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 才能分清谁才是真心对你好的人，就像我的混蛋父母。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac ……或许吧，你说的也是对的…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以有时候不要被表面的承诺迷惑，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 要看清谁才是真正在乎你的人。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…就像保罗…这样的……"]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [404]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 753}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[30]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[7-10夜晚监控]"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 55"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](猪田和她表姐是不是在电视前坐了一天了？)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 结束观看？"]}, {"code": 102, "indent": 0, "parameters": [["是", "否"], 1, 1, 2, 1]}, {"code": 402, "indent": 0, "parameters": [0, "是"]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "否"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊！他们…他们要…这个画面…为什么会有这种场景！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没事的，这是R18分级嘛～"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 欧美片里这种很常见啦，也算一种特色了，而且这是爱的表现。 "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但…但是…这么直接的画面……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 之前强暴的剧情明明就是一笔带过的……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 因为这点剧的重点是玛丽保罗的爱情！我们得看导演想表达的内涵！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你看演员情绪不是很到位嘛~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不是这个问题……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么了，难不成……是第一次看这种？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不是，只是……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 居然不是第一次嘛？真是意外呢嘿嘿~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 之前看的是什么？正规途径的AV，还是偷拍的小视频？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真是的，我要生气啦~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿嘿~ 好啦好啦，我知错了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们还是专心看剧吧！"]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [404]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 799}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[30]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[711酒店口交监控]"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 55"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](猪田表姐在给猪田……口交？！)"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯啾……啾啾……已经变得这么硬了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……唔……你这舌头……太舒服了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我的鸡巴味道怎么样？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾噜……啾啾噗……嗯……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 有点咸咸的……但我不讨厌……滋溜……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊……啊……太棒了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯咕……啾啾……啾噜噜……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊……舒服，太舒服了……你的嘴好软……好会舔……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾噗……啾啾……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔……啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾啾噗噗……嗯……啾噜……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊……对…就是这样……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯啾……啾噗噗……啾啾啾……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦哦~ 爽啊……"]}, {"code": 117, "indent": 0, "parameters": [404]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 820}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 203, "indent": 0, "parameters": [13, 0, 15, 17, 0]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[13] : 像素偏移[0,-3] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [3, 0, 22, 13, 0]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[03] : 像素偏移[24,-6] : 时间[1]"]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 41, "parameters": ["猪头裸", 3], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["猪头裸", 3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [10, 0, 22, 14, 0]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[10] : 像素偏移[24,-24] : 时间[1]"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜裸", 1], "indent": null}, {"code": 17, "indent": null}, {"code": 45, "parameters": ["$gameMap.event(10).setPriorityType(0);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜裸", 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(10).setPriorityType(0);"], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 45, "parameters": ["$gameMap.event(3).requestBalloon(10);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(10).requestBalloon(10);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(3).requestBalloon(10);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(10).requestBalloon(10);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [15, 0, 15, 17, 0]}, {"code": 205, "indent": 0, "parameters": [15, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 执行开启"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层透明度 : 200"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层过渡时间 : 1"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 黑暗层 : 修改黑暗层颜色 : #000000"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 物体照明 : 事件[15] : 照明[26]"]}, {"code": 356, "indent": 0, "parameters": [">自定义照明 : 物体照明 : 事件[15] : 修改图片层级[0]"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 55"]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "打呼噜", "volume": 45, "pitch": 100, "pan": 60}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](空无一人的客厅……)"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](但隐约能听到男人的鼾声。)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](大概是猪田那家伙。大中午了还在睡觉，离谱。)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](不过也有点让人羡慕啊……)"]}, {"code": 117, "indent": 0, "parameters": [404]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 848}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[33]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[714瑜伽口交]"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 55"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">菜单GIF : GIF[8] : 隐藏"]}, {"code": 356, "indent": 0, "parameters": [">菜单GIF : GIF[9] : 显示"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-009口含口交-小", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你口交的技术越来越好了，果然是一起在FOG里的练习打下了基础~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这种感觉想不上瘾都难呀。  "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔…啾…你每次都一定要发表你的变态感想吗…咕……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿，你不就喜欢我变态的时候嘛，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 书上也说，情侣之间口交的时候，要夸奖对方，有利于气氛。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…哈呣…唔…又是不知道从哪找来的歪理…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但是事实上，在我夸你之后，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你舌头的动作明明更快了不是嘛，我能感觉到的。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…啾啾…才，才没有…咕…我只是想早点……呜…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这么急着摆脱我？呜呜…太让人伤心了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可能还要再多花些点时间了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好了…别闹了，我还有别的事要做呢！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔呣…还是…说你想我现在就停？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 行行，别气别气，我错了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你继续，我保证老实点，不闹了。  "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔…咕啾…这次就算了…啾……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 瞧你这小脾气，嘴硬心软~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们互相多享受一下有什么错嘛！  "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咕…笨，笨蛋，谁会享受啊……舔着这么…恶心的东西……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 恶心？那你这嘴怎么还吸得这么紧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 闭嘴！唔…咕啾…快点结束，别废话！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…啾…和你说话…啾…让我觉得自己…像个白痴。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不许你这么说，你是我的宝贝！我的女神！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我心里就只有你。  "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咕…油嘴滑舌……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哎，不要老觉得我坏，你不知道我多迷你，迷到骨子里了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哼…！唔…咕……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那个……可以试试深喉吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就是插到喉咙里面，你肯定没问题的……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 滋咕…又得寸进尺…咕啾……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不难的！就当作一种特殊的瑜伽……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啧咕…嗯嗯…你真是…滋啾…烦死了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你答应我，我就不烦你了~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咕唧…哈啊…好吧…啾唔…但就这一次…！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯呜…不许太粗暴…滋咕…也别、别乱说话！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我保证乖乖的！"]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 117, "indent": 0, "parameters": [404]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 848}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [100, "摄像头适配", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[33]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[714瑜伽深喉]"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create TV"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Thickness 10"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Change TV Corner 0.3"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 55"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">菜单GIF : GIF[8] : 隐藏"]}, {"code": 356, "indent": 0, "parameters": [">菜单GIF : GIF[9] : 显示"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-017口爆-大", "volume": 75, "pitch": 100, "pan": 100}]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 震动长 35 150 -100"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咕啾……嗯……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对…！就是这样…哦~ 哦~ 这紧致感………"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 简直是我的天堂……！比我想象中还要完美……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜…呜呜…！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咕啾……嗯嗯……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你的柔韧性真的厉害！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 下次说不定还能更深~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾唔……咕噜……滋滋……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…你的喉咙好紧……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[1]女声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾唔……嗯呜……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[6]男声\\c[0]:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真是让人上瘾啊……"]}, {"code": 121, "indent": 0, "parameters": [482, 482, 0]}, {"code": 117, "indent": 0, "parameters": [404]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 10, "y": 6}, {"id": 15, "name": "EV015摄像头点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 17}, {"id": 16, "name": "EV016包", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!adult2", "direction": 2, "pattern": 2, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图滤镜 : 纯色滤镜 : 紫色 : 165"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 0}, {"id": 17, "name": "EV017", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 297, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Create Glitch"]}, {"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 10]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 5, 2]}, {"code": 356, "indent": 1, "parameters": ["HorrorEffects Screen Change Glitch Offset 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 77, 0, 8, 2]}, {"code": 356, "indent": 2, "parameters": ["HorrorEffects Screen Change Glitch Offset 3"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 77, 0, 10, 2]}, {"code": 356, "indent": 3, "parameters": ["HorrorEffects Screen Change Glitch Offset 5"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 10]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 7, 2]}, {"code": 356, "indent": 1, "parameters": ["HorrorEffects Screen Change Glitch Frequency 60"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 77, 0, 9, 2]}, {"code": 356, "indent": 2, "parameters": ["HorrorEffects Screen Change Glitch Frequency 45"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 77, 0, 10, 2]}, {"code": 356, "indent": 3, "parameters": ["HorrorEffects Screen Change Glitch Frequency 30"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var waitFrames = Math.floor(Math.random() * 45) + 45;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 0, "parameters": ["HorrorEffects Screen Remove Glitch"]}, {"code": 355, "indent": 0, "parameters": ["var waitFrames = Math.floor(Math.random() * 60) + 120;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 10, "y": 5}, {"id": 18, "name": "EV018", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 912, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [4, 0, 21, 16, 4]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜斑纹泳衣", 0], "indent": 0}, {"code": 41, "parameters": ["亚丝娜斑纹泳衣", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜斑纹泳衣", 0], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜斑纹泳衣", 1], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [5, 0, 19, 16, 6]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[10] : 像素偏移[24,32] : 时间[1]"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "エースDungeon1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [4, 5, true]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 102]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 刚才太冒险了！要是被发现了怎么办？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我确实很快就射了对不对？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你也完美地瞒过去了，他没察觉到任何异常呀！"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 41, "parameters": ["亚丝娜斑纹泳衣", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜斑纹泳衣", 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 7, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈凛然脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不管怎么说，你都不该在视频通话的时候做这种事！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜斑纹泳衣", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜斑纹泳衣", 1], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [5, 4, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但是你不觉得这种刺激很特别吗？就像是我们俩共同经历了一次惊险的冒险！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们被困在酒店，还有可能染上致命的病毒，这种小冒险不正好可以缓解压力吗？"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 全是歪理，总之以后不许再这样。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 关于这个话题，不要让我再说第二遍。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯嗯，不说这个，不说这个。"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你出去吧，我准备要上线做修女任务了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 莉兹也说有事找我。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 12, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": 0}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但是我还硬着呀！而且比之前还更难受了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 求求你了，继续之前没做完的口交吧！"]}, {"code": 213, "indent": 0, "parameters": [4, 11, false]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈掩饰", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 刚才就是我一时搞错了！不可能再做了！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我操你妈！怎么这么不听话？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 刚才给老子口的时候，还以为已经可以完全拿捏了……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么这样……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那用其他方式可以吗？"]}, {"code": 213, "indent": 0, "parameters": [4, 2, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈失望脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac ……什么方式？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](诶！有戏！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 搞不好利用这个机会能更进一步……)"]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 用…屁股的洞来做……"]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈意外脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 屁股的洞……？！"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 121, "indent": 0, "parameters": [420, 420, 0]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈焦急脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不行不行！你脑子里成天都在想些什么呢？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说好只是偶尔帮你处理一下……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 15, "parameters": [45], "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [45], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 11, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 121, "indent": 0, "parameters": [420, 420, 1]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈失落脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 用那里的话，不就是在做…那种事……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不不不，肛交怎么算做爱呢？又不可能怀孕。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这和用手或脚帮我没什么不同，只是换了一个部位而已。"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就算不是做爱…那里和用手脚怎么能一样呢？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 变态！而且会很痛！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 只要事先做好准备就不会痛的啦，而且我保证会非常温柔，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 正好浴室有足够的润滑剂，如果学姐感到不舒服，我们随时可以停下！"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜斑纹泳衣", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜斑纹泳衣", 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜斑纹泳衣", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜斑纹泳衣", 1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈心事脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这……"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜斑纹泳衣", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜斑纹泳衣", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 121, "indent": 0, "parameters": [420, 420, 0]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈叹气脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不行……我还是接受不了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对不起……"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现实不行的话，我们在FOG里试试吧！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这样就完全没有问题了！"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜斑纹泳衣", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜斑纹泳衣", 1], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 另外，我们还可以先用游戏里的道具做预演，比如拉珠这些。"]}, {"code": 213, "indent": 0, "parameters": [4, 2, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 121, "indent": 0, "parameters": [420, 420, 1]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈欲言又止", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 拉珠？那是什么？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好像是专门用来帮助适应的道具来着，游戏里的，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说明起来比较麻烦，待会登录后我拿给你看看就知道了。"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈心事脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 游戏里的道具啊……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜斑纹泳衣", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜斑纹泳衣", 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [4, 11, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [5, 2, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [5, 9, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](这是默许的意思吧！我就知道！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 只要一直死缠烂打，肯定能逐步突破母猪老婆的底线！)"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那就这么说定了！我先上线准备啦。"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "KOK_Systemsounds_MenuOpen_05", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "KOK_Systemsounds_MenuOpen_01", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [298, 298, 0]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 1]}, {"code": 201, "indent": 0, "parameters": [0, 355, 11, 25, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 10, "y": 7}, {"id": 19, "name": "EV019", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 920, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [90, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [91, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 设置动画序列 : 动画序列[30]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 设置动画序列 : 动画序列[32]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [4, 0, 13, 12, 2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 1], "indent": null}, {"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 0], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [5, 0, 14, 12, 2]}, {"code": 203, "indent": 0, "parameters": [13, 0, 15, 17, 0]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[13] : 像素偏移[0,-3] : 时间[1]"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 等待动画序列加载完成"]}, {"code": 241, "indent": 0, "parameters": [{"name": "悲伤-矶村由纪子-風の住む街", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-68, -68, -68, 0], 60, true]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 旁白:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 岁月流转，玛丽带着伤痕回到了大都会。她嫁给了杰克，日子平淡如水，却再也填不满心底的空洞。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 保罗，那个用生命护住她的人，早已化作小镇的风，散在记忆的角落。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 日月如梭，光阴逝去，在又一个为琐事而正常的傍晚，杰克摔门而出的屋内一片寂静。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 旁白:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 玛丽慢慢起身，走到窗边。这一刻，夕阳如血，她仿佛又看到了那张熟悉的脸吗……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 年轻、坚定，带着从自己得而复失的温柔。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 旁白:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 争吵褪去，生活继续，可她的泪水，却为另一个名字而落。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 有些人，注定只能活在余生的回望里。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 结束了……"]}, {"code": 213, "indent": 0, "parameters": [4, 7, true]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 103]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈欲言又止", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 玛丽最后还是忘不了保罗…好感人，可也好难过…"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 要是保罗没死，他们就能一直在一起了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是啊，保罗为了玛丽死了，太惨了。"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哎，回东京以后，我可能也要走了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 出国去打工，大概一辈子都见不到你了吧。"]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈惊讶", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 出国？！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 和妈妈的误会早就解开了，你不会被退学，怎么又要走？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这次隔离我旷工太多，学分修不够，没法毕业。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 父母也不会管我了，我就得流浪打工。"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈担忧", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这……"]}, {"code": 121, "indent": 0, "parameters": [420, 420, 0]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈沉思", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 先不用那么悲观，回去以后我会帮你想办法赚学分的……"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 121, "indent": 0, "parameters": [420, 420, 1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 学分也只是一个方面。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 前几天你跟我谈心，说到我们的关系，我也琢磨了很久。"]}, {"code": 213, "indent": 0, "parameters": [4, 1, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈心事脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可是……这和你要离开有什么关系？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们不是说了可以继续做普通朋友吗？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 7, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [1, 0], "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [1, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 朋友是朋友……"]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我就把实话说了吧！我就是喜欢你！"]}, {"code": 213, "indent": 0, "parameters": [4, 1, true]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我知道这不对，但我根本控制不住！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你那么好，那么美！"]}, {"code": 213, "indent": 0, "parameters": [4, 11, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我知道自己配不上你，可也不想看着你和别人在一起……"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈掩饰", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac ……其实，我也是知道的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但是我也不知道该怎么做…这些都是没办法的事情……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 4, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 249, "indent": 0, "parameters": [{"name": "LNSM_ME13_Comical1", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 17, "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以现实里肛交的事儿，你再想想呗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这是我这辈子最后的心愿了！"]}, {"code": 213, "indent": 0, "parameters": [4, 5, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 什么？！"]}, {"code": 121, "indent": 0, "parameters": [420, 420, 0]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈凛然脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪！田！君！"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈严肃脸红", 0, 0, 310, 20, 100, 100, 255, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 0, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 都这样了还是就知道想那种事。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你呀！真是被你气死。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 121, "indent": 0, "parameters": [420, 420, 1]}, {"code": 213, "indent": 0, "parameters": [5, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对呀，我就是个又好色又无能的废物，这样你也就不会念着我，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 等回去以后，赶紧把我忘了，对我们都好。"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但回去之前，就把那个就当作纪念吧！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你之前也确实答应考虑来着，总不会要考虑到我消失的那天吧？"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 1], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你说的…我明白。可，现实里做这种事…太过了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我也说过很多次了，这和用手用脚没有任何本质的分别，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 难道你还是觉得这样对不起桐人那家伙吗？"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈欲言又止", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我…！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 拜托了，你看试炼的最后也是我帮忙才成功的不是吗？ "]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [-1, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [-1, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且我保证，回东京我就消失，以后绝不再打扰你。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这里发生的一切会永远封存起来。"]}, {"code": 213, "indent": 0, "parameters": [4, 11, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就做一次，算是咱俩的告别。"]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈心事脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 告别…吗……？"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 1], "indent": null}]}, {"code": 121, "indent": 0, "parameters": [420, 420, 0]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈沉默脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}嗯…或许这确实是最好的结局……"]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 0], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈失落脸红", 0, 0, 310, 20, 100, 100, 255, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 0, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 如果这几天，就是我们最后相处的日子……"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈失望脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好吧…我答应你。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 把在这个房间里发生的一切，作为我们最后的告别。"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈叹气脸红", 0, 0, 310, 20, 100, 100, 255, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 0, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但，你也得答应我，振作起来，照顾好你自己。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 0], "indent": null}]}, {"code": 121, "indent": 0, "parameters": [420, 420, 1]}, {"code": 213, "indent": 0, "parameters": [5, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 行！我答应，有了这些和你一起经历的宝贵记忆，我肯定会振作起来的！"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [1, 0], "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [1, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以，我们现在就去卧室……"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 12, false]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈意外脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 等等！不是现在……不是今天！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 121, "indent": 0, "parameters": [420, 420, 0]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈掩饰", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我刚做完仪式，太累了。我…我现在没力气做这个。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且我还跟桐人约好，等他加班完在游戏里陪他。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [5, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好吧……那什么时候？不会是最后一天吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 然后到时候再用别的借口推脱……"]}, {"code": 121, "indent": 0, "parameters": [420, 420, 1]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我是那种不守约定的人吗？既然答应了你，肯定会做到的。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [-1, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [-1, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那是什么时候？"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 8, false]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈失落脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 121, "indent": 0, "parameters": [420, 420, 0]}, {"code": 213, "indent": 0, "parameters": [4, 11, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈心事脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 明天……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 明天晚上。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 行。我永远都相信你。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 当然，就算被你骗，我也认了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过……"]}, {"code": 213, "indent": 0, "parameters": [4, 2, true]}, {"code": 213, "indent": 0, "parameters": [5, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在…能不能先给个预付款？"]}, {"code": 213, "indent": 0, "parameters": [4, 2, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 121, "indent": 0, "parameters": [420, 420, 1]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈失望脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 预付款？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我意思是口交！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 反正之前你就已经给我做过的……"]}, {"code": 213, "indent": 0, "parameters": [4, 11, true]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈凛然脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这方面你还真是一点都不客气啊……"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 121, "indent": 0, "parameters": [420, 420, 0]}, {"code": 213, "indent": 0, "parameters": [4, 5, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈生气脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 大色鬼！"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [5, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 反正以后再也见不到了，我肯定要尽量争取留下美好回忆的机会呀！"]}, {"code": 213, "indent": 0, "parameters": [4, 6, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜白衬衫单马尾像素", 1], "indent": null}]}, {"code": 121, "indent": 0, "parameters": [420, 420, 1]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈沉默脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过，我确实有点累，你那么持久，可能没办法支持到你射精……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没事的，按照你的节奏来就好，累了就休息会~"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但麻烦真的快点，时间长了下巴会痛的。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我尽量喽~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 快点来吧！"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [5]}, {"code": 235, "indent": 0, "parameters": [6]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 等待动画序列加载完成"]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 920, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [100, "711-口交-准备1", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [99, "711-口交-准备2", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [98, "711-口交-结束", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [89, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [90, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [91, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[89] : 设置动画序列 : 动画序列[30]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 设置动画序列 : 动画序列[30]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 设置动画序列 : 动画序列[32]"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 等待动画序列加载完成"]}, {"code": 241, "indent": 0, "parameters": [{"name": "魅惑の扉", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿，亚丝娜，开始吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现实里这还是头一次，啧…光想想就硬得不行了。"]}, {"code": 232, "indent": 0, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 10, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好啦，别催…唉……本来我还没有觉得尴尬的，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在都有些不好意思做了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊！怪我怪我！你就当我刚才什么都没说！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要紧张，慢慢来，放轻松。"]}, {"code": 232, "indent": 0, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 10, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我…我尽量吧……"]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, true]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[711酒店口交一]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘3", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 25, false]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈呣……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](怎么回事？含入的瞬间，下面有点…奇怪的感觉，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 像被轻轻弄着的酥麻感…我真是太下流了…）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 操，亚丝娜，你这小嘴…！现实里比游戏里还带劲儿！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 舌头热乎乎的，舔得我头皮都麻了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那个……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 蛋蛋也帮我舔一下，嘿嘿嘿~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嚤……"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 60, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[711酒店口交舔蛋]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "B_乳首を舐める2", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾啾…啧…别说那么下流的…嗯…比游戏里好吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啧啧…我…我技术有进步？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](他这么夸我…有点开心…下面那感觉…唔…也越来越…绵密了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好羞耻…也好兴奋……）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 何止进步！我爽得要飞了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾…飞什么飞…啧啧…别乱叫，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…我…我尽量让你舒服点。啾啾…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](自己也湿了…这感觉…像被插着…我怎么这么淫乱…可…停不下来…）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](亚丝娜，不要胡思乱想，专心让他赶紧射出来，早点结束！）"]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 十五分钟以后……"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[711酒店口交一]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘3", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，你这吸力，啧啧…比游戏里还强！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现实里你这小舌头一卷，我腿都软了，技术真是越来越牛了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾…啧…腿软…肉棒倒是硬的不行……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](下面…唔…像被抽插着…好羞愧…我居然觉得舒服……）"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我的心，现在跳得好快，就像在仪式的时候……）"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 播放简单状态元集合 : 集合[亚丝娜对面抱位快]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [24], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 40, "pitch": 110, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [24], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 40, "pitch": 110, "pan": 0}], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘3", "volume": 15, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 あんな喘ぎ13の２ 35 100 0"]}, {"code": 356, "indent": 0, "parameters": [">图片滤镜 : 图片[91] : 噪点滤镜 : 55 : 1"]}, {"code": 356, "indent": 0, "parameters": [">图片滤镜 : 图片[91] : 模糊滤镜 : 25 : 1"]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, -72, -50, 110, 110, 235, 0, 120, false]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 10 : 1"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 10 : 1"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊……嗯啊啊……猪田，我喜欢你是因为……那次在路边遇到坏人，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是你冲出来保护我…啊…尽管被打得浑身是伤…我非常非常感动……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯嗯没错！还有呢？再多说点效果应该会更好。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还有……你说过愿意为我改掉那些坏毛病……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac ……啊……我觉得你很用心……哈啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac ……你也喜欢我喜欢的料理和电影，每次聊起来我都很开心……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈，没错！我还特意为你学了按摩呢！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你对我的喜欢肯定不只这些，应该还有吧？"]}, {"code": 232, "indent": 0, "parameters": [89, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, true]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[89] : 播放简单状态元集合 : 集合[711酒店口交二]"]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…你特别会看我情绪…不开心的时候，总能哄我笑…哦……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还有，跟你一起很放松…啊啊…不用注意自己的言行……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这些也是很重要啦，但我觉得还漏了一样……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说说看！我们身体相性方面怎么？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜……还有……跟你做爱……真的很舒服……嗯……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 每次都让我很满足…哈啊……这些都是我喜欢的…原因……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, false]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘3", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我在瞎想什么？！都是这个臭肉棒的浓郁味道让我不正常了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 坏蛋猪田君明明答应我会快一点的，都十几分钟也没有射的意思！）"]}, {"code": 111, "indent": 0, "parameters": [0, 457, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 刚才…啾噜…为了让你保持状态……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 跟桐人电话…滋溜…都没有停……"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾啾…你快点好吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…我…我下巴有点酸了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这就酸了？亚丝娜你不要敷衍我好不好！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这还没多久啊！求求再坚持一下，不然我会下体爆炸的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾…下体爆炸…啾…别夸张啦…我再坚持一下…就是了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾啾…啧……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](他这么爽…我…我也有点开心…而且下面这感觉…让我变得好奇怪……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好想现在就和他做……）"]}, {"code": 108, "indent": 0, "parameters": ["插入仪式接吻片段"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 播放简单状态元集合 : 集合[亚丝娜仪式接吻三]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘3", "volume": 15, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 あんな喘ぎ10 35 100 0"]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, -72, -50, 110, 110, 235, 0, 120, false]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 10 : 1"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 10 : 1"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾…嗯…猪田，啧…别吸那么响……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啧啧…啾…吸响才爽啊，舌头伸出来点！ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾…啧…好啦…嗯…\\i[90] "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾啾…啧…你这舌头真会舔！ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾…别说那么下流…啧啧…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啧…啾啾…啧啧…和我的舌头缠一起~ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾…嗯…缠一起…啧…好羞人啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我也有点晕乎乎的……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啧啧…啾…晕乎乎就对了，啾啾…好爽……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾…嗯…不行…啧啧…我…我喘不过气了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](为什么...接吻居然是如此舒服的事吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 和桐人的时候都没有觉得这样兴奋……)"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 0, 0, 120, false]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘3", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我…我不会真对他……）"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[711酒店口交快]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘6", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我…我不能这么想…不能…）"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，你这技术简直无敌！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 舔得我…操，爽得想表白了！亚丝娜，我爱你！"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾…别喊…啧啧…我…我听到就行了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…啾啾…你…你快点结束吧…"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](他喊我名字…心跳得好快…下面…也更有感觉…"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我真的…对他…不，不可能！）"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜！我爱你！我要射了！"]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 0, 0, 10, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放动作 : 动作元[711酒店口交颜射]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 230, "indent": 0, "parameters": [144]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精･外･短い6", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [36]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精･外･短い6", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [99, "711-口交-结束", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [152]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，你的小嘴太厉害了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我都爽到外天空去了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](猪田射了好多…在我的脸上……气味…好浓……还有一些进到了嘴巴……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 下面…也还在轻微抽动…那瞬间…我差点觉得……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](不，不能再乱想……我…我还是爱桐人的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 只要隔离结束，就要永远和猪田君…分开……）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别…别说那么多了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 下巴好酸，我…我要去洗澡休息了。"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [89]}, {"code": 235, "indent": 0, "parameters": [90]}, {"code": 235, "indent": 0, "parameters": [91]}, {"code": 235, "indent": 0, "parameters": [98]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 235, "indent": 0, "parameters": [100]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 36, "indent": null}, {"code": 34, "indent": null}, {"code": 40, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 121, "indent": 0, "parameters": [920, 920, 1]}, {"code": 121, "indent": 0, "parameters": [300, 300, 1]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 0]}, {"code": 201, "indent": 0, "parameters": [0, 81, 3, 6, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 11, "y": 7}, {"id": 20, "name": "EV020", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 926, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 121, "indent": 0, "parameters": [999, 999, 1]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [90, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [91, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 设置动画序列 : 动画序列[33]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 设置动画序列 : 动画序列[33]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [4, 0, 16, 15, 6]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 1], "indent": 0}, {"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 1], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": 0}]}, {"code": 203, "indent": 0, "parameters": [5, 0, 15, 15, 6]}, {"code": 203, "indent": 0, "parameters": [13, 0, 15, 17, 0]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[13] : 像素偏移[0,-3] : 时间[1]"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": 0}, {"code": 3, "indent": null}, {"code": 27, "parameters": [999], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 27, "parameters": [999], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 15, "parameters": [55], "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [30], "indent": 0}, {"code": 3, "indent": 0}, {"code": 15, "parameters": [30], "indent": 0}, {"code": 3, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [55], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 等待动画序列加载完成"]}, {"code": 241, "indent": 0, "parameters": [{"name": "<PERSON><PERSON><PERSON>'s Theme", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 15, true]}, {"code": 213, "indent": 0, "parameters": [5, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 拜托拜托啦！就穿这一次好不好？"]}, {"code": 213, "indent": 0, "parameters": [4, 7, true]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 8]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别老是提这些羞耻的要求好不好？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我已经……已经够配合你的了！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 999, 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [999, 999, 1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你确实很配合我了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可这种程度，只算是额外再加点小情趣啦！"]}, {"code": 213, "indent": 0, "parameters": [4, 5, true]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 1], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈凛然脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我不！谁要和你这个色鬼搞情趣。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哎呀！搞情趣有什么不好？兔女郎骑在鸡巴动腰，多刺激，多过瘾！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 会让我的鸡巴超硬的！说不定还会更粗！不想试试嘛？"]}, {"code": 213, "indent": 0, "parameters": [4, 11, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈掩饰", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我…我才不想！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真的吗？一点也不想？"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真，真的，一点也不！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我不信！这不可能！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 明明我只是想一想都快要鸡巴爆炸了！"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 37, "indent": null}, {"code": 15, "parameters": [50], "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [50], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈凛然脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我又不是像你一样的变态，当然不会。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 做那种事情的时候穿什么，对我而言……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 都不会有任何特别的感觉。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好可疑啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 要不这样，为了证明你确实不想，你穿上我们做一会，试试就知道了。"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈批评", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 试试就试试……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈鄙夷", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哼！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你以为我会这么说吗？试你个鬼啊！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啧，暴露了吗？"]}, {"code": 213, "indent": 0, "parameters": [4, 5, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 喂，你是不是把我当笨蛋？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 12, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没有没有，笨的是我，但是求求了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们是炮友嘛，没必要害羞的。"]}, {"code": 213, "indent": 0, "parameters": [4, 11, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要以为我答应了当你的…炮友，你就可以为所欲为！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你之前不是说，就算穿了，对你来说也没什么特别的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没有特别，就是不好也不坏。既然不坏，那就穿嘛！"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就当是对我白天表现得那么老实的奖励！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你叫我不骚扰你，我就停了，对不对？"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [5, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 反正也没有几天了，你要是不答应，我这几天都会来求你！"]}, {"code": 213, "indent": 0, "parameters": [4, 6, true]}, {"code": 213, "indent": 0, "parameters": [4, 11, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 1], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嚤……好吧好吧，就这一次，真的是要被你烦死。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 3, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好耶！"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [4, 0, 23, 12, 8]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜兔女郎", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜兔女郎", 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 203, "indent": 0, "parameters": [5, 0, 20, 12, 6]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["猪头裸", 0], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["猪头裸", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 213, "indent": 0, "parameters": [4, 8, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [5, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哇！你这兔女郎装，简直要了我的命！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那腰，那腿，还有这兔尾巴，啧啧，骚得冒泡！"]}, {"code": 213, "indent": 0, "parameters": [4, 11, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 9]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈掩饰", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田，你、你别一直盯着看！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好羞耻的……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 行，我不盯着看……"]}, {"code": 213, "indent": 0, "parameters": [5, 9, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊对了！你腿和屁股还疼吗？"]}, {"code": 213, "indent": 0, "parameters": [4, 5, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哼！现在想起来关心我了？"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈感动", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还有一点点吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但泡完澡以后就好很多了，不影响那个……"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[0,-24] : 时间[30]"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[6,-36] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [1, 0], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [1, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac OK！OK！OK！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那就赶紧骑到我鸡巴上，蹦起来！"]}, {"code": 213, "indent": 0, "parameters": [4, 7, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别、别说这种恶心的话！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](又得意忘形……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我得保持点矜持，不能让他太嚣张。)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[0,-12] : 时间[120]"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈心事脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我坐上来的时候，你、你别乱动……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没问题！快来吧！"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 等待动画序列加载完成"]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 926, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [90, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [91, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 设置动画序列 : 动画序列[33]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 设置动画序列 : 动画序列[33]"]}, {"code": 231, "indent": 0, "parameters": [1, "713-亚丝娜兔女郎骑乘事前-1", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [2, "713-亚丝娜兔女郎骑乘事前-2", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 等待动画序列加载完成"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "bensound-sexy", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿，害羞的小兔子真可爱！来吧，坐上来，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田哥哥的大萝卜早就饥渴难耐了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](这家伙真是一点脸都不要！但他的肉棒……确实好硬啊，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 隔着安全套都能感觉到那股劲……冷静，亚丝娜，冷静。)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别、别说什么“猪田哥哥”的……恶心死了……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊……啊啊……"]}, {"code": 121, "indent": 0, "parameters": [999, 999, 1]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对，就这样慢慢沉下去……呼……亚丝娜的里面……好紧……"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 999, 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [999, 999, 1]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦吼吼，爽啊~ 兔子小姐呢？感觉怎么样？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是不是很喜欢我的大肉棒，是不是一插进去就想高潮了？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 才没有…哈啊…你不要胡说，还有……啊啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 刚才插入的时候…为、为什么突然顶上来…嗯…不是说好不乱动吗…？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我没有啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是你自己没控制好坐下的力度而已~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 骗人……！嗯啊……明明是你……哈啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要在意这种小事了，专心做爱嘛！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看！兔子小姐的淫水……把套子都浸透了呢……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别说了……！嗯嗯……啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田哥哥的大胡萝卜棒好吃吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 蹦起来呀！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……你、你别催，我……我得慢慢来……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这、这太羞耻了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 慢慢来？哈哈，兔子小姐得卖力讨好客人啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 喊句“猪田哥哥好棒”，我就不催你了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](讨好客人？把我当什么了？！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 气死我了……！)"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 播放简单状态元集合 : 集合[713兔女郎骑乘慢二]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-035喘ぎ中", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [36], "indent": null}, {"code": 44, "parameters": [{"name": "fst_sand_light_walk_001", "volume": 25, "pitch": 70, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [36], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "fst_sand_light_walk_001", "volume": 25, "pitch": 70, "pan": 0}], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你、你闭嘴！别老说这些下流话！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……哈……我才不会喊那种话！别、别太过分！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](虽然嘴上这么说，但我的身体却在迎合他……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人，对不起，和这家伙在一起，我就是…控制不住自己……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 来嘛！之前游戏里更淫乱的话不都说过的？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 骚兔子骑得这么欢，爽不爽？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](呜…确实好舒服，比和桐人在一起时……不，不能想这个！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但他的节奏，力度，硬度……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不…不要你管…嗯嗯……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说出来说出来！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说“猪田哥哥操得我好爽”！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](喊那种话？绝对不行……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 一旦说了的话我又要……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不、不行……我不会说那种话的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……啊……猪田，你、你别说了……我、我受不了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 受不了那就再骚点！你是我的专属兔子小姐，给我蹦得更浪！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 一边吃哥哥的大胡萝卜，一边给我喊“猪田哥哥的大鸡巴好厉害”！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](为什么……每次都会这样……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 只要涉及到做爱我从来没有赢过猪田……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210]( 那种感觉……我明明讨厌，却又好想要……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 喊出来吗？不，我不能……但身体已经……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊！我……我不会喊的……嗯……\\i[90]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[713兔女郎骑乘快]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-046喘ぎ特大", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [28], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 120, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [28], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 120, "pan": 0}], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看来还是我不够努力，没办法了，只能进攻兔子小姐的弱点了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊、哈啊………好、好厉害……噢噢~~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 厉害吧？那就再来！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 兔子小姐，给我卖力点，摇你的骚穴，夹紧点！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……啊……猪田……那里……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好舒服……啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿，舒服就好！你是我的骚兔子！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 腰动得再快点，叫得再浪点，这样才会更舒服！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](那种事不可以…但我的身体确实…已经完全被他掌控了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我…我真的好喜欢这种感觉……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊！猪田，你……你太坏了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……哈……我、我……好、好舒服……猪田……是你、你赢了……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说什么胡话，你早就是我的了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 骚兔子，说，“猪田哥哥操得我好爽”！快，喊！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我真的要喊了吗？不，我不能……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但这快感，这羞耻，这一切……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](这，这只是几天的约定罢了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人，等这结束之后我一定……)"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 播放简单状态元集合 : 集合[713兔女郎骑乘慢三]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-027事後呼气-特大", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [76], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 80, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [76], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 80, "pan": 0}], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……啊……猪田……我……猪田哥哥……操、操得我好爽……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈哈，你终于喊了！我的专属骚兔子！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我真的说出来了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好羞耻，但是真的好舒服……\\i[90])"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这就对了！再喊三遍！  "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说\"猪田哥哥的精液灌满子宫好舒服\"！  "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯啊……猪田哥哥的精液……啊啊……不！才不要…！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…这种变态的话…而且我…啊啊…绝对不会…让你无套的……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那么认真干嘛？套子好好带着呢~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 要是真射进去，兔子小姐的子宫会被灌成泡芙吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嚤！你...哈啊…戴着套…还说这种…嗯嗯…下流话……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是…干嘛…啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 想象一下嘛！滚烫的精液噗噜噗噜冲进亚丝娜的子宫，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 黏糊糊地把输卵管都填满~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呀嗯！\\i[90] 别…别说了…啊嗯…\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 会真的…哈啊…有感觉…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看啊，骚兔子的小穴在自动榨精呢！也对！兔子的特长就是繁殖呢~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 要是现在撕开套子，噗咻地射满子宫……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](他的下流话让我更兴奋了，我怎么会变得这么淫荡……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](现在脑海里，都是在FOG被滚烫精液灌满的回忆……好羞耻……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但是子宫真的好痒，好想现在就让他把套子摘了直接射进来…！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…哈啊…！\\i[90] 不要…说出来…啊啊…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘴上说不要，橡胶套都被你夹破了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这么想要哥哥的浓精？嗯？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 叽咕…！\\i[90]才没有…哈啊…\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 只是……"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[713兔女郎骑乘快]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-046喘ぎ特大", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [28], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 120, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [28], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 120, "pan": 0}], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…！嗯嗯…摩擦得好厉害……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 兔子小姐太骚了嘛~ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦…啊…好舒服……啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿，兔子小姐，别光顾着舒服呀！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪先生要噗嗤直接射进你的骚母兔的子宫角了哦！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊！这样…绝对…不可以…！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不可以什么呀？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不可以…射进来……呀啊！\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们之前说好的…嗯…不带套…射外面…啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿，终于上道了，是不是很过瘾~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 继续说！老子就是要把种汁都灌进去！"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 播放简单状态元集合 : 集合[713兔女郎骑乘慢二]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-035喘ぎ中", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [36], "indent": null}, {"code": 44, "parameters": [{"name": "fst_sand_light_walk_001", "volume": 25, "pitch": 70, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [36], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "fst_sand_light_walk_001", "volume": 25, "pitch": 70, "pan": 0}], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不！虽然……啊啊啊……\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但是…不行…会怀孕的……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怀上我的种多好！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔…啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 结城家又不是养不起？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不是…呀啊…这个问题……啊啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那是啥问题？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 到时候你还可以每天挺着大奶子给我喂奶……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔……"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[713兔女郎骑乘快]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-046喘ぎ特大", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [28], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 120, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [28], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 120, "pan": 0}], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呃啊！越想越兴奋！自己给自己挖坑！我快不行了……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊！真的不行了！啊啊啊！！！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…又要去了……"]}, {"code": 242, "indent": 0, "parameters": [6]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 播放动作 : 动作元[713兔女郎骑乘射精]"]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-099絶頂アヘオホ", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [20], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 140, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [20], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 140, "pan": 0}], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 230, "indent": 0, "parameters": [360]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[713兔女郎骑乘事后]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精音・中出し（短い）", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [92]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精音・中出し（短い）", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [130]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 0, 0, 1, false]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [91]}, {"code": 230, "indent": 0, "parameters": [18]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [42]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-024事後呼气-中", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 241, "indent": 0, "parameters": [{"name": "<PERSON><PERSON><PERSON>'s Theme", "volume": 10, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我又一次没控制住自己…桐人对不起……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可每次结束后，心里却这么满足。真可悲啊，这样的我……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](更可悲的是……是连这份悔恨都开始令我作呕了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真恶心啊，不断轮回的赎罪表演，一边忏悔一边享受的…卑劣的我……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](如果我不是结城明日奈，不是闪光的亚丝娜，不是桐人君的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 引以为傲的恋人……是不是……就能原谅这样的我了呢？)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿，亚丝娜，你在想什么呢？在回味刚才的感觉吧~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 一想到回东京就享受不到了，不舍得和我分开吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别、别乱说…我根本…没什么好想的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你别……自作多情……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗨！你这个反应就是被我说中了！先别想以后的事嘛！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 何必给自己那么多负担？享受当下不好吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](享受当下…说的好像很简单……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那以后呢？等回到东京……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac别说了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我…我累了，想休息一下……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好，好，你休息吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 晚安，明天我们再一起玩哦。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…晚安……"]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [90]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 36, "indent": null}, {"code": 34, "indent": null}, {"code": 40, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 121, "indent": 0, "parameters": [926, 926, 1]}, {"code": 121, "indent": 0, "parameters": [300, 300, 1]}, {"code": 201, "indent": 0, "parameters": [0, 81, 12, 6, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 926, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 926, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 设置动画序列 : 动画序列[33]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放动作 : 动作元[713兔女郎骑乘插入]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[713兔女郎骑乘慢一]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-020呼气-中", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 230, "indent": 0, "parameters": [184]}, {"code": 250, "indent": 0, "parameters": [{"name": "挿入する時の音3（勢いよく一気に入れる）", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "AY-118呃呃啊", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [88]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-033喘ぎ中", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [20], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 45, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [64], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 45, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [40], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [20], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 45, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [64], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 45, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [40], "indent": null}]}, {"code": 121, "indent": 0, "parameters": [999, 999, 0]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 12, "y": 7}, {"id": 21, "name": "EV021", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 927, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [90, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [91, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 设置动画序列 : 动画序列[33]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 设置动画序列 : 动画序列[33]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [4, 0, 22, 16, 2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": 0}, {"code": 41, "parameters": ["亚丝娜清凉夏装", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 1], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[0,30] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [5, 0, 17, 14, 6]}, {"code": 203, "indent": 0, "parameters": [13, 0, 15, 17, 0]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[13] : 像素偏移[0,-3] : 时间[1]"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 等待动画序列加载完成"]}, {"code": 241, "indent": 0, "parameters": [{"name": "<PERSON><PERSON><PERSON>'s Theme", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [4, 3, true]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 8]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈叹气", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咻……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": 0}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[0,0] : 时间[3]"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 35, "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [5, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 工作忙完了？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那个…那个……"]}, {"code": 213, "indent": 0, "parameters": [4, 7, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈鄙夷", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别吞吞吐吐地浪费时间了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直说吧，今晚你又有什么要求？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 2, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你怎么知道我要提要求？"]}, {"code": 213, "indent": 0, "parameters": [4, 6, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个语气，加上你那一脸傻样，我还不知道你？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 3, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿嘿嘿，老婆，你真懂我~"]}, {"code": 213, "indent": 0, "parameters": [4, 11, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 谁、谁是你老婆？！不许乱说！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 小心我再也不理你了！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 5, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](切！不愿意当我老婆，那就当我的性奴……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗨！还是老婆吧，再给你一次机会。)"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是是是。本来还想今天晚上玩情侣play的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但你这么抗拒，那就换一个，穿女仆装吧~"]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈意外脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac女仆装？你还真会挑……非要这样吗？"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈失落脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我也是变得奇怪了，像这种过分的要求，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直接拒绝不就好了……)"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 昨天兔子装你都穿了，如果酒吧提供这种服务，怎么样也是成年人限定进入。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而女仆装，一些咖啡店的服务生都会穿的呀，有什么关系嘛！"]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [4, 11, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈心事脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 行吧……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 35, "indent": null}, {"code": 14, "parameters": [-1, 0], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 14, "parameters": [-1, 0], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [-1, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [-1, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 太好了！快去换装吧！"]}, {"code": 213, "indent": 0, "parameters": [4, 6, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [85]}, {"code": 213, "indent": 0, "parameters": [5, 9, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 另外啊……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我突然想到的：女仆可是神圣的职业！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 既然扮成女仆，也要遵守女仆的规则才行！"]}, {"code": 213, "indent": 0, "parameters": [4, 2, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈欲言又止", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 什么规则？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 3, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 很简单！第一，要对我保持微笑。第二，要听我的命令。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 第三，也是最重要的，要用那种可爱的声音叫我主人……"]}, {"code": 213, "indent": 0, "parameters": [4, 7, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈凛然脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 果然又是这种的变态要求……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": 0}]}, {"code": 213, "indent": 0, "parameters": [5, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这也变态？最基础的要求了！"]}, {"code": 213, "indent": 0, "parameters": [4, 11, false]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac “主人”什么的，太羞耻了……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这有什么羞耻的？女仆是高尚的职业！是奉献精神的化身！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac “主人”只不过是一种尊称，是利他主义的体现！"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你觉得羞耻，是刻板印象！是歧视！"]}, {"code": 213, "indent": 0, "parameters": [4, 12, false]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈焦急脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我…！不是…！那个…！"]}, {"code": 213, "indent": 0, "parameters": [4, 6, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈叹气脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唉……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说不过你这套歪理。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不是歪理，是道理！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 既然说不过我，那就该听我的！"]}, {"code": 213, "indent": 0, "parameters": [4, 7, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜清凉夏装", 0], "indent": 0}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 15, "parameters": [45], "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [45], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈妥协", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好吧好吧好吧，随你了……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [4, 9, true]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但你的命令也不能太过分！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没问题！而且我保证今晚会很有趣的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 赶紧换衣服吧~"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [4, 0, 23, 12, 8]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜女仆装", 1], "indent": null}, {"code": 41, "parameters": ["亚丝娜女仆装", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜女仆装", 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜女仆装", 0], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [5, 0, 22, 15, 8]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "volume": 90, "pitch": 130, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move1", "volume": 90, "pitch": 110, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 2, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 11, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [5, 4, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 太美了太美了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不愧是本老爷花大价钱买来的顶级女仆~"]}, {"code": 213, "indent": 0, "parameters": [4, 6, true]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 10]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈心事脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](还什么本老爷……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这家伙也入戏太深了。)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还愣着干什么？快过来翘起屁股对着我，准备开始侍寝！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 动作要优雅，懂吗？"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [4, 7, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈欲言又止", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真的要做到这种程度吗……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那个…我们普通一点不行吗？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 5, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你这是什么态度？忘了自己的身份吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你是我的女仆！太怠慢了吧！叫我‘主人’，重来！"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜女仆装", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜女仆装", 1], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](猪田君一进入这个状态下就会特别强硬……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不好好满足他的话……)"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜女仆装", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜女仆装", 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 7, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈失望脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好……主人，别生气，我知道了。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [5, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿，这才像话~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 快过来吧！"]}, {"code": 213, "indent": 0, "parameters": [4, 11, true]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 15, "parameters": [20], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(5).turnTowardCharacter($gameMap.event(4));"], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [20], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(5).turnTowardCharacter($gameMap.event(4));"], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 2, "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 等待动画序列加载完成"]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 927, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [90, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [91, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 设置动画序列 : 动画序列[33]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 设置动画序列 : 动画序列[33]"]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[714女仆后入待机]"]}, {"code": 241, "indent": 0, "parameters": [{"name": "bensound-sexy", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "U_9_吐息2_01", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 为什么…不插进来……？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯？你是不是忘了什么？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诶？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对本老爷说‘请主人享用您的忠诚女仆’，要诚心诚意！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](真的要配合他说那种羞耻的话吗…？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可我…只是听他的话就让我的身体这么热，难道我也喜欢这种……？)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](不……不对，这只是因为太羞耻了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 并不是我在期待什么……嗯没错就是这样……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么了？犹豫啥？又不听话了？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唉……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 请…主人享用您的…忠诚女仆……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 很好，亚丝娜，你终于有点女仆的样子了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 继续保持，本主人这就来好好赏赐你~"]}, {"code": 121, "indent": 0, "parameters": [999, 999, 1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 设置动画序列 : 动画序列[33]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放动作 : 动作元[714女仆后入插入]"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯呃…哈啊…哈啊……咯呃…哈啊……\\i[90]\\|\\|\\|\\^"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 999, 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [999, 999, 1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不…不要一开始……啊啊………"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就这么快…呀啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿，这算这什么？我可以这样操一晚上！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…嗯…啊啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿嘿，爽啊~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…不…啊啊…慢点……"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 十分钟后……"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-033喘ぎ中", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[714女仆后入男动慢一]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [28], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}, {"code": 15, "parameters": [60], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}, {"code": 15, "parameters": [32], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [28], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [32], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我操！现实里这样挺着腰后入这尼玛累！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 一只脚站地累，跪着也累，妈的……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](诶！不对呀！我是老爷啊！怎么一直都是我出力？)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 喂喂喂！怎么从开始到现在就只是顾着自己爽？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 作为女仆，难道不该关心主人满不满意吗？"]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-033喘ぎ中", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 播放简单状态元集合 : 集合[714女仆后入男动慢二]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [28], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}, {"code": 15, "parameters": [60], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}, {"code": 15, "parameters": [32], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [28], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [32], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 255, 0, 10, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…啊……你这…家伙……嗯…好吧……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…嗯……主人…我的…表现…你还…满意…吗？\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不满意！一直都是我在动腰，搞得像是主人我在伺候你！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你就不能主动一点吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…那…主人…您…要…我怎么…主动…嗯…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这也要我教，你自己动屁股可以的吧？别说做不到！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 白天你做瑜伽的时候，可以扭得很起劲呢~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这…啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要忘记你作为女仆的职责！"]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-033喘ぎ中", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[714女仆后入男动慢一]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [28], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}, {"code": 15, "parameters": [60], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}, {"code": 15, "parameters": [32], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [28], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [32], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 0, 0, 10, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好，好吧…哈啊…我试试吧……"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-031喘ぎ小", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 播放简单状态元集合 : 集合[714女仆后入女动慢一]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [8], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 120, "pan": 0}], "indent": null}, {"code": 15, "parameters": [32], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 120, "pan": 0}], "indent": null}, {"code": 15, "parameters": [32], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [8], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 120, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [32], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 120, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [32], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦~ 啊~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…啊…猪…主人…我…这样…动…您…舒服…吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈，亚丝娜，你这小腰扭得不错！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 想到你白天犯的错误，主人我呀，就是一肚子的气！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 白…天……？哈啊…你在…说…什么？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 今天早上的早安口交居然忘记做了，没能让主人在射精中舒畅的醒来，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 作为女仆，这工作态度可不行！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…那种事？…从来就没有吧？…我…才不…会做…这些…嗯啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 除此之外，今天的茶杯也摆得乱七八糟，地板也完全没扫干净。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真是让主人我失望。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我……没有…做……哈啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 什么？你连自己犯的错都不记得？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你这女仆当得也太失职了吧！快给我好好反省！接下来我要好好惩罚你！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯啊啊……主人…我…错…了…啊…茶杯…是我…摆歪…的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…对…对不起……呀…啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哼，知错就好！还有，你叠的被子，皱得跟狗窝似的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 本主人看了就来气！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…被子？…嗯…抱歉…主人…是我…叠得…不好…"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 您…说…怎么…罚…就…怎么…罚…哈啊…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 为家务事罚你，不不不。这些都是小问题。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但你服侍本主人的时候，态度也不够用心！这个就完全不行了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…诶…？…我…不知道…嗯啊…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 今早你忘记侍奉的事就算了，昨晚你伺候我的时候，动作那么敷衍，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 完全感受不到你的爱意！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…敷衍什么的……我没有……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯啊…哈啊…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](又被他牵着鼻子走了……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还敢顶嘴？作为女仆，伺候主人的每一处都要用心！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 比如……你都没好好舔本老爷的菊花，那可是女仆的职责之一！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…？！…猪田…主人…你…在…说…什么…啊…？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那…是…女仆…该…干的…事…吗…嗯…？"]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 255, 0, 10, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](这，这家伙脑袋坏掉了吧？冷静，明日奈，这应该只是演戏？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不会是真的想我舔那种地方吧……我绝对不会接受的！)"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-044喘ぎ特大", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[714女仆后入男动扣手]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [4], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 130, "pan": 0}], "indent": 0}, {"code": 15, "parameters": [28], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 130, "pan": 0}], "indent": null}, {"code": 15, "parameters": [28], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 130, "pan": 0}], "indent": null}, {"code": 15, "parameters": [16], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 130, "pan": 0}], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [28], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 130, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [28], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 130, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [16], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 0, 0, 10, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嚯啦嚯啦嚯啦！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 等，等下，太快了……我还没准备好…啊啊……\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哼，你这女仆胆子不小，竟敢拒绝老爷！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看我怎么教训你这不听话的小东西！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你看那些顶级的女仆，哪一个不是全心全意服侍主人？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜，你得学着点！现在，承认错误，说你会改！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](不行…理智又快要飞走了……暂时敷衍他吧……只，只是演戏而已……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 反正……我是绝对不可能真的接受这种变态要求的……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我……嗯…好吧…主人…我…承认…啊…服侍…得…不够…用心…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这就对了！说，‘您的女仆会用心服侍主人的后庭’！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 快点，本主人听着呢！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…您…的…女仆……会…用心…服侍…主人…的…后庭…嗯…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 很好，亚丝娜，你终于有点女仆的自觉了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过，光嘴上说可不行，明天晚上，你得用行动证明你的忠诚！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…可…可是……那种事情……我不知道要怎么……嗯啊……主人…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我一定是脑子不正常了，居然没有一口拒绝他……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呵呵，不用急，明天我会教你的。哦~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你的名器小穴，紧得跟吸盘似的，真是爽啊！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…名器…什么…啊…主人…别…乱说…嗯啊啊…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](这家伙又开始得意忘形了…"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可我……竟然有点喜欢听他夸我……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 乱说？本主人说的是实话！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说说看，我的肉棒怎么样？给主人评评！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…嗯…主人的…肉棒…很粗…又长…哈啊…"]}, {"code": 401, "indent": 0, "parameters": ["\\dac ……硬度也很……厉害…嗯…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦？还有呢？继续说！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…很…舒服…顶到…子宫口…也不…痛…嗯…\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还有…那凸起…正好…刺激…我…敏感的…地方…哈啊…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](我怎么把这些都说出来了…羞死人了！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈哈！亚丝娜，你这评价主人爱听！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么样，想不想更直接感受本主人的肉棒？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…更…直接？…主人…你…什么…意思…啊…？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿，就是摘下这碍事的套子，肉贴肉直接感受！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 让你的小穴能够再次直接感受主人的厉害！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…不…不行！…绝对…不能…无套…嗯…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](无套？！这家伙疯了吧！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啧，你敢拒绝主人的命令？真是不知好歹的女仆，算了，你想想，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 肉贴肉的感觉肯定更舒服一百倍！不用担心怀上，主人我会射在外面的。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](更舒服……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不行不行！我不能答应，不能再进一步对不起桐人了)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…主人…别…说了…我…不…同意…嗯…\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这…不能…妥协…啊啊啊…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还嘴硬？作为女仆，服从主人是天职！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以……再考虑考虑嘛，嗯？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…不…行…主人…我……绝对…啊…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](都说不行了！他为什么要一直问啊…我心跳得好快……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我不能！不能松口！如果又一次跨过那条线，我恐怕真的会……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啧……"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 播放简单状态元集合 : 集合[714女仆后入男动慢一]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [28], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}, {"code": 15, "parameters": [60], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}, {"code": 15, "parameters": [32], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [28], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [32], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-033喘ぎ中", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你这女仆还真倔……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 再问一次，真的不行？"]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-033喘ぎ中", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[714女仆后入男动慢二]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [28], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}, {"code": 15, "parameters": [60], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}, {"code": 15, "parameters": [32], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [28], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [32], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 0, 0, 10, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…真…的…不行…主人…不要…再…问…了…呜嗯…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好吧好吧，你赢了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 本主人大人有大量，不跟你计较！"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 播放简单状态元集合 : 集合[714女仆后入男动慢一]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [28], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}, {"code": 15, "parameters": [60], "indent": null}, {"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}, {"code": 15, "parameters": [32], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [28], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "P_singlepiston03", "volume": 35, "pitch": 50, "pan": 0}], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [32], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-033喘ぎ中", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…谢…谢…主人…啊啊…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](终于放弃了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 他如果再多问几遍的话……)"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[714女仆后入男动快]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [4], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 110, "pan": 0}], "indent": null}, {"code": 15, "parameters": [32], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 110, "pan": 0}], "indent": null}, {"code": 15, "parameters": [28], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 110, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [32], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 110, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [28], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-037喘ぎ大", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 0, 0, 10, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这有什么好谢的？妈的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 都怪我太喜欢你了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…哈啊…猪田…主人……嗯…啊啊…这样不行…啊…\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](又开始冲刺了，就是这个……好……好厉害……桐人君完全做不到的……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈，求饶了？现在知道主人的厉害了吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 女仆，你这反应主人我可太喜欢了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…啊啊…先停一下……主人…我…快要…啊……饶了我……嗯…\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](好舒服…不想停……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 一直继续才好……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 想让老爷饶你？哼，那得看你表现！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说，你要怎么服侍主人？快说！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…好…我…服侍…主人…嗯…怎么…都行…啊啊…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦？那好，女仆，明天你得用心舔主人的后庭！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 答应了，我就考虑放过你！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…嗯……好…我…答应……啊啊…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈哈！亚丝娜，你这女仆总算开窍了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 主人很满意，表现不错，可以饶你一回！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过，女仆，你真想让主人慢下来？还是停下来？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊…不…不要…停…嗯…我…快…到了…哈啊…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿，这就对了！主人也不想忍了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 女仆，咱们一起高潮吧！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…啊啊…好…主人…一起…啊啊…哈啊…\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 妈的！要是没带套了就好了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 要射了！！！"]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-044喘ぎ特大", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 播放动作 : 动作元[714女仆后入射精]"]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 255, 0, 10, false]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 150, "pan": 0}], "indent": null}, {"code": 15, "parameters": [16], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 150, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [16], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [136]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 0, 0, 1, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 230, "indent": 0, "parameters": [36]}, {"code": 250, "indent": 0, "parameters": [{"name": "插入音", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [12]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "いき3", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [84]}, {"code": 250, "indent": 0, "parameters": [{"name": "插入音", "volume": 15, "pitch": 120, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [8]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [36]}, {"code": 250, "indent": 0, "parameters": [{"name": "插入音", "volume": 15, "pitch": 120, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [8]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[714女仆后入事后]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-023事後呼气-小", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唉……刚才如果直接内射多爽啊？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就不能让我无套一次吗？"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊…以后…的事……哈啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 以后……再说吧……"]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [90]}, {"code": 235, "indent": 0, "parameters": [91]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 36, "indent": null}, {"code": 34, "indent": null}, {"code": 40, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 121, "indent": 0, "parameters": [926, 926, 1]}, {"code": 121, "indent": 0, "parameters": [300, 300, 1]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 81, 12, 6, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 927, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "挿入する時の音1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 250, "indent": 0, "parameters": [{"name": "あえぎ2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [85]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[714女仆后入男动快]"]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 245, "indent": 0, "parameters": [{"name": "AY-037喘ぎ大", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [4], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 110, "pan": 0}], "indent": null}, {"code": 15, "parameters": [32], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 110, "pan": 0}], "indent": null}, {"code": 15, "parameters": [28], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 110, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [32], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 110, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [28], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [90, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 121, "indent": 0, "parameters": [999, 999, 0]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 927, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["var waitFrames = Math.floor(Math.random() * 240) + 120;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[91] : 播放简单状态元集合 : 集合[714女仆后入女动俯视]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [4], "indent": 0}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 120, "pan": 0}], "indent": null}, {"code": 15, "parameters": [40], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 120, "pan": 0}], "indent": null}, {"code": 15, "parameters": [28], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 120, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [40], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 120, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [28], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 355, "indent": 0, "parameters": ["var waitFrames = Math.floor(Math.random() * 240) + 120;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[90] : 播放简单状态元集合 : 集合[714女仆后入女动慢一]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [8], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 120, "pan": 0}], "indent": null}, {"code": 15, "parameters": [32], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 120, "pan": 0}], "indent": null}, {"code": 15, "parameters": [32], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [8], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 120, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [32], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 120, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [32], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [91, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 13, "y": 7}, null, null]}