{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "Grassland", "battleback2Name": "<PERSON>", "bgm": {"name": "wonderland_Meadow in the Forest", "pan": 0, "pitch": 100, "volume": 70}, "bgs": {"name": "River", "pan": 0, "pitch": 100, "volume": 25}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 23, "note": "", "parallaxLoopX": true, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 1, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 57, "width": 24, "data": [1573, 1577, 2483, 1575, 1568, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1570, 2832, 2840, 1568, 1569, 2483, 1570, 1568, 1569, 1569, 1569, 1595, 2836, 2852, 2080, 2862, 1573, 1577, 1577, 1612, 1577, 1577, 1577, 1577, 1575, 2832, 2840, 1573, 1577, 2483, 1575, 1568, 1569, 1569, 1569, 1569, 2844, 2841, 2089, 2070, 2084, 2850, 2836, 2837, 2861, 2906, 3006, 2958, 2850, 2817, 2818, 2836, 2852, 2080, 2862, 1573, 1577, 1577, 1612, 1577, 3236, 2860, 2910, 2088, 2086, 2832, 2820, 2854, 2907, 2903, 2858, 2910, 2832, 2820, 2844, 2844, 2854, 2065, 2084, 2859, 2849, 2849, 2861, 2082, 3230, 3245, 2858, 3234, 3236, 2856, 2854, 3234, 3220, 3236, 2857, 2838, 2817, 2840, 3243, 3245, 2082, 2049, 2050, 2068, 2068, 2068, 2068, 2049, 2897, 2901, 2860, 3240, 3230, 3233, 3233, 3229, 3228, 3210, 3236, 2832, 2816, 2818, 2852, 2082, 2049, 2048, 2048, 2048, 2048, 2048, 2048, 2048, 2852, 2883, 2909, 2851, 2849, 2849, 2849, 2861, 1590, 3240, 3225, 2856, 2844, 2824, 2840, 2064, 2048, 2048, 2048, 2048, 2048, 2048, 2052, 2076, 2854, 2908, 2859, 2855, 1590, 1566, 1566, 1566, 1567, 2862, 3217, 3236, 1557, 2856, 2854, 2088, 2060, 2076, 2076, 2076, 2076, 2076, 2086, 1590, 1566, 1561, 1566, 1566, 1567, 1569, 1569, 1569, 1570, 3243, 3229, 3225, 1565, 1566, 1589, 2862, 2080, 2859, 2861, 1590, 1566, 1566, 1566, 1567, 1569, 1569, 1569, 1569, 1570, 1569, 1594, 1569, 1570, 2850, 2852, 3232, 1568, 1569, 1565, 1566, 2080, 1566, 1566, 1567, 1569, 1569, 1569, 1570, 1569, 1569, 1569, 1569, 1570, 1569, 1592, 1569, 1570, 2834, 2854, 3244, 1568, 1593, 1568, 1569, 2483, 1569, 1569, 1570, 1569, 1594, 1569, 1570, 1569, 1569, 1569, 1569, 1570, 1577, 1577, 1577, 1575, 2848, 3242, 2858, 1568, 1569, 1568, 1569, 2483, 1569, 1569, 1570, 1569, 1592, 1569, 1570, 1605, 1577, 1577, 1577, 1575, 2090, 2850, 2836, 2836, 2842, 3244, 2860, 1576, 1577, 1568, 1569, 2483, 1569, 1569, 1570, 1577, 1577, 1577, 1575, 2884, 2884, 2884, 2884, 2900, 2092, 2832, 2816, 2816, 2822, 2861, 3235, 3245, 2090, 1573, 1605, 2483, 1577, 1577, 1575, 2090, 2850, 2836, 2836, 2864, 2864, 2864, 2864, 2888, 2851, 2845, 2844, 2844, 2854, 3235, 3239, 2858, 2065, 2068, 2068, 2051, 2068, 2068, 2069, 2087, 2832, 2820, 2844, 2892, 2892, 2892, 2892, 2902, 2848, 3242, 2910, 3235, 3233, 3239, 2850, 2842, 2088, 2076, 2056, 2048, 2048, 2048, 2072, 2859, 2845, 2854, 2082, 3233, 3233, 3245, 2851, 2849, 2855, 3241, 3233, 3239, 2850, 2837, 2845, 2854, 2955, 2957, 2088, 2076, 2076, 2056, 2050, 2068, 2068, 2068, 2049, 2849, 2849, 2849, 2855, 2907, 2897, 2909, 2850, 2836, 2817, 2840, 2898, 2900, 3243, 3233, 3233, 3233, 3245, 2088, 2076, 2056, 2052, 2076, 2076, 1566, 1566, 1566, 1566, 1589, 2859, 2849, 2845, 2844, 2844, 2854, 2904, 2902, 2859, 2849, 2849, 2849, 2849, 2861, 2910, 2088, 2086, 2859, 2849, 1569, 1569, 1569, 1569, 1565, 1566, 1566, 1566, 1566, 1566, 1566, 1566, 1566, 1566, 1566, 1566, 1566, 1566, 1566, 1566, 1566, 1566, 1566, 1566, 1569, 1569, 1594, 1569, 1568, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1605, 1569, 1569, 1568, 1569, 1595, 1569, 1592, 1569, 1594, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1595, 1569, 1569, 1569, 1595, 1569, 1577, 1613, 1577, 1577, 1568, 1569, 1592, 1569, 1569, 1569, 1569, 1593, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1592, 1569, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3456, 3464, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3480, 3478, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3870, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3099, 3101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3435, 3425, 3437, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 59, 175, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 158, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 166, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 159, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 0, 59, 0, 0, 0, 0, 280, 281, 282, 0, 0, 0, 38, 39, 0, 10, 0, 0, 37, 0, 0, 0, 147, 0, 0, 67, 0, 0, 159, 0, 288, 289, 290, 0, 0, 0, 46, 47, 0, 0, 0, 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 0, 296, 297, 298, 37, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 995, 0, 0, 0, 0, 46, 47, 0, 304, 305, 306, 45, 0, 0, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 288, 289, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 288, 289, 290, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 298, 0, 0, 0, 1, 0, 0, 170, 0, 0, 0, 296, 297, 298, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 306, 22, 0, 170, 0, 0, 0, 178, 0, 0, 0, 304, 305, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 178, 0, 0, 0, 0, 0, 7, 0, 0, 22, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 158, 0, 178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 280, 281, 282, 0, 0, 59, 0, 166, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 280, 281, 288, 289, 290, 0, 0, 67, 20, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 288, 289, 296, 297, 298, 0, 0, 37, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 20, 296, 297, 304, 305, 306, 0, 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 205, 0, 0, 0, 191, 0, 37, 0, 304, 305, 40, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 203, 0, 0, 0, 45, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 288, 289, 290, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 183, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 254, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 214}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,-3]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 45, "parameters": ["$gameMap.event(8).requestBalloon(3);"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 214}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,-3]"]}, {"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离8 : 触发独立开关 : A"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 214}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,-3]"]}, {"code": 213, "indent": 0, "parameters": [0, 3, false]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(1);"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(1).requestBalloon(1);"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).setDirection(8);"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(1).setDirection(8);"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<杂烩鸽>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咕咕！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 白色的鸽子，发出咕咕的声音，不会就是这个吧？"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<杂烩鸽>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咕咕！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不管是不是，先去把它抓起来再说！"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这也太快了吧！是不是要用些道具、魔法之类的？"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈苦笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要着急。似乎往山顶飞了，我们追上去吧。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 215]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 214}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 本事件 : 像素偏移[0,-48] : 时间[10]"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 7, "indent": null}, {"code": 7, "indent": 0}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 7, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 7, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 215]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 215}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 3}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 5, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 2, "y": 1}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 5, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 17, "y": 2}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 1, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 16, "y": 9}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 301, "indent": 0, "parameters": [0, 4, false, false]}, {"code": 212, "indent": 0, "parameters": [-1, 154, false]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 18, "indent": null}, {"code": 45, "parameters": ["$gameSelfSwitches.setValue([$gameMap.mapId(), this.eventId(), 'A'], true);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameSelfSwitches.setValue([$gameMap.mapId(), this.eventId(), 'A'], true);"], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [115, 115, 1, 0, 30]}, {"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 3]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 1, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<哀伤恶灵>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呃啊……你们将品尝我所受之苦…… "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 直至灵魂尽头…… "]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 2, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<哀伤恶灵>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 可恶的冒险者…我诅咒你们…… "]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 3, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<哀伤恶灵>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 苦痛，无尽的苦痛，将伴随你们！"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 1, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage3", "direction": 6, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 4}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 301, "indent": 0, "parameters": [0, 4, false, false]}, {"code": 212, "indent": 0, "parameters": [-1, 154, false]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 18, "indent": null}, {"code": 45, "parameters": ["$gameSelfSwitches.setValue([$gameMap.mapId(), this.eventId(), 'A'], true);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameSelfSwitches.setValue([$gameMap.mapId(), this.eventId(), 'A'], true);"], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [115, 115, 1, 0, 30]}, {"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 3]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 1, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<哀伤恶灵>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呃啊……你们将品尝我所受之苦…… "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 直至灵魂尽头…… "]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 2, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<哀伤恶灵>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 可恶的冒险者…我诅咒你们…… "]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 3, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<哀伤恶灵>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 苦痛，无尽的苦痛，将伴随你们！"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 1, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage3", "direction": 6, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 16}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">行走图滤镜 : 指定事件 : 5 : 纯色滤镜 : 青色 : 155 : 1"]}, {"code": 356, "indent": 0, "parameters": [">行走图滤镜 : 指定事件 : 6 : 纯色滤镜 : 青色 : 155 : 1"]}, {"code": 231, "indent": 0, "parameters": [2, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 设置动画序列 : 动画序列[12]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 等待动画序列加载完成"]}, {"code": 223, "indent": 0, "parameters": [[-34, -17, 0, 17], 999, false]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 2, "y": 10}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sho<PERSON>hu", "direction": 2, "pattern": 2, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sho<PERSON>hu", "direction": 2, "pattern": 2, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离2 : 触发独立开关 : A"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "sho<PERSON>hu", "direction": 2, "pattern": 2, "characterIndex": 6}, "list": [{"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 212, "indent": 0, "parameters": [0, 155, false]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(11);"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(1).requestBalloon(1);"]}, {"code": 122, "indent": 0, "parameters": [115, 115, 1, 0, 10]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "sho<PERSON>hu", "direction": 8, "pattern": 2, "characterIndex": 6}, "list": [{"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 3, "y": 6}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 5, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 16, "y": 14}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 115, "variableValid": true, "variableValue": 50}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 199, 0, 2, 4]}, {"code": 213, "indent": 1, "parameters": [-1, 7, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这个debuff的效果似乎是会叠加的，不过还在可接受的范围。"]}, {"code": 355, "indent": 1, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(11);"]}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈苦笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我似乎还好。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 355, "indent": 1, "parameters": ["$gamePlayer.followers().follower(1).requestBalloon(8);"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田怎么不说话？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我没事……  "]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈迟疑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田君……"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 115, "variableValid": true, "variableValue": 100}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 199, 0, 2, 4]}, {"code": 213, "indent": 1, "parameters": [-1, 1, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 又叠加了……但感觉反而比一开始更轻松了。 "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 是因为肾上腺激素的提高覆盖掉了消极效果吗？ "]}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呼…呼……"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜？"]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈苦笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊？我也一样，没什么问题。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 355, "indent": 1, "parameters": ["$gamePlayer.followers().follower(1).requestBalloon(3);"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我也还可以坚持！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 那就好。"]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 9}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 215}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 4, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 282, 35, 17, 4, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 0, "y": 3}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 215}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 4, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 282, 35, 17, 4, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 0, "y": 4}, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]}