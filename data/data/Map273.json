{"autoplayBgm": false, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "SMOOTH15s", "pan": 0, "pitch": 100, "volume": 25}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 22, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 52, "width": 24, "data": [7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7572, 7596, 7580, 7596, 7596, 7596, 7576, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7592, 7090, 7600, 7090, 7090, 7090, 7584, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7572, 7596, 7596, 7596, 7596, 7596, 7593, 7096, 7612, 7096, 7096, 7096, 7586, 7596, 7596, 7596, 7596, 7596, 7596, 7576, 7568, 7568, 7568, 7572, 7606, 6562, 6562, 6562, 6562, 6562, 7600, 3674, 7095, 3666, 3652, 3668, 7600, 6562, 6562, 6562, 6562, 6562, 6562, 7584, 7568, 7568, 7568, 7592, 6566, 6568, 6568, 6568, 6568, 6568, 7600, 3664, 7101, 3650, 3660, 3657, 7612, 6568, 6568, 6568, 6568, 6568, 6568, 7584, 7568, 7568, 7568, 7592, 6572, 4002, 3988, 3988, 3988, 4004, 7600, 3649, 3652, 3658, 7610, 3664, 6567, 3762, 3748, 3748, 3748, 3748, 3764, 7584, 7568, 7568, 7568, 7592, 3294, 3984, 3968, 3968, 3968, 3992, 7600, 3672, 3660, 3670, 7600, 3676, 6573, 3744, 3728, 3728, 3728, 3728, 3752, 7584, 7568, 7568, 7568, 7592, 4002, 3969, 3968, 3968, 3968, 3992, 7609, 7601, 7601, 7601, 7607, 3763, 3761, 3737, 3728, 3728, 3728, 3728, 3752, 7584, 7568, 7568, 7568, 7592, 3984, 3968, 3968, 3968, 3968, 3992, 6563, 6562, 6562, 6562, 6566, 3760, 7610, 3744, 3728, 3728, 3728, 3728, 3752, 7584, 7568, 7568, 7568, 7592, 4008, 3996, 3996, 3996, 3996, 4006, 6569, 6568, 6568, 6568, 6572, 3760, 7600, 3744, 3728, 3728, 3728, 3728, 3752, 7584, 7568, 7568, 7568, 7592, 3762, 3748, 3748, 3748, 3748, 3748, 3748, 3748, 3748, 3748, 3748, 3754, 7600, 3744, 3728, 3728, 3728, 3728, 3752, 7584, 7568, 7568, 7568, 7592, 3768, 3756, 3740, 3756, 3756, 3756, 3756, 3756, 3740, 3756, 3756, 3766, 7600, 3744, 3728, 3728, 3728, 3728, 3752, 7584, 7568, 7568, 7568, 7570, 7589, 7613, 3760, 7611, 7591, 7601, 7601, 7613, 3760, 7611, 7601, 7601, 7595, 3768, 3756, 3756, 3756, 3756, 3766, 7584, 7568, 7568, 7568, 7568, 7592, 6566, 3772, 6563, 7600, 7090, 7090, 7094, 3760, 7091, 7090, 7090, 7585, 7588, 7588, 7588, 7588, 7588, 7588, 7569, 7568, 7568, 7568, 7568, 7592, 6572, 3338, 6569, 7600, 7096, 7096, 7100, 3772, 7097, 7096, 7096, 7584, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7592, 3330, 3299, 3332, 7600, 3666, 3652, 3652, 3652, 3652, 3652, 3668, 7584, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7592, 3336, 3324, 3334, 7600, 3650, 3660, 3660, 3660, 3660, 3660, 3657, 7584, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7592, 1580, 1581, 1582, 7600, 3664, 2370, 2356, 2356, 2356, 2372, 3664, 7584, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7570, 7588, 7588, 7588, 7594, 3664, 2352, 2336, 2336, 2336, 2360, 3664, 7584, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7592, 3664, 2352, 2336, 2336, 2336, 2360, 3664, 7584, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7592, 3676, 2376, 2364, 2364, 2364, 2374, 3676, 7584, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 7570, 7588, 7588, 7588, 7588, 7588, 7588, 7588, 7569, 7568, 7568, 7568, 7568, 7568, 7568, 7568, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3963, 3953, 3965, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3150, 0, 0, 3579, 3569, 3569, 3581, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3810, 3796, 3796, 3812, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3792, 3776, 3776, 3800, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3816, 3804, 3804, 3814, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3150, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 673, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 158, 159, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 139, 0, 163, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 161, 0, 171, 0, 160, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 0, 755, 0, 0, 0, 169, 0, 0, 0, 168, 0, 0, 409, 0, 0, 0, 140, 0, 0, 0, 0, 0, 0, 0, 0, 276, 0, 295, 0, 0, 0, 0, 0, 0, 0, 0, 417, 0, 197, 198, 199, 0, 0, 0, 0, 0, 176, 0, 0, 284, 0, 303, 0, 0, 0, 0, 0, 0, 0, 0, 145, 0, 205, 206, 207, 0, 0, 0, 0, 0, 184, 0, 0, 0, 0, 0, 0, 294, 0, 645, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 148, 0, 0, 0, 0, 149, 467, 427, 428, 429, 0, 0, 0, 0, 410, 226, 227, 410, 0, 0, 0, 0, 0, 0, 156, 0, 0, 0, 0, 157, 475, 435, 436, 437, 133, 0, 0, 0, 0, 234, 235, 0, 0, 0, 0, 0, 0, 0, 141, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 283, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 942, 943, 0, 302, 0, 0, 0, 0, 0, 0, 319, 0, 407, 0, 530, 532, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 333, 0, 0, 0, 0, 540, 0, 0, 0, 740, 741, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 522, 520, 0, 0, 0, 748, 749, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 384, 385, 386, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 392, 393, 394, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Door09b", "direction": 2, "pattern": 2, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 9}, {"id": 2, "name": "猪田2", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头裸", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 19, "y": 8}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 2, "characterIndex": 5}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 6}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 4, "pattern": 0, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 9}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 2, "characterIndex": 5}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 4}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 2, "characterIndex": 5}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 14}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door6", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 14}, {"id": 8, "name": "猪田", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头校服", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "SMOOTH15s", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [0, 4, false]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 29, "parameters": [3], "indent": 0}, {"code": 17, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 19, "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac噗齁齁！进来了进来了！总统套房！上等人的享受，好耶！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桀桀，既然都到酒店开房了，我们算是正式情人关系了吧？"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "京子纠结脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子便服无项圈.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac谁跟你是情人……一副没见过世面的样子……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [0, 3, false]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么不是情人关系？没见过世面又怎么样？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你这个见过世面的大美人还不是要乖乖给我操？"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦，对了，说了要送你的礼物。"]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 1]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 14, "parameters": [0, -2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, -2], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(9).requestBalloon(1);"], "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(10).setTransparent(true);"], "indent": null}, {"code": 15, "parameters": [15], "indent": 0}, {"code": 34, "indent": null}, {"code": 45, "parameters": ["$gameMap.event(9).requestBalloon(8);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(9).requestBalloon(1);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(10).setTransparent(true);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(9).requestBalloon(8);"], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 18, "indent": 0}, {"code": 29, "parameters": [3], "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 35, "indent": null}, {"code": 45, "parameters": ["$gameMap.event(8).requestBalloon(3);"], "indent": null}, {"code": 14, "parameters": [-1, 0], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(8).requestBalloon(3);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [-1, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 0}], "repeat": true, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊，这个沙发真爽！比我的床软多了！"]}, {"code": 231, "indent": 0, "parameters": [2, "京子微惊脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子便服无项圈.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个……日晖之环？虽然不是什么特别贵的东西，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但最近似乎很紧俏，你怎么买到的？不会是偷的吧？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [8, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac别提了，不晓得跑了次，还和店员吵了一架…… "]}, {"code": 213, "indent": 0, "parameters": [8, 5, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你管我怎么买到的！就是我偷的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你要不要嘛，不要我拿去退了！"]}, {"code": 213, "indent": 0, "parameters": [9, 11, false]}, {"code": 231, "indent": 0, "parameters": [2, "京子为难脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子便服无项圈.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我没说我不要……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [8, 5, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别废话了，快给老子过来泄泄火! "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 今天心里生理双重於堵，实在是憋死我了！ "]}, {"code": 213, "indent": 0, "parameters": [9, 11, false]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 34, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "京子忧虑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子便服项圈.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [9, 8, false]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 203, "indent": 0, "parameters": [9, 0, 5, 8, 4]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 2]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头校服", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "【BGM】陵辱系Hシーン【儀式っぽい感じ】", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 203, "indent": 0, "parameters": [0, 0, 4, 8, 6]}, {"code": 203, "indent": 0, "parameters": [9, 0, 5, 8, 4]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[9] : 像素偏移[-24,0] : 时间[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 骚货女儿~ 给你爸爸跪下！"]}, {"code": 245, "indent": 0, "parameters": [{"name": "1-女喘气", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[20]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[京子酒店口交二]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……呼唔……还是这种难闻的味道…… "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你好好洗过吗？感觉真是糟透了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 难闻？这叫男人味！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 狗屁男人味，就是臭猪味……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿~那你还不是喜欢，闻到都湿了吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 才、才没有，我只是想作呕！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哼，作呕是吧？给我含住！ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要对我下命令。"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交音4", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[20]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[京子酒店口交三]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔……呲溜……嘬呣……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦哦哦哦~这个地方爽！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac先舔一会以后再含也行~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呲溜…才不要……听你的话…呣呣……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就要…搞你的心态……呲溜……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你个婊子是不是又搞忘了，我可是有你的把柄！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你不会以为我们真的是平等的情侣关系吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别笑死我了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哼…………"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我…知道了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](我明明已经查清楚了……居然还会听这个人渣猪男的话……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是为了让他不会去接近亚丝娜！没错，因为他是亚丝娜学校的，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 只有稳住他，才能保护好我的亚丝娜！  ）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那就给我好好口，桀桀桀~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 全都含着……这样可以吗？"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交音6", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[20]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[京子酒店口交一]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊！是的，爽爆了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯……呼……嘬噗、啪噜……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我的大肉屌好吃吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哼！嚓，唔嗷呲……！！！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](每次含住他的鸡巴时，鼻腔里都会传来一股刺激的气味。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那种气味在口中扩散开来，本应该感到恶心才对，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但却让我兴奋难耐……只能靠佯装愤怒来掩饰……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 什么什么？说什么母猪话？完全听不懂呢~ 嘻嘻嘻~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜、啧噗……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真是捡到宝，嘿嘿嘿~~~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 丰满的嘴唇，温暖的口腔，噢噢噢噢~ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呲呲呲呲溜……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你老公，还有那些小白脸，都没能享受到这种服务，为他们感到遗憾。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 卟嘴！嗯唔……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿，我们既然成了正式的情人，总是让女儿你这么辛苦，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爸爸多少也是有些过意不去的，所以……"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 300, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哦齁齁，接下来换我出力喽~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac你！你你你！你要做什么？！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桀桀桀，乖女儿，你放松享受就好~"]}, {"code": 119, "indent": 1, "parameters": ["跳过剧情"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[9] : 像素偏移[0,0] : 时间[30]"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 3]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[12] : 像素偏移[0,36] : 时间[10]"]}, {"code": 205, "indent": 0, "parameters": [12, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [9, 1, false]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [0, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦齁齁，接下来换我出力喽~"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 14, "parameters": [1, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [1, 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [9, 11, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "京子惊讶脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子便服无项圈.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你！你你你！你要做什么？！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [8, 3, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桀桀桀，乖女儿，你放松享受就好~"]}, {"code": 213, "indent": 0, "parameters": [9, 12, false]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[8] : 像素偏移[36,0] : 时间[60]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[9] : 像素偏移[24,0] : 时间[60]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 118, "indent": 0, "parameters": ["跳过剧情"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, true]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[20]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[京子酒店口交四]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咕哇噢！？突、突然……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你、你在做什么啊！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 一库哟！"]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘10", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 噗噜、呜嗯、嗯唔……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](可恶的肥猪油渣……居然这样对我！）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯哼……呼、咕、唧噜……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](但是，现在……只能听从这个贱种淫猪的话了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 为了守护结城家的荣誉和家人，为了保卫我自己的尊严，我"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 必须用嘴巴让这个臭气熏天、令人作呕的东西感到舒服。）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘬噜、嘬噜、嗯、 嗯、咕……嗯哼……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](绝对不是因为我喜欢这样！）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊…爽啊~噗嘿咿~~~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](看你这傻样……被这么傻逼的男人……我……）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看到乖女儿不甘心又不得不给爸爸我服务的样子，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 噗嘿嘿~ 忍不住想要更粗暴呢~"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘10", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[20]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[京子酒店口交]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔噢！？呼噬呜！？这个变态……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](怎么会？刺中的地方……一阵阵酥麻……呜……）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac老子要让你知道谁是主人！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯哼！呼噬呜！咳咳，呜呜，呜呜！呜呜！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](主人！？你白日做梦！）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac骚母狗想反抗又做不到的样子，真是让人满足，哦齁齁~！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](啊，不行……这么想让我的身体更奇怪了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我被这个最低级猥琐的男人支配，实在是太下贱了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且还对这种下贱的情况感到兴奋……）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯嗯！感觉再撞几下很快就会射出来喽~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](现在的我，凹陷着脸颊吸吮，紧闭着嘴唇套弄肉竿， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还不停用舌头舔舐并持续刺激龟头……多么悲惨淫贱的模样……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 快射给我啊！让我早点结束这丢人的姿态！）"]}, {"code": 225, "indent": 0, "parameters": [2, 9, 15, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯！"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[京子酒店口交射精]"]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精音Bタイプ外出し①（勢いよくびゅるるるぅ～）", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [80]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精音Bタイプ外出し①（勢いよくびゅるるるぅ～）", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 355, "indent": 0, "parameters": ["$gameMessage.clear();"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gameMessage.clear();"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 111, "indent": 0, "parameters": [0, 300, 0]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 82, 21, 15, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 3}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头裸", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头裸", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咕噗噢唔！？ 唔呜呜！ 呼！？ 呼呜呜！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呀哇呃！？咕噗！咕噗噗！嗯呃噗！\\|\\.\\.\\.\\^"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头裸", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 203, "indent": 0, "parameters": [0, 0, 6, 8, 6]}, {"code": 203, "indent": 0, "parameters": [9, 0, 7, 8, 4]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[8] : 像素偏移[0,0] : 时间[1]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[9] : 像素偏移[-24,0] : 时间[1]"]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [9, 8, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[9] : 像素偏移[24,0] : 时间[30]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "京子逞强脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子便服项圈.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac咳咳……唔……咕嘟……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [0, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嘿嘿嘿，现在不需要我说，就很自然地把精液吞下去了~"]}, {"code": 213, "indent": 0, "parameters": [9, 1, false]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "京子转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子便服项圈.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那是…！那是为了不把地毯弄脏！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [0, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 母狗，刚才很有感觉吧~"]}, {"code": 213, "indent": 0, "parameters": [9, 12, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "京子意外脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子便服项圈.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别说蠢话…硬来这样的事情…怎么会感到快感？！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [0, 3, false]}, {"code": 213, "indent": 0, "parameters": [9, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿嘿，硬来也要有硬来的资本，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你的绿帽阳痿老公，想激烈都激烈不起吧？"]}, {"code": 213, "indent": 0, "parameters": [9, 5, false]}, {"code": 231, "indent": 0, "parameters": [2, "京子生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子便服项圈.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不许你嘲笑彰三！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘻嘻嘻，一副被说中破防的样子~"]}, {"code": 213, "indent": 0, "parameters": [9, 8, false]}, {"code": 231, "indent": 0, "parameters": [2, "京子纠结脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子便服项圈.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](虽然我们分居了，但只是因为现在住一起总是争吵……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我对彰三的感情还是……不能容忍我珍视的家人被侮辱……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 虽然他确实………………)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [8, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘴硬母狗，愣在那里想什么呢？赶紧去房间，该上主菜了~"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": 0}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [9, 8, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [2, 5, false]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 31, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 31, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好了没撒？怎么这么久啊？"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [11, 7, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "京子转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还不是你害的，粗暴地乱弄一通，我的妆都花了！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 切，反正我看不出来区别，女人真是麻烦！"]}, {"code": 213, "indent": 0, "parameters": [11, 8, false]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 终于来啦？自己上来动~"]}, {"code": 213, "indent": 0, "parameters": [11, 11, false]}, {"code": 231, "indent": 0, "parameters": [2, "京子纠结脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哼……猥琐的臭猪……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[11] : 像素偏移[0,-18] : 时间[60]"]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 14, "parameters": [1, 0], "indent": null}, {"code": 19, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [1, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[11] : 像素偏移[0,-36] : 时间[60]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "【BGM】陵辱系Hシーン【儀式っぽい感じ】", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[20]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[京子酒店骑乘位慢]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [24], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 50, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [24], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 50, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 坐上去，内衣也脱掉！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啧，真是没有品味…… "]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 245, "indent": 0, "parameters": [{"name": "maki_bgv002", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯~嗯~嗯嗯~"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呼呼，整根插进去的感觉怎么样？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210]（应该说不愧是母女吗？小穴虽然各有各的爽感，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但敏感点的位置却差不多。而且身上的味道也好像，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 让我有一种和明日奈做爱的错觉。嘿嘿，干脆幻想一下~）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是否切换至猪田幻想视角？"]}, {"code": 102, "indent": 0, "parameters": [["是", "否"], 1, 0, 2, 1]}, {"code": 402, "indent": 0, "parameters": [0, "是"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 30, false]}, {"code": 245, "indent": 1, "parameters": [{"name": "Honeydrop_o_aegisilent", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[22]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜骑乘位幻想慢]"]}, {"code": 121, "indent": 1, "parameters": [184, 184, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "否"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别、别说了…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说起来，我和你做爱了多少次了呢？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 如、如此肮脏的事情，我怎么会去记！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可是……一旦我把鸡巴插进小穴，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 它就立刻回忆起来了，对吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔、唔唔……你在说什么莫名其妙的话……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊唧、啊唧，胡说八啊啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊呀！呀……啊、啊、啊……呼呀!"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210]（被那个猥琐男人插入了低贱的东西……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 为什么会有反应呢……喔……汁液溢出来了。）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 老婆的小穴，含着我的鸡巴，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 发出诱人的声音，还流着口水♪很舒服吧！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别、别说些蠢话……谁、谁是你老婆？！啊、嗯、呼呵　唔唔"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我只是说实话而已。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 老婆的小穴已经变得湿湿软软的了不是吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 闭、闭嘴……哈、哈呜、咕呜，你在说什么……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 如果我让老婆像那次一样高潮不断，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你会跟我和好吗？可以继续当我的女人吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 和好？什么和好？哈、哈呜……你、你打算做什么？！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我会让你高潮不断！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……呜呜……唉唉……这样…这样低贱的男人……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我、我不会屈服……啊，呜呜，我不会败北……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210]（我绝对不能像上次那样……！那种丢脸的样子……）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 从一开始就一直讨厌你，你自我介绍的的时候……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈，哈啊，从一开始我就讨厌你……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可是，现在老婆已经彻底喜欢上我，喜欢上我的鸡巴了，对吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么可能喜欢上你！哈，哈啊，你这种肮脏的猪头……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别得意忘形了！你的鸡巴也是，我一点都不喜欢！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 比我之前的帅哥男友差远了！哦哦哦~~~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 又提那个讨厌的装逼王！可恶可恶可恶！！！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这湿淋淋的骚穴，我要用我的鸡巴征服她！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你、爱怎么说就随你吧！！反正我不会承认的！啊啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210]（京子，忍住，只要再也不发出声音，就是你的胜利，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 守住你的优雅，你的尊严，你的人格，嗯哦…）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 老婆，想要我深入内部吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要再叫我老婆了！那、那种事……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈、哈、呵呃呃……一点，都没有在想的……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210]（不然我的心里，要变得更奇怪了……）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 老婆的湿润小穴，今天我会让她属于我！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呼咿！？我、我会属于你……呜？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210]（你这个肥猪！不仅叫我那个什么、老、老婆……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还想要把我当做你的东西，你、你究竟是什么心态！）　"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们只是交易才……唔，啊~~~~"]}, {"code": 111, "indent": 0, "parameters": [0, 184, 1]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[京子酒店骑乘位快]"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 245, "indent": 1, "parameters": [{"name": "maki_bgv010", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 啪啪啪1 25 100 0"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜骑乘幻想快]"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 245, "indent": 1, "parameters": [{"name": "Honeydrop_o_aegihard1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 啪啪啪1 25 100 0"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿呀嘿呀~！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯呀——！进来了，进来了，进来了呜呜呜呜呜呜！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊嗨♪ 啊嗨♪ 嗯！ 哈！ 哈啊！ 呼、呼唔！ 呜嘶！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 老婆湿润的小穴越来越喜欢我的大肉棒了♪"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 才、才没有喜欢上呢！嗯呀♪ 哈啊 唉呀♪"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 讨厌！非常讨厌！啊嗬！唔呢！哈啊！真是讨厌！嗯！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 老婆，你还记得吧？那天的做爱真是太舒服了"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要说傻话……这种像交配一样的做爱怎么会舒服……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我、我可不是那样的女人啊！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 一定要让老婆亲口说出来，和我的做爱是最棒的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 绝对不会……嗯哼，嗯呼～不会说的！这种事情……哦！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210]（真是气死了……气死了！居然被这种无能的渣滓玩弄得不亦乐乎……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 才能、家世、地位。在各个方面，我都比这个渣滓优秀。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 然而……现在，被这种下流的做爱逼得只能守势……）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210]（今天……今天我要赢……不会被这种下流的做爱打败！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac ……不会，不会被这个渣男的礼物和几句老婆弄乱我的心的！）"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210]（这母猪今天怎么这么顽固，往常不是这样的啊？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可恶，偏偏是在我幻想和老婆做爱的时候不给面子，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看我怎么收拾你！）"]}, {"code": 225, "indent": 0, "parameters": [2, 9, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 都被我肏出白浆了还这么嘴硬？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那么，还可以更激烈一些吧！"]}, {"code": 225, "indent": 0, "parameters": [2, 9, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊呜！嗯呜！呼！呼！啊呜！啊啊呜！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](呜呜……啊……真的假的……为什么会感觉这么舒服呢……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 明明刚刚发誓不会输……子宫一阵阵悸动——"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 本能地渴望着炽热的雄性种子）"]}, {"code": 225, "indent": 0, "parameters": [2, 9, 15, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 高潮的声音，叫给我听吧！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不行啊…不要…啊！这样一直强烈地冲击着深处…啊~~~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 要高潮了吧？那就狠狠地顶进去！来吧！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦啦哦啦哦啦哦啦哦啦哦啦哦啦哦啦哦啦！"]}, {"code": 225, "indent": 0, "parameters": [2, 9, 15, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊啊！停下！停下！我要飞了！我要飞走了！！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我也要射了！来吧！老婆，让我们一起高潮吧！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哦♪ 哈啊！嗯！哦\\i[90] 啊！啊！啊啊啊啊啊！\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 射啦！高潮吧，老婆！"]}, {"code": 111, "indent": 0, "parameters": [0, 184, 1]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[京子酒店骑乘位中出]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[亚丝娜骑乘幻想中出]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精音Bタイプ外出し①（勢いよくびゅるるるぅ～）", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精音Bタイプ外出し①（勢いよくびゅるるるぅ～）", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gameMessage.clear();"]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜呜呜呜呜呜呜…………"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哭啥哭，这还没结束呢！就这样继续下去吧~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](还要继续下去，吗……？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 在我理解这一点的瞬间…… "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 一种既恐惧又愉悦的情绪，涌上心头……）"]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 0, "parameters": [300, 300, 1]}, {"code": 121, "indent": 0, "parameters": [184, 184, 1]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 82, 21, 15, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要啊、不要啊！明明不想输的……\\|"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯！呼！嗯！嗯呼 嗯♪ \\|\\^"]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 6, "y": 12}, {"id": 9, "name": "京子", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "京子短发", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 13}, {"id": 10, "name": "礼物", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "!adult1", "direction": 2, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": false}], "x": 6, "y": 8}, {"id": 11, "name": "京子2", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"tileId": 0, "characterName": "京子短发", "direction": 8, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 5}, {"id": 12, "name": "衣服", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 3}, "directionFix": true, "image": {"tileId": 0, "characterName": "!adult1", "direction": 4, "pattern": 2, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 4, "y": 8}, null]}