{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "夜晚森林篝火", "pan": 0, "pitch": 100, "volume": 55}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 20, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 36, "width": 30, "data": [5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5940, 5964, 5964, 5964, 5964, 5944, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5940, 5964, 5964, 5964, 5964, 5964, 5964, 5964, 5964, 5964, 5964, 5974, 6322, 6322, 6322, 6322, 5976, 5964, 5964, 5964, 5964, 5964, 5964, 5964, 5964, 5964, 5964, 5964, 5944, 5936, 5960, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6326, 6328, 6328, 6328, 6328, 6323, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 6322, 5952, 5936, 5960, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6332, 4050, 4036, 4036, 4052, 6329, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 6328, 5952, 5936, 5960, 4050, 4036, 4036, 4036, 4036, 4036, 4036, 4036, 4036, 4036, 4036, 4017, 4016, 4016, 4018, 4036, 4036, 4036, 4036, 4036, 4036, 4036, 4036, 4036, 4036, 4036, 4052, 5952, 5936, 5960, 4032, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4040, 5952, 5936, 5960, 4032, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4040, 5952, 5936, 5960, 4032, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4020, 4044, 4044, 4024, 4016, 4016, 4016, 4016, 4040, 5952, 5936, 5960, 4032, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4020, 4044, 4024, 4016, 4016, 4016, 4040, 3003, 3005, 4032, 4016, 4016, 4016, 4016, 4040, 5952, 5936, 5960, 4032, 4016, 4016, 4016, 4016, 4020, 4044, 4044, 4024, 4016, 4016, 4016, 4040, 3002, 4032, 4016, 4016, 4016, 4018, 4036, 4036, 4017, 4016, 4016, 4016, 4016, 4040, 5952, 5936, 5960, 4056, 4024, 4016, 4016, 4016, 4040, 3003, 3005, 4032, 4016, 4016, 4016, 4040, 3004, 4034, 4044, 4044, 4024, 4016, 4016, 4016, 4016, 4016, 4020, 4044, 4044, 4054, 5952, 5936, 5938, 5972, 4032, 4016, 4016, 4016, 4018, 4036, 4036, 4017, 4016, 4016, 4016, 4022, 4049, 4043, 3003, 3005, 4032, 4016, 4016, 4016, 4016, 4016, 4040, 5970, 5956, 5956, 5937, 5936, 5936, 5960, 4032, 4016, 4016, 4016, 4016, 4016, 4020, 4044, 4044, 4024, 4016, 4040, 3006, 4033, 4036, 4036, 4017, 4016, 4016, 4020, 4044, 4044, 4054, 5952, 5936, 5936, 5936, 5936, 5936, 5960, 4056, 4044, 4024, 4016, 4016, 4016, 4040, 2994, 2996, 4056, 4024, 4018, 4036, 4017, 4016, 4016, 4016, 4016, 4016, 4040, 5970, 5956, 5956, 5937, 5936, 5936, 5936, 5936, 5936, 5938, 5956, 5972, 4032, 4016, 4016, 4016, 4040, 3000, 2990, 3005, 4032, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4016, 4040, 5952, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5960, 4032, 4016, 4016, 4016, 4022, 4049, 4049, 4038, 4017, 4016, 4016, 4016, 4016, 4016, 4020, 4044, 4044, 4054, 5952, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5960, 4056, 4044, 4044, 4044, 4054, 3003, 3005, 4032, 4016, 4020, 4044, 4024, 4016, 4016, 4040, 5970, 5956, 5956, 5937, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5938, 5956, 5956, 5956, 5956, 5972, 4050, 4036, 4017, 4020, 4054, 3002, 4056, 4024, 4016, 4040, 5952, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5960, 4032, 4016, 4016, 4040, 2994, 2963, 2996, 4032, 4016, 4040, 5952, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 5936, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4203, 4193, 4205, 0, 0, 0, 4203, 4193, 4205, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4254, 0, 4194, 4196, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4200, 4198, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4254, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4302, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4202, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4204, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 138, 137, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 0, 0, 0, 0, 0, 38, 39, 0, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 137, 0, 0, 0, 0, 0, 47, 46, 0, 0, 46, 47, 46, 47, 46, 47, 0, 47, 38, 39, 0, 0, 47, 46, 47, 46, 47, 46, 0, 0, 47, 0, 0, 0, 0, 0, 46, 0, 0, 0, 47, 46, 47, 46, 0, 0, 38, 39, 46, 38, 39, 0, 46, 47, 46, 47, 46, 47, 0, 0, 46, 0, 0, 0, 0, 46, 47, 0, 0, 0, 46, 47, 46, 0, 0, 0, 0, 47, 0, 46, 47, 0, 47, 46, 0, 0, 47, 46, 47, 46, 0, 0, 0, 0, 0, 47, 46, 47, 0, 46, 47, 46, 0, 0, 0, 0, 0, 0, 797, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 805, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 47, 0, 0, 0, 47, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 9, 46, 47, 37, 38, 39, 9, 46, 47, 37, 12, 13, 0, 45, 0, 38, 39, 45, 38, 39, 37, 0, 38, 39, 0, 15, 0, 0, 46, 47, 0, 0, 0, 45, 46, 47, 0, 0, 0, 45, 0, 0, 9, 0, 0, 46, 47, 11, 46, 47, 45, 0, 46, 47, 0, 0, 38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 129, 130, 129, 130, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 47, 0, 129, 130, 0, 0, 129, 0, 0, 129, 130, 0, 0, 137, 38, 39, 138, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 129, 130, 0, 0, 0, 137, 138, 38, 39, 137, 38, 39, 137, 138, 38, 39, 38, 39, 47, 45, 37, 38, 39, 38, 39, 38, 39, 38, 39, 38, 39, 138, 37, 0, 0, 46, 38, 39, 47, 38, 39, 38, 39, 38, 39, 37, 46, 769, 770, 771, 45, 46, 38, 39, 38, 39, 38, 39, 47, 46, 38, 39, 45, 0, 0, 38, 39, 47, 15, 46, 38, 39, 38, 39, 47, 45, 776, 777, 778, 779, 780, 38, 39, 38, 39, 38, 39, 38, 39, 38, 39, 47, 0, 0, 0, 39, 38, 39, 0, 38, 39, 38, 39, 47, 38, 39, 784, 785, 786, 787, 788, 46, 38, 39, 47, 46, 38, 39, 38, 39, 47, 15, 0, 38, 0, 38, 39, 38, 39, 384, 38, 39, 47, 826, 46, 47, 792, 793, 798, 795, 796, 0, 46, 47, 0, 850, 46, 47, 46, 47, 38, 39, 0, 46, 0, 46, 47, 46, 47, 392, 46, 47, 38, 39, 0, 0, 800, 801, 806, 803, 804, 0, 856, 857, 0, 0, 0, 38, 39, 0, 46, 47, 0, 0, 0, 0, 38, 39, 0, 0, 0, 35, 46, 47, 0, 858, 0, 0, 0, 0, 818, 0, 864, 865, 0, 38, 39, 46, 47, 38, 39, 0, 0, 0, 38, 39, 46, 47, 0, 0, 0, 43, 0, 0, 0, 33, 34, 0, 0, 0, 0, 0, 0, 0, 0, 46, 47, 21, 0, 46, 47, 0, 38, 39, 46, 47, 0, 0, 38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 46, 47, 0, 0, 0, 5, 46, 47, 0, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 38, 39, 0, 37, 38, 39, 0, 0, 38, 39, 0, 0, 0, 0, 45, 0, 32, 0, 4, 0, 0, 0, 0, 11, 0, 0, 37, 0, 0, 0, 46, 384, 0, 45, 46, 38, 39, 0, 384, 47, 0, 0, 0, 11, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 0, 0, 38, 39, 392, 38, 39, 0, 46, 47, 0, 392, 38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 0, 0, 38, 39, 46, 47, 0, 46, 38, 39, 0, 0, 38, 39, 46, 47, 0, 38, 39, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 45, 0, 0, 46, 38, 39, 0, 60, 0, 46, 47, 0, 11, 46, 47, 0, 0, 0, 46, 47, 0, 0, 0, 11, 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 46, 384, 0, 38, 39, 0, 38, 39, 0, 0, 0, 38, 39, 38, 39, 0, 38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 12, 13, 0, 38, 39, 392, 0, 46, 47, 0, 46, 47, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, null, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!fsm_Flame04", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 12}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 223, "indent": 0, "parameters": [[-68, -68, 0, 68], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 17, "y": 2}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 85}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头游戏服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 213, "indent": 0, "parameters": [0, 8, true]}, {"code": 250, "indent": 0, "parameters": [{"name": "帘子", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 203, "indent": 0, "parameters": [5, 0, 14, 9, 0]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 17}, {"code": 15, "parameters": [3], "indent": null}, {"code": 18}, {"code": 15, "parameters": [3], "indent": null}, {"code": 19, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["fgo莉兹战斗像素人", 0], "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["fgo莉兹战斗像素人", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 0, "parameters": [1, "FOG莉兹微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo莉兹战斗.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哎呀,想不到你这家伙的能力居然这么方便,这样不是能随便从"]}, {"code": 401, "indent": 0, "parameters": ["\\dac采集那些危险地带的梦幻素材了,以后可能得经常依赖猪田你"]}, {"code": 401, "indent": 0, "parameters": ["\\dac也不一定呢。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹学姐有事找我的话,我一定不会推辞的。"]}, {"code": 213, "indent": 0, "parameters": [5, 3, true]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [1, "FOG莉兹通用", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo莉兹战斗.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac只是这一点就比那个闷骚桐人好不知道多少了呢,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac来先吃点东西吧。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac谢谢学姐,不过学姐我们为什么不使用传送魔法回去城镇?"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [1, "FOG莉兹坏笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo莉兹战斗.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac因为我有一件事无论无何都想找猪田你确认一下呢。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 245, "indent": 0, "parameters": [{"name": "【陵辱系】危機・緊張完成", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [0, 2, true]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac诶,和我确认的事?"]}, {"code": 213, "indent": 0, "parameters": [5, 4, false]}, {"code": 213, "indent": 0, "parameters": [0, 1, true]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 bob_chikan01 20 100 0"]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 356, "indent": 0, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[16]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[篝火旁拷问]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呐~你刚刚说的吧,学姐找你帮忙的话,你任何事都不会推辞呢~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学...学姐...？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac如果我问你和亚丝娜之间的事,死肥...猪田你也会老实的回答吧。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac诶...亚...亚丝娜学姐,她只是我很尊重的前辈的女朋友,而且"]}, {"code": 401, "indent": 0, "parameters": ["\\dac对我比较照顾而已。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我和亚丝娜都讨厌说慌的孩子,周二澡堂的窗户,周三和某个不良少女的交易,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac周四的自导自演,今天在KTV的遭遇,以及在车站一起回家另一个辣妹"]}, {"code": 401, "indent": 0, "parameters": ["\\dac甚至刚刚给你吃的上面就有你们小队今天遭遇的异常状态噩梦。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac怎么..怎么会..你...."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜似乎明天晚上就会回来呢,如果她知道她平时比较照顾"]}, {"code": 401, "indent": 0, "parameters": ["\\dac的学弟背地里都干了一些什么事之后还能和以前一样照顾他吗?"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你说呢,猪田?"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我..我不知道你在说什么...学姐...."]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我的身材和亚丝娜比你觉得怎么样?如果你老实说的话,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我这个学姐在这个游戏里给你点奖励的也可以的噢。"]}, {"code": 250, "indent": 0, "parameters": [{"name": "吞口水", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我...."]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 213, "indent": 0, "parameters": [0, 8, true]}, {"code": 231, "indent": 0, "parameters": [1, "FOG莉兹嘲弄", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo莉兹战斗.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac给你10分钟考虑时间,在这之前我会在帐篷里等你,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac希望你不要让我失望。"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 4, "indent": null}, {"code": 41, "parameters": ["", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [0, 8, true]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 960, false]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [0, 8, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 37, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "帘子", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 18, "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 16, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["NF_NPC削除 1"]}, {"code": 356, "indent": 0, "parameters": ["NF_NPC削除 2"]}, {"code": 122, "indent": 0, "parameters": [80, 80, 0, 4, "\"调查克莱因找到的据点\""]}, {"code": 201, "indent": 0, "parameters": [0, 103, 12, 14, 6, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 14, "y": 11}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 85}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 10}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$fsm_Door06_2_ex2", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 9}, null, null, null, null, null, null, null]}