{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "Combat at the Dungeon_ volcano", "pan": 0, "pitch": 100, "volume": 80}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 35, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "Mountains5", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 57, "width": 36, "data": [1557, 2952, 2950, 1590, 1591, 1567, 1559, 3984, 3992, 6072, 6040, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6056, 2860, 1564, 1580, 2352, 2336, 2336, 2360, 1571, 6072, 6040, 6032, 6032, 6032, 6032, 6032, 6056, 1565, 1566, 1566, 1567, 1569, 1570, 1559, 3984, 3992, 1571, 6072, 6060, 6060, 6060, 6040, 6032, 6032, 6036, 6060, 6070, 1564, 1580, 2370, 2337, 2336, 2336, 2360, 1552, 2906, 6048, 6032, 6032, 6036, 6060, 6060, 6070, 1568, 1620, 1569, 1570, 1577, 1578, 1559, 3984, 3992, 1560, 1563, 2907, 2886, 2900, 6072, 6060, 6060, 6070, 2898, 2900, 1554, 2370, 2337, 2336, 2336, 2340, 2374, 1555, 2896, 6048, 6032, 6036, 6070, 2898, 2884, 2900, 1576, 1577, 1577, 1578, 1590, 1591, 1567, 3984, 3992, 1568, 1571, 2862, 2904, 2874, 2884, 2884, 2884, 2884, 2869, 2902, 1572, 2352, 2336, 2336, 2336, 2360, 1552, 2898, 2890, 6072, 6060, 6070, 2898, 2865, 2868, 2902, 1566, 1566, 1566, 1566, 1567, 1569, 1570, 3984, 3992, 1568, 1560, 1584, 1563, 2904, 2872, 2864, 2864, 2868, 2902, 2858, 1554, 2352, 2336, 2336, 2340, 2374, 1555, 2880, 2870, 2897, 2886, 2884, 2869, 2892, 2902, 2862, 1569, 1569, 1569, 1569, 1570, 1569, 1570, 3984, 3992, 0, 1568, 1569, 1560, 1563, 2904, 2892, 2892, 2902, 2850, 2842, 1572, 2352, 2336, 2336, 2360, 1552, 2898, 2869, 2902, 2862, 2904, 2892, 2902, 2851, 2861, 2906, 1623, 1593, 1621, 1569, 1570, 1577, 1578, 3984, 3992, 2858, 1568, 1569, 1568, 1560, 1561, 1563, 2859, 2849, 2845, 2854, 1572, 2352, 2336, 2336, 2360, 1571, 2904, 2902, 1564, 1579, 1563, 2859, 2839, 2855, 2898, 2890, 1631, 1577, 1629, 1577, 1578, 2907, 2901, 3984, 3992, 2848, 0, 0, 1568, 1568, 1569, 1560, 1584, 1584, 1561, 1584, 1562, 2289, 2288, 2288, 2290, 1560, 1561, 1584, 1562, 1569, 1560, 1562, 2848, 2898, 2865, 2888, 2852, 2907, 2901, 2859, 2849, 2861, 2908, 3984, 3992, 2833, 2836, 2852, 0, 1568, 1569, 1568, 1569, 1569, 1569, 1569, 1570, 2289, 2288, 2288, 2290, 1568, 1569, 1593, 1570, 1577, 1571, 2859, 2855, 2904, 2892, 2889, 2818, 2852, 2905, 2897, 2886, 2900, 2858, 3984, 3992, 2832, 2816, 2822, 2861, 0, 0, 1568, 1569, 1569, 1594, 1569, 1570, 2289, 2288, 2288, 2290, 1568, 1569, 1569, 1552, 2858, 1560, 1584, 1584, 1584, 1563, 2896, 2844, 2846, 2849, 2853, 2904, 2889, 2860, 3984, 3992, 2856, 2844, 2841, 1571, 2851, 2861, 0, 0, 0, 0, 1577, 1570, 2289, 2384, 2288, 2290, 1568, 1577, 1577, 1552, 2848, 1568, 1569, 1569, 1569, 1552, 2908, 1561, 1584, 1563, 2857, 2861, 2905, 2901, 4008, 3978, 3988, 4004, 2857, 2839, 2855, 2994, 2980, 2996, 2862, 1572, 1554, 1570, 2289, 2288, 2288, 2290, 1568, 1552, 2858, 1552, 2860, 1576, 1577, 1577, 1577, 1571, 2862, 1593, 1569, 1560, 1584, 1561, 1563, 2905, 2909, 4008, 3996, 3978, 4004, 2848, 1571, 3000, 2968, 2962, 2996, 1572, 1572, 1570, 2289, 2288, 2384, 2290, 1568, 1571, 2848, 1552, 2907, 2886, 2884, 2884, 2900, 1560, 1561, 1577, 1577, 1568, 1569, 1569, 1560, 1563, 2850, 2836, 2852, 3984, 3992, 2833, 2837, 2861, 2978, 2988, 2998, 1572, 1554, 1570, 2289, 2288, 2288, 2290, 1568, 1552, 2848, 1560, 1563, 2904, 2892, 2892, 2902, 1568, 1569, 2884, 2900, 1576, 1577, 1577, 1568, 1571, 2832, 2816, 2840, 3984, 3992, 2856, 2841, 1571, 3004, 2858, 0, 2862, 1572, 1570, 2289, 2288, 2288, 2290, 1568, 1552, 2848, 1573, 1560, 1584, 1584, 1563, 2862, 1576, 1577, 2868, 2902, 2859, 2849, 2861, 1576, 1552, 2834, 2844, 2841, 4008, 3978, 4004, 2857, 2838, 2836, 2842, 1572, 2906, 1572, 2370, 2337, 2336, 2336, 2338, 2372, 1571, 2833, 2852, 1573, 1574, 1574, 1560, 1561, 1584, 1584, 2902, 1564, 1585, 1584, 1561, 1563, 1552, 2848, 3626, 2857, 2861, 4008, 3978, 4004, 2856, 2824, 2822, 2861, 2896, 1554, 2352, 2336, 2336, 2336, 2336, 2360, 1571, 2832, 2822, 2849, 2849, 2861, 1573, 1574, 1574, 1574, 1584, 1562, 1569, 1606, 1607, 1560, 1571, 2860, 3625, 3606, 3620, 2862, 4008, 3978, 4004, 2856, 2841, 2907, 2903, 1572, 2352, 2336, 2336, 2336, 2336, 2360, 1552, 2834, 2854, 4002, 3988, 3988, 3988, 3988, 3988, 4004, 1593, 1570, 1577, 1614, 1615, 1568, 1560, 1584, 1563, 3624, 3594, 3620, 2862, 4008, 3978, 4004, 2857, 2849, 2861, 1554, 2352, 2336, 2336, 2336, 2336, 2360, 1552, 2860, 4002, 3973, 3996, 3996, 3996, 3996, 3996, 3993, 1577, 1578, 2862, 2898, 2900, 1576, 1568, 1569, 1560, 1563, 3624, 3594, 3620, 2862, 4008, 3978, 3988, 3988, 4004, 1554, 2352, 2336, 2336, 2336, 2336, 2360, 1552, 4002, 3973, 4006, 2898, 2900, 2851, 2849, 2853, 4012, 2861, 2898, 2884, 2865, 2866, 2900, 1568, 1569, 1568, 1552, 2862, 3624, 3614, 3629, 2954, 4008, 3996, 3996, 4006, 1572, 2352, 2336, 2336, 2336, 2336, 2360, 1571, 4008, 4006, 2898, 2869, 2902, 2860, 1581, 2857, 2853, 2884, 2865, 2864, 2868, 2892, 2902, 1568, 1569, 1568, 1560, 1584, 1563, 2858, 2946, 2915, 2948, 2859, 2849, 2853, 1554, 2352, 2336, 2336, 2336, 2336, 2360, 1552, 2858, 2898, 2869, 2902, 2862, 1581, 2862, 1581, 2848, 2892, 2892, 2892, 2902, 2859, 2861, 1576, 1577, 1568, 1568, 1569, 1552, 2860, 2952, 2940, 2922, 2932, 2948, 2848, 1554, 2352, 2336, 2336, 2336, 2336, 2360, 1552, 2848, 2904, 2902, 2858, 1557, 2862, 1581, 2859, 2855, 1584, 1561, 1584, 1584, 1584, 1563, 2859, 2853, 1576, 1568, 1593, 1560, 1584, 1563, 2858, 2952, 2940, 2950, 2848, 1572, 2352, 2336, 2336, 2336, 2336, 2360, 1571, 2860, 3290, 2859, 2855, 1557, 1557, 2862, 6018, 6020, 1574, 1574, 1574, 1612, 1574, 1560, 1563, 2857, 2853, 1568, 1569, 1568, 1571, 1571, 2833, 2837, 2849, 2849, 2855, 1554, 2352, 2336, 2336, 2336, 2336, 2360, 6018, 6020, 3280, 6018, 6004, 6004, 6004, 6004, 5985, 6008, 1585, 1584, 1561, 1584, 1563, 1573, 1560, 1563, 2860, 1576, 1577, 1568, 1571, 1552, 2834, 2854, 1564, 1585, 1584, 1562, 2352, 2336, 2336, 2336, 2336, 2360, 6000, 6008, 3280, 6000, 5984, 5984, 5984, 5984, 5984, 6008, 1569, 1569, 1569, 1569, 1560, 1563, 1573, 1560, 1563, 2850, 2852, 1568, 1571, 1552, 2860, 1564, 1580, 1569, 1569, 1570, 2352, 2336, 2336, 2336, 2336, 2360, 6024, 6022, 3280, 6000, 5984, 5984, 5984, 5984, 5984, 6008, 6004, 6004, 6004, 6020, 1568, 1560, 1563, 1573, 1552, 2856, 2854, 1576, 1571, 1560, 1561, 1562, 1570, 1577, 1577, 1570, 2352, 2336, 2336, 2336, 2336, 2360, 1571, 2858, 3280, 6024, 6012, 6012, 6012, 5992, 5984, 5986, 5984, 5984, 5984, 5986, 6004, 6004, 6020, 2862, 1571, 2955, 2949, 2858, 1560, 1563, 1577, 1572, 1578, 1564, 1580, 1570, 2352, 2336, 2336, 2336, 2340, 2374, 1555, 2860, 3280, 2946, 2932, 2932, 2948, 6024, 5992, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5986, 6004, 6004, 6020, 2944, 2860, 1568, 1552, 2858, 1572, 1584, 1562, 1570, 1661, 2352, 2336, 2336, 2336, 2360, 1571, 2858, 3283, 3287, 2952, 2940, 2940, 2922, 2948, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 2953, 2949, 1568, 1571, 2848, 1572, 1569, 1570, 1661, 2370, 2337, 2336, 2336, 2336, 2360, 1571, 2848, 3292, 2899, 2897, 2897, 2909, 2928, 2936, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5986, 6020, 2956, 1576, 1571, 2848, 1554, 1660, 1661, 2370, 2337, 2336, 2336, 2336, 2340, 2374, 1552, 2848, 2907, 2903, 2946, 2933, 2945, 2941, 2950, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5986, 6004, 6020, 1571, 2848, 1572, 2370, 2356, 2337, 2336, 2336, 2336, 2336, 2360, 1557, 1552, 2860, 2955, 2945, 2941, 2950, 6018, 6004, 6004, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 6008, 1571, 2848, 1554, 2352, 2336, 2336, 2336, 2336, 2336, 2336, 2360, 1557, 6018, 6004, 6004, 6004, 6004, 6004, 5985, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5986, 6020, 2848, 1554, 2352, 2336, 2336, 2336, 2336, 2336, 2336, 2360, 1557, 6000, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 5984, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3186, 3188, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3186, 3153, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3168, 3152, 3176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3192, 3180, 3177, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3196, 3124, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3866, 3132, 3134, 3141, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3145, 3149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3906, 3892, 3908, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3912, 3880, 3874, 3908, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3912, 3900, 3910, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3136, 0, 0, 3918, 0, 3906, 3908, 0, 0, 3476, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3136, 0, 0, 0, 0, 3912, 3910, 0, 0, 3464, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3121, 3140, 0, 0, 0, 0, 0, 0, 0, 3478, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3120, 3110, 3149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3150, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3142, 3090, 3076, 3076, 3076, 3076, 3076, 3076, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3090, 3061, 3084, 3084, 3084, 3084, 3084, 3064, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3090, 3061, 3094, 0, 0, 0, 0, 0, 3096, 0, 0, 3138, 3124, 3124, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3096, 3094, 0, 3138, 3140, 0, 0, 0, 0, 0, 3138, 3105, 3108, 3132, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3122, 3142, 0, 0, 3186, 3188, 0, 3144, 3132, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3130, 0, 0, 3187, 3181, 3190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3142, 0, 0, 3196, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 0, 0, 0, 0, 0, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 257, 294, 0, 0, 0, 0, 256, 272, 0, 173, 181, 0, 0, 0, 0, 0, 302, 0, 0, 0, 0, 256, 256, 257, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 272, 273, 18, 0, 256, 256, 273, 272, 0, 0, 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 272, 272, 273, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 315, 0, 272, 272, 344, 317, 0, 0, 0, 0, 0, 0, 0, 0, 0, 307, 257, 256, 273, 272, 0, 0, 0, 0, 0, 175, 0, 0, 0, 0, 0, 0, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 315, 273, 272, 347, 317, 0, 0, 0, 0, 0, 0, 159, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 167, 0, 67, 0, 0, 0, 0, 104, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 0, 0, 0, 0, 0, 304, 0, 306, 0, 0, 0, 0, 0, 0, 84, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 104, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 104, 81, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 84, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 84, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 180, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 568, 569, 569, 569, 569, 569, 569, 570, 0, 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 579, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 138, 145, 0, 0, 0, 0, 0, 0, 0, 0, 256, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 56, 0, 0, 146, 12, 0, 0, 0, 0, 0, 0, 0, 48, 272, 272, 0, 274, 257, 257, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 273, 273, 274, 274, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 293, 0, 0, 0, 0, 0, 0, 173, 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 172, 0, 181, 0, 0, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 180, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 291, 363, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 360, 271, 277, 0, 0, 0, 0, 321, 363, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 307, 332, 372, 0, 0, 374, 364, 0, 0, 0, 0, 0, 295, 277, 59, 67, 0, 0, 0, 307, 332, 374, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 315, 326, 308, 337, 339, 332, 372, 0, 0, 0, 295, 336, 327, 59, 67, 0, 0, 0, 48, 315, 299, 0, 0, 0, 0, 295, 336, 308, 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 316, 345, 347, 326, 337, 338, 339, 336, 327, 344, 317, 59, 0, 0, 0, 0, 0, 3, 299, 0, 0, 0, 295, 327, 344, 316, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 345, 346, 347, 13, 14, 2, 0, 0, 0, 0, 0, 48, 1, 38, 39, 337, 338, 339, 327, 317, 0, 0, 0, 0, 280, 281, 282, 0, 0, 0, 0, 280, 281, 282, 0, 0, 18, 0, 1, 0, 38, 39, 0, 0, 0, 0, 0, 38, 39, 46, 47, 345, 346, 12, 280, 281, 282, 0, 0, 0, 288, 289, 290, 0, 0, 67, 0, 288, 289, 290, 0, 0, 0, 0, 0, 20, 46, 47, 0, 0, 0, 0, 0, 46, 47, 20, 0, 0, 48, 50, 288, 289, 290, 0, 280, 281, 282, 297, 298, 19, 0, 0, 0, 296, 297, 298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 298, 0, 288, 289, 290, 305, 280, 281, 282, 0, 0, 304, 305, 306, 104, 0, 0, 0, 182, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 77, 79, 304, 305, 306, 0, 296, 297, 298, 0, 288, 289, 290, 0, 0, 0, 2, 88, 84, 38, 39, 0, 0, 174, 0, 0, 0, 0, 0, 0, 0, 0, 158, 0, 58, 67, 0, 0, 0, 0, 0, 0, 304, 305, 306, 0, 296, 297, 298, 0, 0, 0, 0, 0, 0, 46, 47, 5, 7, 7, 82, 170, 0, 0, 0, 0, 0, 0, 166, 0, 0, 18, 0, 159, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 306, 0, 0, 0, 0, 0, 88, 5, 0, 0, 0, 6, 0, 0, 68, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 167, 155, 37, 0, 0, 0, 280, 281, 282, 0, 1, 0, 280, 281, 282, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 34, 0, 5, 163, 45, 0, 0, 0, 288, 289, 290, 0, 0, 0, 288, 289, 290, 0, 0, 0, 88, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 296, 297, 298, 155, 0, 0, 296, 297, 298, 0, 0, 0, 0, 0, 0, 10, 85, 87, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 305, 306, 163, 0, 0, 304, 305, 306, 0, 0, 0, 0, 88, 84, 16, 0, 0, 0, 59, 67, 180, 180, 180, 171, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 47, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 88, 89, 90, 67, 0, 59, 0, 0, 0, 0, 179, 0, 0, 0, 0, 21, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 552, 553, 553, 553, 553, 553, 553, 554, 555, 0, 0, 0, 0, 0, 0, 0, 0, 280, 281, 282, 0, 0, 0, 0, 0, 0, 38, 39, 0, 0, 0, 0, 0, 0, 0, 0, 560, 561, 561, 561, 561, 561, 561, 562, 0, 0, 0, 0, 0, 2, 53, 54, 54, 288, 289, 290, 0, 0, 0, 0, 0, 0, 46, 47, 0, 0, 0, 0, 0, 0, 0, 0, 576, 577, 577, 577, 577, 577, 577, 578, 0, 0, 29, 0, 0, 53, 0, 53, 54, 296, 297, 298, 0, 0, 0, 280, 281, 282, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 584, 585, 585, 585, 585, 585, 585, 586, 587, 0, 0, 0, 53, 10, 53, 0, 0, 304, 305, 306, 0, 9, 1, 288, 289, 290, 0, 0, 0, 0, 0, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 53, 259, 260, 260, 0, 0, 0, 0, 0, 0, 296, 297, 298, 0, 0, 0, 0, 0, 288, 289, 290, 0, 3, 0, 0, 0, 0, 0, 0, 259, 260, 320, 260, 320, 260, 260, 260, 286, 0, 0, 0, 174, 0, 0, 0, 0, 304, 305, 306, 0, 0, 0, 78, 0, 296, 297, 298, 1, 0, 0, 0, 0, 0, 0, 0, 321, 0, 0, 361, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 38, 39, 0, 0, 0, 304, 305, 306, 129, 129, 0, 0, 0, 0, 0, 0, 267, 0, 0, 361, 0, 0, 0, 0, 0, 0, 0, 260, 320, 260, 260, 285, 18, 0, 0, 0, 46, 47, 0, 0, 0, 12, 0, 130, 59, 137, 0, 0, 0, 0, 0, 0, 321, 0, 0, 361, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 287, 320, 260, 285, 0, 0, 2, 0, 0, 0, 0, 0, 59, 59, 145, 0, 0, 0, 0, 0, 0, 341, 337, 339, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 287, 284, 284, 285, 0, 0, 153, 154, 58, 59, 67, 0, 68, 0, 0, 0, 0, 0, 315, 345, 347, 326, 337, 338, 339, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 293, 2, 0, 161, 162, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 315, 345, 346, 347, 326, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 311, 285, 0, 0, 9, 0, 0, 0, 0, 59, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 219, 318, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 287, 284, 285, 0, 0, 0, 0, 59, 67, 0, 0, 0, 0, 53, 0, 1, 0, 0, 0, 259, 260, 320, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 293, 1, 0, 59, 173, 67, 0, 0, 0, 0, 0, 283, 260, 260, 320, 320, 260, 286, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 311, 285, 0, 59, 181, 0, 0, 0, 0, 0, 0, 291, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 293, 0, 0, 0, 0, 0, 0, 0, 0, 0, 321, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 215}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-3,-9]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 2, "indent": 0}, {"code": 2, "indent": 0}, {"code": 2, "indent": 0}, {"code": 2, "indent": 0}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 223, "indent": 0, "parameters": [[-51, -34, 0, 34], 999, false]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 9, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 在那里。"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们这次不要贸然前进。慢慢到达它的警戒距离后，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜就立刻用冰魔法降低它的速度。"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈失落脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人，我在想，要不要休息一下，这种怪物，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你不打扰它，它一般也不会也不太会移动……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还是直接上吧，这可是稀有怪物，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就在眼前不采取行动，简直是煎熬！ "]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那好吧……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 216]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([282, 12, 'A'], true);"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 45, "parameters": ["$gameMap.event(1).requestBalloon(3);"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 216}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-3,-9]"]}, {"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离3 : 触发独立开关 : A"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 45, "parameters": ["$gameMap.event(1).requestBalloon(3);"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 214}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([282, 12, 'A'], false);"]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<杂烩鸽>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咕咕！"]}, {"code": 108, "indent": 0, "parameters": ["玩家朝向事件1"]}, {"code": 355, "indent": 0, "parameters": ["var event = $gameMap.event(1);"]}, {"code": 655, "indent": 0, "parameters": ["var deltaX = $gamePlayer.x - event.x;"]}, {"code": 655, "indent": 0, "parameters": ["var deltaY = $gamePlayer.y - event.y;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["if (Math.abs(deltaX) <= Math.abs(deltaY)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gamePlayer.setDirection(deltaY < 0 ? 2 : 8); // 下或上"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gamePlayer.setDirection(deltaX < 0 ? 6 : 4); // 右或左"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 本事件 : 像素偏移[0,-48] : 时间[10]"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜！就是现在！"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).setDirection($gamePlayer.direction());"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(1).setDirection($gamePlayer.direction());"]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).jump(0, 0);"]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 212, "indent": 0, "parameters": [0, 156, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [0, 12, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 7, "indent": null}, {"code": 7, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 7, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 7, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["var event = $gameMap.event(1);"]}, {"code": 655, "indent": 0, "parameters": ["var deltaX = $gamePlayer.x - event.x;"]}, {"code": 655, "indent": 0, "parameters": ["var deltaY = $gamePlayer.y - event.y;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["if (Math.abs(deltaX) <= Math.abs(deltaY)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gamePlayer.setDirection(deltaY < 0 ? 2 : 8); // 下或上"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gamePlayer.setDirection(deltaX < 0 ? 6 : 4); // 右或左"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 它的速度明显变慢了，再来一次！"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).setDirection($gamePlayer.direction());"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(1).setDirection($gamePlayer.direction());"]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).jump(0, 0);"]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 212, "indent": 0, "parameters": [0, 156, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 14, "parameters": [-2, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [-2, 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [0, 5, false]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 355, "indent": 0, "parameters": ["var event = $gameMap.event(1);"]}, {"code": 655, "indent": 0, "parameters": ["var deltaX = $gamePlayer.x - event.x;"]}, {"code": 655, "indent": 0, "parameters": ["var deltaY = $gamePlayer.y - event.y;"]}, {"code": 655, "indent": 0, "parameters": [""]}, {"code": 655, "indent": 0, "parameters": ["if (Math.abs(deltaX) <= Math.abs(deltaY)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gamePlayer.setDirection(deltaY < 0 ? 2 : 8); // 下或上"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gamePlayer.setDirection(deltaX < 0 ? 6 : 4); // 右或左"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).setDirection($gamePlayer.direction());"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(1).setDirection($gamePlayer.direction());"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 虽然还是让它跑了，但有效降低了它的速度，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 后面再抓它就不难了。我们继续前进吧。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 217]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 214}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["C", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": true, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 218}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 15}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 217}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 278, 11, 22, 4, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 7, "y": 0}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 217}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 278, 11, 22, 4, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 8, "y": 0}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 5, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 21, "y": 15}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 5, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 22, "y": 15}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 5, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 23, "y": 15}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 5, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 24, "y": 15}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 1, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 21, "y": 7}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 1, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 22, "y": 7}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 1, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 23, "y": 7}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"characterIndex": 1, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": false}], "x": 24, "y": 7}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(2);"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 23 感觉…越往山上怪物越少，这里都没有小怪了。"]}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(1).requestBalloon(9);"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 27 学姐，我觉得大概是为了捕捉稀有怪专门设计的特殊区域。"]}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 356, "indent": 0, "parameters": ["PushGab 10 也可能是进入了PVP区，注意防范其他玩家。"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(1).requestBalloon(4);"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 27 我会保护好学姐的！"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(11);"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 356, "indent": 0, "parameters": ["PushGab 10 猪田你还是优先照顾好自己吧。"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([282, 12, 'A'], false);"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 218}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 14}, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]}