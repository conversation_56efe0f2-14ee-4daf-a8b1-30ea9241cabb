{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "Grassland", "battleback2Name": "", "bgm": {"name": "ayato_02_Stopped clock", "pan": 0, "pitch": 100, "volume": 15}, "bgs": {"name": "雷暴雨-电闪雷鸣-持续性大雨-自然天气(thundersto_爱给网_aigei_com", "pan": 0, "pitch": 100, "volume": 50}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 32, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "StarlitSky", "parallaxShow": true, "parallaxSx": 1, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 57, "width": 43, "data": [5123, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 5122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5121, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5121, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5121, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5121, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5121, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5121, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5121, 5120, 5120, 5125, 5125, 5125, 5121, 5120, 5125, 5121, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 5120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5121, 5120, 5125, 5125, 5125, 5125, 5125, 5125, 5125, 5121, 5120, 5120, 5121, 5124, 5121, 5120, 5120, 5120, 5124, 5129, 5132, 5125, 5120, 5120, 5120, 5120, 5120, 5120, 5124, 5121, 5120, 5124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5121, 5125, 5125, 5125, 5125, 5125, 5125, 5125, 5125, 5120, 5120, 5124, 5121, 5124, 5121, 5120, 5125, 5129, 5132, 1581, 1582, 5133, 5121, 5120, 5120, 5120, 5120, 5124, 5125, 5121, 5124, 5121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5121, 5125, 5125, 5125, 5125, 5125, 5125, 5125, 5121, 5120, 5120, 5124, 5121, 5120, 5120, 5124, 5133, 1581, 2850, 2836, 2852, 1582, 5129, 5128, 5128, 5128, 5128, 5132, 5125, 5120, 5124, 5121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5125, 5125, 5125, 5125, 5125, 5125, 5125, 5125, 5121, 5120, 5124, 5125, 5129, 5128, 5128, 5132, 1581, 2850, 2821, 2844, 2846, 2838, 2837, 2849, 2853, 1582, 1557, 1582, 5133, 5125, 5125, 5121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5125, 5125, 5125, 5125, 5125, 5125, 5125, 5125, 5129, 5128, 5128, 5132, 1581, 2850, 2837, 2849, 2849, 2845, 2854, 3234, 3236, 2834, 2854, 2906, 2857, 2861, 1557, 2862, 1582, 5133, 5121, 5124, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5125, 5125, 5125, 5129, 5132, 5125, 5125, 5133, 1581, 2858, 1582, 1557, 2850, 2821, 2854, 3234, 3220, 3220, 3220, 3201, 3224, 2860, 2898, 2867, 2884, 2900, 1565, 1589, 2858, 1582, 5129, 5132, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5125, 5125, 5133, 1581, 1559, 5125, 5133, 1581, 2859, 2855, 1590, 1567, 2834, 2854, 3243, 3229, 3228, 3208, 3200, 3200, 3224, 2907, 2873, 2864, 2864, 2888, 1568, 1557, 2857, 2849, 2861, 1582, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5125, 5133, 1581, 2858, 1582, 5133, 1581, 2858, 2907, 2909, 1559, 1570, 2860, 2898, 2900, 1557, 2906, 3240, 3208, 3200, 3224, 2858, 2904, 2872, 2868, 2902, 1568, 1581, 3627, 3606, 3620, 2858, 1582, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5133, 1581, 2850, 2819, 2836, 2836, 2837, 2847, 2861, 1590, 1567, 1570, 2907, 2873, 2888, 1559, 2908, 2858, 3218, 3228, 3238, 2833, 2852, 2880, 2888, 2858, 1557, 2859, 2861, 3624, 3622, 2857, 2849, 2861, 1582, 0, 0, 0, 0, 0, 0, 0, 0, 2836, 2836, 2821, 2844, 2844, 2844, 2854, 1590, 1566, 1567, 1570, 1570, 2858, 2904, 2902, 1559, 2850, 2842, 3232, 2850, 2836, 2817, 2840, 2882, 2902, 2860, 1565, 1566, 1566, 1566, 1566, 1566, 1566, 1566, 1566, 1567, 5655, 5655, 0, 0, 0, 0, 0, 2844, 2844, 2854, 1590, 1566, 1566, 1566, 1567, 1569, 1570, 1570, 1575, 2857, 2849, 2861, 1559, 2832, 2840, 3232, 2856, 2824, 2816, 2840, 2908, 1590, 1603, 1568, 1569, 1569, 1569, 1569, 1569, 1595, 1569, 1569, 1570, 5653, 5652, 0, 0, 0, 0, 0, 1566, 1566, 1566, 1567, 1569, 1597, 1569, 1570, 1569, 1570, 1570, 2858, 3627, 3629, 2958, 1559, 2832, 2840, 3241, 3237, 2856, 2844, 2826, 2852, 1559, 1611, 1568, 1569, 1569, 1595, 1569, 1569, 1593, 1569, 1569, 1570, 5653, 5652, 5655, 0, 0, 0, 0, 1569, 1569, 1569, 1570, 1569, 1569, 1569, 1570, 1569, 1570, 1575, 2860, 1590, 1566, 1566, 1567, 2832, 2818, 2852, 3241, 3233, 3245, 2856, 2854, 1559, 1619, 1568, 1569, 1569, 1569, 1569, 1569, 1595, 1569, 1569, 1570, 5661, 5653, 5653, 5655, 0, 0, 0, 1569, 1593, 1596, 1570, 1569, 1569, 1569, 1570, 1577, 1575, 1590, 1566, 1567, 1569, 1569, 1570, 2832, 2816, 2818, 2836, 2836, 2852, 3243, 3245, 1559, 2862, 1573, 1577, 1577, 1577, 1577, 1577, 1569, 1569, 1569, 1575, 7243, 5653, 5653, 5653, 0, 0, 0, 1569, 1569, 1569, 1570, 1577, 1577, 1577, 1575, 2910, 2862, 1559, 1569, 1570, 1569, 1569, 1570, 2832, 2816, 2816, 2816, 2816, 2822, 2849, 2861, 1559, 3242, 2858, 1557, 3242, 2858, 2906, 2862, 2907, 2897, 2909, 2858, 1582, 5661, 5661, 5661, 0, 0, 0, 1577, 1577, 1577, 1575, 2850, 2836, 2836, 2852, 1590, 1566, 1567, 1569, 1570, 1569, 1569, 1570, 2832, 2820, 2844, 2844, 2844, 2854, 1590, 1566, 1567, 3232, 2848, 1557, 3232, 2860, 2908, 2090, 2850, 2836, 2836, 2819, 2836, 2852, 1582, 7659, 0, 0, 0, 2849, 2849, 2849, 2849, 2829, 2844, 2844, 2854, 1559, 1569, 1570, 1569, 1570, 1577, 1577, 1575, 2832, 2840, 1590, 1566, 1566, 1566, 1567, 1569, 1570, 3232, 2860, 1557, 3241, 3237, 2858, 2092, 2832, 2820, 2844, 2824, 2820, 2846, 2853, 1582, 0, 0, 0, 2980, 2980, 2981, 3005, 2848, 1590, 1566, 1566, 1567, 1569, 1570, 1577, 1575, 2858, 2899, 2909, 2856, 2854, 1559, 1651, 1652, 1653, 1570, 1569, 1570, 3241, 3237, 1557, 2862, 3244, 2860, 2958, 2834, 2854, 1555, 2832, 2840, 1556, 2857, 2853, 1582, 0, 0, 2964, 2988, 2998, 2859, 2855, 1559, 1569, 1569, 1570, 1569, 1570, 2859, 2839, 2855, 2908, 2862, 1566, 1566, 1567, 1569, 1569, 1569, 1570, 1569, 1570, 2858, 3244, 1565, 1566, 1589, 2907, 2909, 2860, 1555, 2859, 2845, 2846, 2861, 1556, 2857, 2861, 1582, 0, 2998, 2910, 2862, 1590, 1566, 1567, 1569, 1569, 1570, 1577, 1575, 2954, 2860, 1590, 1566, 1567, 1569, 1569, 1570, 1569, 1569, 1569, 1570, 1577, 1575, 2835, 2861, 1568, 1569, 1565, 1561, 1584, 1560, 1561, 1584, 1584, 1584, 1585, 1585, 1587, 1627, 1584, 1584, 2852, 1590, 1566, 1567, 1569, 1570, 1569, 1569, 1570, 2850, 2852, 2953, 2957, 1559, 1569, 1570, 1569, 1569, 1570, 1577, 1577, 1577, 1575, 2859, 2849, 2855, 3242, 1576, 1577, 1568, 1569, 1569, 1568, 1596, 1569, 1569, 1593, 1569, 1596, 1569, 1569, 1569, 1569, 2854, 1559, 1569, 1570, 1569, 1570, 1577, 1577, 1575, 2834, 2854, 1590, 1566, 1567, 1569, 1570, 1569, 1569, 1570, 2850, 2837, 2849, 2861, 3235, 3233, 3233, 3231, 3233, 3245, 1576, 1577, 1577, 1568, 1569, 1569, 1596, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1590, 1567, 1569, 1570, 1569, 1570, 2851, 2849, 2849, 2855, 1590, 1567, 1569, 1570, 1569, 1570, 1577, 1569, 1578, 2856, 2854, 3243, 3233, 3239, 2859, 2849, 2849, 2861, 1590, 1566, 1566, 1566, 1568, 1569, 1594, 1569, 1569, 1595, 1569, 1569, 1569, 1569, 1569, 1567, 1570, 1569, 1570, 1577, 1575, 2860, 1590, 1566, 1566, 1567, 1570, 1569, 1570, 1577, 1575, 2859, 2849, 2861, 3246, 1590, 1603, 1589, 1590, 1566, 1566, 1566, 1566, 1567, 1568, 1569, 1569, 1568, 1569, 1595, 1569, 1569, 1569, 1595, 1569, 1569, 1569, 1569, 1570, 1570, 1577, 1575, 2850, 2852, 1590, 1567, 1569, 1569, 1570, 1570, 1569, 1570, 1565, 1598, 1598, 1598, 1598, 1598, 1583, 1611, 1565, 1567, 1568, 1569, 1569, 1569, 1570, 1568, 1569, 1569, 1568, 1595, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 1569, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3438, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3147, 3126, 3105, 3128, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3132, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3144, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 54, 55, 0, 55, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 54, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 55, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 55, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 55, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 55, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 53, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 55, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 54, 54, 54, 55, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 55, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 9, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 158, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 158, 0, 0, 0, 0, 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 54, 54, 0, 54, 54, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 166, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 298, 21, 304, 0, 0, 0, 0, 54, 55, 0, 0, 0, 0, 0, 0, 0, 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 159, 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 306, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 167, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 49, 49, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 0, 0, 0, 0, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49, 50, 0, 55, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 159, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 0, 0, 0, 0, 0, 0, 59, 0, 0, 38, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 728, 0, 0, 0, 0, 0, 0, 0, 167, 0, 59, 0, 0, 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 159, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 159, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 55, 0, 0, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 280, 281, 282, 0, 53, 54, 0, 0, 55, 0, 288, 289, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 288, 289, 290, 53, 0, 0, 0, 0, 0, 54, 296, 297, 298, 53, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 296, 297, 298, 0, 0, 0, 0, 0, 0, 0, 304, 305, 306, 0, 288, 289, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 155, 54, 55, 53, 5, 304, 305, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 280, 281, 282, 53, 163, 0, 0, 55, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 306, 38, 39, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 288, 289, 290, 0, 0, 0, 0, 170, 51, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 47, 288, 289, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 46, 47, 296, 297, 298, 0, 0, 0, 0, 59, 59, 288, 289, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 0, 0, 0, 0, 0, 296, 297, 298, 0, 0, 0, 0, 0, 0, 0, 0, 54, 0, 0, 0, 304, 305, 306, 5, 0, 0, 170, 59, 59, 296, 297, 298, 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 306, 55, 0, 0, 0, 0, 0, 0, 0, 0, 6, 10, 0, 0, 21, 0, 0, 170, 0, 59, 59, 59, 304, 305, 306, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 170, 0, 0, 0, 59, 0, 59, 59, 67, 1, 21, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 59, 0, 59, 59, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 9, 0, 178, 0, 0, 0, 0, 0, 0, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 280, 281, 282, 59, 0, 59, 67, 0, 170, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 280, 281, 282, 0, 288, 289, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 288, 289, 290, 59, 0, 67, 170, 0, 59, 0, 0, 59, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 53, 288, 289, 290, 54, 296, 297, 298, 54, 55, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 296, 297, 298, 13, 14, 40, 59, 0, 59, 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 280, 281, 282, 305, 306, 0, 280, 281, 282, 0, 0, 0, 0, 158, 38, 39, 0, 67, 304, 305, 306, 0, 170, 0, 59, 0, 59, 0, 0, 59, 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 288, 289, 290, 280, 281, 282, 288, 289, 290, 55, 0, 0, 0, 15, 46, 47, 0, 0, 0, 21, 0, 0, 59, 0, 59, 0, 59, 0, 0, 22, 45, 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 0, 296, 297, 298, 288, 289, 290, 296, 297, 298, 0, 55, 0, 0, 0, 0, 0, 0, 0, 0, 170, 0, 0, 59, 0, 59, 0, 67, 0, 0, 0, 0, 0, 68, 0, 0, 0, 59, 0, 0, 0, 0, 22, 0, 304, 305, 306, 296, 297, 298, 304, 305, 306, 0, 0, 55, 0, 0, 0, 0, 10, 0, 0, 59, 0, 0, 59, 0, 59, 0, 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 7, 0, 48, 304, 305, 306, 13, 14, 0, 15, 49, 49, 49, 0, 0, 0, 0, 170, 0, 59, 0, 0, 59, 0, 67, 1, 0, 0, 0, 68, 0, 0, 59, 0, 0, 0, 59, 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 170, 0, 59, 0, 59, 0, 0, 59, 0, 0, 0, 0, 68, 0, 280, 281, 282, 59, 39, 0, 0, 67, 0, 0, 0, 0, 0, 80, 81, 82, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 59, 0, 59, 729, 0, 67, 4, 0, 0, 0, 59, 0, 288, 289, 290, 59, 47, 0, 0, 0, 0, 0, 0, 0, 0, 88, 89, 90, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 170, 59, 0, 59, 0, 59, 0, 35, 5, 0, 0, 68, 0, 59, 0, 296, 297, 298, 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 59, 0, 67, 0, 43, 0, 0, 68, 59, 0, 59, 0, 304, 305, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 59, 0, 0, 0, 68, 0, 0, 59, 59, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 121, "indent": 0, "parameters": [68, 68, 0]}, {"code": 121, "indent": 0, "parameters": [70, 70, 0]}, {"code": 111, "indent": 0, "parameters": [0, 22, 0]}, {"code": 356, "indent": 1, "parameters": [">地图永久漂浮文字 : 漂浮文字[1] : 清除"]}, {"code": 356, "indent": 1, "parameters": [">地图永久漂浮文字 : 漂浮文字[2] : 清除"]}, {"code": 356, "indent": 1, "parameters": [">高级变量框 : 框设置[1] : 隐藏"]}, {"code": 356, "indent": 1, "parameters": [">高级变量框 : 框设置[2] : 隐藏"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-68, -68, 0, 68], 1, false]}, {"code": 356, "indent": 0, "parameters": ["particle set 雨 weather"]}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 修改聚焦偏移 : 位置[384,0]"]}, {"code": 231, "indent": 0, "parameters": [60, "白色线条864-576x1008", 0, 0, 864, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [48, "黑幕", 0, 0, 864, 0, 40, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [49, "", 0, 0, 864, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [49, 0, 0, 0, 864, 0, 100, 100, 255, 0, 30, false]}, {"code": 231, "indent": 0, "parameters": [50, "", 0, 0, 864, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 播放简单状态元集合 : 集合[木屋对话影绘]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 播放简单状态元集合 : 集合[木屋对话]"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 7, "indent": 0}, {"code": 7, "indent": 0}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 7, "indent": null}, {"code": 7, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 7, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 7, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 7, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 7, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 7, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 111, "indent": 0, "parameters": [0, 233, 1]}, {"code": 122, "indent": 1, "parameters": [58, 58, 1, 0, 50]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 102], 30, false]}, {"code": 356, "indent": 1, "parameters": ["infotext : 11 : 1 : 590 : 140 : 28 : 150 : 0 : 1 : 0 : 亚  丝  娜  信  息  更  新"]}, {"code": 356, "indent": 1, "parameters": ["addLog 亚丝娜的情感发生了一点变化...."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 居然还下起雨了……鸽子呀，抓你是真不容易啊！ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 放心，我会没有痛苦地送你上路的！ "]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 233, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 300, "switch1Valid": true, "switch2Id": 233, "switch2Valid": true, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 121, "indent": 0, "parameters": [68, 68, 0]}, {"code": 111, "indent": 0, "parameters": [0, 22, 0]}, {"code": 356, "indent": 1, "parameters": [">地图永久漂浮文字 : 漂浮文字[1] : 清除"]}, {"code": 356, "indent": 1, "parameters": [">地图永久漂浮文字 : 漂浮文字[2] : 清除"]}, {"code": 356, "indent": 1, "parameters": [">高级变量框 : 框设置[1] : 隐藏"]}, {"code": 356, "indent": 1, "parameters": [">高级变量框 : 框设置[2] : 隐藏"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-68, -68, 0, 68], 1, false]}, {"code": 356, "indent": 0, "parameters": ["particle set 雨 weather"]}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 修改聚焦偏移 : 位置[384,0]"]}, {"code": 231, "indent": 0, "parameters": [60, "白色线条864-576x1008", 0, 0, 864, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [48, "黑幕", 0, 0, 864, 0, 40, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [49, "", 0, 0, 864, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [49, 0, 0, 0, 864, 0, 100, 100, 255, 0, 30, false]}, {"code": 231, "indent": 0, "parameters": [50, "", 0, 0, 864, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 播放简单状态元集合 : 集合[木屋对话影绘]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 播放简单状态元集合 : 集合[木屋对话]"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 7, "indent": 0}, {"code": 7, "indent": 0}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 7, "indent": null}, {"code": 7, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 7, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 7, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 7, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 7, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 7, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 居然还下起雨了……鸽子呀，抓你是真不容易啊！ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 放心，我会没有痛苦地送你上路的！ "]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 24}, {"id": 2, "name": "鸽子", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 8, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 45, "parameters": ["$gameMap.event(1).requestBalloon(3);"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离1 : 触发独立开关 : A"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 45, "parameters": ["$gameMap.event(1).requestBalloon(3);"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 213, "indent": 0, "parameters": [0, 12, false]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 212, "indent": 0, "parameters": [0, 148, false]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 本事件 : 纵向挤扁 : 时间[60] : 纵向比例[1.5]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[18]系统提示：\\c[0]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[101]【桐人小队】\\c[0]在\\c[101]【凄怆山脉-山顶】\\c[0]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 获得稀有素材\\c[14]【秘银之羽】（玩家死亡时掉落）\\c[0]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 获得稀有食材\\c[14]【杂烩鸽肉】（玩家死亡时掉落）\\c[0] "]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 喂喂喂，不是吧？区域广播？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那不是摆明了鼓励其他玩家来抢？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在最好赶紧去和亚丝娜汇合！"]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 222]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 45, "parameters": ["$gameMap.event(1).requestBalloon(3);"], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 25}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 2}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([284, 6, 'A'], false);"]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 0]}, {"code": 356, "indent": 0, "parameters": [">菜单GIF : GIF[24] : 隐藏"]}, {"code": 356, "indent": 0, "parameters": [">菜单GIF : GIF[21] : 显示"]}, {"code": 201, "indent": 0, "parameters": [0, 277, 3, 0, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 21, "y": 31}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 9}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 9}, {"id": 6, "name": "打雷特效", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [90]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 10]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 60) + 120;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 102], 10, false]}, {"code": 232, "indent": 1, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 255, 0, 10, false]}, {"code": 356, "indent": 1, "parameters": ["particle set 闪电 screen 闪电 above"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 30) + 1;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 1, "parameters": ["particle clear 闪电"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 15, false]}, {"code": 232, "indent": 1, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 0, 0, 15, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 90) + 240;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 7, 2]}, {"code": 111, "indent": 1, "parameters": [1, 77, 0, 2, 1]}, {"code": 223, "indent": 2, "parameters": [[0, 0, 0, 102], 3, false]}, {"code": 356, "indent": 2, "parameters": ["particle set 闪电 screen 闪电 above"]}, {"code": 232, "indent": 2, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 255, 0, 3, false]}, {"code": 355, "indent": 2, "parameters": ["var waitFrames = Math.floor(Math.random() * 2) + 3;"]}, {"code": 655, "indent": 2, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 2, "parameters": ["particle clear 闪电"]}, {"code": 230, "indent": 2, "parameters": [15]}, {"code": 223, "indent": 2, "parameters": [[-68, -68, 0, 68], 10, false]}, {"code": 232, "indent": 2, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 0, 0, 15, false]}, {"code": 355, "indent": 2, "parameters": ["var waitFrames = Math.floor(Math.random() * 90) + 180;"]}, {"code": 655, "indent": 2, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 7, 3]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 102], 3, false]}, {"code": 356, "indent": 1, "parameters": ["particle set 闪电 screen 闪电 above"]}, {"code": 232, "indent": 1, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 255, 0, 3, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 2) + 3;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 1, "parameters": ["particle clear 闪电"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 10, false]}, {"code": 232, "indent": 1, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 0, 0, 15, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 3) + 14;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 102], 5, false]}, {"code": 356, "indent": 1, "parameters": ["particle set 闪电 screen 闪电 above"]}, {"code": 232, "indent": 1, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 255, 0, 5, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 5) + 5;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 1, "parameters": ["particle clear 闪电"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 15, false]}, {"code": 232, "indent": 1, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 0, 0, 15, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 120) + 180;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 16, "y": 24}, {"id": 7, "name": "ID007-BOSS", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "$BigMonster1", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": true, "trigger": 0, "walkAnime": true}], "x": 19, "y": 10}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 221}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 原来，是三只鸽子吗……？"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "B", 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 75, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[2] : 像素偏移[0,-48] : 时间[30]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[48,48] : 时间[30]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[-48,48] : 时间[30]"]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 212, "indent": 0, "parameters": [7, 157, false]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 批量事件[2,4,5] : 缩小消失 : 时间[60]"]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 1]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 事件[7] : 放大出现 : 时间[45] : 缓冲时间[30]"]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 居然合体了！ 似乎不好对付……"]}, {"code": 212, "indent": 0, "parameters": [7, 151, true]}, {"code": 212, "indent": 0, "parameters": [7, 151, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爆绀杂烩鸽>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咕咕！"]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但我还是不会输的。要上了！"]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 修改聚焦偏移 : 位置[0,0]"]}, {"code": 235, "indent": 0, "parameters": [48]}, {"code": 235, "indent": 0, "parameters": [49]}, {"code": 235, "indent": 0, "parameters": [50]}, {"code": 235, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["particle clear 雨"]}, {"code": 301, "indent": 0, "parameters": [0, 6, false, false]}, {"code": 121, "indent": 0, "parameters": [185, 185, 1]}, {"code": 356, "indent": 0, "parameters": ["particle set 雨 weather 雨 above"]}, {"code": 231, "indent": 0, "parameters": [60, "白色线条864-576x1008", 0, 0, 864, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [48, "黑幕", 0, 0, 864, 0, 40, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [49, "", 0, 0, 864, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [49, 0, 0, 0, 864, 0, 100, 100, 255, 0, 30, false]}, {"code": 231, "indent": 0, "parameters": [50, "", 0, 0, 864, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 播放简单状态元集合 : 集合[木屋舔穴影绘]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 播放简单状态元集合 : 集合[木屋舔穴]"]}, {"code": 356, "indent": 0, "parameters": [">菜单GIF : GIF[21] : 隐藏"]}, {"code": 356, "indent": 0, "parameters": [">菜单GIF : GIF[24] : 显示"]}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 修改聚焦偏移 : 位置[384,0]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 111, "indent": 0, "parameters": [0, 233, 1]}, {"code": 122, "indent": 1, "parameters": [31, 31, 1, 0, 5]}, {"code": 122, "indent": 1, "parameters": [33, 33, 1, 0, 2]}, {"code": 122, "indent": 1, "parameters": [35, 35, 1, 0, 1]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 102], 30, false]}, {"code": 356, "indent": 1, "parameters": ["infotext : 11 : 1 : 590 : 140 : 28 : 150 : 0 : 1 : 0 : 亚  丝  娜  信  息  更  新"]}, {"code": 356, "indent": 1, "parameters": ["addLog 亚丝娜的经验似乎发生了一些变化..."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 212, "indent": 0, "parameters": [7, 148, false]}, {"code": 213, "indent": 0, "parameters": [7, 12, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爆绀杂烩鸽>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咕……！"]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 事件[7] : 纵向挤扁 : 时间[60] : 纵向比例[1.5]"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[18]系统提示：\\c[0]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[101]【桐人小队】\\c[0]在\\c[101]【凄怆山脉-山顶】\\c[0]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 获得稀有素材\\c[14]【秘银之羽】（玩家死亡时掉落）\\c[0] "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 获得稀有食材\\c[14]【杂烩鸽肉】（玩家死亡时掉落）\\c[0] "]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 喂喂喂，不是吧？区域广播？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那不是摆明了鼓励其他玩家来抢？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在最好赶紧去和亚丝娜汇合！"]}, {"code": 122, "indent": 0, "parameters": [80, 80, 0, 4, "\"回到山中小屋与亚丝娜汇合\""]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 4]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 233, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 221}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 121, "indent": 0, "parameters": [70, 70, 0]}, {"code": 111, "indent": 0, "parameters": [0, 22, 0]}, {"code": 356, "indent": 1, "parameters": [">地图永久漂浮文字 : 漂浮文字[1] : 清除"]}, {"code": 356, "indent": 1, "parameters": [">地图永久漂浮文字 : 漂浮文字[2] : 清除"]}, {"code": 356, "indent": 1, "parameters": [">高级变量框 : 框设置[1] : 隐藏"]}, {"code": 356, "indent": 1, "parameters": [">高级变量框 : 框设置[2] : 隐藏"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-68, -68, 0, 68], 1, false]}, {"code": 356, "indent": 0, "parameters": ["particle set 雨 weather"]}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 修改聚焦偏移 : 位置[384,0]"]}, {"code": 231, "indent": 0, "parameters": [60, "白色线条864-576x1008", 0, 0, 864, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [48, "黑幕", 0, 0, 864, 0, 40, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [49, "", 0, 0, 864, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [49, 0, 0, 0, 864, 0, 100, 100, 255, 0, 30, false]}, {"code": 231, "indent": 0, "parameters": [50, "", 0, 0, 864, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 播放简单状态元集合 : 集合[木屋对话影绘]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 播放简单状态元集合 : 集合[木屋对话]"]}, {"code": 203, "indent": 0, "parameters": [2, 0, 19, 11, 2]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 原来，是三只鸽子吗……？"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "B", 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 75, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[2] : 像素偏移[0,-48] : 时间[30]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[48,48] : 时间[30]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[-48,48] : 时间[30]"]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 212, "indent": 0, "parameters": [7, 157, false]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 批量事件[2,4,5] : 缩小消失 : 时间[60]"]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 1]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 事件[7] : 放大出现 : 时间[45] : 缓冲时间[30]"]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 居然合体了！ 似乎不好对付……"]}, {"code": 212, "indent": 0, "parameters": [7, 151, true]}, {"code": 212, "indent": 0, "parameters": [7, 151, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爆绀杂烩鸽>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咕咕！"]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但我还是不会输的。要上了！"]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 修改聚焦偏移 : 位置[0,0]"]}, {"code": 235, "indent": 0, "parameters": [48]}, {"code": 235, "indent": 0, "parameters": [49]}, {"code": 235, "indent": 0, "parameters": [50]}, {"code": 235, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["particle clear 雨"]}, {"code": 301, "indent": 0, "parameters": [0, 6, false, false]}, {"code": 121, "indent": 0, "parameters": [185, 185, 1]}, {"code": 356, "indent": 0, "parameters": ["particle set 雨 weather 雨 above"]}, {"code": 231, "indent": 0, "parameters": [60, "白色线条864-576x1008", 0, 0, 864, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [48, "黑幕", 0, 0, 864, 0, 40, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [49, "", 0, 0, 864, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [49, 0, 0, 0, 864, 0, 100, 100, 255, 0, 30, false]}, {"code": 231, "indent": 0, "parameters": [50, "", 0, 0, 864, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 播放简单状态元集合 : 集合[木屋舔穴影绘]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 播放简单状态元集合 : 集合[木屋舔穴]"]}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 修改聚焦偏移 : 位置[384,0]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 212, "indent": 0, "parameters": [7, 148, false]}, {"code": 213, "indent": 0, "parameters": [7, 12, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爆绀杂烩鸽>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咕……！"]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 事件[7] : 纵向挤扁 : 时间[60] : 纵向比例[1.5]"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[18]系统提示：\\c[0]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[101]【桐人小队】\\c[0]在\\c[101]【凄怆山脉-山顶】\\c[0]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 获得稀有素材\\c[14]【秘银之羽】（玩家死亡时掉落）\\c[0] "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 获得稀有食材\\c[14]【杂烩鸽肉】（玩家死亡时掉落）\\c[0] "]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 喂喂喂，不是吧？区域广播？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那不是摆明了鼓励其他玩家来抢？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在最好赶紧去和亚丝娜汇合！"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [48]}, {"code": 235, "indent": 0, "parameters": [49]}, {"code": 235, "indent": 0, "parameters": [50]}, {"code": 235, "indent": 0, "parameters": [60]}, {"code": 121, "indent": 0, "parameters": [70, 70, 1]}, {"code": 111, "indent": 0, "parameters": [0, 22, 0]}, {"code": 356, "indent": 1, "parameters": [">地图永久漂浮文字 : 漂浮文字[1] : 创建 : 样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">地图永久漂浮文字 : 漂浮文字[2] : 创建 : 样式[3]"]}, {"code": 356, "indent": 1, "parameters": [">高级变量框 : 框设置[1] : 显示"]}, {"code": 356, "indent": 1, "parameters": [">高级变量框 : 框设置[2] : 显示"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([284, 6, 'A'], false);"]}, {"code": 356, "indent": 0, "parameters": ["particle clear 闪电"]}, {"code": 356, "indent": 0, "parameters": ["particle clear 雨"]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 0]}, {"code": 129, "indent": 0, "parameters": [1, 0, false]}, {"code": 129, "indent": 0, "parameters": [8, 1, false]}, {"code": 201, "indent": 0, "parameters": [0, 81, 20, 14, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 233, "switch1Valid": true, "switch2Id": 300, "switch2Valid": true, "variableId": 100, "variableValid": false, "variableValue": 221}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 原来，是三只鸽子吗……？"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "B", 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 75, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[2] : 像素偏移[0,-48] : 时间[30]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[48,48] : 时间[30]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[-48,48] : 时间[30]"]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 212, "indent": 0, "parameters": [7, 157, false]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 批量事件[2,4,5] : 缩小消失 : 时间[60]"]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 1]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 事件[7] : 放大出现 : 时间[45] : 缓冲时间[30]"]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 居然合体了！ 似乎不好对付……"]}, {"code": 212, "indent": 0, "parameters": [7, 151, true]}, {"code": 212, "indent": 0, "parameters": [7, 151, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爆绀杂烩鸽>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咕咕！"]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但我还是不会输的。要上了！"]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 修改聚焦偏移 : 位置[0,0]"]}, {"code": 235, "indent": 0, "parameters": [48]}, {"code": 235, "indent": 0, "parameters": [49]}, {"code": 235, "indent": 0, "parameters": [50]}, {"code": 235, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["particle clear 雨"]}, {"code": 301, "indent": 0, "parameters": [0, 6, false, false]}, {"code": 121, "indent": 0, "parameters": [185, 185, 1]}, {"code": 356, "indent": 0, "parameters": ["particle set 雨 weather 雨 above"]}, {"code": 231, "indent": 0, "parameters": [60, "白色线条864-576x1008", 0, 0, 864, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [48, "黑幕", 0, 0, 864, 0, 40, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [49, "", 0, 0, 864, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [49, 0, 0, 0, 864, 0, 100, 100, 255, 0, 30, false]}, {"code": 231, "indent": 0, "parameters": [50, "", 0, 0, 864, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 播放简单状态元集合 : 集合[木屋舔穴影绘]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 播放简单状态元集合 : 集合[木屋舔穴]"]}, {"code": 356, "indent": 0, "parameters": [">菜单GIF : GIF[21] : 隐藏"]}, {"code": 356, "indent": 0, "parameters": [">菜单GIF : GIF[24] : 显示"]}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 修改聚焦偏移 : 位置[384,0]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 212, "indent": 0, "parameters": [7, 148, false]}, {"code": 213, "indent": 0, "parameters": [7, 12, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爆绀杂烩鸽>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咕……！"]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 事件[7] : 纵向挤扁 : 时间[60] : 纵向比例[1.5]"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[18]系统提示：\\c[0]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[101]【桐人小队】\\c[0]在\\c[101]【凄怆山脉-山顶】\\c[0]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 获得稀有素材\\c[14]【秘银之羽】（玩家死亡时掉落）\\c[0] "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 获得稀有食材\\c[14]【杂烩鸽肉】（玩家死亡时掉落）\\c[0] "]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 喂喂喂，不是吧？区域广播？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那不是摆明了鼓励其他玩家来抢？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在最好赶紧去和亚丝娜汇合！"]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 4]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 16}, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]}