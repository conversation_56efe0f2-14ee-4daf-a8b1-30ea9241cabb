{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "S - かおりとぬくもり", "pan": 0, "pitch": 100, "volume": 25}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 21, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 50, "width": 30, "data": [2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3859, 3857, 3857, 3857, 3869, 0, 3867, 3857, 3857, 3857, 3861, 0, 0, 3859, 3857, 3857, 3857, 3869, 0, 3867, 3857, 3857, 3857, 3861, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 0, 0, 0, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3868, 0, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 0, 0, 0, 0, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 0, 0, 0, 0, 0, 0, 0, 3866, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3866, 0, 0, 3866, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3866, 0, 0, 0, 0, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 3868, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3868, 0, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3866, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 0, 0, 0, 0, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3866, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3866, 0, 0, 3866, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 3865, 3857, 3857, 3857, 3869, 0, 3867, 3857, 3857, 3857, 3863, 0, 0, 3865, 3857, 3857, 3857, 3869, 0, 3867, 3857, 3857, 3857, 3863, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 79, 15, 23, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 14, "y": 20}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 79, 15, 23, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 15, "y": 20}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "爱丽丝运动像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 爱丽丝"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "爱丽丝运动像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 爱丽丝"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 19}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "克莱因现实", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 其他"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 19}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [91]}, {"code": 121, "indent": 0, "parameters": [21, 21, 1]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 40, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 42, "parameters": [255], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[6] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[7] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[8] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[9] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[10] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[11] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[12] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[13] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[14] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[15] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[16] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[17] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[18] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[19] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[20] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[21] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[22] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[23] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[24] : 强制刷新文本"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 11, "y": 0}, {"id": 6, "name": "直播1", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "克莱因现实", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/5直播(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关直播(一)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "<PERSON><PERSON>", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["D_TEXT \\fb【黑卡蒂】今天是扭屁股练习...\\c[101]♥\\c[0]【直播】 48"]}, {"code": 231, "indent": 1, "parameters": [100, "", 0, 0, 225, 25, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [98, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 100, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 100, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直播扭屁股]"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF SetCreater 5 0 30 25 25 30 0"]}, {"code": 355, "indent": 1, "parameters": ["Zzy.BCF.SetPeople(2000)"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF UsePrivate 1"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater true"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哈哈,运气真好，正好有在开播,扭屁股练习嘛，尺度"]}, {"code": 401, "indent": 1, "parameters": ["\\dac已经开始越来越大了嘛~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac这就是你最近关注的色情女主播吗?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac对啊,怎么样不错吧，卡蒂酱,最开始关注的时候还是个有点"]}, {"code": 401, "indent": 1, "parameters": ["\\dac保守的新人主播来着,最近终于开始越来越放得开了,粉丝好像也"]}, {"code": 401, "indent": 1, "parameters": ["\\dac慢慢多起来了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我有点怀疑你这家伙日常都在干一些什么，好了,好了,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不是打游戏吗,快点关掉吧。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac真冷漠啊桐人老弟，这么雪白的大屁屁加粉嫩的小穴都不欣赏下,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嘛,至少让我也发个弹幕再走嘛。桐人老弟，你说发什么好呢？"]}, {"code": 356, "indent": 1, "parameters": [">字符串核心 : 字符串[10] : 使用弹出框修改 : 请输入"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 初始化 : 样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 修改样式属性-内容文本 : 字符串[10]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 修改样式属性-额外位置偏移 : 偏移[-10,250]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 创建 : 常规弹幕"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 11 发好没啊？"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac好啦好啦~不要急嘛，我要饱含感情地去打字，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac这条弹幕才更有可能吸引小卡蒂的注意嘛！"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 356, "indent": 1, "parameters": ["ForceGabClear"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嘿嘿嘿嘿~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac希望小卡蒂多多看到我发的弹幕，说不定有一天会喜欢上我呀！"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac弹幕又不显示名字，你是不是傻？"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac也对哦！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac唉，有时候真因为认识你感到丢脸...."]}, {"code": 401, "indent": 1, "parameters": ["\\dac行了行了，赶紧关了直播进入游戏吧。"]}, {"code": 250, "indent": 1, "parameters": [{"name": "鼠标点击声音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater false"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 立刻清除全部弹幕"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 235, "indent": 1, "parameters": [98]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 1, "parameters": ["$gameVariables.setValue(194,$gameStrings.value(10));"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "<PERSON><PERSON>", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["D_TEXT \\fb【黑卡蒂】今天是扭屁股练习...\\c[101]♥\\c[0]【直播】 48"]}, {"code": 231, "indent": 1, "parameters": [100, "", 0, 0, 225, 25, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [98, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 100, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 100, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直播扭屁股]"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF SetCreater 5 0 30 25 25 30 0"]}, {"code": 355, "indent": 1, "parameters": ["Zzy.BCF.SetPeople(2000)"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF UsePrivate 1"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater true"]}, {"code": 118, "indent": 1, "parameters": ["选择停止"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac停止观看"]}, {"code": 102, "indent": 1, "parameters": [[" 是", " 否"], 1, 0, 2, 2]}, {"code": 402, "indent": 1, "parameters": [0, " 是"]}, {"code": 356, "indent": 2, "parameters": ["ZzyBCF Creater false"]}, {"code": 356, "indent": 2, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 2, "parameters": [">地图临时漂浮视频弹幕 : 立刻清除全部弹幕"]}, {"code": 235, "indent": 2, "parameters": [99]}, {"code": 235, "indent": 2, "parameters": [98]}, {"code": 235, "indent": 2, "parameters": [100]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 2, "parameters": ["$gameVariables.setValue(194,$gameStrings.value(10));"]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, " 否"]}, {"code": 119, "indent": 2, "parameters": ["选择停止"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 16}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 83}, "directionFix": false, "image": {"tileId": 0, "characterName": "!SF_Door2", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 心灵印痕"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图滤镜 : 着色滤镜 : 致幻色 : 255"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 25, true]}, {"code": 117, "indent": 0, "parameters": [18]}, {"code": 138, "indent": 0, "parameters": [[-255, -255, -255, 0]]}, {"code": 355, "indent": 0, "parameters": ["SceneManager.push(Scene_Item);"]}, {"code": 121, "indent": 0, "parameters": [17, 17, 0]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 25, false]}, {"code": 138, "indent": 0, "parameters": [[0, 0, 0, 0]]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 17, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 83}, "directionFix": false, "image": {"tileId": 0, "characterName": "!SF_Door2", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 心灵印痕"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图滤镜 : 着色滤镜 : 致幻色 : 0"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 25, true]}, {"code": 138, "indent": 0, "parameters": [[-255, -255, -255, 0]]}, {"code": 355, "indent": 0, "parameters": ["SceneManager.push(Scene_Item);"]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 25, false]}, {"code": 138, "indent": 0, "parameters": [[0, 0, 0, 0]]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 16}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 149, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 100}, "directionFix": true, "image": {"tileId": 0, "characterName": "成人女性用", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6女警车震"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 5, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 5 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 5]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 5 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 149, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "成人女性用", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6女警车震"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关女警车震的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [213, 213, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 201, "indent": 1, "parameters": [0, 117, 15, 18, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）（中くらい）", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 D_激しめにフェラ3 35 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[15]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[爱丽丝同事车震]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的女子声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]今....晚....嗯唔~克...嗯♥♥..因~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的男子子声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]呼~♥♥...有...呢?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的女子声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]啊~啊~♥♥~~谷♥♥~~嗯唔,嗯唔♥♥...人...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的男子子声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]好孩子~没..白...疼爱你♥♥~"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 149, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "成人女性用", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6女警车震"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关女警车震的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [213, 213, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 201, "indent": 1, "parameters": [0, 117, 15, 18, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）（中くらい）", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 D_激しめにフェラ3 35 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[15]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[爱丽丝同事车震]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的女子声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]今....晚....嗯唔~克...嗯♥♥..因~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的男子子声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]呼~♥♥...有...呢?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的女子声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]啊~啊~♥♥~~谷♥♥~~嗯唔,嗯唔♥♥...人...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的男子子声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]好孩子~没..白...疼爱你♥♥~"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "成人女性用", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6女警车震"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关女警车震的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [213, 213, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 201, "indent": 1, "parameters": [0, 117, 15, 18, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）（中くらい）", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 D_激しめにフェラ3 35 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[15]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[爱丽丝同事车震]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的女子声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]今....晚....嗯唔~克...嗯♥♥..因~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的男子子声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]呼~♥♥...有...呢?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的女子声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]啊~啊~♥♥~~谷♥♥~~嗯唔,嗯唔♥♥...人...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac模糊不清的男子子声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]好孩子~没..白...疼爱你♥♥~"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 16}, {"id": 9, "name": "直播2", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 143, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 80}, "directionFix": true, "image": {"tileId": 0, "characterName": "克莱因现实", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6直播(二)"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 5, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 5 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 5]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 5 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 143, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "克莱因现实", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6直播(二)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关直播(二)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "<PERSON><PERSON>", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["D_TEXT \\fb【黑卡蒂】今天给大家看看胸...\\c[101]♥\\c[0]【直播】 48"]}, {"code": 231, "indent": 1, "parameters": [100, "", 0, 0, 225, 25, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [98, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 100, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 100, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃直播2]"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF SetCreater 5 0 30 25 25 30 0"]}, {"code": 355, "indent": 1, "parameters": ["Zzy.BCF.SetPeople(2000)"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF UsePrivate 2"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater true"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}果然卡蒂酱的胸也是棒的不行,而且这个形状...."]}, {"code": 356, "indent": 1, "parameters": ["PushGab 12 这家伙又在看昨天那个女主播嘛.."]}, {"code": 230, "indent": 1, "parameters": [100]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}虽然有被人玩弄过的痕迹,但是这个粉嫩的奶头,嗯，应该是是最近才破处的,\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}是男朋友吗，而且凭我多年经验，卡蒂酱的年纪应该和桐人老弟亚丝娜他们差不多吧。"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 12 年纪和我们差不多,表示是学生吗?"]}, {"code": 230, "indent": 1, "parameters": [100]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}学生时期就能开始玩这么大了,以后卡蒂酱绝对是有以后AV热门女优的潜力啊!\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}这时候必须得发个弹幕纪念一下!"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac字好小，但勉强还是可以到得到。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因发的弹幕是……"]}, {"code": 356, "indent": 1, "parameters": [">字符串核心 : 字符串[10] : 还原字符串"]}, {"code": 356, "indent": 1, "parameters": [">字符串核心 : 字符串[10] : 使用弹出框修改 : 请输入"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 初始化 : 样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 修改样式属性-内容文本 : 字符串[10]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 修改样式属性-额外位置偏移 : 偏移[-10,250]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 创建 : 常规弹幕"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}完美的弹幕~不过看的稍微有点心痒痒的,特别爱丽丝大姐头那完美形状的屁股在脑海里挥之不去，\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}不行，必须立刻去游戏找大姐头述说这个思念之情才行!"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 1, "parameters": [{"name": "鼠标点击声音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater false"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 立刻清除全部弹幕"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 235, "indent": 1, "parameters": [98]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 355, "indent": 1, "parameters": ["$gameVariables.setValue(194,$gameStrings.value(10));"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "<PERSON><PERSON>", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["D_TEXT \\fb【黑卡蒂】今天给大家看看胸...\\c[101]♥\\c[0]【直播】 48"]}, {"code": 231, "indent": 1, "parameters": [100, "", 0, 0, 225, 25, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [98, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 100, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 100, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃直播2]"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF SetCreater 5 0 30 25 25 30 0"]}, {"code": 355, "indent": 1, "parameters": ["Zzy.BCF.SetPeople(2000)"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF UsePrivate 2"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater true"]}, {"code": 118, "indent": 1, "parameters": ["选择停止"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac停止观看"]}, {"code": 102, "indent": 1, "parameters": [[" 是", " 否"], 1, 0, 2, 2]}, {"code": 402, "indent": 1, "parameters": [0, " 是"]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 2, "parameters": [{"name": "鼠标点击声音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": ["ZzyBCF Creater false"]}, {"code": 356, "indent": 2, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 2, "parameters": [">地图临时漂浮视频弹幕 : 立刻清除全部弹幕"]}, {"code": 235, "indent": 2, "parameters": [99]}, {"code": 235, "indent": 2, "parameters": [98]}, {"code": 235, "indent": 2, "parameters": [100]}, {"code": 356, "indent": 2, "parameters": ["stop_bgs2"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 355, "indent": 2, "parameters": ["$gameVariables.setValue(194,$gameStrings.value(10));"]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, " 否"]}, {"code": 119, "indent": 2, "parameters": ["选择停止"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 143, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "克莱因现实", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6直播(二)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关直播(二)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "<PERSON><PERSON>", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["D_TEXT \\fb【黑卡蒂】今天给大家看看胸...\\c[101]♥\\c[0]【直播】 48"]}, {"code": 231, "indent": 1, "parameters": [100, "", 0, 0, 225, 25, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [98, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 100, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 100, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃直播2]"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF SetCreater 5 0 30 25 25 30 0"]}, {"code": 355, "indent": 1, "parameters": ["Zzy.BCF.SetPeople(2000)"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF UsePrivate 2"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater true"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}果然卡蒂酱的胸也是棒的不行,而且这个形状...."]}, {"code": 356, "indent": 1, "parameters": ["PushGab 12 这家伙又在看昨天那个女主播嘛.."]}, {"code": 230, "indent": 1, "parameters": [100]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}虽然有被人玩弄过的痕迹,但是这个粉嫩的奶头,嗯，应该是是最近才破处的,\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}是男朋友吗，而且凭我多年经验，卡蒂酱的年纪应该和桐人老弟亚丝娜他们差不多吧。"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 12 年纪和我们差不多,表示是学生吗?"]}, {"code": 230, "indent": 1, "parameters": [100]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}学生时期就能开始玩这么大了,以后卡蒂酱绝对是有以后AV热门女优的潜力啊!\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}这时候必须得发个弹幕纪念一下!"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac字好小，但勉强还是可以到得到。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因发的弹幕是……"]}, {"code": 356, "indent": 1, "parameters": [">字符串核心 : 字符串[10] : 还原字符串"]}, {"code": 356, "indent": 1, "parameters": [">字符串核心 : 字符串[10] : 使用弹出框修改 : 请输入"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 初始化 : 样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 修改样式属性-内容文本 : 字符串[10]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 修改样式属性-额外位置偏移 : 偏移[-10,250]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 创建 : 常规弹幕"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}完美的弹幕~不过看的稍微有点心痒痒的,特别爱丽丝大姐头那完美形状的屁股在脑海里挥之不去，\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}不行，必须立刻去游戏找大姐头述说这个思念之情才行!"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 1, "parameters": [{"name": "鼠标点击声音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater false"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 立刻清除全部弹幕"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 235, "indent": 1, "parameters": [98]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 355, "indent": 1, "parameters": ["$gameVariables.setValue(194,$gameStrings.value(10));"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "<PERSON><PERSON>", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["D_TEXT \\fb【黑卡蒂】今天给大家看看胸...\\c[101]♥\\c[0]【直播】 48"]}, {"code": 231, "indent": 1, "parameters": [100, "", 0, 0, 225, 25, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [98, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 100, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 100, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃直播2]"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF SetCreater 5 0 30 25 25 30 0"]}, {"code": 355, "indent": 1, "parameters": ["Zzy.BCF.SetPeople(2000)"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF UsePrivate 2"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater true"]}, {"code": 118, "indent": 1, "parameters": ["选择停止"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac停止观看"]}, {"code": 102, "indent": 1, "parameters": [[" 是", " 否"], 1, 0, 2, 2]}, {"code": 402, "indent": 1, "parameters": [0, " 是"]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 2, "parameters": [{"name": "鼠标点击声音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": ["ZzyBCF Creater false"]}, {"code": 356, "indent": 2, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 2, "parameters": [">地图临时漂浮视频弹幕 : 立刻清除全部弹幕"]}, {"code": 235, "indent": 2, "parameters": [99]}, {"code": 235, "indent": 2, "parameters": [98]}, {"code": 235, "indent": 2, "parameters": [100]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": ["stop_bgs2"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 355, "indent": 2, "parameters": ["$gameVariables.setValue(194,$gameStrings.value(10));"]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, " 否"]}, {"code": 119, "indent": 2, "parameters": ["选择停止"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "克莱因现实", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6直播(二)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关直播(二)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "<PERSON><PERSON>", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["D_TEXT \\fb【黑卡蒂】今天给大家看看胸...\\c[101]♥\\c[0]【直播】 48"]}, {"code": 231, "indent": 1, "parameters": [100, "", 0, 0, 225, 25, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [98, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 100, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 100, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃直播2]"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF SetCreater 5 0 30 25 25 30 0"]}, {"code": 355, "indent": 1, "parameters": ["Zzy.BCF.SetPeople(2000)"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF UsePrivate 2"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater true"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}果然卡蒂酱的胸也是棒的不行,而且这个形状...."]}, {"code": 356, "indent": 1, "parameters": ["PushGab 12 这家伙又在看昨天那个女主播嘛.."]}, {"code": 230, "indent": 1, "parameters": [100]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}虽然有被人玩弄过的痕迹,但是这个粉嫩的奶头,嗯，应该是是最近才破处的,\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}是男朋友吗，而且凭我多年经验，卡蒂酱的年纪应该和桐人老弟亚丝娜他们差不多吧。"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 12 年纪和我们差不多,表示是学生吗?"]}, {"code": 230, "indent": 1, "parameters": [100]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}学生时期就能开始玩这么大了,以后卡蒂酱绝对是有以后AV热门女优的潜力啊!\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}这时候必须得发个弹幕纪念一下!"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac字好小，但勉强还是可以到得到。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因发的弹幕是……"]}, {"code": 356, "indent": 1, "parameters": [">字符串核心 : 字符串[10] : 还原字符串"]}, {"code": 356, "indent": 1, "parameters": [">字符串核心 : 字符串[10] : 使用弹出框修改 : 请输入"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 初始化 : 样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 修改样式属性-内容文本 : 字符串[10]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 修改样式属性-额外位置偏移 : 偏移[-10,250]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 创建 : 常规弹幕"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}完美的弹幕~不过看的稍微有点心痒痒的,特别爱丽丝大姐头那完美形状的屁股在脑海里挥之不去，\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}不行，必须立刻去游戏找大姐头述说这个思念之情才行!"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 1, "parameters": [{"name": "鼠标点击声音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater false"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 立刻清除全部弹幕"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 235, "indent": 1, "parameters": [98]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 355, "indent": 1, "parameters": ["$gameVariables.setValue(194,$gameStrings.value(10));"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "<PERSON><PERSON>", "volume": 75, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["D_TEXT \\fb【黑卡蒂】今天给大家看看胸...\\c[101]♥\\c[0]【直播】 48"]}, {"code": 231, "indent": 1, "parameters": [100, "", 0, 0, 225, 25, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [98, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 100, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 100, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃直播2]"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF SetCreater 5 0 30 25 25 30 0"]}, {"code": 355, "indent": 1, "parameters": ["Zzy.BCF.SetPeople(2000)"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF UsePrivate 2"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater true"]}, {"code": 118, "indent": 1, "parameters": ["选择停止"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac停止观看"]}, {"code": 102, "indent": 1, "parameters": [[" 是", " 否"], 1, 0, 2, 2]}, {"code": 402, "indent": 1, "parameters": [0, " 是"]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 2, "parameters": [{"name": "鼠标点击声音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": ["ZzyBCF Creater false"]}, {"code": 356, "indent": 2, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 2, "parameters": [">地图临时漂浮视频弹幕 : 立刻清除全部弹幕"]}, {"code": 235, "indent": 2, "parameters": [99]}, {"code": 235, "indent": 2, "parameters": [98]}, {"code": 235, "indent": 2, "parameters": [100]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": ["stop_bgs2"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 355, "indent": 2, "parameters": ["$gameVariables.setValue(194,$gameStrings.value(10));"]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, " 否"]}, {"code": 119, "indent": 2, "parameters": ["选择停止"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 16}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 83}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6通行检查(一)"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 5, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 5 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 5]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 5 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 176}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6通行检查(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/6进入地下街时通行检查的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [223, 223, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 1, true]}, {"code": 201, "indent": 1, "parameters": [0, 261, 44, 33, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 231, "indent": 1, "parameters": [100, "黑幕", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "game_ma<PERSON><PERSON><PERSON><PERSON>_7_event36", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-Rubbing", "volume": 20, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 Amebo_aka_48a 25 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, true]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[23]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[催眠身体检查]"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 请问这样您方便检查吗？还有什么需要我配合的吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不错不错，但检查是一个细致的工作，马虎粗心可不行。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我还要更加仔细地、由外到里地检查，保持这个姿势。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 好的，麻烦您仔细检查。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 话说，你的奶子真大啊。是假吧，里面其实是危险物品吧？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这里面并没有危险品，我游戏里的身材是按现实形象制作的。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 可我觉得有点太大了，总感觉很羞耻……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哦？太大了会让你很在意的吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 是啊……不知道以后有了孩子，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 喂宝宝吃母乳时会不会不方便呢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 为什么会不方便呢？奶子大不是奶水更多吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac但宝宝的手很小，可能会不好抓握吧。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你这对奶子这么色，肯定没问题啦。而且，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 如果你生的是我的宝宝，喂奶时我也会帮忙哦。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我应该是不会为您生孩子的。但您的建议我会考虑的。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 只是考虑吗？这个答案我可不满意哦。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 至少也应该作为目标努力嘛。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 很抱歉我的回答让您失望。虽然现在我还不能给您生孩子，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但我会把它作为目标，想办法努力去实现的。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呵呵，虽然只是口是心非的敷衍，但还是让我硬了呢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 说起来，我的检查的手法不够好吗？你的语气一直很平静呢？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不，您的检查的手法很棒。其实我刚刚就轻微高潮了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但我必须保持恭敬，如果语调不够平稳，那就太失礼了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊，原来如此。不愧是女警，意志力还真是惊人啊。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 多亏我审问你，你才能知道自己有这样的忍耐力吧？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯，感谢您审问我。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 唔，差不多要进入下一步的检查了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯，请问是什么呢？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不知道你的屁眼和骚屄里有没有藏什么东西，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我要用我的检查棒好好地深入检查一下才行！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 屁眼……？！骚逼……？！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我……这是…………在做什么？"]}, {"code": 231, "indent": 1, "parameters": [100, "黑幕", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 催眠要解开了吗？是因为言语的刺激，还是时限到了？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 或者单纯是自己的精神力太强吗？这个女人有点意思。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 今天只是甜点，以后我们慢慢玩~ 呵呵，先继续扮演守卫吧。"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 225, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 176}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6通行检查(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关进入地下街时通行检查(一)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [223, 223, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 1, true]}, {"code": 201, "indent": 1, "parameters": [0, 261, 44, 33, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 231, "indent": 1, "parameters": [100, "黑幕", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "game_ma<PERSON><PERSON><PERSON><PERSON>_7_event36", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-Rubbing", "volume": 20, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 Amebo_aka_48a 25 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, true]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[23]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[催眠身体检查]"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 请问这样您方便检查吗？还有什么需要我配合的吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不错不错，但检查是一个细致的工作，马虎粗心可不行。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我还要更加仔细地、由外到里地检查，保持这个姿势。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 好的，麻烦您仔细检查。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 话说，你的奶子真大啊。是假吧，里面其实是危险物品吧？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这里面并没有危险品，我游戏里的身材是按现实形象制作的。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 可我觉得有点太大了，总感觉很羞耻……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哦？太大了会让你很在意的吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 是啊……不知道以后有了孩子，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 喂宝宝吃母乳时会不会不方便呢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 为什么会不方便呢？奶子大不是奶水更多吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac但宝宝的手很小，可能会不好抓握吧。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你这对奶子这么色，肯定没问题啦。而且，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 如果你生的是我的宝宝，喂奶时我也会帮忙哦。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我应该是不会为您生孩子的。但您的建议我会考虑的。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 只是考虑吗？这个答案我可不满意哦。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 至少也应该作为目标努力嘛。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 很抱歉我的回答让您失望。虽然现在我还不能给您生孩子，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但我会把它作为目标，想办法努力去实现的。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呵呵，虽然只是口是心非的敷衍，但还是让我硬了呢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 说起来，我的检查的手法不够好吗？你的语气一直很平静呢？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不，您的检查的手法很棒。其实我刚刚就轻微高潮了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但我必须保持恭敬，如果语调不够平稳，那就太失礼了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊，原来如此。不愧是女警，意志力还真是惊人啊。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 多亏我审问你，你才能知道自己有这样的忍耐力吧？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯，感谢您审问我。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 唔，差不多要进入下一步的检查了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯，请问是什么呢？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不知道你的屁眼和骚屄里有没有藏什么东西，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我要用我的检查棒好好地深入检查一下才行！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 屁眼……？！骚逼……？！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我……这是…………在做什么？"]}, {"code": 231, "indent": 1, "parameters": [100, "黑幕", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 催眠要解开了吗？是因为言语的刺激，还是时限到了？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 或者单纯是自己的精神力太强吗？这个女人有点意思。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 今天只是甜点，以后我们慢慢玩~ 呵呵，先继续扮演守卫吧。"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 176}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6通行检查(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关进入地下街时通行检查(一)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [223, 223, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 1, true]}, {"code": 201, "indent": 1, "parameters": [0, 261, 44, 33, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 231, "indent": 1, "parameters": [100, "黑幕", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "game_ma<PERSON><PERSON><PERSON><PERSON>_7_event36", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-Rubbing", "volume": 20, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 Amebo_aka_48a 25 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, true]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[23]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[催眠身体检查]"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 请问这样您方便检查吗？还有什么需要我配合的吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不错不错，但检查是一个细致的工作，马虎粗心可不行。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我还要更加仔细地、由外到里地检查，保持这个姿势。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 好的，麻烦您仔细检查。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 话说，你的奶子真大啊。是假吧，里面其实是危险物品吧？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这里面并没有危险品，我游戏里的身材是按现实形象制作的。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 可我觉得有点太大了，总感觉很羞耻……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哦？太大了会让你很在意的吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 是啊……不知道以后有了孩子，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 喂宝宝吃母乳时会不会不方便呢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 为什么会不方便呢？奶子大不是奶水更多吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac但宝宝的手很小，可能会不好抓握吧。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你这对奶子这么色，肯定没问题啦。而且，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 如果你生的是我的宝宝，喂奶时我也会帮忙哦。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我应该是不会为您生孩子的。但您的建议我会考虑的。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 只是考虑吗？这个答案我可不满意哦。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 至少也应该作为目标努力嘛。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 很抱歉我的回答让您失望。虽然现在我还不能给您生孩子，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但我会把它作为目标，想办法努力去实现的。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呵呵，虽然只是口是心非的敷衍，但还是让我硬了呢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 说起来，我的检查的手法不够好吗？你的语气一直很平静呢？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不，您的检查的手法很棒。其实我刚刚就轻微高潮了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但我必须保持恭敬，如果语调不够平稳，那就太失礼了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊，原来如此。不愧是女警，意志力还真是惊人啊。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 多亏我审问你，你才能知道自己有这样的忍耐力吧？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯，感谢您审问我。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 唔，差不多要进入下一步的检查了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯，请问是什么呢？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不知道你的屁眼和骚屄里有没有藏什么东西，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我要用我的检查棒好好地深入检查一下才行！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 屁眼……？！骚逼……？！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我……这是…………在做什么？"]}, {"code": 231, "indent": 1, "parameters": [100, "黑幕", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac守门人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 催眠要解开了吗？是因为言语的刺激，还是时限到了？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 或者单纯是自己的精神力太强吗？这个女人有点意思。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 今天只是甜点，以后我们慢慢玩~ 呵呵，先继续扮演守卫吧。"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 16}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 187}, "directionFix": true, "image": {"tileId": 0, "characterName": "爱丽丝现实", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11坦白从宽"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 5, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 5 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 5]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 5 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 176}, "directionFix": true, "image": {"tileId": 0, "characterName": "爱丽丝现实", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11坦白从宽"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关爱丽丝审讯自首罪犯的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [235, 235, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 289, 8, 6, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [235, 235, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 201, "indent": 1, "parameters": [0, 289, 8, 6, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 176}, "directionFix": true, "image": {"tileId": 0, "characterName": "爱丽丝现实", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11坦白从宽"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关爱丽丝审讯自首罪犯的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [235, 235, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 289, 8, 6, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [235, 235, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 201, "indent": 1, "parameters": [0, 289, 8, 6, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 16}, {"id": 12, "name": "直播3", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 143, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 165}, "directionFix": true, "image": {"tileId": 0, "characterName": "克莱因现实", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/10直播(三)"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 5, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 5 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 5]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 5 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 162, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "克莱因现实", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/10直播(三)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关直播(三)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "B - Non Venda Rally", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["D_TEXT \\fb【黑卡蒂】对不起迟到了~今天是在学校露出哦\\c[101]♥\\c[0]【直播】 48"]}, {"code": 231, "indent": 1, "parameters": [100, "", 0, 0, 200, 25, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [98, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 100, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 100, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃图书室露出]"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF SetCreater 5 0 30 25 25 30 0"]}, {"code": 355, "indent": 1, "parameters": ["Zzy.BCF<PERSON>(3000)"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF UsePrivate 3"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater true"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 发一条惊天地泣鬼神的弹幕吧~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人老爷。你说发什么好？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 随便你。 "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 来嘛来嘛！又不是第一次了！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](敷衍一下吧。)"]}, {"code": 356, "indent": 1, "parameters": [">字符串核心 : 字符串[10] : 使用弹出框修改 : 请输入"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 初始化 : 样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 修改样式属性-内容文本 : 字符串[10]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 修改样式属性-额外位置偏移 : 偏移[-10,250]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 创建 : 常规弹幕"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你看这腰，这胸~是不是很正点？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 也就一般吧…… "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](前两次还不觉得，现在越看越有一种奇妙的熟悉感……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呵呵，你就嘴硬吧？下面是不是也已经硬了？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不要说这么恶心的话行不行？我确实觉得一般。 "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 除了亚丝娜，我对别的女人没兴趣。 "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你这种大帅哥，不开后宫，一辈子只搞一个女人，不觉得亏吗？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](后宫吗……？)"]}, {"code": 356, "indent": 1, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 102, "indent": 1, "parameters": [["我确定一定以及肯定只要亚丝娜就够了", "喜欢我的人只有亚丝娜，怎么开后宫？"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "我确定一定以及肯定只要亚丝娜就够了"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 我再说一遍，我只要亚丝娜！确定一定以及肯定。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "喜欢我的人只有亚丝娜，怎么开后宫？"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Poison", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 2, "parameters": [[68, 255, 68, 170], 60, true]}, {"code": 356, "indent": 2, "parameters": ["addLog 对于开后宫的妄想有了一丝的瘙痒..."]}, {"code": 356, "indent": 2, "parameters": ["addLog 变态度↑\\c[28]1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 喜欢我的女人只有亚丝娜，我不具备开后宫的基本条件啊。 "]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 那你呢？打算开后宫？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我哪能啊？你看我这个硬件配置，怎么开后宫？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 再说我现在可是坚定纯爱党，单推爱丽丝大人。 "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 说起来，我一开始的问题其实就是想问你这个。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你在警局门口蹲点，不会想尾行爱丽丝小姐吧？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人老弟，你在逗我？她是女警我尾行她？ 其实嘛……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我动用我的人脉搞到一些情报，帮女神抓了好几个马仔。 "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 爱丽丝大人一高兴就同意和我今晚约会了，我在等她下班。 "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 是吗？我看是你死缠烂打，她才勉强同意和你吃个饭的吧？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嘿嘿，什么都瞒不过桐人老爷的慧眼。但万事开头难嘛！ "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 只要这第一步踏出去了，恋爱、结婚不都是迟早的事？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我孩子名字都想好了，就叫尤吉欧，很不错吧，嘿嘿~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 服了你了。我本来还准备请你吃晚饭的。那就祝你约会顺利了。"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 1, "parameters": [{"name": "鼠标点击声音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater false"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 立刻清除全部弹幕"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 235, "indent": 1, "parameters": [98]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 355, "indent": 1, "parameters": ["$gameVariables.setValue(194,$gameStrings.value(10));"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "B - Non Venda Rally", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["D_TEXT \\fb【黑卡蒂】对不起迟到了~今天是在学校露出哦\\c[101]♥\\c[0]【直播】 48"]}, {"code": 231, "indent": 1, "parameters": [100, "", 0, 0, 200, 25, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [98, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 100, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 100, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃图书室露出]"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF SetCreater 5 0 30 25 25 30 0"]}, {"code": 355, "indent": 1, "parameters": ["Zzy.BCF<PERSON>(3000)"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF UsePrivate 3"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater true"]}, {"code": 118, "indent": 1, "parameters": ["选择停止"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac停止观看"]}, {"code": 102, "indent": 1, "parameters": [[" 是", " 否"], 1, 0, 2, 2]}, {"code": 402, "indent": 1, "parameters": [0, " 是"]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 2, "parameters": [{"name": "鼠标点击声音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": ["ZzyBCF Creater false"]}, {"code": 356, "indent": 2, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 2, "parameters": [">地图临时漂浮视频弹幕 : 立刻清除全部弹幕"]}, {"code": 235, "indent": 2, "parameters": [99]}, {"code": 235, "indent": 2, "parameters": [98]}, {"code": 235, "indent": 2, "parameters": [100]}, {"code": 356, "indent": 2, "parameters": ["stop_bgs2"]}, {"code": 355, "indent": 2, "parameters": ["$gameVariables.setValue(194,$gameStrings.value(10));"]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, " 否"]}, {"code": 119, "indent": 2, "parameters": ["选择停止"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 143, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "克莱因现实", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/10直播(三)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关直播(二)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "B - Non Venda Rally", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["D_TEXT \\fb【黑卡蒂】对不起迟到了~今天是在学校露出哦\\c[101]♥\\c[0]【直播】 48"]}, {"code": 231, "indent": 1, "parameters": [100, "", 0, 0, 200, 25, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [98, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 100, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 100, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃图书室露出]"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF SetCreater 5 0 30 25 25 30 0"]}, {"code": 355, "indent": 1, "parameters": ["Zzy.BCF<PERSON>(3000)"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF UsePrivate 3"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater true"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 发一条惊天地泣鬼神的弹幕吧~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人老爷。你说发什么好？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 随便你。 "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 来嘛来嘛！又不是第一次了！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](敷衍一下吧。)"]}, {"code": 356, "indent": 1, "parameters": [">字符串核心 : 字符串[10] : 使用弹出框修改 : 请输入"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 初始化 : 样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 修改样式属性-内容文本 : 字符串[10]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 修改样式属性-额外位置偏移 : 偏移[-10,250]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 创建 : 常规弹幕"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你看这腰，这胸~是不是很正点？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 也就一般吧…… "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](前两次还不觉得，现在越看越有一种奇妙的熟悉感……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呵呵，你就嘴硬吧？下面是不是也已经硬了？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不要说这么恶心的话行不行？我确实觉得一般。 "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 除了亚丝娜，我对别的女人没兴趣。 "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你这种大帅哥，不开后宫，一辈子只搞一个女人，不觉得亏吗？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](后宫吗……？)"]}, {"code": 356, "indent": 1, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 102, "indent": 1, "parameters": [["我确定一定以及肯定只要亚丝娜就够了", "喜欢我的人只有亚丝娜，怎么开后宫？"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "我确定一定以及肯定只要亚丝娜就够了"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 我再说一遍，我只要亚丝娜！确定一定以及肯定。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "喜欢我的人只有亚丝娜，怎么开后宫？"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Poison", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 2, "parameters": [[68, 255, 68, 170], 60, true]}, {"code": 356, "indent": 2, "parameters": ["addLog 对于开后宫的妄想有了一丝的瘙痒..."]}, {"code": 356, "indent": 2, "parameters": ["addLog 变态度↑\\c[28]1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 喜欢我的女人只有亚丝娜，我不具备开后宫的基本条件啊。 "]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 那你呢？打算开后宫？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我哪能啊？你看我这个硬件配置，怎么开后宫？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 再说我现在可是坚定纯爱党，单推爱丽丝大人。 "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 说起来，我一开始的问题其实就是想问你这个。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你在警局门口蹲点，不会想尾行爱丽丝小姐吧？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人老弟，你在逗我？她是女警我尾行她？ 其实嘛……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我动用我的人脉搞到一些情报，帮女神抓了好几个马仔。 "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 爱丽丝大人一高兴就同意和我今晚约会了，我在等她下班。 "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 是吗？我看是你死缠烂打，她才勉强同意和你吃个饭的吧？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嘿嘿，什么都瞒不过桐人老爷的慧眼。但万事开头难嘛！ "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 只要这第一步踏出去了，恋爱、结婚不都是迟早的事？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我孩子名字都想好了，就叫尤吉欧，很不错吧，嘿嘿~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 服了你了。我本来还准备请你吃晚饭的。那就祝你约会顺利了。"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 1, "parameters": [{"name": "鼠标点击声音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater false"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 立刻清除全部弹幕"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 235, "indent": 1, "parameters": [98]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 355, "indent": 1, "parameters": ["$gameVariables.setValue(194,$gameStrings.value(10));"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "B - Non Venda Rally", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["D_TEXT \\fb【黑卡蒂】对不起迟到了~今天是在学校露出哦\\c[101]♥\\c[0]【直播】 48"]}, {"code": 231, "indent": 1, "parameters": [100, "", 0, 0, 200, 25, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [98, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 100, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 100, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃图书室露出]"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF SetCreater 5 0 30 25 25 30 0"]}, {"code": 355, "indent": 1, "parameters": ["Zzy.BCF<PERSON>(3000)"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF UsePrivate 3"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater true"]}, {"code": 118, "indent": 1, "parameters": ["选择停止"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac停止观看"]}, {"code": 102, "indent": 1, "parameters": [[" 是", " 否"], 1, 0, 2, 2]}, {"code": 402, "indent": 1, "parameters": [0, " 是"]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 2, "parameters": [{"name": "鼠标点击声音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": ["ZzyBCF Creater false"]}, {"code": 356, "indent": 2, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 2, "parameters": [">地图临时漂浮视频弹幕 : 立刻清除全部弹幕"]}, {"code": 235, "indent": 2, "parameters": [99]}, {"code": 235, "indent": 2, "parameters": [98]}, {"code": 235, "indent": 2, "parameters": [100]}, {"code": 356, "indent": 2, "parameters": ["stop_bgs2"]}, {"code": 355, "indent": 2, "parameters": ["$gameVariables.setValue(194,$gameStrings.value(10));"]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, " 否"]}, {"code": 119, "indent": 2, "parameters": ["选择停止"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "克莱因现实", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/10直播(三)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关直播(二)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "B - Non Venda Rally", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["D_TEXT \\fb【黑卡蒂】对不起迟到了~今天是在学校露出哦\\c[101]♥\\c[0]【直播】 48"]}, {"code": 231, "indent": 1, "parameters": [100, "", 0, 0, 200, 25, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [98, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 100, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 100, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃图书室露出]"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF SetCreater 5 0 30 25 25 30 0"]}, {"code": 355, "indent": 1, "parameters": ["Zzy.BCF<PERSON>(3000)"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF UsePrivate 3"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater true"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 发一条惊天地泣鬼神的弹幕吧~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人老爷。你说发什么好？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 随便你。 "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 来嘛来嘛！又不是第一次了！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](敷衍一下吧。)"]}, {"code": 356, "indent": 1, "parameters": [">字符串核心 : 字符串[10] : 使用弹出框修改 : 请输入"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 初始化 : 样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 修改样式属性-内容文本 : 字符串[10]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 修改样式属性-额外位置偏移 : 偏移[-10,250]"]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 临时对象 : 创建 : 常规弹幕"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你看这腰，这胸~是不是很正点？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 也就一般吧…… "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](前两次还不觉得，现在越看越有一种奇妙的熟悉感……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呵呵，你就嘴硬吧？下面是不是也已经硬了？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不要说这么恶心的话行不行？我确实觉得一般。 "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 除了亚丝娜，我对别的女人没兴趣。 "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你这种大帅哥，不开后宫，一辈子只搞一个女人，不觉得亏吗？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](后宫吗……？)"]}, {"code": 356, "indent": 1, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 102, "indent": 1, "parameters": [["我确定一定以及肯定只要亚丝娜就够了", "喜欢我的人只有亚丝娜，怎么开后宫？"], 1, 0, 2, 0]}, {"code": 402, "indent": 1, "parameters": [0, "我确定一定以及肯定只要亚丝娜就够了"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 我再说一遍，我只要亚丝娜！确定一定以及肯定。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "喜欢我的人只有亚丝娜，怎么开后宫？"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Poison", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 2, "parameters": [[68, 255, 68, 170], 60, true]}, {"code": 356, "indent": 2, "parameters": ["addLog 对于开后宫的妄想有了一丝的瘙痒..."]}, {"code": 356, "indent": 2, "parameters": ["addLog 变态度↑\\c[28]1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 喜欢我的女人只有亚丝娜，我不具备开后宫的基本条件啊。 "]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 那你呢？打算开后宫？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我哪能啊？你看我这个硬件配置，怎么开后宫？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 再说我现在可是坚定纯爱党，单推爱丽丝大人。 "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 说起来，我一开始的问题其实就是想问你这个。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你在警局门口蹲点，不会想尾行爱丽丝小姐吧？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人老弟，你在逗我？她是女警我尾行她？ 其实嘛……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我动用我的人脉搞到一些情报，帮女神抓了好几个马仔。 "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 爱丽丝大人一高兴就同意和我今晚约会了，我在等她下班。 "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 是吗？我看是你死缠烂打，她才勉强同意和你吃个饭的吧？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嘿嘿，什么都瞒不过桐人老爷的慧眼。但万事开头难嘛！ "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 只要这第一步踏出去了，恋爱、结婚不都是迟早的事？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我孩子名字都想好了，就叫尤吉欧，很不错吧，嘿嘿~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 服了你了。我本来还准备请你吃晚饭的。那就祝你约会顺利了。"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 1, "parameters": [{"name": "鼠标点击声音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater false"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮视频弹幕 : 立刻清除全部弹幕"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 235, "indent": 1, "parameters": [98]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 355, "indent": 1, "parameters": ["$gameVariables.setValue(194,$gameStrings.value(10));"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "B - Non Venda Rally", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["D_TEXT \\fb【黑卡蒂】对不起迟到了~今天是在学校露出哦\\c[101]♥\\c[0]【直播】 48"]}, {"code": 231, "indent": 1, "parameters": [100, "", 0, 0, 200, 25, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [98, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 100, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 100, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃图书室露出]"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF SetCreater 5 0 30 25 25 30 0"]}, {"code": 355, "indent": 1, "parameters": ["Zzy.BCF<PERSON>(3000)"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF UsePrivate 3"]}, {"code": 356, "indent": 1, "parameters": ["ZzyBCF Creater true"]}, {"code": 118, "indent": 1, "parameters": ["选择停止"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac停止观看"]}, {"code": 102, "indent": 1, "parameters": [[" 是", " 否"], 1, 0, 2, 2]}, {"code": 402, "indent": 1, "parameters": [0, " 是"]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 2, "parameters": [{"name": "鼠标点击声音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": ["ZzyBCF Creater false"]}, {"code": 356, "indent": 2, "parameters": ["ZzyBCF ClearCache "]}, {"code": 356, "indent": 2, "parameters": [">地图临时漂浮视频弹幕 : 立刻清除全部弹幕"]}, {"code": 235, "indent": 2, "parameters": [99]}, {"code": 235, "indent": 2, "parameters": [98]}, {"code": 235, "indent": 2, "parameters": [100]}, {"code": 356, "indent": 2, "parameters": ["stop_bgs2"]}, {"code": 355, "indent": 2, "parameters": ["$gameVariables.setValue(194,$gameStrings.value(10));"]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, " 否"]}, {"code": 119, "indent": 2, "parameters": ["选择停止"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 14}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 242}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/12等级考试"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 15, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 15 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 15]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 15 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 176}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/12等级考试"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/12爱丽丝参加妓女等级考试的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [236, 236, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 326, 15, 9, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [236, 236, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 5]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 325, 11, 16, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 176}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/12等级考试"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/12爱丽丝参加妓女等级考试的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [236, 236, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 326, 15, 9, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [236, 236, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 5]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 325, 11, 16, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 14}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 149, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 222}, "directionFix": true, "image": {"tileId": 0, "characterName": "成人女性用", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11偷窥室友(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/11克莱因偷窥室友的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 129, "indent": 1, "parameters": [60, 0, false]}, {"code": 121, "indent": 1, "parameters": [241, 241, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 201, "indent": 1, "parameters": [0, 285, 16, 20, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 129, "indent": 1, "parameters": [60, 0, false]}, {"code": 121, "indent": 1, "parameters": [241, 241, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 201, "indent": 1, "parameters": [0, 285, 16, 20, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 14}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 242}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/12女武神之影"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 10, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 10 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 10]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 10 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 193, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 176}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/12女武神之影"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/12桐人通过妓院窗户看到妓女口交的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [244, 244, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 317, 20, 16, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [244, 244, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [207]}, {"code": 201, "indent": 1, "parameters": [0, 84, 5, 15, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 193, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 176}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/12女武神之影"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/12桐人通过妓院窗户看到妓女口交的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [244, 244, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 317, 20, 16, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [244, 244, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [207]}, {"code": 201, "indent": 1, "parameters": [0, 84, 5, 15, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 176}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/12女武神之影"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/12桐人通过妓院窗户看到妓女口交的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [244, 244, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 317, 20, 16, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [244, 244, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [207]}, {"code": 201, "indent": 1, "parameters": [0, 84, 5, 15, 2, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 14}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 361}, "directionFix": true, "image": {"tileId": 0, "characterName": "爱丽丝现实", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/16女警集训"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 15]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 176}, "directionFix": true, "image": {"tileId": 0, "characterName": "爱丽丝现实", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/16女警集训"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/16福克西集训爱丽丝和小柔的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [250, 250, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 350, 11, 24, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [250, 250, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 350, 8, 8, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 176}, "directionFix": true, "image": {"tileId": 0, "characterName": "爱丽丝现实", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/16女警集训"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/16福克西集训爱丽丝和小柔的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [250, 250, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 350, 11, 24, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [250, 250, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 350, 8, 8, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 14}, {"id": 17, "name": "EV017", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 425}, "directionFix": true, "image": {"tileId": 0, "characterName": "爱丽丝私服像素人", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/20操练口舌"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 15]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 176}, "directionFix": true, "image": {"tileId": 0, "characterName": "爱丽丝私服像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/20操练口舌"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/20福克西和爱丽丝在试衣间发生事情的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [261, 261, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 339, 14, 16, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [261, 261, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 339, 7, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 176}, "directionFix": true, "image": {"tileId": 0, "characterName": "爱丽丝私服像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/20操练口舌"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/20福克西和爱丽丝在试衣间发生事情的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [261, 261, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 339, 14, 16, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [261, 261, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 339, 7, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 12}, {"id": 18, "name": "上班族", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 60, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 433}, "directionFix": false, "image": {"tileId": 0, "characterName": "登场人物 老师", "direction": 8, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/20直播(三)"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 10]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 339, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 433}, "directionFix": false, "image": {"tileId": 0, "characterName": "登场人物 老师", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/20直播(三)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关直播(三)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [263, 263, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 117, "indent": 1, "parameters": [220]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [263, 263, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 117, "indent": 1, "parameters": [220]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 339, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 433}, "directionFix": false, "image": {"tileId": 0, "characterName": "登场人物 老师", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/20直播(三)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关直播(三)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [263, 263, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 117, "indent": 1, "parameters": [220]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [263, 263, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 117, "indent": 1, "parameters": [220]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 433}, "directionFix": false, "image": {"tileId": 0, "characterName": "登场人物 老师", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/20直播(三)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关直播(三)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [263, 263, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 117, "indent": 1, "parameters": [220]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [263, 263, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 117, "indent": 1, "parameters": [220]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 14}, {"id": 19, "name": "EV019", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 476}, "directionFix": true, "image": {"tileId": 0, "characterName": "爱丽丝现实", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/27红落之时"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 10]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 476}, "directionFix": true, "image": {"tileId": 0, "characterName": "爱丽丝现实", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/27红落之时"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/27爱丽丝第一次做爱的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝第一次的对象是？"]}, {"code": 102, "indent": 1, "parameters": [["克莱因", "福克西"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "克莱因"]}, {"code": 121, "indent": 2, "parameters": [352, 352, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "福克西"]}, {"code": 121, "indent": 2, "parameters": [352, 352, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [264, 264, 0]}, {"code": 121, "indent": 1, "parameters": [353, 353, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [49, 49, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 287, 11, 11, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝第一次的对象是？"]}, {"code": 102, "indent": 1, "parameters": [["克莱因", "福克西"], 1, -1, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "克莱因"]}, {"code": 121, "indent": 2, "parameters": [352, 352, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "福克西"]}, {"code": 121, "indent": 2, "parameters": [352, 352, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [264, 264, 0]}, {"code": 121, "indent": 1, "parameters": [353, 353, 0]}, {"code": 121, "indent": 1, "parameters": [49, 49, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 287, 11, 11, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 476}, "directionFix": true, "image": {"tileId": 0, "characterName": "爱丽丝现实", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/27红落之时"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/27爱丽丝第一次做爱的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝第一次的对象是？"]}, {"code": 102, "indent": 1, "parameters": [["克莱因", "福克西"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "克莱因"]}, {"code": 121, "indent": 2, "parameters": [352, 352, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "福克西"]}, {"code": 121, "indent": 2, "parameters": [352, 352, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [264, 264, 0]}, {"code": 121, "indent": 1, "parameters": [353, 353, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [49, 49, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 287, 11, 11, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac爱丽丝第一次的对象是？"]}, {"code": 102, "indent": 1, "parameters": [["克莱因", "福克西"], 1, -1, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "克莱因"]}, {"code": 121, "indent": 2, "parameters": [352, 352, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "福克西"]}, {"code": 121, "indent": 2, "parameters": [352, 352, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [264, 264, 0]}, {"code": 121, "indent": 1, "parameters": [353, 353, 0]}, {"code": 121, "indent": 1, "parameters": [49, 49, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 287, 11, 11, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 12}, {"id": 20, "name": "EV020", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 500}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/27宾主尽欢"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 20]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 176}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/27宾主尽欢"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/27爱丽丝在妓院接客的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [266, 266, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [49, 49, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 99, 16, 16, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [266, 266, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [49, 49, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 99, 16, 16, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 176}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/27宾主尽欢"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/27爱丽丝在妓院接客的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [266, 266, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [49, 49, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 99, 16, 16, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [266, 266, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [49, 49, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 99, 16, 16, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 12}, {"id": 21, "name": "EV021", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 562}, "directionFix": true, "image": {"tileId": 0, "characterName": "爱丽丝私服像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/30病院圣女"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/30爱丽丝在医院帮助病友的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [275, 275, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 560]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 96, 27, 19, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [275, 275, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 562]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 96, 26, 19, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 10}, {"id": 22, "name": "EV022", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 60, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 577}, "directionFix": false, "image": {"tileId": 0, "characterName": "BB_TheOtaku", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/1直播(四)"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 5]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 394, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 577}, "directionFix": false, "image": {"tileId": 0, "characterName": "BB_TheOtaku", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/1直播(四)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关直播(四)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [276, 276, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 117, "indent": 1, "parameters": [330]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [276, 276, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 117, "indent": 1, "parameters": [330]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 577}, "directionFix": false, "image": {"tileId": 0, "characterName": "BB_TheOtaku", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/1直播(四)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关直播(四)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [276, 276, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 117, "indent": 1, "parameters": [330]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [276, 276, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 117, "indent": 1, "parameters": [330]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 394, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 577}, "directionFix": false, "image": {"tileId": 0, "characterName": "BB_TheOtaku", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/1直播(四)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关直播(四)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [276, 276, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 117, "indent": 1, "parameters": [330]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [276, 276, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 117, "indent": 1, "parameters": [330]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 12}, {"id": 23, "name": "EV023", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 60, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 594}, "directionFix": false, "image": {"tileId": 0, "characterName": "BB_TheOtaku", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/2直播(五)"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 5]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 405, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 594}, "directionFix": false, "image": {"tileId": 0, "characterName": "BB_TheOtaku", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/2直播(五)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关直播(五)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [276, 276, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 117, "indent": 1, "parameters": [337]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [276, 276, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 117, "indent": 1, "parameters": [337]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 405, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 594}, "directionFix": false, "image": {"tileId": 0, "characterName": "BB_TheOtaku", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/2直播(五)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关直播(五)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [276, 276, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 117, "indent": 1, "parameters": [337]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [276, 276, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 117, "indent": 1, "parameters": [337]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 12}, {"id": 24, "name": "EV024", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 722}, "directionFix": true, "image": {"tileId": 0, "characterName": "爱丽丝像素（面具）", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/8通行检查(二)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/8进入舞厅时通行检查的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [916, 916, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 722]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 317, 34, 17, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [916, 916, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 723]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 317, 34, 17, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 10}, {"id": 25, "name": "EV025", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 704}, "directionFix": true, "image": {"tileId": 0, "characterName": "爱丽丝现实", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/8艳舞迷情"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/8爱丽丝与桐人在警局发生事情的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [436, 436, 1]}, {"code": 121, "indent": 1, "parameters": [918, 918, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 702]}, {"code": 201, "indent": 1, "parameters": [0, 350, 8, 17, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [436, 436, 1]}, {"code": 121, "indent": 1, "parameters": [918, 918, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 703]}, {"code": 201, "indent": 1, "parameters": [0, 350, 8, 17, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 10}]}