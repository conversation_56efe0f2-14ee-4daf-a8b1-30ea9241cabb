[null, {"id": 1, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 2, "members": [{"enemyId": 2, "x": 0, "y": 346, "hidden": false}], "name": "狡猾的茂凯", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": true}, "list": [{"code": 231, "indent": 0, "parameters": [4, "外框_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [3, "暗角_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [2, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [1, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 2 1"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 设置动画序列 : 动画序列[12]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 播放简单状态元集合 : 集合[待机动作]"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 640, 0, 100, 100, 255, 0, 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Powerup", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 326, "indent": 0, "parameters": [0, 0, 0, 0, 100]}, {"code": 333, "indent": 0, "parameters": [-1, 0, 67]}, {"code": 333, "indent": 0, "parameters": [-1, 0, 68]}, {"code": 313, "indent": 0, "parameters": [0, 4, 0, 19]}, {"code": 313, "indent": 0, "parameters": [0, 8, 0, 19]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 356, "indent": 0, "parameters": ["PushGab 23 桐人君,这个应该就是BOSS了请多小心!"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["PushGab 10 交给我吧，亚丝娜!"]}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 1, "turnB": 1, "turnEnding": true, "turnValid": true}, "list": [{"code": 111, "indent": 0, "parameters": [0, 132, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 117, "indent": 1, "parameters": [29]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 25, "enemyIndex": 0, "enemyValid": true, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": true, "turnValid": false}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Monster9_B", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<茂凯>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可..恶..的..冒..险..者，又..想..剥..我..的..皮,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac给..我..留..下..吧.."]}, {"code": 356, "indent": 0, "parameters": ["PushGab 23 小心,桐人君,这个家伙状态有点不对劲"]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 356, "indent": 0, "parameters": ["PushGab 10 嗯,它似乎要放什么技能了"]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 250, "indent": 0, "parameters": [{"name": "Darkness4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["PushGab 10 烟雾,是异常系的法术嘛,如果只是这个的话..."]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 250, "indent": 0, "parameters": [{"name": "Monster9_B", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<茂凯>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac陷..入..悔..恨..的..梦..境..里，和..我..一..起..在..此,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac长..眠..吧.."]}, {"code": 333, "indent": 0, "parameters": [-1, 0, 69]}, {"code": 333, "indent": 0, "parameters": [-1, 1, 67]}, {"code": 333, "indent": 0, "parameters": [-1, 1, 68]}, {"code": 313, "indent": 0, "parameters": [0, 4, 0, 70]}, {"code": 313, "indent": 0, "parameters": [0, 8, 0, 70]}, {"code": 356, "indent": 0, "parameters": ["PushGab 23 是催眠系的异常状态嘛,桐人君，我马上来帮你解除!"]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人陷入噩梦状态!"]}, {"code": 121, "indent": 0, "parameters": [132, 132, 0]}, {"code": 0, "indent": 0, "parameters": []}], "span": 0}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 132, "switchValid": true, "turnA": 1, "turnB": 2, "turnEnding": false, "turnValid": false}, "list": [{"code": 122, "indent": 0, "parameters": [103, 103, 1, 0, 1]}, {"code": 117, "indent": 0, "parameters": [31]}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 1, "turnEnding": false, "turnValid": true}, "list": [{"code": 111, "indent": 0, "parameters": [4, 4, 6, 19]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 313, "indent": 1, "parameters": [0, 4, 0, 19]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [4, 8, 6, 19]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 313, "indent": 1, "parameters": [0, 8, 0, 19]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}]}, {"id": 3, "members": [{"enemyId": 1, "x": 0, "y": 277, "hidden": false}], "name": "树妖", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": true}, "list": [{"code": 333, "indent": 0, "parameters": [-1, 0, 68]}, {"code": 231, "indent": 0, "parameters": [4, "外框_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [3, "暗角_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [2, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [1, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 2 1"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 设置动画序列 : 动画序列[12]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 播放简单状态元集合 : 集合[待机动作]"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 640, 0, 100, 100, 255, 0, 30, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Powerup", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 326, "indent": 0, "parameters": [0, 0, 0, 0, 100]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 356, "indent": 0, "parameters": ["PushGab 23 桐人君,战斗就拜托你了!"]}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 1, "turnB": 1, "turnEnding": true, "turnValid": true}, "list": [{"code": 117, "indent": 0, "parameters": [29]}, {"code": 111, "indent": 0, "parameters": [0, 34, 0]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[2] : 播放简单状态元集合 : 集合[待机动作]"]}, {"code": 121, "indent": 1, "parameters": [34, 34, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 117, "indent": 1, "parameters": [25]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 0, "enemyIndex": 0, "enemyValid": true, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 235, "indent": 0, "parameters": [1]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 235, "indent": 0, "parameters": [4]}, {"code": 235, "indent": 0, "parameters": [5]}, {"code": 235, "indent": 0, "parameters": [6]}, {"code": 235, "indent": 0, "parameters": [7]}, {"code": 235, "indent": 0, "parameters": [8]}, {"code": 235, "indent": 0, "parameters": [9]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 121, "indent": 0, "parameters": [33, 33, 1]}, {"code": 122, "indent": 0, "parameters": [86, 86, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 333, "indent": 0, "parameters": [-1, 1, 68]}, {"code": 333, "indent": 0, "parameters": [-1, 0, 1]}, {"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 4, "members": [{"enemyId": 3, "x": 27, "y": 241, "hidden": false}], "name": "哀伤恶灵", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": true}, "list": [{"code": 333, "indent": 0, "parameters": [-1, 0, 68]}, {"code": 231, "indent": 0, "parameters": [20, "外框_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [3, "暗角_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [2, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [1, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 2 1"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 250, "indent": 0, "parameters": [{"name": "Powerup", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 326, "indent": 0, "parameters": [0, 0, 0, 0, 100]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 设置动画序列 : 动画序列[12]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 等待动画序列加载完成"]}, {"code": 111, "indent": 0, "parameters": [1, 115, 0, 100, 1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[2] : 播放简单状态元集合 : 集合[战斗发情待机]"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 \\}哈啊……啊……"]}, {"code": 119, "indent": 1, "parameters": ["情欲判定"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 115, 0, 50, 1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[2] : 播放简单状态元集合 : 集合[战斗脸红待机]"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 桐人君…战斗…拜托了……"]}, {"code": 119, "indent": 1, "parameters": ["情欲判定"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 播放简单状态元集合 : 集合[待机动作]"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 23 桐人君,战斗就拜托你了!"]}, {"code": 118, "indent": 0, "parameters": ["情欲判定"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 640, 0, 100, 100, 255, 0, 30, false]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 212, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 是新技能的效果吗？环境音似乎完全消失了…… "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac …………"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]桐人学长？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯，等会试试实战效果~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人学长！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊？猪田，有事吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 没事，就是跟你说一声，我们已经站好位了，你随时可以进攻。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]嘻嘻嘻，似乎只要压低声音，让系统认为是环境音，桐人这家伙就完全听不见了~"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 1, "turnB": 1, "turnEnding": true, "turnValid": true}, "list": [{"code": 117, "indent": 0, "parameters": [29]}, {"code": 111, "indent": 0, "parameters": [0, 34, 1]}, {"code": 117, "indent": 1, "parameters": [36]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 0, "enemyIndex": 0, "enemyValid": true, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 235, "indent": 0, "parameters": [1]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 235, "indent": 0, "parameters": [4]}, {"code": 235, "indent": 0, "parameters": [5]}, {"code": 235, "indent": 0, "parameters": [6]}, {"code": 235, "indent": 0, "parameters": [7]}, {"code": 235, "indent": 0, "parameters": [8]}, {"code": 235, "indent": 0, "parameters": [9]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 121, "indent": 0, "parameters": [35, 35, 1]}, {"code": 121, "indent": 0, "parameters": [33, 33, 1]}, {"code": 111, "indent": 0, "parameters": [1, 86, 0, 1, 1]}, {"code": 122, "indent": 1, "parameters": [86, 86, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 212, 0]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 213]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 333, "indent": 0, "parameters": [0, 1, 68]}, {"code": 333, "indent": 0, "parameters": [0, 0, 1]}, {"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 5, "members": [{"enemyId": 6, "x": 255, "y": 253, "hidden": false}], "name": "测试蝙蝠", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": true}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 1, "turnB": 1, "turnEnding": false, "turnValid": true}, "list": [{"code": 118, "indent": 0, "parameters": ["1"]}, {"code": 212, "indent": 0, "parameters": [-1, 183, false]}, {"code": 119, "indent": 0, "parameters": ["1"]}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}]}, {"id": 6, "members": [{"enemyId": 4, "x": 30, "y": 241, "hidden": false}], "name": "爆绀杂烩鸽", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": true}, "list": [{"code": 356, "indent": 0, "parameters": ["particle set 雨 battle 雨 above"]}, {"code": 231, "indent": 0, "parameters": [49, "", 0, 0, 648, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [50, "", 0, 0, 648, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 0, "parameters": [0, 233, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 0]}, {"code": 231, "indent": 2, "parameters": [61, "白色线条864-576x1008", 0, 0, 648, 0, 100, 100, 255, 0]}, {"code": 119, "indent": 2, "parameters": ["真实世界"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [60, "外框_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [59, "暗角_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [48, "战斗透视镜改", 0, 0, 648, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 49 48"]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 50 48"]}, {"code": 118, "indent": 0, "parameters": ["真实世界"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 播放简单状态元集合 : 集合[木屋揉胸影绘]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 播放简单状态元集合 : 集合[木屋揉胸]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Powerup", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 326, "indent": 0, "parameters": [0, 0, 0, 0, 100]}, {"code": 333, "indent": 0, "parameters": [-1, 0, 68]}, {"code": 313, "indent": 0, "parameters": [0, 4, 0, 19]}, {"code": 313, "indent": 0, "parameters": [0, 8, 0, 19]}, {"code": 121, "indent": 0, "parameters": [185, 185, 0]}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 0, "enemyIndex": 0, "enemyValid": true, "switchId": 1, "switchValid": false, "turnA": 3, "turnB": 0, "turnEnding": true, "turnValid": false}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Monster3", "volume": 80, "pitch": 100, "pan": 0}]}, {"code": 111, "indent": 0, "parameters": [1, 199, 0, 4, 5]}, {"code": 122, "indent": 1, "parameters": [199, 199, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 199, 0, 4, 0]}, {"code": 235, "indent": 1, "parameters": [48]}, {"code": 235, "indent": 1, "parameters": [49]}, {"code": 235, "indent": 1, "parameters": [50]}, {"code": 356, "indent": 1, "parameters": ["REMOVEMASK 49"]}, {"code": 356, "indent": 1, "parameters": ["REMOVEMASK 50"]}, {"code": 235, "indent": 1, "parameters": [59]}, {"code": 235, "indent": 1, "parameters": [60]}, {"code": 111, "indent": 1, "parameters": [0, 233, 0]}, {"code": 111, "indent": 2, "parameters": [0, 19, 0]}, {"code": 235, "indent": 3, "parameters": [61]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [185, 185, 1]}, {"code": 356, "indent": 1, "parameters": ["particle clear 闪电"]}, {"code": 356, "indent": 1, "parameters": ["particle clear 雨"]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 15, false]}, {"code": 333, "indent": 1, "parameters": [-1, 1, 68]}, {"code": 333, "indent": 1, "parameters": [0, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 199, 0, 3, 0]}, {"code": 121, "indent": 1, "parameters": [185, 185, 1]}, {"code": 356, "indent": 1, "parameters": ["particle clear 闪电"]}, {"code": 231, "indent": 1, "parameters": [51, "暗角_黑", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 15, false]}, {"code": 232, "indent": 1, "parameters": [51, 0, 0, 0, 640, 0, 100, 100, 255, 0, 10, false]}, {"code": 331, "indent": 1, "parameters": [0, 0, 0, 9999, false]}, {"code": 337, "indent": 1, "parameters": [0, 49, false]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 又复活了……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 难道说，因为是三鸽合体，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 所以命也有三条……吗？"]}, {"code": 121, "indent": 1, "parameters": [185, 185, 0]}, {"code": 232, "indent": 1, "parameters": [51, 0, 0, 0, 640, 0, 100, 100, 0, 0, 10, false]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[49] : 播放简单状态元集合 : 集合[木屋乳交影绘]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[50] : 播放简单状态元集合 : 集合[木屋乳交]"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 199, 0, 2, 0]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[49] : 播放简单状态元集合 : 集合[木屋舔胸影绘]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[50] : 播放简单状态元集合 : 集合[木屋舔胸]"]}, {"code": 331, "indent": 1, "parameters": [0, 0, 0, 9999, false]}, {"code": 337, "indent": 1, "parameters": [0, 49, false]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 复活？那我就再杀你一次！"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}]}, {"id": 7, "members": [{"enemyId": 180, "x": 30, "y": 364, "hidden": false}, {"enemyId": 183, "x": 218, "y": 444, "hidden": false}, {"enemyId": 182, "x": 42, "y": 304, "hidden": true}, {"enemyId": 186, "x": 222, "y": 247, "hidden": true}, {"enemyId": 181, "x": 216, "y": 318, "hidden": true}, {"enemyId": 184, "x": 201, "y": 424, "hidden": true}, {"enemyId": 185, "x": 0, "y": 232, "hidden": true}, {"enemyId": 188, "x": 150, "y": 394, "hidden": true}], "name": "弑神堕天", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": true}, "list": [{"code": 356, "indent": 0, "parameters": ["particle set 雨 battle 雨 above"]}, {"code": 333, "indent": 0, "parameters": [1, 0, 68]}, {"code": 231, "indent": 0, "parameters": [49, "", 0, 0, 648, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [50, "", 0, 0, 648, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 0, "parameters": [0, 233, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 0]}, {"code": 231, "indent": 2, "parameters": [61, "白色线条864-576x1008", 0, 0, 648, 0, 100, 100, 255, 0]}, {"code": 119, "indent": 2, "parameters": ["真实世界"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [60, "外框_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [59, "暗角_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [48, "战斗透视镜改", 0, 0, 648, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 49 48"]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 50 48"]}, {"code": 118, "indent": 0, "parameters": ["真实世界"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 播放简单状态元集合 : 集合[木屋贴窗后入影绘]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 播放简单状态元集合 : 集合[木屋贴窗后入]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Powerup", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 326, "indent": 0, "parameters": [0, 0, 0, 0, 100]}, {"code": 313, "indent": 0, "parameters": [0, 4, 0, 19]}, {"code": 313, "indent": 0, "parameters": [0, 8, 0, 19]}, {"code": 121, "indent": 0, "parameters": [185, 185, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "span": 0}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 0, "enemyIndex": 0, "enemyValid": true, "switchId": 1, "switchValid": false, "turnA": 3, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 333, "indent": 0, "parameters": [0, 1, 68]}, {"code": 333, "indent": 0, "parameters": [0, 0, 1]}, {"code": 326, "indent": 0, "parameters": [0, 0, 0, 0, 100]}, {"code": 0, "indent": 0, "parameters": []}], "span": 0}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 0, "enemyIndex": 1, "enemyValid": true, "switchId": 1, "switchValid": false, "turnA": 3, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 播放简单状态元集合 : 集合[木屋抵墙后入影绘]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 播放简单状态元集合 : 集合[木屋抵墙后入]"]}, {"code": 335, "indent": 0, "parameters": [3]}, {"code": 333, "indent": 0, "parameters": [3, 0, 68]}, {"code": 335, "indent": 0, "parameters": [2]}, {"code": 333, "indent": 0, "parameters": [2, 0, 68]}, {"code": 333, "indent": 0, "parameters": [1, 1, 68]}, {"code": 333, "indent": 0, "parameters": [1, 0, 1]}, {"code": 326, "indent": 0, "parameters": [0, 0, 0, 0, 100]}, {"code": 0, "indent": 0, "parameters": []}], "span": 0}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 0, "enemyIndex": 2, "enemyValid": true, "switchId": 1, "switchValid": false, "turnA": 3, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 333, "indent": 0, "parameters": [2, 1, 68]}, {"code": 333, "indent": 0, "parameters": [2, 0, 1]}, {"code": 326, "indent": 0, "parameters": [0, 0, 0, 0, 100]}, {"code": 0, "indent": 0, "parameters": []}], "span": 0}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 0, "enemyIndex": 3, "enemyValid": true, "switchId": 1, "switchValid": false, "turnA": 3, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 播放简单状态元集合 : 集合[木屋俯视后背位普通影绘]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 播放简单状态元集合 : 集合[木屋俯视后背位普通]"]}, {"code": 335, "indent": 0, "parameters": [6]}, {"code": 333, "indent": 0, "parameters": [6, 1, 68]}, {"code": 335, "indent": 0, "parameters": [4]}, {"code": 333, "indent": 0, "parameters": [4, 0, 68]}, {"code": 333, "indent": 0, "parameters": [3, 1, 68]}, {"code": 333, "indent": 0, "parameters": [3, 0, 1]}, {"code": 326, "indent": 0, "parameters": [0, 0, 0, 0, 100]}, {"code": 0, "indent": 0, "parameters": []}], "span": 0}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 0, "enemyIndex": 4, "enemyValid": true, "switchId": 1, "switchValid": false, "turnA": 3, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 播放简单状态元集合 : 集合[木屋俯视后背位按头影绘]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 播放简单状态元集合 : 集合[木屋俯视后背位按头]"]}, {"code": 335, "indent": 0, "parameters": [5]}, {"code": 333, "indent": 0, "parameters": [5, 0, 68]}, {"code": 333, "indent": 0, "parameters": [4, 1, 68]}, {"code": 333, "indent": 0, "parameters": [4, 0, 1]}, {"code": 326, "indent": 0, "parameters": [0, 0, 0, 0, 100]}, {"code": 0, "indent": 0, "parameters": []}], "span": 0}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 0, "enemyIndex": 5, "enemyValid": true, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 1, "turnEnding": false, "turnValid": false}, "list": [{"code": 121, "indent": 0, "parameters": [185, 185, 1]}, {"code": 121, "indent": 0, "parameters": [187, 187, 0]}, {"code": 356, "indent": 0, "parameters": [">高级BOSS框 : BOSS设置[3] : 固定框 : 显示"]}, {"code": 335, "indent": 0, "parameters": [7]}, {"code": 333, "indent": 0, "parameters": [7, 0, 68]}, {"code": 333, "indent": 0, "parameters": [5, 1, 68]}, {"code": 333, "indent": 0, "parameters": [5, 0, 1]}, {"code": 326, "indent": 0, "parameters": [0, 0, 0, 0, 100]}, {"code": 0, "indent": 0, "parameters": []}], "span": 0}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 100, "enemyIndex": 7, "enemyValid": false, "switchId": 188, "switchValid": true, "turnA": 0, "turnB": 1, "turnEnding": false, "turnValid": false}, "list": [{"code": 356, "indent": 0, "parameters": ["particle clear 雨"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 121, "indent": 0, "parameters": [188, 188, 1]}, {"code": 0, "indent": 0, "parameters": []}], "span": 2}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 0, "enemyIndex": 7, "enemyValid": true, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 1, "turnEnding": false, "turnValid": false}, "list": [{"code": 111, "indent": 0, "parameters": [0, 187, 1]}, {"code": 333, "indent": 1, "parameters": [7, 1, 68]}, {"code": 333, "indent": 1, "parameters": [7, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 337, "indent": 1, "parameters": [7, 41, false]}, {"code": 331, "indent": 1, "parameters": [7, 0, 0, 300, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 333, "indent": 0, "parameters": [7, 1, 68]}, {"code": 0, "indent": 0, "parameters": []}], "span": 0}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": true}, "list": [{"code": 111, "indent": 0, "parameters": [4, 8, 6, 70]}, {"code": 313, "indent": 1, "parameters": [0, 8, 1, 70]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}]}, {"id": 8, "members": [{"enemyId": 5, "x": 90, "y": 412, "hidden": false}], "name": "矿洞兽人", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": true}, "list": [{"code": 111, "indent": 0, "parameters": [0, 69, 0]}, {"code": 313, "indent": 1, "parameters": [0, 8, 0, 66]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 313, "indent": 1, "parameters": [0, 4, 0, 66]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 293, 0]}, {"code": 121, "indent": 1, "parameters": [293, 293, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 333, "indent": 0, "parameters": [-1, 0, 68]}, {"code": 121, "indent": 0, "parameters": [99, 99, 0]}, {"code": 231, "indent": 0, "parameters": [30, "外框_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [20, "暗角_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [1, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [2, "", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 2 1"]}, {"code": 231, "indent": 0, "parameters": [3, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [4, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 4 3"]}, {"code": 231, "indent": 0, "parameters": [5, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [6, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 6 5"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 250, "indent": 0, "parameters": [{"name": "Powerup", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 326, "indent": 0, "parameters": [0, 0, 0, 0, 100]}, {"code": 122, "indent": 0, "parameters": [85, 85, 0, 2, 1, 100]}, {"code": 117, "indent": 0, "parameters": [301]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [319]}, {"code": 121, "indent": 0, "parameters": [99, 99, 1]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 1, "turnEnding": true, "turnValid": true}, "list": [{"code": 230, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [42]}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 1, "turnEnding": true, "turnValid": true}, "list": [{"code": 111, "indent": 0, "parameters": [0, 291, 1]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 117, "indent": 1, "parameters": [300]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 1, "turnEnding": true, "turnValid": true}, "list": [{"code": 111, "indent": 0, "parameters": [0, 291, 0]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 117, "indent": 1, "parameters": [307]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 1, "turnEnding": true, "turnValid": true}, "list": [{"code": 111, "indent": 0, "parameters": [0, 291, 1]}, {"code": 111, "indent": 1, "parameters": [1, 86, 0, 6, 4]}, {"code": 111, "indent": 2, "parameters": [1, 86, 0, 3, 1]}, {"code": 111, "indent": 3, "parameters": [1, 297, 0, 800, 3]}, {"code": 122, "indent": 4, "parameters": [84, 84, 0, 2, 801, 1000]}, {"code": 111, "indent": 4, "parameters": [1, 297, 1, 84, 1]}, {"code": 121, "indent": 5, "parameters": [291, 291, 0]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 0, "enemyIndex": 0, "enemyValid": true, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 235, "indent": 0, "parameters": [4]}, {"code": 235, "indent": 0, "parameters": [5]}, {"code": 235, "indent": 0, "parameters": [6]}, {"code": 235, "indent": 0, "parameters": [7]}, {"code": 235, "indent": 0, "parameters": [8]}, {"code": 235, "indent": 0, "parameters": [9]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [100, 100, 1]}, {"code": 121, "indent": 0, "parameters": [292, 292, 1]}, {"code": 121, "indent": 0, "parameters": [35, 35, 1]}, {"code": 121, "indent": 0, "parameters": [33, 33, 1]}, {"code": 122, "indent": 0, "parameters": [86, 86, 0, 0, 0]}, {"code": 333, "indent": 0, "parameters": [0, 1, 68]}, {"code": 331, "indent": 0, "parameters": [0, 1, 0, 3000, true]}, {"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 9, "members": [{"enemyId": 6, "x": 99, "y": 444, "hidden": false}], "name": "矿洞强兽人", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": true}, "list": [{"code": 313, "indent": 0, "parameters": [0, 4, 0, 66]}, {"code": 333, "indent": 0, "parameters": [-1, 0, 67]}, {"code": 333, "indent": 0, "parameters": [-1, 0, 68]}, {"code": 231, "indent": 0, "parameters": [30, "外框_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [20, "暗角_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [1, "暗角_黑", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [3, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [4, "", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [5, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [6, "亚丝娜反抗", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [7, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [8, "亚丝娜跌倒", 0, 0, 340, -42, 200, 200, 0, 0]}, {"code": 231, "indent": 0, "parameters": [9, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [10, "亚丝娜脸红攻击", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 4 3"]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 6 5"]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 8 7"]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 10 9"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[4] : 设置动画序列 : 动画序列[12]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[4] : 播放简单状态元集合 : 集合[双人待机发情]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "1-女喘气", "volume": 20, "pitch": 120, "pan": 60}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Powerup", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 326, "indent": 0, "parameters": [0, 0, 0, 0, 100]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这个兽人身上有股诡异的气息，大家小心！"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[8]嗯嗯，知道知道！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}\\c[8]呜…嗯…嗯啊…啊~\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<兽人首领>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哼！你还有闲心顾及其他人啊？"]}, {"code": 337, "indent": 0, "parameters": [0, 151, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<兽人首领>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{死斗战吼！"]}, {"code": 313, "indent": 0, "parameters": [0, 4, 0, 72]}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 1, "turnB": 0, "turnEnding": false, "turnValid": true}, "list": [{"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯？"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac技能都被封印了？"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<兽人首领>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没错！直到战场上有人死去为止，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac所有人都只能使用最原始的攻击方式！"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呵，我看你是选了一种最痛苦的去世方式！"]}, {"code": 0, "indent": 0, "parameters": []}], "span": 0}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 1, "turnB": 0, "turnEnding": true, "turnValid": true}, "list": [{"code": 223, "indent": 0, "parameters": [[0, 0, 0, 102], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[4] : 设置动画序列 : 动画序列[12]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[4] : 播放简单状态元集合 : 集合[发情揉屁股]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "Amebo_aka_51b", "volume": 15, "pitch": 100, "pan": 60}]}, {"code": 356, "indent": 0, "parameters": ["PushGab 27 \\c[8]学姐的乳头硬了哦~你是不是想要了？"]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 356, "indent": 0, "parameters": ["PushGab 80 \\}\\c[8]才没有…！哦~\\i[90]呣~你不要乱说……！"]}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 2, "turnB": 0, "turnEnding": true, "turnValid": true}, "list": [{"code": 223, "indent": 0, "parameters": [[0, 0, 0, 102], 10, false]}, {"code": 121, "indent": 0, "parameters": [337, 337, 0]}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 2, "turnB": 1, "turnEnding": true, "turnValid": true}, "list": [{"code": 111, "indent": 0, "parameters": [0, 337, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac技能封印解开了？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac懒得耗下去了，给你一个痛快吧！"]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 235, "indent": 1, "parameters": [3]}, {"code": 235, "indent": 1, "parameters": [4]}, {"code": 235, "indent": 1, "parameters": [5]}, {"code": 235, "indent": 1, "parameters": [6]}, {"code": 235, "indent": 1, "parameters": [7]}, {"code": 235, "indent": 1, "parameters": [8]}, {"code": 235, "indent": 1, "parameters": [9]}, {"code": 235, "indent": 1, "parameters": [10]}, {"code": 235, "indent": 1, "parameters": [20]}, {"code": 235, "indent": 1, "parameters": [30]}, {"code": 333, "indent": 1, "parameters": [-1, 1, 67]}, {"code": 333, "indent": 1, "parameters": [-1, 1, 68]}, {"code": 339, "indent": 1, "parameters": [1, 4, 256, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}]}, {"id": 10, "members": [{"enemyId": 7, "x": 159, "y": 391, "hidden": false}], "name": "恶魔瓦拉克", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": true}, "list": [{"code": 111, "indent": 0, "parameters": [0, 69, 0]}, {"code": 313, "indent": 1, "parameters": [0, 8, 0, 66]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 313, "indent": 1, "parameters": [0, 4, 0, 66]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 333, "indent": 0, "parameters": [0, 0, 65]}, {"code": 333, "indent": 0, "parameters": [-1, 0, 68]}, {"code": 121, "indent": 0, "parameters": [340, 340, 1]}, {"code": 122, "indent": 0, "parameters": [300, 300, 0, 0, 0]}, {"code": 122, "indent": 0, "parameters": [119, 119, 0, 0, 0]}, {"code": 231, "indent": 0, "parameters": [30, "外框_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [20, "暗角_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [1, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [2, "", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 2 1"]}, {"code": 231, "indent": 0, "parameters": [3, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [4, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 4 3"]}, {"code": 231, "indent": 0, "parameters": [5, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [6, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 6 5"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 设置动画序列 : 动画序列[12]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 播放简单状态元集合 : 集合[单人待机]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac被动技能失效……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac似乎是这个恶魔BOSS的光环效果。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<恶魔瓦拉克>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呵呵呵呵~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不错的判断力，的确是精英战士。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<恶魔瓦拉克>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac战斗结束后，我会让那个锻造师把你们的尸体做成战傀。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac所以牺牲并非无谓，安心上路吧！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这个恶魔真是让人恶心……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac很快他就无法嚣张了。"]}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 1, "turnEnding": true, "turnValid": true}, "list": [{"code": 230, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [42]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 1, "turnB": 1, "turnEnding": true, "turnValid": true}, "list": [{"code": 230, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [229]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 1, "turnEnding": true, "turnValid": true}, "list": [{"code": 230, "indent": 0, "parameters": [15]}, {"code": 111, "indent": 0, "parameters": [1, 119, 0, 9, 0]}, {"code": 111, "indent": 1, "parameters": [0, 69, 0]}, {"code": 313, "indent": 2, "parameters": [0, 8, 1, 71]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 313, "indent": 2, "parameters": [0, 4, 1, 71]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜，就是现在！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人君！哈啊~\\i[90]"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯！我们…合力一处！"]}, {"code": 122, "indent": 1, "parameters": [119, 119, 1, 0, 1]}, {"code": 121, "indent": 1, "parameters": [338, 338, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<恶魔瓦拉克>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac这不可能！！！\\|\\|"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我是不可战胜的！\\|\\|"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哼！"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac只要我和亚丝娜双剑合璧，就没有不可能战胜的敌人！"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯啊…哈啊…哈欠…哈啊……"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 112, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 338, 1]}, {"code": 113, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 413, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 235, "indent": 1, "parameters": [3]}, {"code": 235, "indent": 1, "parameters": [4]}, {"code": 235, "indent": 1, "parameters": [5]}, {"code": 235, "indent": 1, "parameters": [6]}, {"code": 235, "indent": 1, "parameters": [7]}, {"code": 235, "indent": 1, "parameters": [8]}, {"code": 235, "indent": 1, "parameters": [9]}, {"code": 235, "indent": 1, "parameters": [10]}, {"code": 235, "indent": 1, "parameters": [20]}, {"code": 235, "indent": 1, "parameters": [30]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 313, "indent": 1, "parameters": [0, 4, 1, 66]}, {"code": 122, "indent": 1, "parameters": [119, 119, 0, 0, 0]}, {"code": 333, "indent": 1, "parameters": [0, 1, 68]}, {"code": 333, "indent": 1, "parameters": [0, 0, 1]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 119, 0, 5, 0]}, {"code": 111, "indent": 1, "parameters": [0, 69, 0]}, {"code": 313, "indent": 2, "parameters": [0, 8, 1, 71]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 313, "indent": 2, "parameters": [0, 4, 1, 71]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac沉默解除了！我可以使用技能了！不能错过这个机会！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜，我们一起进攻！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯…我会…哈欠…尽力配合你的……"]}, {"code": 339, "indent": 1, "parameters": [1, 4, 256, 0]}, {"code": 117, "indent": 1, "parameters": [231]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [229]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}]}, {"id": 11, "members": [{"enemyId": 5, "x": 0, "y": 385, "hidden": false}, {"enemyId": 5, "x": 231, "y": 444, "hidden": false}], "name": "矿洞双兽人", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": true}, "list": [{"code": 313, "indent": 0, "parameters": [0, 4, 0, 66]}, {"code": 231, "indent": 0, "parameters": [30, "外框_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [20, "暗角_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [1, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [2, "", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 2 1"]}, {"code": 231, "indent": 0, "parameters": [3, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [4, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 4 3"]}, {"code": 231, "indent": 0, "parameters": [5, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [6, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 6 5"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 250, "indent": 0, "parameters": [{"name": "Powerup", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 326, "indent": 0, "parameters": [0, 0, 0, 0, 100]}, {"code": 121, "indent": 0, "parameters": [99, 99, 1]}, {"code": 117, "indent": 0, "parameters": [301]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田，我现在进入战斗后，被动技能会让我完全屏蔽外在干扰。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac因此可能无暇顾及队友。所以亚丝娜就交给你保护了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[8]放心吧，学长！我一定会好好守护学姐的！"]}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 1, "turnEnding": true, "turnValid": true}, "list": [{"code": 230, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [42]}, {"code": 0, "indent": 0, "parameters": []}], "span": 0}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 1, "turnEnding": true, "turnValid": true}, "list": [{"code": 111, "indent": 0, "parameters": [0, 291, 1]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 117, "indent": 1, "parameters": [300]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}]}, {"id": 12, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 13, "members": [{"enemyId": 8, "x": 171, "y": 396, "hidden": false}], "name": "魔化植物", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": true}, "list": [{"code": 223, "indent": 0, "parameters": [[-34, -34, -34, 0], 30, false]}, {"code": 231, "indent": 0, "parameters": [30, "外框_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [20, "暗角_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [10, "暗角_黑", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 111, "indent": 0, "parameters": [0, 69, 0]}, {"code": 313, "indent": 1, "parameters": [0, 8, 0, 66]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 313, "indent": 1, "parameters": [0, 4, 0, 66]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 293, 0]}, {"code": 121, "indent": 1, "parameters": [293, 293, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 333, "indent": 0, "parameters": [-1, 0, 68]}, {"code": 121, "indent": 0, "parameters": [99, 99, 0]}, {"code": 231, "indent": 0, "parameters": [1, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [2, "", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 2 1"]}, {"code": 231, "indent": 0, "parameters": [3, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [4, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 4 3"]}, {"code": 231, "indent": 0, "parameters": [5, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [6, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 6 5"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 640, 0, 100, 100, 0, 0, 15, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Powerup", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 326, "indent": 0, "parameters": [0, 0, 0, 0, 100]}, {"code": 122, "indent": 0, "parameters": [85, 85, 0, 2, 1, 100]}, {"code": 111, "indent": 0, "parameters": [1, 300, 0, 10000, 2]}, {"code": 111, "indent": 1, "parameters": [1, 85, 0, 80, 2]}, {"code": 117, "indent": 2, "parameters": [301]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 117, "indent": 2, "parameters": [302]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 85, 0, 50, 2]}, {"code": 117, "indent": 2, "parameters": [301]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 85, 0, 95, 4]}, {"code": 117, "indent": 3, "parameters": [302]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 117, "indent": 3, "parameters": [303]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [319]}, {"code": 121, "indent": 0, "parameters": [99, 99, 1]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 1, "turnEnding": true, "turnValid": true}, "list": [{"code": 230, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [311]}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 1, "turnEnding": true, "turnValid": true}, "list": [{"code": 111, "indent": 0, "parameters": [0, 291, 1]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 117, "indent": 1, "parameters": [309]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 117, "indent": 1, "parameters": [307]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 1, "turnEnding": true, "turnValid": true}, "list": [{"code": 117, "indent": 0, "parameters": [310]}, {"code": 0, "indent": 0, "parameters": []}], "span": 1}, {"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 0, "enemyIndex": 0, "enemyValid": true, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 235, "indent": 0, "parameters": [4]}, {"code": 235, "indent": 0, "parameters": [5]}, {"code": 235, "indent": 0, "parameters": [6]}, {"code": 235, "indent": 0, "parameters": [7]}, {"code": 235, "indent": 0, "parameters": [8]}, {"code": 235, "indent": 0, "parameters": [9]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [20]}, {"code": 235, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [100, 100, 1]}, {"code": 121, "indent": 0, "parameters": [292, 292, 1]}, {"code": 121, "indent": 0, "parameters": [35, 35, 1]}, {"code": 121, "indent": 0, "parameters": [33, 33, 1]}, {"code": 122, "indent": 0, "parameters": [86, 86, 0, 0, 0]}, {"code": 333, "indent": 0, "parameters": [0, 1, 68]}, {"code": 333, "indent": 0, "parameters": [0, 0, 1]}, {"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 14, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 15, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 16, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 17, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 18, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 19, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 20, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 21, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 22, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 23, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 24, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 25, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 26, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 27, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 28, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 29, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 30, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 31, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 32, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 33, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 34, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 35, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 36, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 37, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 38, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 39, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 40, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 41, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 42, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 43, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 44, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 45, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 46, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 47, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 48, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 49, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 50, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 51, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 52, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 53, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 54, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 55, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 56, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 57, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 58, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 59, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 60, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 61, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 62, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 63, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 64, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 65, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 66, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 67, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 68, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 69, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 70, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 71, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 72, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 73, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 74, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 75, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 76, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 77, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 78, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 79, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 80, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 81, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 82, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 83, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 84, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 85, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 86, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 87, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 88, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 89, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 90, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 91, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 92, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 93, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 94, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 95, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 96, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 97, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 98, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 99, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}, {"id": 100, "members": [], "name": "", "pages": [{"conditions": {"actorHp": 50, "actorId": 1, "actorValid": false, "enemyHp": 50, "enemyIndex": 0, "enemyValid": false, "switchId": 1, "switchValid": false, "turnA": 0, "turnB": 0, "turnEnding": false, "turnValid": false}, "list": [{"code": 0, "indent": 0, "parameters": []}], "span": 0}]}]