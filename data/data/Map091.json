{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "Grassland", "battleback2Name": "Forest", "bgm": {"name": "魔王魂  ヒーリング05", "pan": 0, "pitch": 100, "volume": 50}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 30, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 2, "width": 40, "data": [7664, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7668, 7692, 7692, 7692, 7692, 7692, 7692, 7692, 7692, 7692, 7692, 7692, 7672, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7688, 2832, 2840, 7680, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7668, 7692, 7692, 7692, 7692, 7692, 7692, 7692, 7702, 2850, 2836, 2837, 2849, 2849, 2838, 2836, 2836, 2836, 2836, 2852, 7704, 7692, 7692, 7692, 7692, 7692, 7692, 7692, 7692, 7702, 2832, 2840, 7680, 7664, 7664, 7664, 7664, 7664, 7664, 7668, 7702, 2850, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2817, 2816, 2840, 2082, 2084, 2832, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2836, 2817, 2840, 7704, 7672, 7664, 7664, 7664, 7664, 7668, 7702, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2064, 2072, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 7704, 7672, 7664, 7664, 7664, 7688, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2064, 2050, 2084, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2846, 2853, 7680, 7664, 7664, 7664, 7688, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2088, 2056, 2072, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2906, 2848, 7704, 7672, 7664, 7664, 7688, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 2064, 2050, 2084, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2908, 2857, 2853, 7680, 7664, 7664, 7688, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2088, 2056, 2072, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 2910, 2848, 7680, 7664, 7664, 7688, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 2064, 2072, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2837, 2855, 7680, 7664, 7664, 7688, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2064, 2072, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 7698, 7665, 7664, 7664, 7666, 7700, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2064, 2072, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 7680, 7664, 7664, 7664, 7664, 7688, 2832, 2816, 2816, 2816, 2816, 2820, 2844, 2824, 2816, 2816, 2820, 2844, 2854, 2082, 2049, 2072, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 7704, 7672, 7664, 7692, 7692, 7702, 2832, 2816, 2816, 2820, 2844, 2854, 7466, 2856, 2844, 2824, 2840, 2082, 2068, 2049, 2052, 2086, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 7680, 7664, 2849, 2849, 2849, 2845, 2844, 2844, 2841, 7458, 7444, 7427, 7444, 7460, 2834, 2854, 2064, 2048, 2052, 2086, 2850, 2817, 2816, 2820, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 7680, 7664, 2068, 2068, 2068, 2068, 2068, 2084, 2860, 7464, 7432, 7424, 7424, 7448, 2860, 2082, 2049, 2052, 2086, 2850, 2817, 2816, 2816, 2840, 2906, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 7680, 7664, 2076, 2076, 2076, 2076, 2056, 2050, 2084, 7811, 7464, 7452, 7432, 7448, 2082, 2049, 2052, 2086, 2850, 2817, 2816, 2816, 2816, 2840, 2908, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 7680, 7664, 2849, 2849, 2849, 2853, 2088, 2056, 2072, 7817, 7811, 7814, 7464, 7462, 2064, 2052, 2086, 2850, 2817, 2816, 2816, 2816, 2816, 2818, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 7680, 7664, 7684, 7685, 7709, 2833, 2852, 2088, 2058, 2084, 7817, 7816, 7811, 7814, 2066, 2086, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2840, 7698, 7665, 7664, 7664, 7688, 2851, 2845, 2826, 2852, 2088, 2058, 2084, 2862, 7817, 7820, 2080, 2859, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2898, 2900, 2832, 2816, 2816, 2816, 2816, 2816, 2840, 7704, 7672, 7664, 7664, 7688, 2848, 2906, 2832, 2818, 2852, 2088, 2058, 2068, 2068, 2068, 2074, 2910, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2904, 2902, 2832, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 7680, 7664, 7664, 7688, 2848, 2908, 2832, 2816, 2818, 2852, 2088, 2076, 2076, 2076, 2086, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 7680, 7664, 7664, 7688, 2833, 2837, 2845, 2824, 2816, 2818, 2852, 2910, 2850, 2836, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 7680, 7664, 7692, 7702, 2832, 2840, 2910, 2832, 2816, 2816, 2818, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 7680, 7664, 2849, 2849, 2845, 2846, 2849, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 7704, 7672, 2885, 2897, 2897, 2897, 2909, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 7680, 2902, 2859, 2849, 2849, 2838, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2907, 2909, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 7680, 7684, 7684, 7684, 7700, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2817, 2816, 2820, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 7698, 7665, 7664, 7664, 7664, 7666, 7700, 2856, 2844, 2844, 2844, 2844, 2844, 2824, 2816, 2816, 2816, 2820, 2844, 2844, 2844, 2854, 7698, 7684, 7684, 7684, 7684, 7684, 7684, 7700, 2856, 2844, 2844, 2844, 2824, 2816, 2820, 2844, 2854, 7698, 7665, 7664, 7664, 7664, 7664, 7664, 7666, 7684, 7684, 7684, 7684, 7684, 7700, 2856, 2844, 2844, 2844, 2854, 7698, 7684, 7684, 7684, 7665, 7664, 7664, 7664, 7664, 7664, 7664, 7666, 7684, 7684, 7684, 7700, 2856, 2844, 2854, 7698, 7684, 7665, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7666, 7684, 7684, 7684, 7684, 7684, 7665, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7664, 7666, 7684, 7684, 7684, 7665, 7664, 7664, 7664, 7664, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3042, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3043, 3053, 0, 3042, 3028, 3029, 3041, 3053, 0, 3024, 3032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3051, 3045, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3043, 3047, 0, 0, 3048, 3036, 3046, 0, 0, 3051, 3037, 3046, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3049, 3053, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3052, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3050, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3042, 3011, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3051, 3017, 3008, 3014, 3053, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3048, 3020, 3046, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3052, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3050, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3025, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3048, 3038, 3045, 0, 0, 0, 0, 0, 3043, 3053, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3050, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3052, 0, 0, 0, 0, 0, 3052, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3042, 3011, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3051, 3017, 3008, 3014, 3053, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3054, 0, 0, 3048, 3020, 3046, 0, 3054, 0, 3050, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3050, 0, 0, 3052, 0, 0, 0, 3042, 3011, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3040, 0, 0, 0, 0, 0, 0, 3048, 3036, 3046, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3050, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3043, 3039, 3053, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3042, 3011, 3044, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3052, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3051, 3017, 3008, 3014, 3053, 0, 3051, 3053, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3048, 3020, 3046, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3050, 0, 0, 0, 0, 0, 3054, 0, 0, 0, 3052, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3051, 3045, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3052, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3052, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3051, 3045, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3051, 3053, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3052, 0, 3054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 123, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 114, 0, 0, 0, 0, 0, 0, 0, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 123, 0, 0, 0, 0, 0, 0, 121, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 114, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 123, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 113, 112, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 121, 120, 121, 120, 0, 0, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 122, 123, 122, 123, 122, 123, 122, 123, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 121, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 121, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 121, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 121, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 121, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 121, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 121, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 121, 120, 121, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 121, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 121, 120, 121, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 121, 120, 121, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 121, 120, 121, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 121, 120, 121, 120, 121, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 121, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 121, 120, 121, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 121, 120, 121, 120, 121, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 123, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 121, 120, 121, 120, 121, 0, 0, 120, 0, 0, 0, 0, 0, 0, 0, 112, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 122, 123, 123, 122, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 122, 123, 122, 123, 122, 123, 122, 123, 122, 123, 122, 123, 122, 113, 122, 123, 122, 123, 122, 123, 122, 123, 122, 123, 122, 123, 122, 123, 122, 123, 122, 123, 122, 113, 112, 123, 122, 123, 122, 123, 114, 115, 114, 115, 113, 115, 114, 115, 114, 115, 114, 115, 114, 121, 120, 121, 120, 121, 120, 121, 120, 121, 93, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 93, 120, 115, 114, 115, 114, 115, 122, 123, 122, 113, 121, 120, 121, 120, 121, 120, 112, 113, 93, 0, 0, 0, 0, 0, 0, 0, 0, 105, 101, 120, 121, 120, 121, 120, 121, 120, 121, 120, 121, 101, 112, 123, 122, 123, 122, 123, 114, 115, 113, 93, 0, 105, 89, 0, 0, 0, 120, 121, 101, 0, 0, 0, 0, 99, 112, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 115, 114, 115, 114, 115, 122, 123, 121, 101, 0, 0, 112, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 121, 0, 0, 0, 112, 113, 0, 0, 112, 113, 0, 0, 0, 0, 0, 0, 120, 115, 123, 122, 123, 114, 115, 113, 0, 0, 0, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 114, 115, 113, 112, 114, 115, 113, 0, 0, 0, 0, 0, 0, 120, 115, 114, 115, 122, 123, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 113, 122, 123, 112, 113, 122, 123, 112, 113, 112, 113, 0, 0, 0, 99, 120, 122, 123, 114, 115, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 112, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 113, 0, 0, 0, 112, 114, 115, 122, 123, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 17, 0, 0, 0, 93, 112, 113, 112, 113, 112, 113, 112, 113, 122, 123, 121, 0, 0, 112, 123, 122, 123, 114, 115, 113, 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 101, 120, 121, 120, 121, 120, 121, 120, 112, 114, 115, 113, 0, 0, 120, 115, 114, 115, 122, 123, 122, 113, 0, 0, 93, 0, 0, 0, 0, 112, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 89, 112, 113, 0, 0, 99, 90, 112, 113, 122, 123, 121, 0, 0, 112, 123, 122, 123, 114, 115, 114, 121, 0, 0, 101, 0, 93, 0, 0, 120, 112, 113, 0, 0, 0, 0, 0, 0, 0, 0, 112, 114, 115, 113, 0, 0, 112, 113, 112, 113, 121, 0, 0, 0, 120, 115, 114, 115, 122, 123, 122, 113, 0, 0, 0, 0, 101, 0, 0, 0, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 120, 122, 123, 121, 0, 0, 120, 112, 113, 93, 0, 0, 0, 0, 0, 120, 122, 123, 120, 121, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 121, 92, 0, 0, 0, 120, 121, 101, 0, 0, 0, 0, 0, 112, 114, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 93, 0, 120, 122, 123, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 113, 112, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 101, 0, 112, 114, 115, 112, 113, 112, 113, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 112, 113, 112, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 123, 122, 123, 114, 115, 114, 121, 101, 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 99, 93, 112, 113, 112, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 115, 114, 115, 122, 123, 121, 98, 0, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 101, 120, 112, 113, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 123, 122, 123, 114, 115, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 113, 112, 113, 0, 0, 0, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 112, 113, 0, 0, 0, 120, 122, 114, 115, 122, 123, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 113, 112, 113, 112, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 113, 112, 113, 0, 0, 0, 120, 122, 123, 114, 115, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 113, 112, 113, 112, 113, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 113, 112, 113, 112, 113, 0, 0, 112, 114, 115, 122, 123, 121, 0, 0, 0, 0, 0, 0, 0, 0, 112, 113, 112, 113, 112, 113, 121, 96, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 113, 112, 113, 112, 113, 112, 113, 0, 120, 122, 123, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 112, 113, 112, 113, 112, 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 112, 113, 112, 113, 112, 113, 121, 0, 112, 114, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 112, 113, 121, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 113, 112, 113, 112, 113, 112, 113, 93, 120, 115, 123, 113, 112, 113, 112, 113, 0, 0, 93, 0, 0, 0, 0, 0, 120, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 96, 120, 112, 113, 112, 113, 112, 113, 93, 101, 112, 123, 115, 122, 123, 122, 123, 122, 113, 0, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 113, 112, 113, 112, 113, 112, 113, 112, 113, 122, 123, 112, 113, 93, 101, 112, 123, 122, 123, 114, 115, 114, 115, 114, 115, 113, 112, 113, 112, 113, 98, 98, 0, 0, 112, 113, 112, 113, 112, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 123, 121, 101, 112, 123, 115, 114, 115, 122, 123, 122, 123, 122, 123, 122, 123, 122, 123, 122, 113, 112, 113, 112, 123, 122, 123, 122, 123, 122, 123, 122, 123, 122, 123, 122, 123, 122, 123, 122, 123, 122, 113, 112, 123, 122, 123, 122, 123, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 114, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 62}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [9, "", 0, 0, 346, 400, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [2, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 设置动画序列 : 动画序列[12]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[9] : 设置动画序列 : 动画序列[6]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[2] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[9] : 等待动画序列加载完成"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Up4", "volume": 90, "pitch": 120, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 43, "parameters": [0], "indent": null}, {"code": 41, "parameters": ["桐人", 0], "indent": null}, {"code": 34, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 43, "parameters": [0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 63]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这里就是莉兹说的地方嘛,确实周围有怪物的气息，接下来"]}, {"code": 401, "indent": 0, "parameters": ["\\dac稍微小心点的前进吧。"]}, {"code": 213, "indent": 0, "parameters": [3, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那个有关我的技能其实有个新的能力想告诉下学姐和前辈。"]}, {"code": 213, "indent": 0, "parameters": [-1, 2, false]}, {"code": 213, "indent": 0, "parameters": [2, 2, true]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐,那个能让我握一下你的手吗?"]}, {"code": 213, "indent": 0, "parameters": [2, 1, true]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 0, "parameters": [1, "FOG明日奈理解", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac诶...怎么这么突然..."]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac抱..抱歉，因为这是个必须接触才能发动的能力。"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac接触才能使用的能力嘛,结合猪田你的特殊技能来看难道是"]}, {"code": 401, "indent": 0, "parameters": ["\\dac能让你的那个技能作用于你接触的人吗?"]}, {"code": 213, "indent": 0, "parameters": [2, 9, false]}, {"code": 213, "indent": 0, "parameters": [3, 9, true]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不愧是桐人前辈,确实介绍是个这样的能力，但是我在习得"]}, {"code": 401, "indent": 0, "parameters": ["\\dac之后还没有尝试用过,所以也不知道能不能成功,不过我想如果这个"]}, {"code": 401, "indent": 0, "parameters": ["\\dac技能能有效的话,以后我也能稍微在旁边帮上学姐的忙。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac确实听起来是个非常强大的效果,有尝试一下的价值,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac如果成功的话以后猪田的隐身在遇到危险的时候是个很好"]}, {"code": 401, "indent": 0, "parameters": ["\\dac的保护手段。"]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 231, "indent": 0, "parameters": [1, "FOG明日奈苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac既然桐人君都这么说的话，那就稍微试一下吧...."]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 7, false]}, {"code": 213, "indent": 0, "parameters": [3, 4, true]}, {"code": 212, "indent": 0, "parameters": [3, 45, true]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 42, "parameters": [50], "indent": null}, {"code": 37, "indent": null}, {"code": 2, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [50], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 42, "parameters": [50], "indent": null}, {"code": 19, "indent": null}, {"code": 41, "parameters": ["$猪亚同框(静态)", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [50], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["$猪亚同框(静态)", 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 1, true]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac居然真的隐身了,而且感觉不到任何气息,好强大的能力。"]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 231, "indent": 0, "parameters": [1, "FOG明日奈迟疑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[8]桐人君好像真的完全看不见,猪田君你居然觉醒了这么强大的能力。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 213, "indent": 0, "parameters": [3, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[8]这都亏了学姐和前辈带我一起组队离开了新手城,而且修建据点期间也分了很多经验值给我。"]}, {"code": 231, "indent": 0, "parameters": [1, "FOG明日奈苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[8]毕竟猪田君也帮了我们不少忙嘛，那个差不多可以松开手了猪田君。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 213, "indent": 0, "parameters": [3, 8, true]}, {"code": 224, "indent": 0, "parameters": [[255, 136, 221, 170], 60, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "puu10", "volume": 60, "pitch": 90, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [8, "", 0, 0, 346, 400, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [4, "闪图透视镜", 0, 0, 380, 338, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": ["SETMASK 8 4"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[8] : 设置动画序列 : 动画序列[6]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[8] : 播放简单状态元集合 : 集合[摸屁股]"]}, {"code": 231, "indent": 0, "parameters": [5, "剧情小图背景3", 0, 0, -816, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [6, "剧情小图背景1", 0, 0, -29, -7, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [7, "剧情小图背景2", 0, 0, 29, 7, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [6, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 0, "parameters": [7, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 0, "parameters": [8, 0, 0, 0, 375, 407, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 0, "parameters": [4, 0, 0, 0, 404, 345, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 0, "parameters": [5, 0, 0, 0, -20, 0, 100, 100, 255, 0, 15, true]}, {"code": 232, "indent": 0, "parameters": [5, 0, 0, 0, 20, 0, 100, 100, 255, 0, 30, false]}, {"code": 232, "indent": 0, "parameters": [6, 0, 0, 0, 58, 14, 100, 100, 0, 0, 45, false]}, {"code": 232, "indent": 0, "parameters": [7, 0, 0, 0, -29, -7, 100, 100, 0, 0, 45, false]}, {"code": 232, "indent": 0, "parameters": [5, 0, 0, 0, 836, 0, 100, 100, 0, 0, 45, false]}, {"code": 232, "indent": 0, "parameters": [4, 0, 0, 0, 438, 352, 100, 100, 0, 0, 50, false]}, {"code": 232, "indent": 0, "parameters": [8, 0, 0, 0, 404, 414, 100, 100, 0, 0, 50, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 235, "indent": 0, "parameters": [5]}, {"code": 235, "indent": 0, "parameters": [6]}, {"code": 235, "indent": 0, "parameters": [7]}, {"code": 235, "indent": 0, "parameters": [8]}, {"code": 235, "indent": 0, "parameters": [4]}, {"code": 122, "indent": 0, "parameters": [33, 33, 1, 0, 1]}, {"code": 356, "indent": 0, "parameters": ["addLog 亚丝娜的经验似乎发生了一些变化..."]}, {"code": 213, "indent": 0, "parameters": [2, 1, true]}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 42, "parameters": [255], "indent": null}, {"code": 41, "parameters": ["亚丝娜", 0], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 40, "indent": 0}, {"code": 42, "parameters": [255], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 2, false]}, {"code": 213, "indent": 0, "parameters": [2, 5, false]}, {"code": 213, "indent": 0, "parameters": [3, 4, true]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac突然解除了，嗯?亚丝娜发生什么了吗?"]}, {"code": 213, "indent": 0, "parameters": [2, 1, true]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac第一次用这种能力有点不太习惯,好像没控制好，突然解除了"]}, {"code": 401, "indent": 0, "parameters": ["\\dac估计吓到亚丝娜学姐了。"]}, {"code": 213, "indent": 0, "parameters": [2, 7, true]}, {"code": 231, "indent": 0, "parameters": [1, "FOG明日奈妥协", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯...突然解开了稍微吓了一跳。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不过真是不得了的能力,有了猪田的这个能力加上亚丝娜自身"]}, {"code": 401, "indent": 0, "parameters": ["\\dac的大部分异常状态免疫，我也可以更加安心面对眼前的怪物了。"]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 231, "indent": 0, "parameters": [1, "FOG明日奈不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不过看起来猪田君的这个能力还是有不稳定性，我看还是"]}, {"code": 401, "indent": 0, "parameters": ["\\dac少用会比较好,一般情况猪田君还是先自己躲起来吧。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac也有道理,那猪田还是优先照顾自身，如果周围有其他怪物"]}, {"code": 401, "indent": 0, "parameters": ["\\dac靠近或者异常成功在使用这个能力和亚丝娜一起隐藏吧。"]}, {"code": 213, "indent": 0, "parameters": [3, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯...我知道了,我会好好观察周围的。"]}, {"code": 213, "indent": 0, "parameters": [2, 3, true]}, {"code": 231, "indent": 0, "parameters": [1, "FOG明日奈微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我也为了桐人君学习了新的技能,这种等级的怪物桐人君"]}, {"code": 401, "indent": 0, "parameters": ["\\dac也一定能够直接秒杀的。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我会努力的~"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Item1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac从现在开始以后的战斗亚丝娜不会在主界面参战,会维持后"]}, {"code": 401, "indent": 0, "parameters": ["\\dac卫状态每次回合结束都会有随机概率给桐人进行支援,同时因为猪田"]}, {"code": 401, "indent": 0, "parameters": ["\\dac觉醒了新的能力,随着战斗时间的增加周边可能会有怪物接近的风险，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这个时候猪田就会接近亚丝娜并且使用能力进入两人的隐身状态。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac进入隐身状态后,亚丝娜将不会在进行主动支援，不过桐人"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可以使用'援'技能向隐身中的亚丝娜下达想要的支援效果。"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": ["NF_NPC追加 23 1"]}, {"code": 356, "indent": 0, "parameters": ["NF_NPC追加 27 2"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": ["PushGab 23 看起来是个很普通的森林,不过也不要掉以轻心桐人君"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 10 嗯,我会的,亚丝娜多注意自己的位置就好"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 27 桐人前辈放心,有什么异常情况我也会用新能力保护学姐的~"]}, {"code": 356, "indent": 0, "parameters": ["<PERSON><PERSON><PERSON><PERSON> 23 .........."]}, {"code": 356, "indent": 0, "parameters": ["PushGab 10 真是可靠啊,猪田!"]}, {"code": 356, "indent": 0, "parameters": ["SetArray 90 1 2"]}, {"code": 355, "indent": 0, "parameters": ["$gameParty._steps = 0"]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 64]}, {"code": 201, "indent": 0, "parameters": [0, 91, 8, 23, 6, 2]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 17}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 63}, "directionFix": false, "image": {"tileId": 0, "characterName": "亚丝娜", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 22}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 63}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头游戏服", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 22}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [83, 83, 0, 3, 7, 3, 0]}, {"code": 111, "indent": 0, "parameters": [1, 83, 0, 50, 1]}, {"code": 121, "indent": 1, "parameters": [40, 40, 0]}, {"code": 356, "indent": 1, "parameters": ["RandomNumber 90 102"]}, {"code": 111, "indent": 1, "parameters": [1, 102, 0, 1, 0]}, {"code": 224, "indent": 2, "parameters": [[255, 136, 221, 170], 60, false]}, {"code": 250, "indent": 2, "parameters": [{"name": "puu10", "volume": 60, "pitch": 90, "pan": 0}]}, {"code": 231, "indent": 2, "parameters": [8, "", 0, 0, 346, 400, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": [">图片动态遮罩板A : 简单透视镜 : 图片[8] : 样式[20]"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 356, "indent": 2, "parameters": [">图片动态遮罩板A : 图片[8] : 启用该插件的动态遮罩板"]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[8] : 设置动画序列 : 动画序列[6]"]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[8] : 播放简单状态元集合 : 集合[地图行走]"]}, {"code": 231, "indent": 2, "parameters": [5, "剧情小图背景3", 0, 0, -816, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 2, "parameters": [6, "剧情小图背景1", 0, 0, -29, -7, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [7, "剧情小图背景2", 0, 0, 29, 7, 100, 100, 0, 0]}, {"code": 232, "indent": 2, "parameters": [6, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 2, "parameters": [7, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 2, "parameters": [8, 0, 0, 0, 375, 407, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 2, "parameters": [5, 0, 0, 0, -20, 0, 100, 100, 255, 0, 15, true]}, {"code": 232, "indent": 2, "parameters": [5, 0, 0, 0, 20, 0, 100, 100, 255, 0, 30, false]}, {"code": 232, "indent": 2, "parameters": [6, 0, 0, 0, 58, 14, 100, 100, 0, 0, 45, false]}, {"code": 232, "indent": 2, "parameters": [7, 0, 0, 0, -29, -7, 100, 100, 0, 0, 45, false]}, {"code": 232, "indent": 2, "parameters": [5, 0, 0, 0, 836, 0, 100, 100, 0, 0, 45, false]}, {"code": 232, "indent": 2, "parameters": [8, 0, 0, 0, 404, 414, 100, 100, 0, 0, 90, true]}, {"code": 235, "indent": 2, "parameters": [5]}, {"code": 235, "indent": 2, "parameters": [6]}, {"code": 235, "indent": 2, "parameters": [7]}, {"code": 235, "indent": 2, "parameters": [8]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(1).requestBalloon(4);"]}, {"code": 230, "indent": 2, "parameters": [100]}, {"code": 356, "indent": 2, "parameters": ["PushGab 27 \\}\\c[8]学姐漂亮的屁股在我面前一抖一抖的~"]}, {"code": 230, "indent": 2, "parameters": [100]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(11);"]}, {"code": 230, "indent": 2, "parameters": [100]}, {"code": 356, "indent": 2, "parameters": ["PushGab 10 嗯?猪田你刚刚有说什么嘛?"]}, {"code": 230, "indent": 2, "parameters": [100]}, {"code": 356, "indent": 2, "parameters": ["PushGab 27 没什么,没什么，只是感觉今天天气好像不错呢~"]}, {"code": 230, "indent": 2, "parameters": [100]}, {"code": 356, "indent": 2, "parameters": ["<PERSON><PERSON><PERSON><PERSON> 23 .........."]}, {"code": 355, "indent": 2, "parameters": ["$gameParty._steps = 0"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 102, 0, 2, 0]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(1).requestBalloon(9);"]}, {"code": 230, "indent": 2, "parameters": [120]}, {"code": 356, "indent": 2, "parameters": ["NF_NPC削除 1"]}, {"code": 356, "indent": 2, "parameters": ["NF_NPC削除 2"]}, {"code": 356, "indent": 2, "parameters": ["NF_NPC追加 98 1"]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(2);"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 23 \\}\\c[8]怎..怎么了?别突然离我这么近...."]}, {"code": 230, "indent": 2, "parameters": [100]}, {"code": 356, "indent": 2, "parameters": ["PushGab 27 \\}\\c[8]学姐好像有只虫子沾在你衣服上了"]}, {"code": 230, "indent": 2, "parameters": [100]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(1);"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 23 \\}\\c[8]诶..真的吗!快帮我弄掉它！"]}, {"code": 230, "indent": 2, "parameters": [100]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(4);"]}, {"code": 230, "indent": 2, "parameters": [100]}, {"code": 356, "indent": 2, "parameters": ["PushGab 27 \\}\\c[8]放心交给我吧~"]}, {"code": 230, "indent": 2, "parameters": [100]}, {"code": 224, "indent": 2, "parameters": [[255, 136, 221, 170], 60, false]}, {"code": 250, "indent": 2, "parameters": [{"name": "puu10", "volume": 60, "pitch": 90, "pan": 0}]}, {"code": 231, "indent": 2, "parameters": [8, "", 0, 0, 346, 400, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": [">图片动态遮罩板A : 简单透视镜 : 图片[8] : 样式[20]"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 356, "indent": 2, "parameters": [">图片动态遮罩板A : 图片[8] : 启用该插件的动态遮罩板"]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[8] : 设置动画序列 : 动画序列[6]"]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[8] : 播放简单状态元集合 : 集合[地图打屁股]"]}, {"code": 230, "indent": 2, "parameters": [5]}, {"code": 356, "indent": 2, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 23 嗯啊♥~"]}, {"code": 231, "indent": 2, "parameters": [5, "剧情小图背景3", 0, 0, -816, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 2, "parameters": [6, "剧情小图背景1", 0, 0, -29, -7, 100, 100, 255, 0]}, {"code": 231, "indent": 2, "parameters": [7, "剧情小图背景2", 0, 0, 29, 7, 100, 100, 0, 0]}, {"code": 232, "indent": 2, "parameters": [6, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 2, "parameters": [7, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 2, "parameters": [8, 0, 0, 0, 375, 407, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 2, "parameters": [5, 0, 0, 0, -20, 0, 100, 100, 255, 0, 15, true]}, {"code": 232, "indent": 2, "parameters": [5, 0, 0, 0, 20, 0, 100, 100, 255, 0, 30, false]}, {"code": 232, "indent": 2, "parameters": [6, 0, 0, 0, 58, 14, 100, 100, 0, 0, 60, false]}, {"code": 232, "indent": 2, "parameters": [7, 0, 0, 0, -29, -7, 100, 100, 0, 0, 60, false]}, {"code": 232, "indent": 2, "parameters": [5, 0, 0, 0, 836, 0, 100, 100, 0, 0, 60, false]}, {"code": 232, "indent": 2, "parameters": [8, 0, 0, 0, 404, 414, 100, 100, 0, 0, 120, true]}, {"code": 235, "indent": 2, "parameters": [5]}, {"code": 235, "indent": 2, "parameters": [6]}, {"code": 235, "indent": 2, "parameters": [7]}, {"code": 235, "indent": 2, "parameters": [8]}, {"code": 356, "indent": 2, "parameters": ["NF_NPC削除 1"]}, {"code": 356, "indent": 2, "parameters": ["NF_NPC追加 99 1"]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(11);"]}, {"code": 213, "indent": 2, "parameters": [-1, 2, false]}, {"code": 230, "indent": 2, "parameters": [100]}, {"code": 356, "indent": 2, "parameters": ["PushGab 10 亚丝娜？发生什么事了吗?"]}, {"code": 230, "indent": 2, "parameters": [100]}, {"code": 356, "indent": 2, "parameters": ["PushGab 23 没...没什么..不小心被虫子咬了一下..."]}, {"code": 230, "indent": 2, "parameters": [100]}, {"code": 356, "indent": 2, "parameters": ["PushGab 10 嘛,毕竟这个地方是森林...."]}, {"code": 230, "indent": 2, "parameters": [100]}, {"code": 356, "indent": 2, "parameters": ["PushGab 27 没事的前辈,我已经帮学姐把虫子赶跑了♥♥~"]}, {"code": 230, "indent": 2, "parameters": [100]}, {"code": 356, "indent": 2, "parameters": ["PushGab 10 辛苦了,猪田,亚丝娜很害怕虫子,我们还是赶紧完成委托离开森林吧"]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(5);"]}, {"code": 230, "indent": 2, "parameters": [100]}, {"code": 250, "indent": 2, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": ["NF_NPC削除 1"]}, {"code": 356, "indent": 2, "parameters": ["NF_NPC追加 23 1"]}, {"code": 356, "indent": 2, "parameters": ["NF_NPC追加 27 2"]}, {"code": 355, "indent": 2, "parameters": ["$gameParty._steps = 0"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 102, 0, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [40, 40, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 65}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 14}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": false, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 301, "indent": 0, "parameters": [0, 3, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [104, 104, 1, 0, 1]}, {"code": 111, "indent": 0, "parameters": [1, 104, 0, 5, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Monster5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 桐人君,看,那棵枯树发生了变化,可能就是莉兹说的怪物"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 10 嗯,打起精神上吧!"]}, {"code": 356, "indent": 1, "parameters": ["<PERSON><PERSON><PERSON>ab 27 ……"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 1, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 40, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": false, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜和猪田似乎没有做好战斗的准备，现在不是发起攻击的正确时机。"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": true, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 65}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 230, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [0, 1, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 15]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 6, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 212, "indent": 1, "parameters": [0, 6, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 9, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [7]}, {"code": 212, "indent": 1, "parameters": [0, 31, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 10, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [7]}, {"code": 212, "indent": 1, "parameters": [0, 26, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 11, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [7]}, {"code": 212, "indent": 1, "parameters": [0, 147, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 12, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 212, "indent": 1, "parameters": [0, 148, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 13, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 212, "indent": 1, "parameters": [0, 149, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 14, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 212, "indent": 1, "parameters": [0, 27, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 15, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 212, "indent": 1, "parameters": [0, 27, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["攻击结束"]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 315, "indent": 0, "parameters": [0, 4, 0, 0, 1408, false]}, {"code": 356, "indent": 0, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置-玩家 : 文本[\\c[21]EXP\\c[0]+\\c[18]1408] : 样式[1] : 弹道[1] : 持续时间[120]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": true, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 26, "y": 25}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": false, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 301, "indent": 0, "parameters": [0, 3, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [104, 104, 1, 0, 1]}, {"code": 111, "indent": 0, "parameters": [1, 104, 0, 5, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Monster5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 桐人君,看,那棵枯树发生了变化,可能就是莉兹说的怪物"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 10 嗯,打起精神上吧!"]}, {"code": 356, "indent": 1, "parameters": ["<PERSON><PERSON><PERSON>ab 27 ……"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 1, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 40, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": false, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜和猪田似乎没有做好战斗的准备，现在不是发起攻击的正确时机。"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": true, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 65}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 230, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [0, 1, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 15]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 6, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 212, "indent": 1, "parameters": [0, 6, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 9, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [7]}, {"code": 212, "indent": 1, "parameters": [0, 31, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 10, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [7]}, {"code": 212, "indent": 1, "parameters": [0, 26, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 11, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [7]}, {"code": 212, "indent": 1, "parameters": [0, 147, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 12, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 212, "indent": 1, "parameters": [0, 148, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 13, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 212, "indent": 1, "parameters": [0, 149, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 14, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 212, "indent": 1, "parameters": [0, 27, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 15, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 212, "indent": 1, "parameters": [0, 27, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["攻击结束"]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 315, "indent": 0, "parameters": [0, 4, 0, 0, 1408, false]}, {"code": 356, "indent": 0, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置-玩家 : 文本[\\c[21]EXP\\c[0]+\\c[18]1408] : 样式[1] : 弹道[1] : 持续时间[120]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": true, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 32, "y": 12}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": false, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 301, "indent": 0, "parameters": [0, 3, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [104, 104, 1, 0, 1]}, {"code": 111, "indent": 0, "parameters": [1, 104, 0, 5, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Monster5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 桐人君,看,那棵枯树发生了变化,可能就是莉兹说的怪物"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 10 嗯,打起精神上吧!"]}, {"code": 356, "indent": 1, "parameters": ["<PERSON><PERSON><PERSON>ab 27 ……"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 1, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 40, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": false, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜和猪田似乎没有做好战斗的准备，现在不是发起攻击的正确时机。"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": true, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 65}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 230, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [0, 1, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 15]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 6, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 212, "indent": 1, "parameters": [0, 6, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 9, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [7]}, {"code": 212, "indent": 1, "parameters": [0, 31, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 10, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [7]}, {"code": 212, "indent": 1, "parameters": [0, 26, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 11, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [7]}, {"code": 212, "indent": 1, "parameters": [0, 147, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 12, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 212, "indent": 1, "parameters": [0, 148, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 13, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 212, "indent": 1, "parameters": [0, 149, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 14, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 212, "indent": 1, "parameters": [0, 27, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 15, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 212, "indent": 1, "parameters": [0, 27, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["攻击结束"]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 315, "indent": 0, "parameters": [0, 4, 0, 0, 1408, false]}, {"code": 356, "indent": 0, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置-玩家 : 文本[\\c[21]EXP\\c[0]+\\c[18]1408] : 样式[1] : 弹道[1] : 持续时间[120]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 1, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": true, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 32, "y": 4}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": false, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 301, "indent": 0, "parameters": [0, 3, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [104, 104, 1, 0, 1]}, {"code": 111, "indent": 0, "parameters": [1, 104, 0, 5, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Monster5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 桐人君,看,那棵枯树发生了变化,可能就是莉兹说的怪物"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 10 嗯,打起精神上吧!"]}, {"code": 356, "indent": 1, "parameters": ["<PERSON><PERSON><PERSON>ab 27 ……"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 40, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": false, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜和猪田似乎没有做好战斗的准备，现在不是发起攻击的正确时机。"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": true, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 65}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 5}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": false, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 301, "indent": 0, "parameters": [0, 3, false, false]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [104, 104, 1, 0, 1]}, {"code": 111, "indent": 0, "parameters": [1, 104, 0, 5, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Monster5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 桐人君,看,那棵枯树发生了变化,可能就是莉兹说的怪物"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 10 嗯,打起精神上吧!"]}, {"code": 356, "indent": 1, "parameters": ["<PERSON><PERSON><PERSON>ab 27 ……"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 40, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": false, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜和猪田似乎没有做好战斗的准备，现在不是发起攻击的正确时机。"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 64}, "directionFix": true, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 65}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 230, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [0, 1, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 15]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 6, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 212, "indent": 1, "parameters": [0, 6, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 9, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [7]}, {"code": 212, "indent": 1, "parameters": [0, 31, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 10, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [7]}, {"code": 212, "indent": 1, "parameters": [0, 26, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 11, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [7]}, {"code": 212, "indent": 1, "parameters": [0, 147, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 12, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 212, "indent": 1, "parameters": [0, 148, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 13, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 212, "indent": 1, "parameters": [0, 149, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 14, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 212, "indent": 1, "parameters": [0, 27, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 15, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 12, "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 212, "indent": 1, "parameters": [0, 27, false]}, {"code": 119, "indent": 1, "parameters": ["攻击结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["攻击结束"]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 315, "indent": 0, "parameters": [0, 4, 0, 0, 1408, false]}, {"code": 356, "indent": 0, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置-玩家 : 文本[\\c[21]EXP\\c[0]+\\c[18]1408] : 样式[1] : 弹道[1] : 持续时间[120]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 1, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": true, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 19, "y": 20}, {"id": 10, "name": "茂凯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 104, "variableValid": false, "variableValue": 5}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$树精", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这棵树有点奇怪……"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 104, "variableValid": true, "variableValue": 5}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$树精", "direction": 2, "pattern": 2, "characterIndex": 0}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 132, "indent": 0, "parameters": [{"name": "ISAo_05 The Evil Sacrifice Archenemies", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 91, 10, 9, 8, 2]}, {"code": 356, "indent": 0, "parameters": ["NF_NPC削除 1"]}, {"code": 356, "indent": 0, "parameters": ["NF_NPC削除 2"]}, {"code": 301, "indent": 0, "parameters": [0, 2, false, false]}, {"code": 117, "indent": 0, "parameters": [13]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 122, "indent": 0, "parameters": [39, 39, 1, 0, 2]}, {"code": 122, "indent": 0, "parameters": [33, 33, 1, 0, 5]}, {"code": 122, "indent": 0, "parameters": [31, 31, 1, 0, 20]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 102], 30, false]}, {"code": 356, "indent": 0, "parameters": ["infotext : 11 : 1 : 590 : 140 : 28 : 150 : 0 : 1 : 0 : 亚  丝  娜  信  息  更  新"]}, {"code": 356, "indent": 0, "parameters": ["addLog 亚丝娜的经验似乎发生了一些变化..."]}, {"code": 132, "indent": 0, "parameters": [{"name": "B - THE MARS LIGHT", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 65]}, {"code": 213, "indent": 0, "parameters": [11, 6, false]}, {"code": 213, "indent": 0, "parameters": [-1, 9, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这个掉落的树皮就是莉兹要求的那个素材吧。"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Item1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac获得了\\c[103]茂凯的皮革\\c[103]!"]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这次多亏有亚丝娜的净化能力,不然这boss还挺难对付的，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac难怪莉兹说单人的话对付这个确实会比较麻烦,那个技能"]}, {"code": 401, "indent": 0, "parameters": ["\\dac有点防不胜防。"]}, {"code": 213, "indent": 0, "parameters": [11, 9, true]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 0, "parameters": [1, "FOG明日奈转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这..这毕竟是我的职责嘛,而且让桐人君陷入异常状态那么久"]}, {"code": 401, "indent": 0, "parameters": ["\\dac才进行了净化，抱歉。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac说起这个,我在陷入那个莫名其妙的噩梦前好像听到亚丝娜"]}, {"code": 401, "indent": 0, "parameters": ["\\dac在说猪田怎么了,是因为这个原因吗?"]}, {"code": 231, "indent": 0, "parameters": [1, "FOG明日奈掩饰", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯....因为那个猪田君似乎也受到了那个状态影响，陷入了"]}, {"code": 401, "indent": 0, "parameters": ["\\dac奇怪的状态.那个为了制服他所以耽误了不少时间才导致帮桐人君"]}, {"code": 401, "indent": 0, "parameters": ["\\dac解除状态比较晚。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac原来是这样,这也不是亚丝娜你的错,战斗本来就有各种情况,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那个，我好像也没看见猪田，他没事吧?"]}, {"code": 213, "indent": 0, "parameters": [11, 11, true]}, {"code": 231, "indent": 0, "parameters": [1, "FOG明日奈掩饰", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac因为猪田君中了那个状态之后，情...情况一直很不对劲，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac一直阻扰我施法，所..所以我没办法就...."]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac额，就怎么了?"]}, {"code": 231, "indent": 0, "parameters": [1, "FOG明日奈岔开", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那个实在没办法,所以我就想用木杖把猪田君给敲晕..."]}, {"code": 401, "indent": 0, "parameters": ["\\dac但是没考虑到他因为那个特殊技能基础能力值太低了，好像不"]}, {"code": 401, "indent": 0, "parameters": ["\\dac小心把他血条给敲空了，现在他的尸体就躺我旁边的树林里..."]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac被亚丝娜你一棍子敲死了.....嘛.嘛.毕竟也算是意外，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田应该也能理解的，能用复活术之类的复活吗?"]}, {"code": 213, "indent": 0, "parameters": [11, 7, true]}, {"code": 231, "indent": 0, "parameters": [1, "FOG明日奈苦笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac好像因为是我本人击杀的,所以好像复活术之类的根本不起效果，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不过我想回到城里的话，应该就会自动复活才对。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这样,嘛，反正我们还有莉兹的信物,也不用就这样带着猪田"]}, {"code": 401, "indent": 0, "parameters": ["\\dac的尸体的回去，直接使用传送回去莉兹的店吧，这样猪田应该也能"]}, {"code": 401, "indent": 0, "parameters": ["\\dac恢复才对。"]}, {"code": 231, "indent": 0, "parameters": [1, "FOG明日奈苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈战斗立绘.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯,好像也只有这样了，那就拜托你了，桐人君。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 212, "indent": 0, "parameters": [-1, 133, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 43, "parameters": [1], "indent": null}, {"code": 41, "parameters": ["!Flame", 6], "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [20], "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 43, "parameters": [1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["!Flame", 6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [20], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 43, "parameters": [1], "indent": null}, {"code": 41, "parameters": ["!Flame", 6], "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [20], "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 43, "parameters": [1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["!Flame", 6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [20], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [12, {"list": [{"code": 43, "parameters": [1], "indent": null}, {"code": 41, "parameters": ["!Flame", 6], "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [20], "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 43, "parameters": [1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["!Flame", 6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [20], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 180, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 44, "parameters": [{"name": "Up3", "volume": 90, "pitch": 120, "pan": 0}], "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "Up3", "volume": 90, "pitch": 120, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 44, "parameters": [{"name": "Up3", "volume": 90, "pitch": 120, "pan": 0}], "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "Up3", "volume": 90, "pitch": 120, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [12, {"list": [{"code": 44, "parameters": [{"name": "Up3", "volume": 90, "pitch": 120, "pan": 0}], "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "Up3", "volume": 90, "pitch": 120, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 43, "parameters": [0], "indent": 0}, {"code": 41, "parameters": ["桐人", 0], "indent": null}, {"code": 40, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 43, "parameters": [0], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 66]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 201, "indent": 0, "parameters": [0, 73, 15, 8, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 40, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 104, "variableValid": true, "variableValue": 5}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$树精", "direction": 2, "pattern": 2, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜和猪田似乎没有做好战斗的准备，现在不是发起攻击的正确时机。"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 40, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 104, "variableValid": true, "variableValue": 5}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$树精", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": false, "switch1Id": 40, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$树精", "direction": 2, "pattern": 2, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$树精", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 8}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 65}, "directionFix": false, "image": {"tileId": 0, "characterName": "亚丝娜", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 12}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 65}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪田倒地图", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 12}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 4}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 在攻略茂凯吗？单挑？ "]}, {"code": 118, "indent": 0, "parameters": ["y判定"]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, 18, 1]}, {"code": 111, "indent": 0, "parameters": [1, 198, 0, 10, 0]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([91, 18, 'B'], true);"]}, {"code": 212, "indent": 1, "parameters": [18, 101, false]}, {"code": 213, "indent": 1, "parameters": [18, 12, false]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([91, 18, 'A'], true);"]}, {"code": 213, "indent": 1, "parameters": [17, 3, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["y判定"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是进入噩梦状态了吧！那人似乎并没有清除debuff的提前准备…… "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不带队友没有保命道具，我也是服了。算了，先帮他脱困再说。 "]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 29, "parameters": [6], "indent": 0}, {"code": 14, "parameters": [-4, 1], "indent": null}, {"code": 37, "indent": null}, {"code": 7, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [-4, 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 7, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [17, 1, false]}, {"code": 212, "indent": 0, "parameters": [10, 26, false]}, {"code": 213, "indent": 0, "parameters": [10, 1, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 7, "indent": null}, {"code": 14, "parameters": [2, 0], "indent": null}, {"code": 16, "indent": null}, {"code": 5, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 7, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [2, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 5, "indent": null}]}, {"code": 212, "indent": 0, "parameters": [10, 27, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 5, "indent": 0}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 5, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 事件[10] : 立即终止动作"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([91, 10, 'B'], true);"]}, {"code": 315, "indent": 0, "parameters": [0, 4, 0, 0, 5000, false]}, {"code": 356, "indent": 0, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置-玩家 : 文本[\\c[21]EXP\\c[0]+\\c[18]5000] : 样式[1] : 弹道[1] : 持续时间[120]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [17, 3, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 茂凯第一阶段过了以后，只要没被噩梦控制，击败它还是很轻松的嘛！"]}, {"code": 213, "indent": 0, "parameters": [17, 8, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 毫无准备单挑茂凯……这人怎么想的？似乎一时半会儿醒不来了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 先把他送回城，送去旅店…算了，就丢公会里吧。"]}, {"code": 213, "indent": 0, "parameters": [17, 8, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">举起花盆 : 强制举起事件 : 18"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [17, 4, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [163, 163, 0]}, {"code": 356, "indent": 0, "parameters": [">举起花盆 : 强制销毁运输的事件"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [1], "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 72, 14, 15, 4, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 163, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 173}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 19, "y": 8}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 4, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 65, 21, 38, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 0, "y": 24}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [113, 113, 0, 3, 3, 4, 0]}, {"code": 111, "indent": 0, "parameters": [1, 113, 1, 195, 5]}, {"code": 122, "indent": 1, "parameters": [195, 195, 0, 3, 3, 4, 0]}, {"code": 122, "indent": 1, "parameters": [113, 113, 0, 3, 3, 4, 0]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置-玩家 : 文本[\\c[21]LevelUP] : 样式[1] : 弹道[1] : 持续时间[120]"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置-玩家 : 文本[Lv\\v[195]] : 样式[1] : 弹道[1] : 持续时间[120]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 1, "y": 15}, {"id": 17, "name": "莉法", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "莉法FOG(2)", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 163, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 173}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 4}, {"id": 18, "name": "蛇岛", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": true, "image": {"tileId": 0, "characterName": "蛇岛FOG", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 212, "indent": 0, "parameters": [10, 1, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 4, "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 5, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 171}, "directionFix": true, "image": {"tileId": 0, "characterName": "蛇岛FOG", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 4, "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 5, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 171}, "directionFix": true, "image": {"tileId": 0, "characterName": "蛇岛受伤", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [0, 10, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [0, 7, true]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 163, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 173}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 10}, {"id": 19, "name": "EV019", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">持续动作 : 事件[10] : 呼吸效果 : 持续时间[无限] : 周期[45] : 呼吸幅度[1]"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 173}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 4}, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]}