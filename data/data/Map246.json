{"autoplayBgm": false, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 20, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "!旅馆一楼-白天", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 26, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 768, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 768, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 768, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 768, 768, 768, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 16, 16, 16, 16, 16, 16, 0, 0, 18, 18, 18, 18, 18, 18, 0, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 16, 16, 16, 16, 16, 16, 0, 0, 18, 18, 18, 18, 18, 18, 0, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 15, 16, 15, 15, 15, 16, 0, 0, 17, 18, 17, 17, 17, 18, 0, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 15, 16, 15, 15, 15, 16, 0, 0, 17, 18, 17, 17, 17, 18, 0, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 15, 15, 15, 15, 15, 15, 0, 0, 17, 17, 17, 17, 17, 17, 0, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 15, 15, 15, 15, 15, 15, 0, 0, 17, 17, 17, 17, 17, 17, 0, 50, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 5, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 284, "indent": 0, "parameters": ["!旅馆一楼-白天", false, false, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["LAYER 246 2 旅馆一楼-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 246 3 旅馆一楼-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 284, "indent": 0, "parameters": ["!旅馆一楼-夜晚", false, false, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["LAYER 246 2 旅馆一楼-夜晚光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 246 3 旅馆一楼-夜晚阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 298, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 121, "indent": 0, "parameters": [298, 298, 1]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 18, "y": 3}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Door11", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 7, "y": 11}, {"id": 3, "name": "EV003房间2门", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Door11", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 12, "y": 11}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "People1", "direction": 6, "pattern": 1, "characterIndex": 5}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<旅店女老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这个时间的话应该不是来住宿的吧，如果是找东西的"]}, {"code": 401, "indent": 0, "parameters": ["\\dac话目前所有客房都是空置的，请随意查看。"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 13}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 202, 14, 12, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 214, 14, 12, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 8, "y": 16}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 202, 14, 12, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 214, 14, 12, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 9, "y": 16}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 202, 14, 12, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 214, 14, 12, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 7, "y": 16}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 513}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 284, "indent": 0, "parameters": ["!旅馆一楼-夜晚", false, false, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["LAYER 246 2 旅馆一楼-夜晚光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 246 3 旅馆一楼-夜晚阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 129, "indent": 0, "parameters": [80, 0, false]}, {"code": 129, "indent": 0, "parameters": [81, 1, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 41, "parameters": ["亚丝娜", 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[10] : 像素偏移[0,-50] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [10, 0, 12, 7, 2]}, {"code": 203, "indent": 0, "parameters": [11, 0, 13, 6, 4]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [10, 8, true]}, {"code": 213, "indent": 0, "parameters": [11, 4, true]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 15 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐，我带着便携式洗浴用品哦……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac让我来我帮你擦个澡，说不定你就会好了，嘿嘿嘿~"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[11] : 像素偏移[-18,0] : 时间[60]"]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [10, 1, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 514]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 241, "indent": 0, "parameters": [{"name": "crime_Encryption Key", "volume": 15, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.restore(180);"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[10] : 像素偏移[0,0] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [11, 12, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[11] : 像素偏移[0,0] : 时间[15]"]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [10, 8, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [10, 7, true]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈不适", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这里…这里是哪里……？\\|"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我……我…我的头好痛……感觉…好混乱……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦哦！学姐，你好啦！你刚才可能受了刺激，眼睛直愣愣的，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac跟你说话也没反应。所以我就把你带到旅馆来休息了……"]}, {"code": 213, "indent": 0, "parameters": [11, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac因为比据点近一些嘛~"]}, {"code": 213, "indent": 0, "parameters": [10, 2, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈欲言又止", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac什么……？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 117, "indent": 0, "parameters": [123]}, {"code": 117, "indent": 0, "parameters": [131]}, {"code": 213, "indent": 0, "parameters": [10, 9, false]}, {"code": 231, "indent": 0, "parameters": [2, "fog亚丝娜惊讶脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊…！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我记起来了……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [10, 8, true]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈失望脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac谢谢你，猪田君……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [10, 7, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈失落脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唉……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [11, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}\\c[8]嘿嘿嘿嘿~"]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐，你不要生桐人学长的气。像他这种帅哥，偶尔沾花惹草也是正常的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac再说了，英雄难过美人关嘛！"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [6, 11, 8]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈掩饰", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我哪有资格生他的气呢……？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac都是我的错……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯……"]}, {"code": 213, "indent": 0, "parameters": [11, 1, false]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊？！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac什么什么？！"]}, {"code": 213, "indent": 0, "parameters": [10, 8, false]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈沉默脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](我原以为桐人君和我性爱时表现不佳,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac只是因为SAO中困了两年的后遗症……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈凛然脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](但刚才他和那个贱婊…金发妓女做爱时激烈的样子，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac完全就像另一个人。)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈心事脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](是我太保守了吗？因为我不好意思尝试那些离谱的玩法。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac之前拒绝过桐人几次后，现在他连提都不会跟我提了……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [11, 8, true]}, {"code": 213, "indent": 0, "parameters": [10, 1, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈迟疑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](还是说……我给了他太多的压力？！)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈失落脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](他明明是想做VR设备的研发，为了得到母亲的认可，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在却委曲求全地在市场部受累……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [11, 2, true]}, {"code": 213, "indent": 0, "parameters": [10, 7, false]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](虽然现在大部分事情都在向好的方向发展……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但都是建立在桐人君的牺牲之上！我作为女友，又给过他什么支持呢？)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈沉默脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](相反……我还一度误会了他、冷落了他、伤害了他。)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](从之前猪田说的情况来推断，桐人君开始去那种地方，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac去做那种事情，应该就是在他压力最大的时候。)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [11, 7, true]}, {"code": 213, "indent": 0, "parameters": [10, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈欲言又止", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](他能维持住坚强、勇敢、执着，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac应该就用的是这种特殊的解压方式吧……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](我又怎么能去苛求他？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac又有什么资格责怪他？)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [11, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐，你还好吧？你别吓我！"]}, {"code": 213, "indent": 0, "parameters": [10, 8, true]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没事的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac只是在反省自己这段时间的过失。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [11, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你的过失？怎么是你的过失？可是可是可是……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这怎么是学姐的错呢？明明是桐人学长他自甘堕落……"]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 213, "indent": 0, "parameters": [10, 5, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈批评", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不要这么说他！！！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [10, 6, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈叹气", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唉……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[10] : 像素偏移[24,0] : 时间[30]"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 41, "parameters": ["猪头游戏服", 0], "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(11).requestBalloon(6);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["猪头游戏服", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(11).requestBalloon(6);"], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[10] : 像素偏移[0,0] : 时间[30]"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 45, "parameters": ["$gameMap.event(11).setPriorityType(1);"], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(11).setPriorityType(1);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [10, 7, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈无奈", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac对不起，猪田君，我有点累……想一个人静静……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac先下线休息了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 事件[10] : 纵向挤扁 : 时间[60] : 纵向比例[1.5]"]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐，那个说好的奖励……"]}, {"code": 213, "indent": 0, "parameters": [11, 7, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 249, "indent": 0, "parameters": [{"name": "Inn", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 515]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 129, "indent": 0, "parameters": [4, 0, false]}, {"code": 129, "indent": 0, "parameters": [80, 1, false]}, {"code": 201, "indent": 0, "parameters": [0, 99, 2, 13, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 513}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 85"]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 着色滤镜 : 关闭滤镜"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 80"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 75"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 70"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 65"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 60"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 55"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 50"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 45"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 40"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 35"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 30"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 25"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 20"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 15"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 10"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 5"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">地图滤镜 : 模糊滤镜 : 关闭滤镜"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 513}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 3, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 212, "indent": 0, "parameters": [11, 171, false]}, {"code": 356, "indent": 0, "parameters": [">多帧行走图 : 事件[11] : 固定帧 : 1"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 27 噗咿!!!"]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 32, "indent": null}, {"code": 13, "indent": 0}, {"code": 16, "indent": null}, {"code": 41, "parameters": ["猪田倒地图", 0], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(11).setPriorityType(0);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 32, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["猪田倒地图", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(11).setPriorityType(0);"], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">多帧行走图 : 事件[11] : 解除固定帧"]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 515}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 672}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 246 2 旅馆一楼-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 246 3 旅馆一楼-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 203, "indent": 0, "parameters": [19, 0, 7, 6, 4]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [19, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "Dungeon3", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [19, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 玩家 : 像素偏移[-9,0] : 时间[30]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 13, "indent": null}, {"code": 34, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 等一下，先等一下！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 听我说……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 上次事态紧急，我也乱了方寸。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 后来我想过，其实我可以自己把体液装在容器里给你呀。"]}, {"code": 213, "indent": 0, "parameters": [19, 8, true]}, {"code": 122, "indent": 0, "parameters": [131, 131, 0, 0, 21]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG诗乃傲娇", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [167]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 自以为聪明。HSVA-03是专门研制用于侵犯和控制女性的紧要，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么会那么简单就被破解？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[19] : 像素偏移[-21,0] : 时间[30]"]}, {"code": 205, "indent": 0, "parameters": [19, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG诗乃得意", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [167]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 只要不是通过双方身体接触取得的体液，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就不会被判定有效。"]}, {"code": 111, "indent": 0, "parameters": [0, 403, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 而且这次口水已经不行了，必须是精液。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呃……"]}, {"code": 213, "indent": 0, "parameters": [19, 4, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG诗乃挖苦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [167]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 知道你为难，纯情大帅哥。但为了救人性命，又能怎么办呢？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 脱裤子吧。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 122, "indent": 0, "parameters": [134, 134, 0, 0, 50]}, {"code": 102, "indent": 0, "parameters": [["\\dac 同意（\\c[28]变态度\\c[0]:\\c[18]\\v[134]\\c[0]） ", "\\dac 犹豫"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "\\dac 同意（\\c[28]变态度\\c[0]:\\c[18]\\v[134]\\c[0]） "]}, {"code": 111, "indent": 1, "parameters": [1, 74, 1, 134, 4]}, {"code": 225, "indent": 2, "parameters": [5, 9, 15, false]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 0, 68], 15, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[28]变态度\\c[101]不足"]}, {"code": 119, "indent": 2, "parameters": ["非变态"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 250, "indent": 2, "parameters": [{"name": "Poison", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 2, "parameters": [[68, 255, 68, 170], 60, true]}, {"code": 111, "indent": 2, "parameters": [0, 415, 1]}, {"code": 121, "indent": 3, "parameters": [415, 415, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [0, 432, 1]}, {"code": 121, "indent": 3, "parameters": [432, 432, 0]}, {"code": 122, "indent": 3, "parameters": [161, 161, 1, 0, 1]}, {"code": 122, "indent": 3, "parameters": [74, 74, 1, 0, 3]}, {"code": 356, "indent": 3, "parameters": ["addLog 变态度↑\\c[28]1"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 好吧……"]}, {"code": 213, "indent": 2, "parameters": [19, 3, true]}, {"code": 231, "indent": 2, "parameters": [2, "FOG诗乃开心", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 2, "parameters": [167]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 这才像样嘛！"]}, {"code": 235, "indent": 2, "parameters": [2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\dac 犹豫"]}, {"code": 118, "indent": 1, "parameters": ["非变态"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但是……"]}, {"code": 213, "indent": 1, "parameters": [19, 5, true]}, {"code": 231, "indent": 1, "parameters": [2, "FOG诗乃训斥", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [167]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 唉，真是个纠结的家伙。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 还是得我自己来！"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 玩家 : 像素偏移[0,0] : 时间[1]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "bob_cloth1", "volume": 85, "pitch": 120, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 673]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 673}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 111, "indent": 0, "parameters": [0, 415, 1]}, {"code": 108, "indent": 1, "parameters": ["手交台词"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃榨精玩弄龟头]"]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 20, "pitch": 50, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["乳交台词"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[9]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃榨精乳交]"]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 40, "pitch": 60, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么样啊，桐人？这样舒服吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这不是为了解毒吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我的感觉不重要好吧……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 开玩笑，不舒服怎么让你射精？我还怎么解毒。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你，必须回答。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊……舒，舒服……"]}, {"code": 111, "indent": 0, "parameters": [0, 415, 1]}, {"code": 108, "indent": 1, "parameters": ["手交台词"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 把肉棒放在我的胸部中间摩擦的话，就会变得舒服哦。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 想不想试一试？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你在胡说什么？！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我怎么会想那种事！"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["乳交台词"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这个呢，把肉棒放在胸部中间，然后这样摩擦，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 叫做乳交哦，学到了吧？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你……！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 别瞧不起人！乳交我是知道的！亚丝娜也给我做过！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗨！我干嘛跟你说这些……！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊~ 啊……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呵呵，所以，你是知道自己…正在和…女友的闺蜜，乳交。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 有没有很兴奋啊……？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 无…！无可奉告！"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呵呵，闷骚。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我应该不是第一个这么说你的吧？"]}, {"code": 111, "indent": 0, "parameters": [0, 415, 1]}, {"code": 108, "indent": 1, "parameters": ["手交台词"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 无可奉告……"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 30, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃榨精手交]"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [42], "indent": null}, {"code": 44, "parameters": [{"name": "パイズリ・手コキ(１回のみ)", "volume": 35, "pitch": 70, "pan": 0}], "indent": null}, {"code": 15, "parameters": [42], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [42], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 44, "parameters": [{"name": "パイズリ・手コキ(１回のみ)", "volume": 35, "pitch": 70, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [42], "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哈啊啊…啊……"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["乳交台词"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不要再说这些有的没的了……"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真是越来越觉得你可爱了~"]}, {"code": 111, "indent": 0, "parameters": [0, 403, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 上次说过要帮你锻炼持久度的……"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呐，是不是虽然觉得舒服，但并不是那么想射？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诶诶…？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那个……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 今天我选了这种低刺激度的方式，控制着节奏，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还用各种乱七八糟的话题来分散你的注意力，效果不错吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 算是吧……哈啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但完全没必要！诗乃，算我拜托你，快点结束吧！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你知道你刚才的话，从另一个角度理解是什么意思吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那就是……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac “诗乃酱，求求你，让我更加舒服吧~”"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 㗅！才不是……！"]}, {"code": 111, "indent": 0, "parameters": [0, 415, 1]}, {"code": 108, "indent": 1, "parameters": ["手交台词"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嘻嘻，欲盖弥彰~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 那我就满足你吧~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊啊，唔……哈啊啊……"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["乳交台词"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 30, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[诗乃榨精乳交追加]"]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 40, "pitch": 80, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不用跟我客气，就如你所愿吧！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呀啊啊……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 真是可爱的声音呢……\\i[90]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 诗，诗乃，咕嗯……！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 稍微增加一点压力就这样了，我的胸部就这么舒服吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 舒…！舒服…哈啊啊啊啊……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我还可以让桐人的变态肉棒变得更舒服哦……？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不行…慢点……啊啊啊……"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 一会要快，又一会要慢的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我才不听咧~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好刺激…！啊呃……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 色色的汁水已经从肉棒顶端流出来了呢~\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊，诗乃……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 再这样刺激下去的话……我……真的会坏掉的……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呼呼，桐人要射出来了吗……？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没，没错……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那就这样……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 射~ 出~ 来~ 吧~\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯啊……诗乃……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊啊……！"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就用桐人的精液，射满我的嘴巴……"]}, {"code": 123, "indent": 0, "parameters": ["C", 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诗乃:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咻噜，啾噜，啾噜噜……嗯，啾咕……"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [6, 0, 8]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 908, 0]}, {"code": 121, "indent": 1, "parameters": [908, 908, 1]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 1, 82]}, {"code": 111, "indent": 1, "parameters": [4, 4, 0]}, {"code": 129, "indent": 2, "parameters": [1, 0, false]}, {"code": 129, "indent": 2, "parameters": [4, 1, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 83, 24, 13, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 674]}, {"code": 201, "indent": 1, "parameters": [0, 305, 21, 32, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 673}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[诗乃榨精含入]"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 230, "indent": 0, "parameters": [48]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精-02", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["C", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 674}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 19, "y": 3}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 211, 20, 13, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 211, 20, 13, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 18, "y": 10}, {"id": 10, "name": "EV010亚丝娜", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 44, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "亚丝娜", "direction": 2, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 0}, {"id": 11, "name": "EV011猪田", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 43, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头游戏服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 1}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 4}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 5}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 8}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 7}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 513}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.target(12,2.1,300);"]}, {"code": 355, "indent": 0, "parameters": ["var waitFrames = Math.floor(Math.random() * 60) + 60;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(waitFrames);"]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.target(13,2,300);"]}, {"code": 355, "indent": 0, "parameters": ["var waitFrames = Math.floor(Math.random() * 60) + 60;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(waitFrames);"]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.target(14,2.1,300);"]}, {"code": 355, "indent": 0, "parameters": ["var waitFrames = Math.floor(Math.random() * 60) + 60;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(waitFrames);"]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.target(15,2,300);"]}, {"code": 355, "indent": 0, "parameters": ["var waitFrames = Math.floor(Math.random() * 60) + 60;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 514}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 19, "y": 2}, {"id": 17, "name": "EV017", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 269, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[28]"]}, {"code": 284, "indent": 0, "parameters": ["!旅馆一楼-白天", false, false, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["LAYER 246 2 旅馆一楼-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 246 3 旅馆一楼-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [10, 0, 14, 6, 2]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [11, 0, 12, 5, 8]}, {"code": 203, "indent": 0, "parameters": [18, 0, 14, 8, 0]}, {"code": 205, "indent": 0, "parameters": [18, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[11] : 像素偏移[0,16] : 时间[1]"]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 45, "parameters": ["$gameMap.event(11).requestBalloon(10);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(11).requestBalloon(10);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": 0}, {"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(10).setPriorityType(0);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(10).setPriorityType(0);"], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 15, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, false]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 事件[10] : 纵向冒出 : 时间[60] : 纵向比例[1.5]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [10, 8, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈叹气", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](唉……几乎是一宿没睡。)"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG明日奈妥协", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](但总算是稍微收拾了一下心情……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](虽然还是不知道怎么在现实中面对桐人君。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac给他留个言，等他上线以后好好聊一下吧……)"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": 0}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [10, 1, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 4"]}, {"code": 231, "indent": 0, "parameters": [4, "FOG明日奈惊讶", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [4, 0, 0, 0, 310, 20, 100, 100, 255, 0, 10, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{嗯！！！\\}"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{这里是……？！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 235, "indent": 0, "parameters": [4]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 213, "indent": 0, "parameters": [11, 7, false]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{烦死了！大周末的，吵什么吵？！"]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [11, 1, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[11] : 像素偏移[0,0] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 14, "parameters": [1, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [1, 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 1, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊！是学姐啊！你这么早就上线啦！"]}, {"code": 213, "indent": 0, "parameters": [11, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac是来给我奖励的吗？！"]}, {"code": 213, "indent": 0, "parameters": [10, 7, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈严肃", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac才不是！我一上线就在这里了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac也不知道为什么登录地点会变更……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [11, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这个啊……昨天开房的时候，我用了个情侣价的折扣，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac附带的效果就是旅馆房间成了我俩的临时据点。"]}, {"code": 213, "indent": 0, "parameters": [10, 6, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac原来是你捣的鬼，还以为又出什么BUG了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [11, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐，来都来了，就顺便把昨天的欠账结一下嘛~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac为了你的奖励，我昨晚都忍着没有自慰啊！"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈批评", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田君，别闹了好吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你觉得我现在有那个心情吗？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "A", 1]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可是……"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [10, 8, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈凛然", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我先回去了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [11, 5, false]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 213, "indent": 0, "parameters": [10, 9, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈回应", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac旅馆作为临时据点的设定怎么取消？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac在基本设定里面……"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈失望", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac基本设定……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 16, "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "系统_提示_5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [18, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [10, 8, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [10, 1, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈惊讶", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac诶？！桐人君居然在线？"]}, {"code": 213, "indent": 0, "parameters": [10, 7, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG明日奈失落", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac而且……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还在地下街……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [11, 9, true]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐，我觉得桐人学长这会儿肯定已经操完……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac肯定办完事情了，我觉得你可以直接跟他联系看看。"]}, {"code": 205, "indent": 0, "parameters": [18, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [10, 8, true]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 41, "parameters": ["亚丝娜", 4], "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你如果觉得我在这里不方便的话，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我现在就回避！"]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 2]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 213, "indent": 0, "parameters": [10, 6, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": 0}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈担忧", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哎，不用……"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [10, 8, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG明日奈叹气", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](猪田君不干那种事的时候倒是很会看气氛……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac算了现在不是说这种事的时候。)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈心事", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](要打给桐人吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可是……)"]}, {"code": 213, "indent": 0, "parameters": [10, 7, true]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG明日奈凛然", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](振作点啊我！昨晚不是已经决定好了吗？一定要包容淡定。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在选择逃避的话，以后还怎么去面对他，怎么在他面前保持平和？)"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈沉默", 0, 0, 310, 20, 100, 100, 255, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 0, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](没事的没事的……只是通过而已……不要紧张……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac语气一定要温柔……)"]}, {"code": 213, "indent": 0, "parameters": [10, 8, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG明日奈叹气", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呼……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "LNSM_SE08_Sense8", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 250, "indent": 0, "parameters": [{"name": "LNSM_SE08_Sense8", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [75]}, {"code": 213, "indent": 0, "parameters": [10, 8, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "LNSM_SE08_Sense8", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "koukaongen_022", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 213, "indent": 0, "parameters": [10, 1, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈担忧2", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 10, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](没开视频……)"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG明日奈凛然", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 10, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](现在不是关注这种事情的时候！)"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈微笑", 0, 0, 310, 20, 100, 100, 255, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 0, 0, 10, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君，你今天这么早就上线啦~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊，算是吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜，你也上线的很早啊。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 213, "indent": 0, "parameters": [10, 4, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯……因为昨天你一直没有回来嘛， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac本来说了要陪人家的呀~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呃……亚丝娜，你没事吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac怎么声音语调都怪怪的？"]}, {"code": 213, "indent": 0, "parameters": [10, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac克莱因:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}桐人啊，快点吧！ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}知道了，别催！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](克莱因！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac果然是他带桐人君去的！不靠谱的大人！)"]}, {"code": 213, "indent": 0, "parameters": [10, 9, true]}, {"code": 111, "indent": 0, "parameters": [0, 365, 1]}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 1, "parameters": [3, "FOG明日奈逃避", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](这么说来……那个叫爱丽丝的，在FOG卖春……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac他不会不知道。)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 1, "parameters": [3, "FOG明日奈妥协", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](他还在妓院外……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac看着女友卖春的样子自慰……)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [10, 6, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈凛然脸红", 0, 0, 310, 20, 100, 100, 255, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 0, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](这两口子也真是荒唐……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac咳咳……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac刚才喉咙有点不舒服，现在正常了吗？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac正常了。那个……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜，现在有什么事吗，我这边还有点事。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [10, 7, false]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 2, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还没处理完啊，昨天晚上的事…？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [10, 9, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈担忧", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你不会通宵没睡吧？！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呃……是这样。昨晚做任务上头，一不小心就搞晚了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜，你先说你的事吧！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈岔开", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac其实也没什么，就是想和你聊聊……"]}, {"code": 213, "indent": 0, "parameters": [10, 12, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG明日奈苦笑", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac另外我觉得…在地下街……那种地方，做任务……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你也要注意不能太辛苦……而且那里鱼龙混杂，一定要注意安全。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [10, 8, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [10, 7, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈沉思", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](桐人君怎么沉默了？是我说得太直接了点吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac明明决定好不干涉的，但却没能忍住……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac不耐烦的女声:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}桐人君，可以请你稍微快一点吗？我和阿克已经被晾在这里很久了。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [10, 1, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈严肃", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](女声？！是爱丽丝那个卖淫女警吧！)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}抱歉抱歉！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [10, 5, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈凛然", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](听上去桐人君好像被她指挥着？难道是有把柄被她捏住了？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这个坏女人……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜，不用担心。只是普通游戏而已，能有什么危险？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac又不是SAO那种死亡游戏，没什么好担心的！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [10, 1, false]}, {"code": 356, "indent": 0, "parameters": [""]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">多帧行走图 : 事件[10] : 固定帧 : 1"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[10] : 像素偏移[0,12] : 时间[15]"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈担忧2", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": [">多帧行走图 : 事件[10] : 解除固定帧"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac游戏…而已……？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [10, 8, true]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[10] : 像素偏移[0,0] : 时间[30]"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG明日奈沉默脸红", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](这样啊…桐人君也只是把FOG里发生的一切当作是游戏……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac就像我和猪田那样……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac是啊！而且我现在正和克莱因，还有他女朋友在一起，放心了吧！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我们还有点事情，等会再联系你！先挂了啊！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [10, 7, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 3]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](我已经完全搞不懂了……)"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": 0}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[10] : 像素偏移[12,0] : 时间[120]"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG明日奈欲言又止", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](如果说桐人君做这种事还只是为了释放压力，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可其他人怎么都那么开放啊…？)"]}, {"code": 213, "indent": 0, "parameters": [10, 6, true]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈凛然脸红", 0, 0, 310, 20, 100, 100, 255, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 0, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](克莱因交往了一个在FOG里卖淫的坏女人，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹和诗乃妹妹也都对游戏里的性爱一副无所谓的态度……)"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": 0}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG明日奈心事", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](就连最正派的艾基尔大叔，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac居然也在地下街里经营着灰色产业……)"]}, {"code": 213, "indent": 0, "parameters": [10, 9, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈无奈", 0, 0, 310, 20, 100, 100, 255, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 0, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](还是说……我的观念确实太迂腐守旧了吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那些妈妈一直灌输我的名门理念……是桐人君极度厌恶的吧？)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [10, 7, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 30, "pitch": 100, "pan": -40}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 17}, {"code": 15, "parameters": [3], "indent": null}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 42, "parameters": [125], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [150], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [175], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [200], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [225], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [255], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [125], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [150], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [175], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [200], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [225], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[11] : 像素偏移[12,0] : 时间[30]"]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [11, 3, false]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(10).requestBalloon(1);"], "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(10).requestBalloon(1);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐在苦恼吗？"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈担忧2", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田君？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [6, 11, 8]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我没有偷听哦，只是刚才房间里突然安静下来，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac然后就没半点动静……"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[11] : 像素偏移[12,16] : 时间[30]"]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我担心学姐想不开，所以情急之下就进来了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac就算学姐责怪，我也认了！"]}, {"code": 213, "indent": 0, "parameters": [10, 6, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac瞎说什么，我哪有那么容易想不开？"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[10] : 像素偏移[12,16] : 时间[30]"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG明日奈无奈", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}本来也不是什么大不了的事情……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [11, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}诶？！"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田君，我想休息一下。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你先回去吧。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [11, 7, false]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可是学姐，说好的奖励呢？"]}, {"code": 213, "indent": 0, "parameters": [10, 7, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](又在说奖励的事了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我是答应过，但现在哪里有心情给他……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [11, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我知道学姐现在没有心情，不用担心，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac全部交给我就好啦~"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[11] : 像素偏移[12,-24] : 时间[60]"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(10).requestBalloon(1);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(10).requestBalloon(1);"], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 4]}, {"code": 201, "indent": 0, "parameters": [0, 246, 13, 5, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 269, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[28]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[猪头亚丝娜强吻]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[11] : 像素偏移[12,-24] : 时间[60]"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "Kiss_20s", "volume": 15, "pitch": 110, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呜呜！！！呣…唔……！！！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](怎么亲上来了？！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这绝对不可以！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啾啾啾……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田…！呣……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你…唔…怎么能……唔……呣……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](呜……力气好大，没法推开……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯嗯~~~啾，嗯啾……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯…嗯嗯…啾……"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, false]}, {"code": 213, "indent": 0, "parameters": [11, 4, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[11] : 像素偏移[12,0] : 时间[60]"]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 15, "parameters": [60], "indent": 0}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [10, 11, false]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嘿嘿嘿…学姐的嘴巴，超～甜~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈啊，哈啊哈啊…哈啊……"]}, {"code": 213, "indent": 0, "parameters": [11, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac虽然开始有点抗拒，但后来也让我把舌头伸进去了呢~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac果然接吻很舒服不是嘛！"]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我们再来一次吧！"]}, {"code": 213, "indent": 0, "parameters": [10, 11, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[10] : 像素偏移[12,0] : 时间[60]"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不，不要再亲了，我们说好的……！"]}, {"code": 213, "indent": 0, "parameters": [11, 5, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac什么嘛！桐人学长在游戏里嫖妓你都觉得无所谓，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我们还有必要弄那些条条框框吗？来嘛！"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[11] : 像素偏移[12,-40] : 时间[12]"]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [9]}, {"code": 213, "indent": 0, "parameters": [10, 12, false]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[猪头亚丝娜舌吻]"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[10] : 像素偏移[12,16] : 时间[10]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[11] : 像素偏移[12,-24] : 时间[10]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 245, "indent": 0, "parameters": [{"name": "Honeydrop_kiss1", "volume": 25, "pitch": 110, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯嗯…！嗯呜…！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](又亲上了，舌头都伸了进来……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](明明不应该和他这样，但……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](只是被猪田用力抱住，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac全身就会涌入一种安心的感觉……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](好像渐渐没有那么悲伤了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这种感觉……还想要……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯嗯，嗯…啾………"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](嘴巴麻麻的，像是一种甜腻的触电感……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac身体也慢慢有了感觉，为什么会这样……)"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 25, "pitch": 110, "pan": 0}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 5]}, {"code": 201, "indent": 0, "parameters": [0, 246, 13, 5, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 269, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [10, 0, 13, 5, 2]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[10] : 像素偏移[12,16] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [11, 0, 13, 6, 8]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[11] : 像素偏移[12,-18] : 时间[1]"]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": 0}, {"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 33, "indent": null}, {"code": 45, "parameters": ["$gameMap.event(10).setPriorityType(0);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(10).setPriorityType(0);"], "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, false]}, {"code": 213, "indent": 0, "parameters": [10, 4, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [11, 4, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[11] : 像素偏移[12,16] : 时间[60]"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦哦，爽啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac和学姐舌吻真是太舒服了！"]}, {"code": 213, "indent": 0, "parameters": [10, 11, false]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 41, "parameters": ["亚丝娜", 4], "indent": 0}, {"code": 15, "parameters": [15], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可以了吧？这份奖励我觉得足够了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还不回去。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": 0}, {"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [11, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐在说什么啊！这怎么能算奖励完了呢？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哪有只吃前菜不吃主餐的道理？"]}, {"code": 213, "indent": 0, "parameters": [10, 12, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[10] : 像素偏移[-6,16] : 时间[60]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[11] : 像素偏移[-40,16] : 时间[45]"]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [11, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 15, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 6]}, {"code": 201, "indent": 0, "parameters": [0, 246, 13, 5, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 269, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [100, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[28]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[100] : 设置动画序列 : 动画序列[28]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[旅馆抠穴循环]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[100] : 等待动画序列加载完成"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "bensound-sexy", "volume": 15, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "あんな喘ぎ手マン", "volume": 25, "pitch": 90, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 handjob_medium 25 100 0"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唔~\\i[90] "]}, {"code": 401, "indent": 0, "parameters": ["\\dac不要…我现在不想做这样的事……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐嘴巴上说着不要不要，下面的水都流成小溪了~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac其实已经很想要了吧~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我…没有……只是正常生理反应……啊！\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac是是是！那至少也说明身体有这个需求嘛~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐就当作是按摩，好好享受吧！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac情绪低落的时候，最需要的就是放松了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈啊…啊…慢一点……哦~哦哦~~~\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐这个样子真是既风骚又可爱，哪个男人受得了啊？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但桐人学长却还跑到外面嫖妓，学姐知道是为什么吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯？嗯嗯~~~\\i[90] "]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我可以提供一点男性视角的解答哦~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐想不想知道？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊~\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈啊……唔…你……你告诉我……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐在床上总是放不开，对桐人学长那种喜欢冒险的人来说，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac肯定更喜欢会花火又没下限的妓女啦~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哼…！啊啊~\\i[90] "]}, {"code": 401, "indent": 0, "parameters": ["\\dac那个地方不行……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这个理由不用我说，学姐自己应该也很清楚。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但关键其实不在这里哦~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac什么……？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊~啊~啊啊~ \\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac真正最主要，是学姐给人的压迫感太强了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐人美心善，品学兼优，还是结城家的大小姐，要我说……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac就算是桐人学长这种英雄人物，也会在你面前感到自卑呢！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊~ \\i[90] 啊啊嗯……怎么会……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac而学姐呢，明明这么优秀，这么完美，无时无刻不再散发着圣母的光辉，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但却把全部的情感一股脑地都放到桐人学长的身上……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦~ \\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我……咿，啊啊嗯嗯……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac说起来，不愧是京子阿姨的女儿呢，你们这点也都好像！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯……？！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](我和妈妈很像？)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子阿姨就是太爱学姐你了，所以之前对学姐你特别严格要求，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你是不是也会觉得喘不过气？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我，我…我…没有要求过桐人……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯哈啊啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac虽然学姐不像阿姨那样，直接把要求说出口，但站在桐人学长的立场上想想，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你这么优秀的女友时时刻刻盯着他，压力能小吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac最近你们和京子阿姨的关系是不是就好很多了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac就是因为京子阿姨不再那么步步紧逼了，对不对？"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[旅馆抠穴事后]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[旅馆抠穴高潮]"]}, {"code": 108, "indent": 0, "parameters": ["高潮动作帧数为616"]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 handjob_fast 25 100 0"]}, {"code": 245, "indent": 0, "parameters": [{"name": "あんな喘ぎ手マン激しめ", "volume": 25, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [390]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [225]}, {"code": 245, "indent": 0, "parameters": [{"name": "呻吟", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦哦，又潮喷了~ 好壮观！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这游戏真是完美还原了学姐现实的体质诶！"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈啊…哈啊…我……哈啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不想给桐人压力的……"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但结果……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac把他逼到需要去游戏里的娼馆来发泄……"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我已经不知道该怎么办了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嘿嘿嘿~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac我知道怎么办……"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "あぁん！", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[旅馆正常位待机]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "吐息連結", "volume": 20, "pitch": 110, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 108, "indent": 0, "parameters": ["切阶段一插入前"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐，过度消极可是要不得的呀！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这也是会给伴侣压力的哦~ "]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不…不要，我才刚刚高潮，如果现在被插入……"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac来吧，我们一起来做快乐的事情，把烦恼都丢到一边吧！"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 108, "indent": 0, "parameters": ["切插入动画"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[旅馆正常位慢]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[旅馆正常位插入]"]}, {"code": 230, "indent": 0, "parameters": [160]}, {"code": 245, "indent": 0, "parameters": [{"name": "呻吟2", "volume": 20, "pitch": 110, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [140]}, {"code": 250, "indent": 0, "parameters": [{"name": "wtp_s_waterpiston11", "volume": 50, "pitch": 90, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "あえぎ2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [83]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 44, "parameters": [{"name": "wtp_s_waterpiston10", "volume": 15, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [80], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "wtp_s_waterpiston10", "volume": 15, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [80], "indent": null}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 245, "indent": 0, "parameters": [{"name": "夏梅10_SEX喘ぎ通常", "volume": 15, "pitch": 110, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac快停下……哈啊，嗯嗯……呼啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac很爽的对吧。和桐人学长做爱没有这样的感受对吧！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac才…不是……呼啊，啊啊啊嗯……嗯呀啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不要骗我了，其实学姐是欲求不满吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那是不可能的…不要胡说…… 嗯嗯~\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐还是不老实呢……"]}, {"code": 111, "indent": 0, "parameters": [0, 370, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac学姐之前不是问我怎么知道桐人学长鸡鸡的大小吗？"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac其实桐人学长有跟我交流过哦？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](什么？桐人怎么可能会和猪田君说这种事？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不可能！一定是骗人的！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac他说，在性方面，无论是大小，还是持久力，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac都感觉没办法满足学姐无底洞般的欲望，很苦恼呢！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你骗我……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呀啊……啊啊啊啊啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我才没有骗人！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人学长不仅尺寸不大，而且做爱时间一般都没超过两分钟，对吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人学长只会男上女下，根本不用别的体位和学姐做爱，是不是？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人学长每次秒射以后，都是倒头就睡，我没说错吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唔……！啊啊，呀啊啊！嗯唏咿咿~~~！！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac如果不是桐人学长告诉我这些，我又怎么知道的呢？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac而这些桐人学长的问题，其实都是学姐给他太多压力造成的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐，要不然你也把你那边的感受跟我说说呗，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我能更好地帮你改善和桐人学长的关系呢！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪、猪田……你，不要……不要再说了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呼啊啊，嗯啊啊啊啊啊！！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐真是的，总是不坦率，但我知道怎么让你诚实起来！"]}, {"code": 108, "indent": 0, "parameters": ["切阶段一加速"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[旅馆正常位快]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [4], "indent": null}, {"code": 44, "parameters": [{"name": "wtp_s_waterpiston10", "volume": 20, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [20], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "wtp_s_waterpiston10", "volume": 20, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [20], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "夏梅11_SEX喘ぎ激しめ", "volume": 15, "pitch": 110, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 wtp_l_waterpiston04 25 100 0"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯哈啊啊啊啊~~~~！！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嘿嘿，秘诀就是持续高速抽插！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但也不能太快，如果学姐失神了，就没办法听到问题了~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊啊，呀啊啊，不要这样，猪田君……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯哈啊啊啊~~~\\i[90]\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐和桐人学长的做爱有过高潮吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈啊…没有，一次也没有……啊啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](呜…明明不打算和任何人说的……为什么…)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac为什么没有高潮？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呼啊……大小都是其次…啊啊~\\i[90] 还是时间太短了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac根本…根本来不及……高潮…啊啊~~~\\i[90]\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](对猪田君的问题，嘴巴自己就回答了！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](虽然很羞耻，但是说出来的时候好舒服…"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这种感觉…好厉害……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人学长似乎也不喜欢前戏，觉得浪费时间，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac他一次都没有给学姐你舔过下面，对吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac是…嗯嗯……呼啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](明明不可以回答的，但是还想要……)"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 245, "indent": 0, "parameters": [{"name": "夏梅13_アナル喘ぎ激しめ", "volume": 15, "pitch": 120, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[旅馆正常位高潮]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 44, "parameters": [{"name": "wtp_s_waterpiston10", "volume": 25, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [24], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "wtp_s_waterpiston10", "volume": 25, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [24], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唔！！！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯~ 啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac去，去了……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这就高潮啦？哈哈哈！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac高潮时候…不可以…啊啊…这么激烈呀……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我要疯掉…了啊啊啊…！！！\\i[90]\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac和我做爱更舒服对吧！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐会经常想要我的大肉棒，对吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac想，想要……唔~唔嗯~\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不，不行……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呵呵呵呵~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐，该换你到上面喽~"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 45, "pitch": 75, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [20], "indent": null}, {"code": 44, "parameters": [{"name": "wtp_s_waterpiston01", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [20], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "wtp_s_waterpiston01", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "夏梅12_アナル喘ぎ通常", "volume": 20, "pitch": 120, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[旅馆骑乘位男快]"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐，这样是不是可以刺激到不同的位置，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac和刚才的感觉很不同吧~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不要再说了……哈啊啊……啊啊啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac为什么不说呢？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐难道不觉得做爱的时候说骚话会更爽吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没有…啊嗯……呀啊啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac来，说你最喜欢学弟的大鸡巴了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我！我怎么可能会说啊……那种话……"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[旅馆骑乘位待机]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 245, "indent": 0, "parameters": [{"name": "呻吟", "volume": 15, "pitch": 110, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac真的可以么？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不说的话，我就不动了哦？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac怎么这样……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac怎么怎样啊？学姐，我一直在努力让你舒服，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你说点鼓励的话都不行吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唉……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学弟的…学弟的……学弟的……"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不行！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我真的说不出口…！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac行~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那我们就暂时这样呗~"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唔……"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[旅馆骑乘位女扭腰]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 245, "indent": 0, "parameters": [{"name": "呻吟2", "volume": 50, "pitch": 110, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac喔嚯！学姐主动扭起屁股了耶~ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac果然是非常喜欢我的鸡巴呀！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯嗯！用行动回答也可以，完全可以！"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[100] : 播放简单状态元集合 : 集合[旅馆骑乘位女动一]"]}, {"code": 232, "indent": 0, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 10, false]}, {"code": 245, "indent": 0, "parameters": [{"name": "捂嘴娇喘", "volume": 25, "pitch": 110, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [40], "indent": null}, {"code": 44, "parameters": [{"name": "wtp_s_waterpiston01", "volume": 35, "pitch": 60, "pan": 0}], "indent": null}, {"code": 15, "parameters": [64], "indent": null}, {"code": 44, "parameters": [{"name": "wtp_s_waterpiston01", "volume": 35, "pitch": 60, "pan": 0}], "indent": 0}, {"code": 15, "parameters": [24], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [40], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "wtp_s_waterpiston01", "volume": 35, "pitch": 60, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [64], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "wtp_s_waterpiston01", "volume": 35, "pitch": 60, "pan": 0}], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [24], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不是的……是小穴擅自动了起来…这实在太奇怪了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊啊啊，呼啊啊啊……啊啊啊嗯……\\i[90]\\i[90]\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦哦哦！！！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac爽诶！！！"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac虽然女上位我们以前用过，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但学姐这样主动骑鸡巴还是第一次呢~ "]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac主动的学姐实在太可爱了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不给奖励不行啊！"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[旅馆骑乘位男超快]"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 232, "indent": 0, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 10, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 44, "parameters": [{"name": "wtp_s_waterpiston01", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "wtp_s_waterpiston01", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "娇喘7", "volume": 25, "pitch": 110, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac怎么样，和跟学长做的时候完全不一样吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯……啊啊啊~\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呼啊啊，哦吼……嗯咿咿咿……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac叫得真是浪啊~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac请不要说……哈啊啊~~~\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊啊啊啊嗯……呀啊啊……\\i[90]\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac比桐人学长的鸡巴大多了，爽多了是吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你底下的嘴可是这样说的哦？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊……嗯嗯~~\\i[90] "]}, {"code": 401, "indent": 0, "parameters": ["\\dac不，不行……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦哦，夹得好紧……看来你是真爽到了～"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac再继续下去，真的不行……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊啊啊，啊啊……要迷上这种感觉了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac承认之后就会轻松多了哦~ 回答我吧："]}, {"code": 401, "indent": 0, "parameters": ["\\dac是不是跟我做爱更爽啊？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那是绝对……不可能的……呼啊啊啊！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哼，今天一定要让你说实话！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊啊啊，呼啊啊……哦哦，哦吼哦哦……！"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac十分钟后……"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[旅馆骑乘位女动二]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [36], "indent": null}, {"code": 44, "parameters": [{"name": "wtp_s_waterpiston13", "volume": 35, "pitch": 70, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [36], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "wtp_s_waterpiston13", "volume": 35, "pitch": 70, "pan": 0}], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "娇喘14", "volume": 25, "pitch": 110, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac肉棒好厉害……啊……\\i[90] "]}, {"code": 401, "indent": 0, "parameters": ["\\dac好爽……啊啊嗯\\i[90]\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](说，说出来了……)"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](那么夸张的话，下流的话，被我好大声地，说出来了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但是……)"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](心跳得好厉害……这种心情…还是第一次……\\i[90] )"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](把你的本性彻底暴露出来吧，母猪老婆！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac把你超级淫乱的本性，全部暴露出来吧！)"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但不过哦，我要纠正一下你不恰当的用词……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不是肉棒，要说鸡巴！是谁的鸡巴让你爽的啊？"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学弟的鸡巴…！啊嗯~ \\i[90] 好爽！好厉害……啊啊~~~\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哪里爽啊？说呀？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学弟的鸡巴是怎么个厉害法儿？"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯呜……\\i[90]猪田的，粗大的…鸡巴…啊\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗞咕嗞咕地剐蹭着……学姐小穴里最想要的地方……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac插得好舒服~\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac小穴？是骚屄吧！而且不要说插，要说肏！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac来，说给我听~"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac只有猪田君的粗大鸡巴…能肏到…啊嗯嗯~\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac骚屄最爽的地方…哦啊~ \\i[90] 学姐…被肏得，好舒服…！\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐，你现在的表情超淫乱哦~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac羞人的表情全给我看光了也没关系吗？"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊……\\i[90]啊嗯嗯……可，可以的……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac想给……嗯……\\i[90]猪田你看……啊嗯……\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](我，好淫乱……但真的好爽……超级…刺激啊……\\i[90])"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](再…再多一点，下流的话……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还想再多说一点，再兴奋一点！\\i[90])"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac鸡…鸡巴，再多插我，再深一点……\\i[90]\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我还想要更舒服……\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这么喜欢做爱的话，就多多地、下流地叫出那些浪荡的声音啊！"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[旅馆骑乘位女动三]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [20], "indent": null}, {"code": 44, "parameters": [{"name": "wtp_s_waterpiston13", "volume": 35, "pitch": 70, "pan": 0}], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [20], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "wtp_s_waterpiston13", "volume": 35, "pitch": 70, "pan": 0}], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "娇喘16", "volume": 25, "pitch": 110, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊…啊嗯\\i[90]啊\\i[90]啊…啊，啊啊嗯\\i[90]\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac好爽…啊…\\i[90]好，好爽……\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没错没错！尽情释放欲望吧！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac只有这样，学姐才能在日常里平和地面对学长，才能不给学长压力！"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac所以，为了桐人学长，尽情地和我做爱吧！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac变成一个下流的，脑子坏掉的女人吧！"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](为了桐人，尽情地和猪田君做爱……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac成为…下流的，脑子坏掉的女人……\\i[90])"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac再…再多点……！再肏我咕啾咕啾的小骚穴……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还想变得更爽啊……！\\i[90] "]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac小穴的…淫液，喷了好多！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac用，用学弟粗大的肉棒…肏我……！\\i[90]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](好羞耻！好羞耻！好羞耻…！)"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](明明好羞人的…但是却舒服到大脑都不正常了……)"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈啊\\i[90]啊，啊嗯\\i[90]好爽…好爽，好爽\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田君的大鸡巴好爽……！\\i[90] )"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](好舒服啊……这样下去，和猪田的虚拟做爱，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我真的要上瘾了……\\i[90])"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](桐人君，我也有了释放欲望的方法……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这样就不会再给你压力了吧……)"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac又，又要，去了……"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac突然夹紧…？！我也……！！！"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[旅馆骑乘位中出]"]}, {"code": 230, "indent": 0, "parameters": [160]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "あえぎ9", "volume": 25, "pitch": 110, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精-01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 250, "indent": 0, "parameters": [{"name": "wtp_s_waterpiston13", "volume": 90, "pitch": 70, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 250, "indent": 0, "parameters": [{"name": "あえぎ8", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精-01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [112]}, {"code": 250, "indent": 0, "parameters": [{"name": "wtp_s_waterpiston13", "volume": 90, "pitch": 70, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [8]}, {"code": 250, "indent": 0, "parameters": [{"name": "いき4", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精-01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [112]}, {"code": 232, "indent": 0, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [300, 300, 1]}, {"code": 121, "indent": 0, "parameters": [269, 269, 1]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 0]}, {"code": 201, "indent": 0, "parameters": [0, 81, 17, 10, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 4, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 269, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 200, 0, 3, 0]}, {"code": 205, "indent": 1, "parameters": [11, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 200, 0, 2, 0]}, {"code": 205, "indent": 1, "parameters": [11, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 250, "indent": 1, "parameters": [{"name": "Open1", "volume": 45, "pitch": 100, "pan": -20}]}, {"code": 205, "indent": 1, "parameters": [3, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 1, "parameters": [11, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 212, "indent": 1, "parameters": [11, 45, false]}, {"code": 205, "indent": 1, "parameters": [11, {"list": [{"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [240], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [220], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [190], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [150], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [100], "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 42, "parameters": [240], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 42, "parameters": [220], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 42, "parameters": [190], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 42, "parameters": [150], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 42, "parameters": [100], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 1, "parameters": [3, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 205, "indent": 1, "parameters": [11, {"list": [{"code": 4, "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": 0}]}, {"code": 112, "indent": 1, "parameters": []}, {"code": 111, "indent": 2, "parameters": [6, 10, 8]}, {"code": 113, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [5]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 413, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [11, {"list": [{"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 200, 0, 1, 0]}, {"code": 356, "indent": 1, "parameters": [">行走图额外位置偏移量 : 事件[11] : 像素偏移[30,0] : 时间[15]"]}, {"code": 205, "indent": 1, "parameters": [11, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 213, "indent": 1, "parameters": [10, 7, false]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 356, "indent": 1, "parameters": [">行走图额外位置偏移量 : 事件[10] : 像素偏移[-18,0] : 时间[10]"]}, {"code": 205, "indent": 1, "parameters": [10, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 356, "indent": 1, "parameters": [">行走图额外位置偏移量 : 事件[11] : 像素偏移[0,0] : 时间[6]"]}, {"code": 205, "indent": 1, "parameters": [11, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 356, "indent": 1, "parameters": [">行走图额外位置偏移量 : 事件[10] : 像素偏移[0,0] : 时间[20]"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 205, "indent": 1, "parameters": [10, {"list": [{"code": 34, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null}]}, {"code": 213, "indent": 1, "parameters": [11, 5, false]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 20, "y": 4}, {"id": 18, "name": "EV018电子屏", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 495}, "directionFix": false, "image": {"tileId": 0, "characterName": "$悬浮屏幕", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,-24]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true}], "x": 25, "y": 2}, {"id": 19, "name": "EV019诗乃", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "诗乃fog像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 3}, null, null, null]}