{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "S - かおりとぬくもり", "pan": 0, "pitch": 100, "volume": 25}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 21, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 50, "width": 30, "data": [2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3859, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3861, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3821, 0, 0, 0, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3821, 0, 0, 0, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3821, 0, 0, 0, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3821, 0, 0, 0, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3821, 0, 0, 0, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3821, 0, 0, 0, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3821, 0, 0, 0, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3821, 0, 0, 0, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3865, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3861, 0, 0, 0, 0, 3859, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3857, 3863, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [91]}, {"code": 121, "indent": 0, "parameters": [21, 21, 1]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 135, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[5] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[7] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[8] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[9] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[10] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[11] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[12] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[13] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[15] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[17] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[18] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[19] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[20] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[21] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[22] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[24] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[25] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[26] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[27] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[28] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[29] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[30] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[31] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[32] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[33] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[34] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[35] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[36] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[37] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[38] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[39] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[40] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[41] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[42] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[43] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[44] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[45] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[46] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[47] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[48] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[49] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[50] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[51] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[52] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[53] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[54] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[55] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[56] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[57] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[58] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[59] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[60] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[61] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[62] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[63] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[64] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[65] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[66] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[67] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[68] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[69] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": ["particle set 黑暗气息 event:49"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 11, "y": 0}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 79, 15, 14, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 15, "y": 20}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜裸", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 亚丝娜"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 19}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "猪头裸", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 猪田"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 19}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜裸", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 5/31序章"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关5/31序章的回想,要回顾吗?(此回想非常长)"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [201, 201, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 5, 21, 22, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 241, "indent": 1, "parameters": [{"name": "Storm1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [1, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 108, "indent": 1, "parameters": ["阳台抚摸事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 あんな喘ぎ手マン 50 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[阳台骚扰]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不，不要这样，猪田君...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人，桐人，学姐老是拿学长来作为借口，明明刚刚就"]}, {"code": 401, "indent": 1, "parameters": ["\\dac用手帮我射精了，现在又拿前辈来当挡箭牌，太狡猾了，学姐。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac那明明是你.....唔....."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哼，又想逃避现实，把错误推给我嘛，如果学姐从一开始"]}, {"code": 401, "indent": 1, "parameters": ["\\dac就拒绝我的话，我也不会产生各种奢侈的幻想，但是既然学姐给了"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我希望，那只有今天也好，我也想正视自己的心意!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不...不是那样的...我只是....舌头...不要...舔..."]}, {"code": 401, "indent": 1, "parameters": ["\\dac快…停下来，声音要….呜…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac在学姐变得坦率之前我是不会停下的。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](怎，怎么会...力...力气完全使不上来..."]}, {"code": 401, "indent": 1, "parameters": ["\\dac在..在这么下去）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的2根手指在亚丝娜的蜜穴中把腔内的嫩肉搅动的天翻地覆，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac肆意刮弄，穴汁伴随着淫荡的水声止不住的往下流。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac明明都已经湿成这样了，学姐也早就忍不住了吧，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac只是一次而已，学姐，吸溜~学姐的脖子也好香啊~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不，不要!"]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [1, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac..你冷静一下，我..我去借用一下浴室..."]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 60, true]}, {"code": 108, "indent": 1, "parameters": ["厕所强上"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪..猪田君，你怎么进来的，我明明有锁上门...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我这里的浴室门锁早坏了，从外面稍微拉一下就自然打开了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不...不是那样的....我..我只是..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac真是不坦率啊，学姐，怎么样还是让我来让学姐变舒服吧？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](好..好近...猪田的汗臭味甚至都能清楚的闻到..."]}, {"code": 401, "indent": 1, "parameters": ["\\dac已经见过很多次的肉棒，在这种时候似乎更加显得黝黑壮硕，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac甚至能透过薄薄的避孕套看见赤红硕大的龟头，如果被这种东西插进里面...)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](不行..不行..我在想什么..从刚刚开始身体就好奇怪..."]}, {"code": 401, "indent": 1, "parameters": ["\\dac好热...好热....到底是怎么了...必须得....)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac你...你快出去...不然我...我要生气了...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哼。"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobHigh", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 あんな喘ぎ手マン激しめ 35 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[浴室前戏]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac真亏学姐在下面湿成这样的情况下，嘴巴上面还在硬撑着呢，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac看来还是用这种方式来沟通比较好，唔，学姐的敏感点，我记得应该是...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac咿~~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](这感觉...和刚刚自慰时完全不同..."]}, {"code": 401, "indent": 1, "parameters": ["\\dac这个男人精准地刺激着里面的敏感点..."]}, {"code": 401, "indent": 1, "parameters": ["\\dac这，这样下去的话..)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac好像发出了非常可爱的声音啊~学姐，学姐的身体我可是"]}, {"code": 401, "indent": 1, "parameters": ["\\dac已经了解的一清二楚，在稍微摩擦一下这里的话~你看果然就"]}, {"code": 401, "indent": 1, "parameters": ["\\dac牢牢吸住了我的手指不放不是嘛。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]虽然亚丝娜努力的把下半身往下缩，想逃避猪田的手指玩弄，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac但是猪田的2根手指就像附骨之趋，紧紧的跟随她的蜜穴行动，只是随便"]}, {"code": 401, "indent": 1, "parameters": ["\\dac的几下抠挖就能把她的蜜穴玩成汪洋，滑腻的淫水伴随这丢人声音不断"]}, {"code": 401, "indent": 1, "parameters": ["\\dac的往外流，滑过光滑的大腿，落到了光洁的浴室地板上。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不，不，不，不要.....啊，啊。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac说什么不要，明明乳头也已经涨的这么厉害了，我的下半身"]}, {"code": 401, "indent": 1, "parameters": ["\\dac涨的好痛，咕噜，我也忍不住了，学姐!"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [1, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]猪田低沉的说完之后，亚丝娜甚至还没反应过来，就被猪田按着"]}, {"code": 401, "indent": 1, "parameters": ["\\dac趴伏到了浴盆上，然后用像猪爪的双手扶起了亚丝娜白嫩的翘臀，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac身下赤红硕大的龟头也已经顶住亚丝娜阴户的蜜唇正在慢慢摩擦。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不..不要..求求你...猪田君..只有这个...不行..绝对不行.."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac学姐，现在说什么都已经晚了。"]}, {"code": 250, "indent": 1, "parameters": [{"name": "挿入する時の音3（勢いよく一気に入れる）", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac呼~~~~终于进来了...学姐的体内...能把我现实的处男给学姐"]}, {"code": 401, "indent": 1, "parameters": ["\\dac实在是太棒了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac骗...骗人..真的插进来了....不...不要...这种事情...呜...."]}, {"code": 401, "indent": 1, "parameters": ["\\dac...桐人君..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac好...好紧...和游戏里的压迫感完全不一样....明明还只插入了"]}, {"code": 401, "indent": 1, "parameters": ["\\dac一半左右，就感觉里面的肉壁挤压的让人受不了~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac才...才一半，怎..怎么会...不行，...不行..快拔出去..."]}, {"code": 401, "indent": 1, "parameters": ["\\dac这种..这种东西...我..我会坏掉的.....呜！！！！！！！！"]}, {"code": 250, "indent": 1, "parameters": [{"name": "挿入する時の音1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac噢~~~~终于全部进去了~~这就是学姐的小穴最深处~~不是虚拟"]}, {"code": 401, "indent": 1, "parameters": ["\\dac的，是实实在在的学姐，学姐的温度学姐的一切正从肉棒上面"]}, {"code": 401, "indent": 1, "parameters": ["\\dac传递过来，这种满足感，征服感~~从出生开始最高的瞬间!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac啊~~~快拔出去.....你现在拔出去我还是会原谅你的!"]}, {"code": 401, "indent": 1, "parameters": ["\\dac啊~~别动呀...啊!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](这是什么感觉?！只是插进去我就差点失去意识了...."]}, {"code": 401, "indent": 1, "parameters": ["\\dac这么深，从来没有过...不行，这样下去不行!）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac居然反应这么大，难道桐人学长，从来没有插进学姐你的最深处，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac那意思是我是第一次进入这里面的人，哈..哈哈，我才是第一个贯穿学姐"]}, {"code": 401, "indent": 1, "parameters": ["\\dac全部的男人，不过没事的学姐，我听说只要抽动几下适应了就会开始舒服了。"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[浴室后背位慢速]"]}, {"code": 245, "indent": 1, "parameters": [{"name": "pierrot_doubleinsert01_slow", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 Honeydrop_i_tickle 35 100 0"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac噢噢噢噢~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯….嗯啊…呜…."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac学姐里面好温暖，好柔软，但同时又夹得好紧?"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我要被融化了一样~~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac如果不是有游戏的经验，我恐怕真的坚持不了一分钟。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac啊~啊~停下...啊~~猪...猪田...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac太，太舒服了!和游戏里完全不一样，这种真实的感觉，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac这..这不是做梦吧，我正在和那个SAO里闪光的亚丝娜，学院"]}, {"code": 401, "indent": 1, "parameters": ["\\dac里大家的偶像做爱，我的肉棒真的正在那个亚丝娜的小穴里驰骋，哈..哈哈。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac呜...呜呜...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac学姐也差不多已经适应了吧，是不是越来越舒服了？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac那么我差不多也要开始加速了哦！学姐。"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 245, "indent": 1, "parameters": [{"name": "pierrot_piston01_fast", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 Honeydrop_i_ticklehard 35 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[浴室后背位快速]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac啊啊~~~嗯~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](骗人，我居然这么快就要去了....)"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[0]嗯啊~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac呼....呼...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac学姐这个肉搏的褶皱好舒服啊…腰根本停不下来~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯啊~~啊~~~哈啊~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac每次插到最深，学姐的小穴都会收缩挤压，这也太爽了！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我..能够遇到学姐真是太好了，学姐是唯一真心对我好的人，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我喜欢学姐~~，我这悲剧一样的人生，只有学姐能让我看见唯一的一丝曙光，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac明日奈，我要你!你是我的!噢~噢~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不....唔...别..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](腰自己动起来了.怎么会?！不可以啊....)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嘶哈…学姐，你的腰扭的好厉害，我快到极限了…实在太舒服了..."]}, {"code": 401, "indent": 1, "parameters": ["\\dac学姐...可以吧...我直接射也没关系吧...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯~~~~等…..啊~~~不...不要....求..求你..啊~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]\\{(不可以！不可以！不可以！)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯啊，我知道学姐嗨没有满足，不用担心，我很快就能恢复的!"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我来了学姐!小穴深处的第一发!"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[浴室后背位射精]"]}, {"code": 230, "indent": 1, "parameters": [20]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 250, "indent": 1, "parameters": [{"name": "射精_膣内01", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 250, "indent": 1, "parameters": [{"name": "レン絶頂3カット", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [125]}, {"code": 231, "indent": 1, "parameters": [1, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac赶紧去床上吧~学姐♥♥"]}, {"code": 108, "indent": 1, "parameters": ["床上事件"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac学姐，我一定会好好利用从网上学到的知识让你舒服的~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac........"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]房间里弥漫着汗水和荷尔蒙的味道....旁边的垃圾箱里"]}, {"code": 401, "indent": 1, "parameters": ["\\dac的最上面还显眼的放着之前帮猪田君手交之后擦拭着的卫生纸。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]身体，甚至是灵魂，都好像已经不属于自己，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac身体的快感却也无时不刻在提醒我在桐人君一墙之隔的地方"]}, {"code": 401, "indent": 1, "parameters": ["\\dac被桐人君之外的男人占有的事实。但是更让我自我厌恶的是，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac对之后会发生的事情有了不可否认的期待感。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]真恶心....我是个这么淫荡的女人......"]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 245, "indent": 1, "parameters": [{"name": "handjob_slow", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 あんな喘ぎ4 35 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[猪田床上对面位]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哈啊....哈啊...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac........"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac怎么样学姐，这个姿势我从网上看到的，说是很受情侣的欢迎来着。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac........"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac学姐不要这个表情嘛，是不是不喜欢这个姿势?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不!一定是我还不够努力，没让学姐舒服起来。"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]我应该怪猪田君吗?如果当初没有和他相遇...."]}, {"code": 401, "indent": 1, "parameters": ["\\dac但是我又能怪人家什么呢?是他逼我了吗？他给我下药了吗?"]}, {"code": 401, "indent": 1, "parameters": ["\\dac还是他刻意的用阴谋诡计来接近我呢?相反他从流氓手中救下了我，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac...他只是单纯的喜欢着我，顺从着他的本心来亲近我而已。"]}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストングチュ音多め（低速）", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 Amebo_aka_59b 35 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[猪田床上后入位]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac呼，学姐，这个姿势舒服吗....哈哈，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac这样还可以看到学姐的小菊花，简直让我根本停不下来。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哈啊….哈呜…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac学姐也慢慢开始发出有趣的声音了，说明我的努力也正在"]}, {"code": 401, "indent": 1, "parameters": ["\\dac让学姐你变舒服吧~我太开心了!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac啊….嗯…嗯啊…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac噢噢~刚才学姐这个声音太可爱了!说实话每次上学从后面"]}, {"code": 401, "indent": 1, "parameters": ["\\dac看着和学长并排一起走着随着裙摆露出的一点点白皙的大腿我下面就会"]}, {"code": 401, "indent": 1, "parameters": ["\\dac硬的不行，一直幻想着能像这天一样，能摸着学姐的翘臀和大腿狠狠的后入学姐~太爽了!"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]我该怪桐人君吗？怪他太迟钝了?怪他今天不在家?"]}, {"code": 401, "indent": 1, "parameters": ["\\dac怪他最开始没有果断点让我不要在理猪田君......."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]可明明桐人君当初就提醒我注意帮助猪田君"]}, {"code": 401, "indent": 1, "parameters": ["\\dac的边界，我为什么没好好听他的话呢?"]}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストンタイプB（グチュ音多め）（中くらい）", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 床震2 35 100 0"]}, {"code": 356, "indent": 1, "parameters": ["play_bgs3 Honeydrop_o_aegisilent 50 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[猪田床上正常位]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac感觉有点抓到窍门了，嘿嘿。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯啊~~嗯~~呜~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac而且学姐的腰也开始配合我的动作了~真是太色情了，学姐"]}, {"code": 401, "indent": 1, "parameters": ["\\dac的身体~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac….呜"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Phone", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac手机?我看看...是学长打来的...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac!!!"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Phone", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac噢~~突然一下居然比最开始收缩的还厉害，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac果然这个还是接听会比较有意思吧~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac等，等一下，不行 …"]}, {"code": 250, "indent": 1, "parameters": [{"name": "Phone", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac噢~~和明日奈学姐合为一体的感觉太棒了~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}停...停一下...."]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]不行!无论如何，不能让桐人君知道!"]}, {"code": 108, "indent": 1, "parameters": ["中途电话"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[通讯中正常位]"]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[101]啊！太好了，接通了，亚丝娜，听得见吗？"]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac…嗯，桐人君…听得见，这，这么着急有…什么事，事吗？"]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[101]最近你一直感冒在家里，可能不知道今天台风的事，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac现在下了很强的暴雨，你说今天有事来着，我有点担心你。"]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac啧。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac这..这样啊，我…我没事的。"]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[101]那就好，对了，你现在在哪里啊？"]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯…我现在在…啊♥ ♥~我爸爸那…那边的别墅里……呜…唔…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac其实是在学长你的隔壁啦~嘿嘿。"]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[101]原来是去了叔叔那边，那我就放心了"]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯....."]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[101]不过没事吧，总感觉你今天说话有点怪怪的？"]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac太，太舒服了学姐~~我要忍不住了~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac有…有吗…那可能，可能是我现在在做瑜伽的关系…这.."]}, {"code": 401, "indent": 1, "parameters": ["\\dac这样同时和桐..桐人君你聊天有点吃力.."]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[101]原来是这样，不过，这种天气做瑜伽嘛？"]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac噢~~~"]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[101]亚丝娜你旁边有谁在吗？我怎么好像听到了其他人的声音？"]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac那….那是妈妈的声音啦…她，她好像对我分心打，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac打电话..啊~好像很生气….对不起，桐人君，我先挂…挂嗯~~啊~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac受...受不了了，要射了~~~"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs3"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]在和其他人做爱的同时，不可饶恕的和桐人君在电话中撒谎了..."]}, {"code": 401, "indent": 1, "parameters": ["\\dac甚至连罪恶感都还没来得及体会就继续沉迷在欲望之中了...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]呵，明日奈，你真够绿茶的。"]}, {"code": 108, "indent": 1, "parameters": ["最后冲刺"]}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストングチュ音多め（低速）", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 Honeydrop_o_aegihard3 35 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[猪田床上配种位]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嘶哈...嘶哈..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯啊~~~呜噢~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac听说这是情侣间最能增加感情的姿势，因为能让对面完全的"]}, {"code": 401, "indent": 1, "parameters": ["\\dac感觉到自己的全部。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯呜~~噢噢~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac学姐你也这么觉得对吧，我能清楚感觉到每一次顶到最深处之后"]}, {"code": 401, "indent": 1, "parameters": ["\\dac学姐身体传来的雀跃感，这种感觉桐人学长应该没有给你带来过吧！学姐！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac好香，学姐你好香啊，我不会允许任何人夺走你，我们才"]}, {"code": 401, "indent": 1, "parameters": ["\\dac是两情相悦的，我要让你永远记住我的存在!噢！！！！！"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]不行..这种感觉是什么，要...要变奇怪了，好烫，猪田君的"]}, {"code": 401, "indent": 1, "parameters": ["\\dac肉棒在我体内最深处肆无忌惮的释放着自己的温度，刺激着我全部的敏感点，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac明明那都是桐人君从来没有能踏足过的地方..."]}, {"code": 401, "indent": 1, "parameters": ["\\dac我的小穴就像专门来给这个肉棒插一样...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]如果桐人君能够更持久一些，如果能更深入一些，该有多好啊..."]}, {"code": 401, "indent": 1, "parameters": ["\\dac下半身传来的快感已经快淹没我的理智，对不起，桐人君。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac就让我放纵这一回吧。之后，我一定好好做给了结..."]}, {"code": 401, "indent": 1, "parameters": ["\\dac一定...."]}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストングチュ音多め（中速）", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 Honeydrop_i_ticklehard 35 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[猪田床上配种位2]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac噢噢~学姐的脚居然在主动在缠着我的腰....太棒了，太棒了，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac学姐也终于认可我了嘛!我太感动了，太感动了！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac啊~~噢啊~~别说了~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac这个，我想要的就是这个!必须在学姐心里牢牢印下我猪田"]}, {"code": 401, "indent": 1, "parameters": ["\\dac的烙印才行。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哈啊~~~哈啊~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac呜，我忍不住了~~~学姐，再抱紧一点，射了---!"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[猪田床上配种位射精]"]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 Honeydrop_i_acme1 35 100 0"]}, {"code": 230, "indent": 1, "parameters": [80]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [80]}, {"code": 250, "indent": 1, "parameters": [{"name": "挿入03", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 250, "indent": 1, "parameters": [{"name": "射精音・中出し（モンスターや触手など人外の射精音）激しく注ぎ込まれる１０秒①", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [200]}, {"code": 251, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 242, "indent": 1, "parameters": [1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]猪田君的性欲就像是无底洞一样...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]在那之后的事情，我已经记不清了...但是..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]很舒服......令人难以置信程度的...而且那些快感或许现在"]}, {"code": 401, "indent": 1, "parameters": ["\\dac也深深隐藏在我这淫荡的身体里。"]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 17}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 79, 15, 14, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 14, "y": 20}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜裸", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/2奇怪的梦(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/2奇怪的梦(一)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [202, 202, 0]}, {"code": 121, "indent": 1, "parameters": [27, 27, 1]}, {"code": 121, "indent": 1, "parameters": [26, 26, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 231, "indent": 1, "parameters": [1, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 201, "indent": 1, "parameters": [0, 78, 25, 5, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 245, "indent": 2, "parameters": [{"name": "ピストン乾き中速１９", "volume": 10, "pitch": 100, "pan": -100}]}, {"code": 356, "indent": 2, "parameters": ["play_bgs2 淫乱普通に喘ぐ2 10 100 100"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 2, "parameters": ["\\dac上方好像有什么声音..."]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 231, "indent": 2, "parameters": [2, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 2, "parameters": [1, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac???"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\}啊~♥♥~啊~好舒服~再深一点~在深一点~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 2, "parameters": ["\\dac这个声音....好熟悉。"]}, {"code": 241, "indent": 2, "parameters": [{"name": "脈打つ音単体（中くらい）５回", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 231, "indent": 2, "parameters": [3, "噩梦1遮罩", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[2] : 设置动画序列 : 动画序列[5]"]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[2] : 播放简单状态元集合 : 集合[梦1]"]}, {"code": 356, "indent": 2, "parameters": ["SETMASK 2 3"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚..亚丝娜！?"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 2, "parameters": ["\\dac嗯~嗯~对，对不起啊~桐人君，好像把你吵醒了~啊~啊~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚..亚丝娜，你这是在做什么?"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 2, "parameters": ["\\dac是在做非常舒服的事~♥♥因为太舒服了，没忍住叫了出来，"]}, {"code": 401, "indent": 2, "parameters": ["\\dac好像把桐人君给吵醒了，对不起~因为这个真的实在是啊~啊~♥♥"]}, {"code": 401, "indent": 2, "parameters": ["\\dac太舒服了~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 2, "parameters": ["\\dac为什么亚丝娜没有穿衣服!？"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 2, "parameters": ["\\dac做这么舒服的事情~肯定不会穿衣服啊，桐，桐人君真是"]}, {"code": 401, "indent": 2, "parameters": ["\\dac个笨蛋呢~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 2, "parameters": ["\\dac舒服的事情?难，难道是？不，不可能..这种事情..."]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac猪田"]}, {"code": 401, "indent": 2, "parameters": ["\\dac学姐,我的大肉棒肏的你舒服吗~"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 2, "parameters": ["\\dac超~超舒服，和桐人君的完全不一样~，大肉棒顶到最里面的感觉~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 2, "parameters": ["\\dac不，不可能，亚丝娜不会做这种事情的，这不是真的。"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac猪田"]}, {"code": 401, "indent": 2, "parameters": ["\\dac明明我都肏的学姐淫水流个不停滴到前辈的脸上了，"]}, {"code": 401, "indent": 2, "parameters": ["\\dac前辈似乎都还不接受现实呢，你女朋友的小骚屄早就已经"]}, {"code": 401, "indent": 2, "parameters": ["\\dac被我的大肉棒给肏习惯了~"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 2, "parameters": ["\\dac你这家伙是谁，你刚刚又说了什么!？"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 2, "parameters": ["\\dac看不见..."]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 2, "parameters": ["\\dac这边也看不见...到底是谁..给我出来!"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac猪田"]}, {"code": 401, "indent": 2, "parameters": ["\\dac真可怜啊，对自己女朋友一无所知，满足不了自己的女朋友，"]}, {"code": 401, "indent": 2, "parameters": ["\\dac连自己女朋友在自己住的隔壁被人肏了一个晚上也不知道"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人君~真可怜~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 2, "parameters": ["\\dac住嘴，不要在说了，不要在说了!"]}, {"code": 224, "indent": 2, "parameters": [[255, 255, 255, 170], 60, false]}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 250, "indent": 2, "parameters": [{"name": "Chime1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": ["PushGab 30 没错吧~你看看他的那里~"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 22 哇，果然和你说的一样呵呵呵♥只是看着我们，就已经勃起了~"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 30 他喜欢就让他多看点吧~"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 22 嗯,你要好好看清楚哦，桐人君~这可是你的小孩子肉棒永远也做不到的~真正的，大人的性爱♥♥"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 13 不，不要!不要在说了，亚丝娜！"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 22 又盯着胸部看了嘛，毕竟桐人君很喜欢胸部呢"]}, {"code": 356, "indent": 2, "parameters": ["<PERSON><PERSON><PERSON><PERSON> 13 呜...."]}, {"code": 356, "indent": 2, "parameters": ["PushGab 22 又大又软,可是选择已经不能让你摸了哦~这已经是他的东西了~♥♥"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 13 为什么....会这样.."]}, {"code": 356, "indent": 2, "parameters": ["PushGab 22 哈啊~~~嗯~~~"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 13 亚丝娜,这一定是在开玩笑吧?"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 22 抱歉，桐人君，我很喜欢你~但是做爱的话嘛~♥♥"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 22 果然还是得和他这样的男人♥"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 30 哈哈哈，说的好，明日奈"]}, {"code": 356, "indent": 2, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 102, "indent": 2, "parameters": [["给我闭嘴！"], 0, 0, 1, 1]}, {"code": 402, "indent": 2, "parameters": [0, "给我闭嘴！"]}, {"code": 356, "indent": 3, "parameters": ["ForceGabClear"]}, {"code": 241, "indent": 3, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 3, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 3, "parameters": ["stop_bgs2"]}, {"code": 356, "indent": 3, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 221, "indent": 3, "parameters": []}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 235, "indent": 3, "parameters": [3]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 235, "indent": 3, "parameters": [1]}, {"code": 222, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [1, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 245, "indent": 2, "parameters": [{"name": "ピストン乾き中速１９", "volume": 10, "pitch": 100, "pan": -100}]}, {"code": 356, "indent": 2, "parameters": ["play_bgs2 淫乱普通に喘ぐ2 10 100 100"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 2, "parameters": ["\\dac上方好像有什么声音..."]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 231, "indent": 2, "parameters": [2, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, true]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[2] : 设置动画序列 : 动画序列[5]"]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[2] : 播放简单状态元集合 : 集合[梦1]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac???"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\}啊~♥♥~啊~好舒服~再深一点~在深一点~"]}, {"code": 241, "indent": 2, "parameters": [{"name": "脈打つ音単体（中くらい）５回", "volume": 70, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚..亚丝娜！?"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 2, "parameters": ["\\dac嗯~嗯~对，对不起啊~桐人君，好像把你吵醒了~啊~啊~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚..亚丝娜，你这是在做什么?"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 2, "parameters": ["\\dac是在做非常舒服的事~♥♥因为太舒服了，没忍住叫了出来，"]}, {"code": 401, "indent": 2, "parameters": ["\\dac好像把桐人君给吵醒了，对不起~因为这个真的实在是啊~啊~♥♥"]}, {"code": 401, "indent": 2, "parameters": ["\\dac太舒服了~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 2, "parameters": ["\\dac为什么亚丝娜没有穿衣服!？"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 2, "parameters": ["\\dac做这么舒服的事情~肯定不会穿衣服啊，桐，桐人君真是"]}, {"code": 401, "indent": 2, "parameters": ["\\dac个笨蛋呢~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 2, "parameters": ["\\dac舒服的事情?难，难道是？不，不可能..这种事情..."]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac猪田"]}, {"code": 401, "indent": 2, "parameters": ["\\dac学姐,我的大肉棒肏的你舒服吗~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 2, "parameters": ["\\dac超~超舒服，和桐人君的完全不一样~，大肉棒顶到最里面的感觉~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 2, "parameters": ["\\dac不，不可能，亚丝娜不会做这种事情的，这不是真的。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac猪田"]}, {"code": 401, "indent": 2, "parameters": ["\\dac明明我都肏的学姐淫水流个不停滴到前辈的脸上了，"]}, {"code": 401, "indent": 2, "parameters": ["\\dac前辈似乎都还不接受现实呢，你女朋友的小骚屄早就已经"]}, {"code": 401, "indent": 2, "parameters": ["\\dac被我的大肉棒给肏习惯了~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac猪田"]}, {"code": 401, "indent": 2, "parameters": ["\\dac真可怜啊，对自己女朋友一无所知，满足不了自己的女朋友，"]}, {"code": 401, "indent": 2, "parameters": ["\\dac连自己女朋友在自己住的隔壁被人肏了一个晚上也不知道"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜"]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人君~真可怜~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人"]}, {"code": 401, "indent": 2, "parameters": ["\\dac住嘴，不要在说了，不要在说了!"]}, {"code": 224, "indent": 2, "parameters": [[255, 255, 255, 170], 60, false]}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 250, "indent": 2, "parameters": [{"name": "Chime1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": ["PushGab 29 没错吧~你看看他的那里~"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 25 哇，果然和你说的一样呵呵呵♥只是看着我们，就已经勃起了~"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 29 他喜欢就让他多看点吧~"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 25 嗯,你要好好看清楚哦，桐人君~这可是你的小孩子肉棒永远也做不到的~真正的，大人的性爱♥♥"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 13 不，不要!不要在说了，亚丝娜！"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 25 又盯着胸部看了嘛，毕竟桐人君很喜欢胸部呢"]}, {"code": 356, "indent": 2, "parameters": ["<PERSON><PERSON><PERSON><PERSON> 13 呜...."]}, {"code": 356, "indent": 2, "parameters": ["PushGab 25 又大又软,可择已经决定不能让你摸了哦~这已经是他的东西了~♥♥"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 13 为什么....会这样.."]}, {"code": 356, "indent": 2, "parameters": ["PushGab 25 哈啊~~~嗯~~~"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 13 亚丝娜,这一定是在开玩笑吧?"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 25 抱歉，桐人君，我很喜欢你~但是做爱的话嘛~♥♥"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 25 果然还是得和他这样的男人♥"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 29 哈哈哈，说的好，明日奈"]}, {"code": 356, "indent": 2, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 102, "indent": 2, "parameters": [["给我闭嘴！"], 0, 0, 1, 1]}, {"code": 402, "indent": 2, "parameters": [0, "给我闭嘴！"]}, {"code": 356, "indent": 3, "parameters": ["ForceGabClear"]}, {"code": 241, "indent": 3, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 3, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 3, "parameters": ["stop_bgs2"]}, {"code": 356, "indent": 3, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 221, "indent": 3, "parameters": []}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 235, "indent": 3, "parameters": [1]}, {"code": 222, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 17}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "猪头裸", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/2据点偷窥"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 5, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 5 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 5]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 5 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "猪头裸", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/2据点偷窥"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/2猪田据点偷窥的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([88, 21, 'A'], false);"]}, {"code": 121, "indent": 1, "parameters": [205, 205, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [28, 28, 1]}, {"code": 121, "indent": 1, "parameters": [29, 29, 1]}, {"code": 121, "indent": 1, "parameters": [30, 30, 1]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 201, "indent": 1, "parameters": [0, 88, 26, 13, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([88, 21, 'A'], true);"]}, {"code": 121, "indent": 1, "parameters": [205, 205, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [28, 28, 1]}, {"code": 121, "indent": 1, "parameters": [29, 29, 1]}, {"code": 121, "indent": 1, "parameters": [30, 30, 1]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 201, "indent": 1, "parameters": [0, 88, 26, 13, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "猪头裸", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/2据点偷窥"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/2猪田据点偷窥的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([88, 21, 'A'], false);"]}, {"code": 121, "indent": 1, "parameters": [205, 205, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [28, 28, 1]}, {"code": 121, "indent": 1, "parameters": [29, 29, 1]}, {"code": 121, "indent": 1, "parameters": [30, 30, 1]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 201, "indent": 1, "parameters": [0, 88, 26, 13, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([88, 21, 'A'], true);"]}, {"code": 121, "indent": 1, "parameters": [205, 205, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [28, 28, 1]}, {"code": 121, "indent": 1, "parameters": [29, 29, 1]}, {"code": 121, "indent": 1, "parameters": [30, 30, 1]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 201, "indent": 1, "parameters": [0, 88, 26, 13, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 17}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "猪头裸", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/1砸电脑"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 15, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 15 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 15]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 15 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "猪头裸", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/1砸电脑"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/1猪田砸电脑的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [206, 206, 0]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 201, "indent": 1, "parameters": [0, 113, 15, 8, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "【BGM】陵辱系Hシーン【儀式っぽい感じ】", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "喘ぎ声とピストン　弱", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 着色滤镜 : 黑白 : 255 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[无套骑乘位]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 噢~太爽了!和戴套的感觉完全不一样~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊~啊~♥♥~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 居然不知不觉准备的避孕套这么快就用完了~学姐接下来不用带套也没关系吧!"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 唔唔~\\}不~\\{唔啊，\\{可以啊~~~♥♥~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 学姐，你自己说可以的啊，嘿嘿~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊啊啊啊~♥~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 学姐的腰居然在自己扭动着耶~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呣唔~~♥♥~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 脑子已经一片混乱了啊~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊~~哦哦~~~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但快感还是让学姐你本能地配合我呢~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯嗯~~唔~~啊~~~~♥"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 果然我们才是最适配的两个人~"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 着色滤镜 : 黑白 : 0 : 1"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 246, "indent": 1, "parameters": [1]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "猪头裸", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/1砸电脑"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/1猪田砸电脑的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [206, 206, 0]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 201, "indent": 1, "parameters": [0, 113, 15, 8, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "【BGM】陵辱系Hシーン【儀式っぽい感じ】", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "喘ぎ声とピストン　弱", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 着色滤镜 : 黑白 : 255 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[无套骑乘位]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 噢~太爽了!和戴套的感觉完全不一样~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊~啊~♥♥~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 居然不知不觉准备的避孕套这么快就用完了~学姐接下来不用带套也没关系吧!"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 唔唔~\\}不~\\{唔啊，\\{可以啊~~~♥♥~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 学姐，你自己说可以的啊，嘿嘿~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊啊啊啊~♥~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 学姐的腰居然在自己扭动着耶~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呣唔~~♥♥~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 脑子已经一片混乱了啊~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊~~哦哦~~~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但快感还是让学姐你本能地配合我呢~"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯嗯~~唔~~啊~~~~♥"]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 果然我们才是最适配的两个人~"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 着色滤镜 : 黑白 : 0 : 1"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 246, "indent": 1, "parameters": [1]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 17}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 66}, "directionFix": true, "image": {"tileId": 0, "characterName": "sho<PERSON>hu", "direction": 4, "pattern": 1, "characterIndex": 4}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/5茂凯"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/5茂凯boss战(桐人视角)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 10, true]}, {"code": 231, "indent": 1, "parameters": [2, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[2] : 设置动画序列 : 动画序列[12]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[2] : 等待动画序列加载完成"]}, {"code": 129, "indent": 1, "parameters": [8, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 283, "indent": 1, "parameters": ["Grassland", "Forest"]}, {"code": 132, "indent": 1, "parameters": [{"name": "ISAo_05 The Evil Sacrifice Archenemies", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 301, "indent": 1, "parameters": [0, 2, false, false]}, {"code": 132, "indent": 1, "parameters": [{"name": "B - THE MARS LIGHT", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 129, "indent": 1, "parameters": [1, 0, false]}, {"code": 129, "indent": 1, "parameters": [8, 1, false]}, {"code": 121, "indent": 1, "parameters": [207, 207, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 231, "indent": 1, "parameters": [1, "茂凯战斗背景回想", 0, 0, 0, 0, 150, 150, 255, 0]}, {"code": 231, "indent": 1, "parameters": [98, "茂凯", 0, 0, 120, 100, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[98] : 反复缩放 : 持续时间[无限] : 周期[120] : 最小缩放[1.00] : 最大缩放[1.03]"]}, {"code": 241, "indent": 1, "parameters": [{"name": "ISAo_05 The Evil Sacrifice Archenemies", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [3, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 1, "parameters": [2, "战斗透视镜改", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 1, "parameters": [5, "外框_改", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 1, "parameters": [4, "暗角_改", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 356, "indent": 1, "parameters": ["SETMASK 3 2"]}, {"code": 108, "indent": 1, "parameters": ["事件开头"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 232, "indent": 2, "parameters": [4, 0, 0, 0, 640, 0, 100, 100, 255, 0, 30, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [3, 0, 0, 0, 640, 0, 100, 100, 255, 0, 30, false]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 640, 0, 100, 100, 255, 0, 30, false]}, {"code": 232, "indent": 1, "parameters": [5, 0, 0, 0, 640, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[3] : 设置动画序列 : 动画序列[12]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[3] : 播放简单状态元集合 : 集合[待机动作]"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["噩梦1"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 250, "indent": 1, "parameters": [{"name": "Powerup", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人陷入噩梦状态!"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 30, false]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "pierrot_piston01_fast", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 Honeydrop_i_ticklehard 35 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 着色滤镜 : 黑白 : 255 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[浴室后背位快速]"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 着色滤镜 : 黑白 : 255 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我..能够遇到学姐真是太好了，学姐是唯一真心对我好的人，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我喜欢学姐~~，我这悲剧一样的人生，只有学姐能让我看见唯一的一丝曙光，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac明日奈，我要你!你是我的!噢~噢~"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 着色滤镜 : 黑白 : 0 : 1"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人仍处于噩梦之中!"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 圣光之祝福，聆听我的请求..."]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs3"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 108, "indent": 1, "parameters": ["骚扰1"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[3] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[3] : 播放简单状态元集合 : 集合[猪田抱住]"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 猪..猪田君,别胡闹,我要帮桐人君净化"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 27 嗯呜唔唔....嚎~~~~"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 糟...糟糕..难道猪田君也被那个魔法影响了..."]}, {"code": 230, "indent": 1, "parameters": [360]}, {"code": 108, "indent": 1, "parameters": ["噩梦2"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人噩梦状态更严重了……"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 30, false]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "handjob_slow", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 あんな喘ぎ4 35 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 着色滤镜 : 黑白 : 255 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[猪田床上对面位]"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 着色滤镜 : 黑白 : 255 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac怎么样学姐，这个姿势我从网上看到的，说是很受情侣的欢迎来着。"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 着色滤镜 : 黑白 : 0 : 1"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人仍处于噩梦之中!"]}, {"code": 356, "indent": 1, "parameters": ["ForceGabClear"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 108, "indent": 1, "parameters": ["骚扰2"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 250, "indent": 1, "parameters": [{"name": "Slash4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 311, "indent": 1, "parameters": [0, 4, 1, 0, 500, false]}, {"code": 245, "indent": 1, "parameters": [{"name": "呻吟", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 这样的话...根本没有释放魔法的空隙...猪田君快点醒过来"]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 356, "indent": 1, "parameters": ["PushGab 27 欸嘿♥♥嗯唔~"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[3] : 播放简单状态元集合 : 集合[猪田袭胸]"]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 诶~怎么会...唔...不要突然揉那种地方..."]}, {"code": 230, "indent": 1, "parameters": [240]}, {"code": 245, "indent": 1, "parameters": [{"name": "呻吟", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 108, "indent": 1, "parameters": ["噩梦3"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人噩梦状态更严重了……"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 30, false]}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストングチュ音多め（低速）", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 Amebo_aka_59b 35 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 着色滤镜 : 黑白 : 255 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[猪田床上后入位]"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 着色滤镜 : 黑白 : 255 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac噢噢~刚才学姐这个声音太可爱了!说实话每次上学从后面"]}, {"code": 401, "indent": 1, "parameters": ["\\dac看着和学长并排一起走着随着裙摆露出的一点点白皙的大腿我下面就会"]}, {"code": 401, "indent": 1, "parameters": ["\\dac硬的不行，一直幻想着能像这天一样，能摸着学姐的翘臀和大腿狠狠的后入学姐~太爽了!"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 着色滤镜 : 黑白 : 0 : 1"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人仍处于噩梦之中!"]}, {"code": 356, "indent": 1, "parameters": ["ForceGabClear"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Slash4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "捂嘴娇喘3", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 108, "indent": 1, "parameters": ["骚扰3"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 挣...挣脱不开,好大的力气,既然这样的话,那就直接吟唱..."]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 圣..圣光之祝福，聆听...."]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 356, "indent": 1, "parameters": ["<PERSON><PERSON><PERSON>ab 27 哼~"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[3] : 播放简单状态元集合 : 集合[猪田入嘴]"]}, {"code": 245, "indent": 1, "parameters": [{"name": "捂嘴娇喘3", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 呜唔呜呜..手...手指....我.....舌...舌头.."]}, {"code": 230, "indent": 1, "parameters": [180]}, {"code": 108, "indent": 1, "parameters": ["噩梦4"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人噩梦状态更严重了……"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 30, false]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストンタイプB（グチュ音多め）（中くらい）", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 Honeydrop_o_aegihard1 35 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 着色滤镜 : 黑白 : 255 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[猪田床上正常位]"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 着色滤镜 : 黑白 : 255 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac而且学姐的腰也开始配合我的动作了~真是太色情了，学姐"]}, {"code": 401, "indent": 1, "parameters": ["\\dac的身体~"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 着色滤镜 : 黑白 : 0 : 1"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人仍处于噩梦之中!"]}, {"code": 356, "indent": 1, "parameters": ["ForceGabClear"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Slash4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 あんな喘ぎ手マン 50 100 0"]}, {"code": 108, "indent": 1, "parameters": ["骚扰4"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 咕噜..呜...呜...这..这样...不..不行...对..对不起..桐人君稍微等会...我去处理一下猪田君..."]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 356, "indent": 1, "parameters": ["<PERSON>ushGab 27 嘿嘿~♥♥"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[3] : 播放简单状态元集合 : 集合[战斗揉胸抠穴]"]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 あんな喘ぎ手マン 50 100 0"]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 我..我的裙子..嗯唔♥♥~不..不要乱摸那种地方...你个笨蛋..."]}, {"code": 230, "indent": 1, "parameters": [180]}, {"code": 108, "indent": 1, "parameters": ["噩梦5"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人噩梦状态更严重了……"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 30, false]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "P_piston03", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 床震2 35 100 0"]}, {"code": 356, "indent": 1, "parameters": ["play_bgs3 Honeydrop_o_aegisilent 50 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 着色滤镜 : 黑白 : 255 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[通讯中正常位]"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 着色滤镜 : 黑白 : 255 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac受...受不了了，要射了~~~"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 着色滤镜 : 黑白 : 0 : 1"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人仍处于噩梦之中!"]}, {"code": 356, "indent": 1, "parameters": ["ForceGabClear"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs3"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Slash4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 311, "indent": 1, "parameters": [0, 4, 1, 0, 500, false]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 B_嫌そうに2 50 100 0"]}, {"code": 108, "indent": 1, "parameters": ["骚扰5"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 你..那个笨蛋...快..快给我醒过来..不然我...我..."]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 356, "indent": 1, "parameters": ["PushGab 27 (((‵□′))叽叽歪歪的烦死了!"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[3] : 播放简单状态元集合 : 集合[战斗抠穴接吻]"]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 B_嫌そうに2 50 100 0"]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 呜~~~~~~吧唧~~~~~~唔唔~~~~呜~~~~"]}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 108, "indent": 1, "parameters": ["噩梦6"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人噩梦状态更严重了……"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 30, false]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストングチュ音多め（中速）", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 Honeydrop_i_ticklehard 35 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 着色滤镜 : 黑白 : 255 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[猪田床上配种位2]"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 着色滤镜 : 黑白 : 255 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac噢噢~学姐的脚居然在主动在缠着我的腰....太棒了，太棒了，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac学姐也终于认可我了嘛!我太感动了，太感动了！"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 着色滤镜 : 黑白 : 0 : 1"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人仍处于噩梦之中!"]}, {"code": 356, "indent": 1, "parameters": ["ForceGabClear"]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Slash4", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 B_嫌そうに2 50 100 0"]}, {"code": 108, "indent": 1, "parameters": ["骚扰6"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 呜唔唔呜!(捶打声)"]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 356, "indent": 1, "parameters": ["<PERSON><PERSON><PERSON><PERSON> 27 ........"]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 224, "indent": 1, "parameters": [[255, 136, 221, 170], 30, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "puu10", "volume": 60, "pitch": 90, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [10, "", 0, 0, 346, 400, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [6, "闪图透视镜", 0, 0, 380, 338, 100, 100, 255, 0]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": ["SETMASK 10 6"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[10] : 设置动画序列 : 动画序列[6]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[战斗中接吻结束]"]}, {"code": 231, "indent": 1, "parameters": [7, "剧情小图背景3", 0, 0, -816, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 1, "parameters": [8, "剧情小图背景1", 0, 0, -29, -7, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [9, "剧情小图背景2", 0, 0, 29, 7, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [8, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 1, "parameters": [9, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 1, "parameters": [10, 0, 0, 0, 375, 407, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 1, "parameters": [6, 0, 0, 0, 404, 345, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 1, "parameters": [7, 0, 0, 0, -20, 0, 100, 100, 255, 0, 15, true]}, {"code": 232, "indent": 1, "parameters": [7, 0, 0, 0, 20, 0, 100, 100, 255, 0, 250, false]}, {"code": 232, "indent": 1, "parameters": [8, 0, 0, 0, 58, 14, 100, 100, 0, 0, 265, false]}, {"code": 232, "indent": 1, "parameters": [9, 0, 0, 0, -29, -7, 100, 100, 0, 0, 265, false]}, {"code": 232, "indent": 1, "parameters": [7, 0, 0, 0, 836, 0, 100, 100, 0, 0, 265, false]}, {"code": 232, "indent": 1, "parameters": [6, 0, 0, 0, 438, 352, 100, 100, 0, 0, 280, false]}, {"code": 232, "indent": 1, "parameters": [10, 0, 0, 0, 404, 414, 100, 100, 0, 0, 280, true]}, {"code": 235, "indent": 1, "parameters": [6]}, {"code": 235, "indent": 1, "parameters": [7]}, {"code": 235, "indent": 1, "parameters": [8]}, {"code": 235, "indent": 1, "parameters": [9]}, {"code": 235, "indent": 1, "parameters": [10]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 哈啊...哈啊...你...你这个...嗯唔~~"]}, {"code": 231, "indent": 1, "parameters": [4, "暗角_黑", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 B_嫌そうに2 50 100 0"]}, {"code": 230, "indent": 1, "parameters": [100]}, {"code": 108, "indent": 1, "parameters": ["骚扰7"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 呜..唔...你...你这个笨蛋...给..给我...适可而...止...!"]}, {"code": 232, "indent": 1, "parameters": [4, 0, 0, 0, 640, 0, 100, 100, 255, 0, 30, true]}, {"code": 224, "indent": 1, "parameters": [[255, 136, 221, 170], 30, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "puu10", "volume": 60, "pitch": 90, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [10, "", 0, 0, 346, 400, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [6, "闪图透视镜", 0, 0, 380, 338, 100, 100, 255, 0]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": ["SETMASK 10 6"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[10] : 设置动画序列 : 动画序列[6]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[战斗敲头]"]}, {"code": 231, "indent": 1, "parameters": [7, "剧情小图背景3", 0, 0, -816, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 1, "parameters": [8, "剧情小图背景1", 0, 0, -29, -7, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [9, "剧情小图背景2", 0, 0, 29, 7, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [8, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 1, "parameters": [9, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 1, "parameters": [10, 0, 0, 0, 375, 407, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 1, "parameters": [6, 0, 0, 0, 404, 345, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 1, "parameters": [7, 0, 0, 0, -20, 0, 100, 100, 255, 0, 15, true]}, {"code": 232, "indent": 1, "parameters": [7, 0, 0, 0, 20, 0, 100, 100, 255, 0, 30, false]}, {"code": 232, "indent": 1, "parameters": [8, 0, 0, 0, 58, 14, 100, 100, 0, 0, 45, false]}, {"code": 232, "indent": 1, "parameters": [9, 0, 0, 0, -29, -7, 100, 100, 0, 0, 45, false]}, {"code": 232, "indent": 1, "parameters": [7, 0, 0, 0, 836, 0, 100, 100, 0, 0, 45, false]}, {"code": 232, "indent": 1, "parameters": [6, 0, 0, 0, 438, 352, 100, 100, 0, 0, 60, false]}, {"code": 232, "indent": 1, "parameters": [10, 0, 0, 0, 404, 414, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 1, "parameters": [6]}, {"code": 235, "indent": 1, "parameters": [7]}, {"code": 235, "indent": 1, "parameters": [8]}, {"code": 235, "indent": 1, "parameters": [9]}, {"code": 235, "indent": 1, "parameters": [10]}, {"code": 230, "indent": 1, "parameters": [100]}, {"code": 356, "indent": 1, "parameters": ["PushGab 27 (⊙﹏⊙)呜.."]}, {"code": 230, "indent": 1, "parameters": [100]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 231, "indent": 2, "parameters": [4, "暗角_改", 0, 0, 640, 0, 100, 100, 255, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[3] : 播放简单状态元集合 : 集合[战斗中事后]"]}, {"code": 356, "indent": 1, "parameters": [">图片滤镜 : 图片[3] : 模糊滤镜 : 0 : 1"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 356, "indent": 1, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 23 哈啊...哈啊.....抱..抱歉..桐人君..让你久等了....马上就帮你治疗..."]}, {"code": 230, "indent": 1, "parameters": [100]}, {"code": 250, "indent": 1, "parameters": [{"name": "Heal3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 60, true]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人从噩梦中清醒了!"]}, {"code": 356, "indent": 1, "parameters": ["ForceGabClear"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac多谢了!亚丝娜,我会尽快解决它的!"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 235, "indent": 1, "parameters": [3]}, {"code": 235, "indent": 1, "parameters": [4]}, {"code": 235, "indent": 1, "parameters": [5]}, {"code": 235, "indent": 1, "parameters": [98]}, {"code": 242, "indent": 1, "parameters": [1]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 121, "indent": 1, "parameters": [207, 207, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 15}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 78}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜私服像素人", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6KTV"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 5, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 5 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 5]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 5 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 78}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜私服像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6KTV"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/6KTV的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [215, 215, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 120, 21, 9, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 78}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜私服像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6KTV"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/6KTV的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [215, 215, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 120, 21, 9, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 15}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 128, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "猪头裸", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/3交易"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 5, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 5 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 5]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 5 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 128, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "猪头裸", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/3交易"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/3猪田交易的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [208, 208, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [21, 21, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 114, 19, 14, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 231, "indent": 1, "parameters": [100, "杂物室偷窥", 0, 0, -310, 0, 150, 150, 255, 0]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[100] : 反复缩放 : 持续时间[无限] : 周期[120] : 最小缩放[1.00] : 最大缩放[1.05]"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[10]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[杂物室偷窥口交]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}噢~稍微有..那么点感觉了...只是还稍微差那么点..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac口齿不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}咕噜..噗嗤不...不可能，区..区区一个死..猪..嗯唔...居然能坚持这么久..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}不是说只...手，1分钟...就让我...精吗?...连嘴巴也用...了，...似乎...什么效果啊..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac口齿不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}咕噜...居然..嗯唔这么大...还能....咕噜这么持久....."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}..那个...做一次...我都付了那么多钱了...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac口齿不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}闭..闭嘴..嗯唔死...不要...和肥猪做..咕噜马上就...你射...."]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 128, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "猪头裸", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/3交易"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/3猪田交易的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [208, 208, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [21, 21, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 114, 19, 14, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 231, "indent": 1, "parameters": [100, "杂物室偷窥", 0, 0, -310, 0, 150, 150, 255, 0]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[100] : 反复缩放 : 持续时间[无限] : 周期[120] : 最小缩放[1.00] : 最大缩放[1.05]"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 5 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[10]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[杂物室偷窥口交]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}噢~稍微有..那么点感觉了...只是还稍微差那么点..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac口齿不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}咕噜..噗嗤不...不可能，区..区区一个死..猪..嗯唔...居然能坚持这么久..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}不是说只...手，1分钟...就让我...精吗?...连嘴巴也用...了，...似乎...什么效果啊..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac口齿不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}咕噜...居然..嗯唔这么大...还能....咕噜这么持久....."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}..那个...做一次...我都付了那么多钱了...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac口齿不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}闭..闭嘴..嗯唔死...不要...和肥猪做..咕噜马上就...你射...."]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "猪头裸", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/3交易"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/3猪田交易的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [208, 208, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [21, 21, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 114, 19, 14, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 231, "indent": 1, "parameters": [100, "杂物室偷窥", 0, 0, -310, 0, 150, 150, 255, 0]}, {"code": 356, "indent": 1, "parameters": [">持续动作 : 图片[100] : 反复缩放 : 持续时间[无限] : 周期[120] : 最小缩放[1.00] : 最大缩放[1.05]"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 5 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[10]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[杂物室偷窥口交]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}噢~稍微有..那么点感觉了...只是还稍微差那么点..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac口齿不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}咕噜..噗嗤不...不可能，区..区区一个死..猪..嗯唔...居然能坚持这么久..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}不是说只...手，1分钟...就让我...精吗?...连嘴巴也用...了，...似乎...什么效果啊..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac口齿不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}咕噜...居然..嗯唔这么大...还能....咕噜这么持久....."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的男声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}..那个...做一次...我都付了那么多钱了...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac口齿不清的女声:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}闭..闭嘴..嗯唔死...不要...和肥猪做..咕噜马上就...你射...."]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 17}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜学校服", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/4原谅"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 10, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 10 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 10]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 10 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜学校服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/4原谅"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/4亚丝娜原谅猪田的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [217, 217, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 113, 11, 14, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[事后入睡]"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac唔嗯.....zzz....zzz...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac吧唧..吧唧..学..学姐..我先稍微休息下..zzz..."]}, {"code": 401, "indent": 1, "parameters": ["\\dac马上就会恢复精神了zzz..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac zzz..学姐的里面好温柔zzz,干脆就这样吧唧...吧唧...插着睡吧...zzz...."]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜学校服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/4原谅"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/4亚丝娜原谅猪田的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [217, 217, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 113, 11, 14, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[2]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[事后入睡]"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac唔嗯.....zzz....zzz...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac吧唧..吧唧..学..学姐..我先稍微休息下..zzz..."]}, {"code": 401, "indent": 1, "parameters": ["\\dac马上就会恢复精神了zzz..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac zzz..学姐的里面好温柔zzz,干脆就这样吧唧...吧唧...插着睡吧...zzz...."]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 17}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!笔记本", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[12,6]"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 60, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 60 \\c[0]解锁此功能？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 60]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 121, "indent": 2, "parameters": [146, 146, 0]}, {"code": 117, "indent": 2, "parameters": [33]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 401, "indent": 2, "parameters": ["\\dac因为未知的力量，在现实中也可以访问已经设置私密的博客内容……"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此功能需要\\c[113] 60 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 146, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!笔记本", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 121, "indent": 0, "parameters": [146, 146, 0]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null}, {"code": 19, "indent": null}, {"code": 35, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[12,6]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "KOK_Systemsounds_MenuOpen_01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [33]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">信息面板K : 打开面板"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null}, {"code": 16, "indent": null}, {"code": 35, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!笔记本", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 121, "indent": 0, "parameters": [146, 146, 0]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null}, {"code": 19, "indent": null}, {"code": 35, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[12,6]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "KOK_Systemsounds_MenuOpen_01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [33]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">信息面板K : 打开面板"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 36, "indent": null}, {"code": 16, "indent": null}, {"code": 35, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 17}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 64}, "directionFix": true, "image": {"tileId": 0, "characterName": "7ou2synp", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/5树妖"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/5树妖战斗的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["进入战斗", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "进入战斗"]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 10, true]}, {"code": 129, "indent": 1, "parameters": [8, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 283, "indent": 1, "parameters": ["Grassland", "Forest"]}, {"code": 121, "indent": 1, "parameters": [69, 69, 0]}, {"code": 231, "indent": 1, "parameters": [2, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[2] : 设置动画序列 : 动画序列[12]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[2] : 等待动画序列加载完成"]}, {"code": 301, "indent": 1, "parameters": [0, 3, false, false]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 15, false]}, {"code": 121, "indent": 1, "parameters": [69, 69, 1]}, {"code": 129, "indent": 1, "parameters": [1, 0, false]}, {"code": 129, "indent": 1, "parameters": [8, 1, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 26, "y": 17}, {"id": 16, "name": "模板", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 10]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 19, 1]}, {"code": 356, "indent": 1, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 19, 1]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 0}, {"id": 17, "name": "EV017", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 66}, "directionFix": true, "image": {"tileId": 0, "characterName": "sho<PERSON>hu", "direction": 4, "pattern": 2, "characterIndex": 4}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/5茂凯"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 10, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 10 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 10]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 10 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 66}, "directionFix": true, "image": {"tileId": 0, "characterName": "sho<PERSON>hu", "direction": 4, "pattern": 1, "characterIndex": 4}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/5茂凯(亚丝娜)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/5茂凯boss战(亚丝娜视角)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 241, "indent": 1, "parameters": [{"name": "ISAo_05 The Evil Sacrifice Archenemies", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 あんな喘ぎ手マン 50 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[17]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[战斗骚扰回想1]"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 201, "indent": 1, "parameters": [0, 125, 8, 10, 8, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 241, "indent": 1, "parameters": [{"name": "ISAo_05 The Evil Sacrifice Archenemies", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [1, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 あんな喘ぎ手マン 50 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[17]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[战斗骚扰回想1]"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我..我的裙子..嗯唔♥♥~不..不要乱摸那种地方...你个笨蛋..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac.........."]}, {"code": 356, "indent": 1, "parameters": ["PushGab 100 \\c[101]系统:你的队友正在被噩梦折磨!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac唔...桐人君...我必须得快点才行..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac.........."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac你..这个笨蛋...快..快给我醒过来..不然我...我..."]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哈...哈...真是的，为什么会变成这样..."]}, {"code": 356, "indent": 1, "parameters": ["PushGab 27 (((‵□′))叽叽歪歪的烦死了!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac呜!"]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 B_嫌そうに2 50 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[17]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[战斗骚扰回想2]"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac唔呜唔呜!"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 100 \\c[101]系统:你的队友正在被噩梦折磨!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac.........."]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哈啊...哈啊...你...你这个...嗯唔~~"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac呜..唔...你...你这个笨蛋...给..给我...适可而...止...!"]}, {"code": 242, "indent": 1, "parameters": [1]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [1, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 66}, "directionFix": true, "image": {"tileId": 0, "characterName": "sho<PERSON>hu", "direction": 4, "pattern": 1, "characterIndex": 4}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/5茂凯(亚丝娜)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/5茂凯boss战(亚丝娜视角)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 241, "indent": 1, "parameters": [{"name": "ISAo_05 The Evil Sacrifice Archenemies", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 あんな喘ぎ手マン 50 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[17]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[战斗骚扰回想1]"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 201, "indent": 1, "parameters": [0, 125, 8, 10, 8, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 241, "indent": 1, "parameters": [{"name": "ISAo_05 The Evil Sacrifice Archenemies", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [1, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 あんな喘ぎ手マン 50 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[17]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[战斗骚扰回想1]"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我..我的裙子..嗯唔♥♥~不..不要乱摸那种地方...你个笨蛋..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac.........."]}, {"code": 356, "indent": 1, "parameters": ["PushGab 100 \\c[101]系统:你的队友正在被噩梦折磨!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac唔...桐人君...我必须得快点才行..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac.........."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac你..这个笨蛋...快..快给我醒过来..不然我...我..."]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哈...哈...真是的，为什么会变成这样..."]}, {"code": 356, "indent": 1, "parameters": ["PushGab 27 (((‵□′))叽叽歪歪的烦死了!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac呜!"]}, {"code": 245, "indent": 1, "parameters": [{"name": "1-HandjobLow", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 B_嫌そうに2 50 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[17]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[战斗骚扰回想2]"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac唔呜唔呜!"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 100 \\c[101]系统:你的队友正在被噩梦折磨!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac.........."]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 250, "indent": 1, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哈啊...哈啊...你...你这个...嗯唔~~"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac呜..唔...你...你这个笨蛋...给..给我...适可而...止...!"]}, {"code": 242, "indent": 1, "parameters": [1]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [1, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 15}, {"id": 18, "name": "EV018", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 125}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜学校服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/8化学狂暴"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/8感冒药副作用的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [221, 221, 0]}, {"code": 121, "indent": 1, "parameters": [228, 228, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 115, 22, 5, 2, 0]}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "エースDungeon2", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [48], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 90, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [48], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 90, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 231, "indent": 1, "parameters": [10, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[10] : 设置动画序列 : 动画序列[21]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[桐亚房间正常位慢]"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊！！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac骗人的吧……桐人君有这么大吗……？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 噫……啊啊啊……呜……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人君、等、等等……慢一点……我还没……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\{噢！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊啊！！！！！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]原来哥哥也有这样的一面……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊……啊……好……涨…呜呜……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]怎么可能……感觉可以和猪田君的尺寸相提并论了……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 噫啊啊啊……啊……嗯……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但是……虽然尺寸相似……刺激到的部位却完全不同……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哈啊……哈啊……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 顶端每次撞到里面的时候，好酸胀……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\{啊！啊！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊……桐人君……轻、轻一点点，可以吗……？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呜呜…… "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]哥哥……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]啊~啊……好羡慕……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人君似乎失去理智了…因为发烧的原因的吗……？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 从来没听说过会这样啊……"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 245, "indent": 1, "parameters": [{"name": "喘ぎ声とベッド　強　", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 あんな喘ぎ手マン 100 100 0"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[桐亚房间正常位快]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 噫…呃…呜嗯……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\{亚丝娜！亚丝娜……！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 噫…呜…好胀……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}\\c[8]亚丝娜……也会露出这么扭曲的表情吗……？ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\{要射了…………！"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[10] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[10] : 播放动作 : 动作元[桐亚房间正常位中出]"]}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 250, "indent": 1, "parameters": [{"name": "射精1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 250, "indent": 1, "parameters": [{"name": "射精1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [135]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [10]}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac唔…哦…………"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呜呜…啊…啊……射出来了吗？桐人君……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 终于结束了…哈啊，哈啊……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}唉，第一次对桐人君的持久力差感到庆幸……)"]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 242, "indent": 1, "parameters": [1]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 15}, {"id": 19, "name": "EV019", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 160}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜拉拉队", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/10猪的安慰"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 10, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 10 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 10]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 10 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜拉拉队", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/10猪的安慰"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关亚丝娜在6/10的啦啦队活动的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 130, 15, 14, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [224, 224, 0]}, {"code": 121, "indent": 1, "parameters": [148, 148, 1]}, {"code": 121, "indent": 1, "parameters": [147, 147, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 231, "indent": 1, "parameters": [100, "黑幕", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "【BGM】陵辱系Hシーン【儀式っぽい感じ】", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "bob_chikan01", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, false]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[22]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜手交慢]"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac啊，学姐的手，凉凉的，好光滑，久违了的感觉！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊啊，舒服~哦哦~~~学姐，可以再用点力~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这，这样可以吗……？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](调整一下力度吗？也提高一点揉搓的幅度好了……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac很久没做过了，感觉都生疏了……）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](呸呸呸!我在遗憾什么呀？）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac噢噢，对对对，就是这样！啊～"]}, {"code": 401, "indent": 1, "parameters": ["\\dac ……保持，继续~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](笨蛋，一脸很舒服的样子，蠢死了~呵呵呵~)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac啊啊～"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](味道……好浓啊………………)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac学姐，太舒服了，\\.你真好~\\.\\.我爱……\\.\\^"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\{不许乱说话！\\}\\|不然我下次再也不帮你了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac唔……好，好吧…………"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](没想到会让他这么失落，感觉肉棒都缩了一点。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 有点可怜……猪田君只是说说话，并没有过分的行为……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 是我反应得太激烈了吗……？嗯，亚丝娜，千万不要再心软！）"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac 十分钟后……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人此时是否打来电话？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 2, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Phone", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 250, "indent": 2, "parameters": [{"name": "Phone", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 电话？桐人？！我接一下，猪田你千万别出声！"]}, {"code": 250, "indent": 2, "parameters": [{"name": "接电话(Phone Pick Up)_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 2, "parameters": [{"name": "bob_chikan02", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜手交通话中]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 桐桐桐人君？那个，有、有什么事吗？"]}, {"code": 232, "indent": 2, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 想你了，所以给你打个电话。"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 诶？我们半个小时前才刚通过话呀？"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac哈、哈，确实……你还在体育馆吗？"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 嗯。我现在还在体育馆处理点事情，应该……没那么快回家。"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 桐人君，你现在可以先去咖啡厅找小直，在那儿吃晚饭。"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 处理事情……是发什么什么意外了吗？ "]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 这个……就是帮忙做做清洁，收拾一下场地什么……"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 校队那么多男生，还让你们啦啦队的女生做这些？"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 也太没有绅士风度了，下次我要跟他们说道说道。"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 不用！不用的，我们都是同学，为这点小事争吵不值得。"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 也没让我们做重活，就是……\\.擦擦栏杆什么的……"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\}嘿嘿~学姐，栏杆的凹槽和连接处也帮忙擦擦~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\}你不要说话！"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 什么？"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 那个……\\.\\.是…\\.后辈在调侃我做清洁的动作，烦人……"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 先不跟你说了，我想一鼓作气弄完，然后早点回家。 "]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 早点回家？！不不不，亚丝娜，你不要着急，慢慢来。 "]}, {"code": 401, "indent": 2, "parameters": ["\\dac 现在还早，阿姨应该才刚下班吧？到你哪里也需要时间。 "]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 啊？嗯……妈妈确实还要过一会儿才能到……"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 不管怎么，事情还是要做完的，我先忙了。"]}, {"code": 250, "indent": 2, "parameters": [{"name": "電話が切れるツーツー", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 猪田君真是的，刚才又胡闹！我差点被你吓出心脏病。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 抱歉抱歉~黄段子说顺嘴了，一时没有忍住。 "]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 232, "indent": 2, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 怎么还没射呀，同样的手法，桐人昨天明明很快就……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac………学姐，我可以提一个要求吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不\\.可\\.以。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac唔，怎么这样……我都还没说呢！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac无非就是要我脱衣服之类的吧？之前已经说过了，不可以！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不是不是。学姐现在不是穿着啦啦队服吗？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我是在想……能不能……一边帮我，一边……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 说啦啦队的口号帮我应援…………应该不算过分吧。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac…………………………\\|"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 好吧，这个确实还可以接受。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但相对的，你也给我快点结束！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯嗯嗯！我其实已经感觉快坚持不住了，配上学姐悦耳的声音，搞不好会秒射~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](才怪！好不容易才又能享受到，我怎么也要尽量坚持久一点！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但确实太舒服了，配上学姐的应援，不知道能不能做到……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哼，肉麻……"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 245, "indent": 1, "parameters": [{"name": "bob_chikan02", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜手交快]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 加油加油，猪田加油~猪田猪田…硬如钢铁~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac ……这样可以吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac没错没错！不愧是啦啦队长，能现编这么贴切的应援词！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 油腔滑调……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac除了应援我，可以也帮我的小弟弟应援一下吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac诶？……………………好吧…………我试试。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田猪田你真棒，你、你的……\\}你、的、\\}肉棒\\{最最棒！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](我操，这也太有感觉了！真的可能随时受不了啊！)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac学、\\.姐、\\.请、\\.你、\\.继、\\.续……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](色过头了我操！感觉随时要出来了……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\.不行！\\.撑住啊，猪田！！\\.你一定可以的！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\.噗唔………………)"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac 十分钟后……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人此时是否打来电话？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 2, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 245, "indent": 2, "parameters": [{"name": "bob_chikan02", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜手交通话中]"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 232, "indent": 2, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人君，我们不是才刚通过电话吗？！还有什么事吗……？"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\}噢~~~"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 呃，是这样……忘记问一个重要的事情了：今天比赛怎么样？"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 比赛…什么比赛？\\|哦，比赛。\\.我们学校输了。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\}弄痛你了吗？"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac没想到有你应援他们还会输啊？哈哈。 "]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 嗯……\\.啊？\\.\\>我哪有那种作用……"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\}没有……不痛，不如说…\\.快要忍不住了…\\.在脸上可以的吧……？"]}, {"code": 224, "indent": 2, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[亚丝娜手交通话射精]"]}, {"code": 122, "indent": 2, "parameters": [200, 200, 0, 0, 6]}, {"code": 230, "indent": 2, "parameters": [224]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 2, "parameters": [{"name": "射精1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [88]}, {"code": 250, "indent": 2, "parameters": [{"name": "射精1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [70]}, {"code": 356, "indent": 2, "parameters": ["PushGab 28 咻~爽了~"]}, {"code": 230, "indent": 2, "parameters": [50]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 232, "indent": 2, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210](真的不行了，不行了……！)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 猪田君，你的表情好像很痛苦，我太用力了吗？"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\}没有……不痛，不如说…\\.快要忍不住了…\\.在脸上可以的吧……？"]}, {"code": 224, "indent": 2, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[亚丝娜手交普通射精]"]}, {"code": 122, "indent": 2, "parameters": [200, 200, 0, 0, 6]}, {"code": 230, "indent": 2, "parameters": [224]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 2, "parameters": [{"name": "射精1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [88]}, {"code": 250, "indent": 2, "parameters": [{"name": "射精1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [120]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [224, 224, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["$gameMessage.clear();"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜拉拉队", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/10猪的安慰"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关亚丝娜在6/10的啦啦队活动的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 130, 15, 14, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [224, 224, 0]}, {"code": 121, "indent": 1, "parameters": [148, 148, 1]}, {"code": 121, "indent": 1, "parameters": [147, 147, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 231, "indent": 1, "parameters": [100, "黑幕", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "【BGM】陵辱系Hシーン【儀式っぽい感じ】", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "bob_chikan01", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, false]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[22]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜手交慢]"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac啊，学姐的手，凉凉的，好光滑，久违了的感觉！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊啊，舒服~哦哦~~~学姐，可以再用点力~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这，这样可以吗……？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](调整一下力度吗？也提高一点揉搓的幅度好了……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac很久没做过了，感觉都生疏了……）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](呸呸呸!我在遗憾什么呀？）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac噢噢，对对对，就是这样！啊～"]}, {"code": 401, "indent": 1, "parameters": ["\\dac ……保持，继续~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](笨蛋，一脸很舒服的样子，蠢死了~呵呵呵~)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac啊啊～"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](味道……好浓啊………………)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac学姐，太舒服了，\\.你真好~\\.\\.我爱……\\.\\^"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\{不许乱说话！\\}\\|不然我下次再也不帮你了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac唔……好，好吧…………"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](没想到会让他这么失落，感觉肉棒都缩了一点。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 有点可怜……猪田君只是说说话，并没有过分的行为……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 是我反应得太激烈了吗……？嗯，亚丝娜，千万不要再心软！）"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac 十分钟后……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人此时是否打来电话？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 2, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Phone", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 250, "indent": 2, "parameters": [{"name": "Phone", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 电话？桐人？！我接一下，猪田你千万别出声！"]}, {"code": 250, "indent": 2, "parameters": [{"name": "接电话(Phone Pick Up)_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 2, "parameters": [{"name": "bob_chikan02", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜手交通话中]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 桐桐桐人君？那个，有、有什么事吗？"]}, {"code": 232, "indent": 2, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 想你了，所以给你打个电话。"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 诶？我们半个小时前才刚通过话呀？"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac哈、哈，确实……你还在体育馆吗？"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 嗯。我现在还在体育馆处理点事情，应该……没那么快回家。"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 桐人君，你现在可以先去咖啡厅找小直，在那儿吃晚饭。"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 处理事情……是发什么什么意外了吗？ "]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 这个……就是帮忙做做清洁，收拾一下场地什么……"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 校队那么多男生，还让你们啦啦队的女生做这些？"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 也太没有绅士风度了，下次我要跟他们说道说道。"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 不用！不用的，我们都是同学，为这点小事争吵不值得。"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 也没让我们做重活，就是……\\.擦擦栏杆什么的……"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\}嘿嘿~学姐，栏杆的凹槽和连接处也帮忙擦擦~"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\}你不要说话！"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 什么？"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 那个……\\.\\.是…\\.后辈在调侃我做清洁的动作，烦人……"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 先不跟你说了，我想一鼓作气弄完，然后早点回家。 "]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 早点回家？！不不不，亚丝娜，你不要着急，慢慢来。 "]}, {"code": 401, "indent": 2, "parameters": ["\\dac 现在还早，阿姨应该才刚下班吧？到你哪里也需要时间。 "]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 啊？嗯……妈妈确实还要过一会儿才能到……"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 不管怎么，事情还是要做完的，我先忙了。"]}, {"code": 250, "indent": 2, "parameters": [{"name": "電話が切れるツーツー", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 猪田君真是的，刚才又胡闹！我差点被你吓出心脏病。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 抱歉抱歉~黄段子说顺嘴了，一时没有忍住。 "]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 232, "indent": 2, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 怎么还没射呀，同样的手法，桐人昨天明明很快就……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac………学姐，我可以提一个要求吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不\\.可\\.以。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac唔，怎么这样……我都还没说呢！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac无非就是要我脱衣服之类的吧？之前已经说过了，不可以！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不是不是。学姐现在不是穿着啦啦队服吗？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我是在想……能不能……一边帮我，一边……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 说啦啦队的口号帮我应援…………应该不算过分吧。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac…………………………\\|"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 好吧，这个确实还可以接受。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但相对的，你也给我快点结束！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯嗯嗯！我其实已经感觉快坚持不住了，配上学姐悦耳的声音，搞不好会秒射~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](才怪！好不容易才又能享受到，我怎么也要尽量坚持久一点！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但确实太舒服了，配上学姐的应援，不知道能不能做到……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哼，肉麻……"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 245, "indent": 1, "parameters": [{"name": "bob_chikan02", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜手交快]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 加油加油，猪田加油~猪田猪田…硬如钢铁~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac ……这样可以吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac没错没错！不愧是啦啦队长，能现编这么贴切的应援词！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 油腔滑调……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac除了应援我，可以也帮我的小弟弟应援一下吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac诶？……………………好吧…………我试试。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 猪田猪田你真棒，你、你的……\\}你、的、\\}肉棒\\{最最棒！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](我操，这也太有感觉了！真的可能随时受不了啊！)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac学、\\.姐、\\.请、\\.你、\\.继、\\.续……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](色过头了我操！感觉随时要出来了……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\.不行！\\.撑住啊，猪田！！\\.你一定可以的！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\.噗唔………………)"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac 十分钟后……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人此时是否打来电话？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 2, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 245, "indent": 2, "parameters": [{"name": "bob_chikan02", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜手交通话中]"]}, {"code": 230, "indent": 2, "parameters": [1]}, {"code": 232, "indent": 2, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人君，我们不是才刚通过电话吗？！还有什么事吗……？"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\}噢~~~"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 呃，是这样……忘记问一个重要的事情了：今天比赛怎么样？"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 比赛…什么比赛？\\|哦，比赛。\\.我们学校输了。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\}弄痛你了吗？"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac没想到有你应援他们还会输啊？哈哈。 "]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 嗯……\\.啊？\\.\\>我哪有那种作用……"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\}没有……不痛，不如说…\\.快要忍不住了…\\.在脸上可以的吧……？"]}, {"code": 224, "indent": 2, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[亚丝娜手交通话射精]"]}, {"code": 122, "indent": 2, "parameters": [200, 200, 0, 0, 6]}, {"code": 230, "indent": 2, "parameters": [224]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 2, "parameters": [{"name": "射精1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [88]}, {"code": 250, "indent": 2, "parameters": [{"name": "射精1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [70]}, {"code": 356, "indent": 2, "parameters": ["PushGab 28 咻~爽了~"]}, {"code": 230, "indent": 2, "parameters": [50]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 232, "indent": 2, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210](真的不行了，不行了……！)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 猪田君，你的表情好像很痛苦，我太用力了吗？"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\}没有……不痛，不如说…\\.快要忍不住了…\\.在脸上可以的吧……？"]}, {"code": 224, "indent": 2, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[亚丝娜手交普通射精]"]}, {"code": 122, "indent": 2, "parameters": [200, 200, 0, 0, 6]}, {"code": 230, "indent": 2, "parameters": [224]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 2, "parameters": [{"name": "射精1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [88]}, {"code": 250, "indent": 2, "parameters": [{"name": "射精1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 2, "parameters": [120]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [224, 224, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 355, "indent": 1, "parameters": ["$gameMessage.clear();"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 15}, {"id": 20, "name": "EV020", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 170}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜修女", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/10虚伪的忏悔"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 10, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 10 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 10]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 10 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜修女", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/10虚伪的忏悔"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关亚丝娜在6/10的忏悔室的回想,要回顾吗? "]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 242, 9, 12, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 231, "indent": 1, "parameters": [100, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "エースDungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "教会2", "volume": 15, "pitch": 100, "pan": 100}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, true]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[22]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[教会素股]"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](嗯，猪田君只是平常地拥抱，完全没有多余地奇怪动作。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 果然之前，是因为我给了他错误的预期，他才会对我…………"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 以后只要我自己把握好分寸，还是可以重新信任他的。）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]( 咻~~~总算是大功告成！嘿嘿嘿~想不到吧，升级以后我可以部分隐身，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 而且身体接触也发现不了。我蹭我蹭~光是抱抱怎么过瘾~ "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不过老婆感觉不到也挺可惜，好想看她被我摩擦到高潮的样子。）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](莉兹那个女人，确实有点东西，按她设计的大纲，配合我的天才演技，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 轻轻松松就过关，比我之前自由发挥的好多了。就是莉兹那个态度实在让我不爽……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但……还不能和她翻脸，时机不成熟……）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪……咳咳，我是说这位信徒，这个拥抱足够证明了吧。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯嗯，够了，够了。但我现在什么也看不到，周围都好黑，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 说实话感觉有点害怕，让修女姐姐看笑话了~能不能多抱一会……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不过放心，我会在致盲解除前结束的，不会去看修女姐姐的脸。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](就再抱一会儿好了，看他这个样子，我实在无法狠心拒绝……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯，好吧……"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 10 \\}\\c[8]什么？！那是之前……不！我从来没有怀疑过！！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](嘿嘿，装可怜真有用~）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](致盲的显示效果还有3分钟，妈的，这点时间完全不够啊！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不能让老婆起疑心，只能加快速度了，可恶！操操操！！！）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](肉肉的大腿，安产型的屁股，好爽，好爽~~~）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](好怀念更衣室的那次，唉，没惹老婆生气就好了。）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](想要射在老婆的屁股上！精液应该也可以隐形，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 技能升级了，应该没问题的……吧？)"]}, {"code": 356, "indent": 1, "parameters": ["ForceGabClear"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](啊！受不了啊！有问题也不管了，我就要射！憋不住了！）"]}, {"code": 225, "indent": 1, "parameters": [1, 5, 15, false]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 68], 15, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哦~~~~"]}, {"code": 225, "indent": 1, "parameters": [1, 5, 15, false]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 68], 15, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](完美！是隐形的！我自己都看不见…！还是说…直接取消射精特效了？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不过，还有点点味道，但比平时淡多了，老婆应该不会注意到……）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯？怎么了？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](是错觉吗？突然闻到一点点奇怪的味道……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 咻~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊，是、是这样的……修女姐姐身上有种好闻的香味，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 让我想到了我的那个她……刚才吸得太投入了，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 一时忘记换气，差点憋死，嘿嘿~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 笨、笨蛋！"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜修女", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/10虚伪的忏悔"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关亚丝娜在6/10的忏悔室的回想,要回顾吗? "]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 242, 9, 12, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 231, "indent": 1, "parameters": [100, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "エースDungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "教会2", "volume": 15, "pitch": 100, "pan": 100}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, true]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[22]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[教会素股]"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](嗯，猪田君只是平常地拥抱，完全没有多余地奇怪动作。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 果然之前，是因为我给了他错误的预期，他才会对我…………"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 以后只要我自己把握好分寸，还是可以重新信任他的。）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210]( 咻~~~总算是大功告成！嘿嘿嘿~想不到吧，升级以后我可以部分隐身，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 而且身体接触也发现不了。我蹭我蹭~光是抱抱怎么过瘾~ "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不过老婆感觉不到也挺可惜，好想看她被我摩擦到高潮的样子。）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](莉兹那个女人，确实有点东西，按她设计的大纲，配合我的天才演技，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 轻轻松松就过关，比我之前自由发挥的好多了。就是莉兹那个态度实在让我不爽……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但……还不能和她翻脸，时机不成熟……）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪……咳咳，我是说这位信徒，这个拥抱足够证明了吧。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯嗯，够了，够了。但我现在什么也看不到，周围都好黑，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 说实话感觉有点害怕，让修女姐姐看笑话了~能不能多抱一会……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不过放心，我会在致盲解除前结束的，不会去看修女姐姐的脸。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](就再抱一会儿好了，看他这个样子，我实在无法狠心拒绝……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯，好吧……"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 10 \\}\\c[8]什么？！那是之前……不！我从来没有怀疑过！！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](嘿嘿，装可怜真有用~）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](致盲的显示效果还有3分钟，妈的，这点时间完全不够啊！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不能让老婆起疑心，只能加快速度了，可恶！操操操！！！）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](肉肉的大腿，安产型的屁股，好爽，好爽~~~）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](好怀念更衣室的那次，唉，没惹老婆生气就好了。）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](想要射在老婆的屁股上！精液应该也可以隐形，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 技能升级了，应该没问题的……吧？)"]}, {"code": 356, "indent": 1, "parameters": ["ForceGabClear"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](啊！受不了啊！有问题也不管了，我就要射！憋不住了！）"]}, {"code": 225, "indent": 1, "parameters": [1, 5, 15, false]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 68], 15, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哦~~~~"]}, {"code": 225, "indent": 1, "parameters": [1, 5, 15, false]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 68], 15, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](完美！是隐形的！我自己都看不见…！还是说…直接取消射精特效了？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不过，还有点点味道，但比平时淡多了，老婆应该不会注意到……）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嗯？怎么了？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](是错觉吗？突然闻到一点点奇怪的味道……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 咻~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊，是、是这样的……修女姐姐身上有种好闻的香味，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 让我想到了我的那个她……刚才吸得太投入了，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 一时忘记换气，差点憋死，嘿嘿~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 笨、笨蛋！"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 13}, {"id": 21, "name": "EV021", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 120}, "directionFix": true, "image": {"tileId": 0, "characterName": "fog亚丝娜浴衣", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/7按摩(一)"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 10, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 10 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 10]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 10 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "fog亚丝娜浴衣", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/7按摩(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/7亚丝娜第一次按摩的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [219, 219, 0]}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 259, 8, 6, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 231, "indent": 1, "parameters": [100, "黑幕", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "【BGM】陵辱系Hシーン【儀式っぽい感じ】", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "呻吟", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[22]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜按摩一]"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac ……唔……\\|啊~~~\\i[90]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](糟糕，居然没忍住叫出来了……好羞耻……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac \\|有点胀感，但很舒服……\\.而且刚才的困意瞬间消失了。)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](嗯！似乎游戏内也有效果。应该说不愧是母女吗？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 敏感点差不多，反应也很一致~那么，继续……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这里~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯~~\\i[90]唔……啊~~~\\i[90]\\i[90]\\i[90]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](感觉……\\|身体有点发热…\\.是血液循环更顺畅了吗?)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 还有这里……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哦~~\\i[90]不敢相信…\\.猪田君的手法……\\|"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}真的好舒服~~~\\.\\{啊~\\i[90]"]}, {"code": 231, "indent": 1, "parameters": [100, "黑幕", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac三十分钟后……"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](嘿嘿，按了半个小时，老婆越来越进入状态了，嘿嘿。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 如果现实里也能这样光明正大抚摸老婆就好了……)"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哦~\\i[90]哦~~~\\i[90] \\i[90] \\i[90]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](不行，不是错觉，从刚刚开始，下面、下面就开始觉得好痒~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的手法和按摩位置都很正常。是因为经络疏通了，所以身体敏感了吗？)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](还是因为之前和桐人…的时候…积累的欲望…………"]}, {"code": 401, "indent": 1, "parameters": ["\\dac ……不管因为什么，今天这种状态都太危险了……）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac \\}猪田君，停一下……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](说什么小猪话，停？是不可能停的！我们才刚刚开始呢~)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}猪田君，\\{停下……\\|"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\{猪田君！"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 5, true]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 250, "indent": 1, "parameters": [{"name": "hit01", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac咿~~！"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "fog亚丝娜浴衣", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/7按摩(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/7亚丝娜第一次按摩的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [219, 219, 0]}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 259, 8, 6, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 231, "indent": 1, "parameters": [100, "黑幕", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "【BGM】陵辱系Hシーン【儀式っぽい感じ】", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "呻吟", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[22]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜按摩一]"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac ……唔……\\|啊~~~\\i[90]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](糟糕，居然没忍住叫出来了……好羞耻……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac \\|有点胀感，但很舒服……\\.而且刚才的困意瞬间消失了。)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](嗯！似乎游戏内也有效果。应该说不愧是母女吗？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 敏感点差不多，反应也很一致~那么，继续……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这里~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯~~\\i[90]唔……啊~~~\\i[90]\\i[90]\\i[90]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](感觉……\\|身体有点发热…\\.是血液循环更顺畅了吗?)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 还有这里……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哦~~\\i[90]不敢相信…\\.猪田君的手法……\\|"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}真的好舒服~~~\\.\\{啊~\\i[90]"]}, {"code": 231, "indent": 1, "parameters": [100, "黑幕", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac三十分钟后……"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](嘿嘿，按了半个小时，老婆越来越进入状态了，嘿嘿。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 如果现实里也能这样光明正大抚摸老婆就好了……)"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哦~\\i[90]哦~~~\\i[90] \\i[90] \\i[90]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](不行，不是错觉，从刚刚开始，下面、下面就开始觉得好痒~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的手法和按摩位置都很正常。是因为经络疏通了，所以身体敏感了吗？)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](还是因为之前和桐人…的时候…积累的欲望…………"]}, {"code": 401, "indent": 1, "parameters": ["\\dac ……不管因为什么，今天这种状态都太危险了……）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac \\}猪田君，停一下……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](说什么小猪话，停？是不可能停的！我们才刚刚开始呢~)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}猪田君，\\{停下……\\|"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\{猪田君！"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 5, true]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 250, "indent": 1, "parameters": [{"name": "hit01", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac咿~~！"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 15}, {"id": 22, "name": "EV022", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 博客-更新至[\\v[99]]"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-48,0]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 17}, {"id": 23, "name": "EV023", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 224, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}欸？\\|不不不、\\.不要！！\\|\\|唔~~~！\\|\\.\\.唉………\\|\\|\\^"]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 7]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 224, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 7}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 15}, {"id": 24, "name": "EV024", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 144}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog日常服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/9料理香肠"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/9亚丝娜在据点厨房为桐人发泄的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 136]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 33, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 281, 7, 8, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 231, "indent": 1, "parameters": [100, "黑幕", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "甘い香り", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "bob_chikan01", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[21]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[桐亚厨房手交慢]"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 怎么样……感觉舒服吗……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯，当然…舒服极了……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 感受像是在偷情一样，好刺激~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人君，真是的！这种情况明明只会让人觉得难为情……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 没想到桐人君也有稍微变态的一面。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嘿嘿，抱歉抱歉，最近可能真的是压抑久了~亚丝娜，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac你不知道你对男人的吸引力，我会稍微变态你也有点责任哦~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 居然说我有责任……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我这是在夸你！你别生气啊。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这样握住我的下体，会让你觉得恶心吗？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 当然不会……别说恶心了……有种微妙的心动感……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](有点想确认一下亚丝娜对于尺寸的看法，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但这个话题感觉有点变态，确定要问吗？)"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 2, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 那，你会不会觉得我的太小了……"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 不会，桐人君的不小。而且生理课学过的，"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 女性的阴道一般也只有7厘米左右，太大了反而不好。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210](耶！忍不住确认了一下，果然亚丝娜不觉得我小！)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 可以再多说说对我肉棒的感想吗？"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac欸？！"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 那，那个……非常的热……"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 每次摩擦的时候，会震一下的样子，很可爱……"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 嘿嘿嘿~~~"]}, {"code": 250, "indent": 2, "parameters": [{"name": "Poison", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 2, "parameters": [[68, 255, 68, 170], 60, true]}, {"code": 356, "indent": 2, "parameters": ["addLog 变态度↑\\c[28]1"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 桐人君，你现在笑的样子有点猥琐哦~"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我做的可以吗？舒服吗？感觉桐人君的表情很扭曲……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊啊，做得很好。这是舒服的表情……呜……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](虽然……很舒服，但是如果能做爱就更好了……）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 赶紧射吧，猪田君回来就尴尬了。"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[桐亚厨房手交快]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](我倒是还想多享受享受啊，但感觉真的撑不了多久了……）"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不用担心，我应该快了……"]}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这样刺激龟头的话会让我忍不住的！"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[桐亚厨房手交颜射]"]}, {"code": 230, "indent": 1, "parameters": [64]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 1, "parameters": [{"name": "射精1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [96]}, {"code": 250, "indent": 1, "parameters": [{"name": "射精1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [80]}, {"code": 250, "indent": 1, "parameters": [{"name": "射精1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [55]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呼……这么多啊……"]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 15}, {"id": 25, "name": "EV025", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 200}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜学校服", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11圣母膝枕"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 10]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜学校服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11圣母膝枕"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/11亚丝娜给猪田膝枕手交的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 121, "indent": 1, "parameters": [230, 230, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 113, 13, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [230, 230, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 113, 13, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜学校服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11圣母膝枕"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/11亚丝娜给猪田膝枕手交的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 121, "indent": 1, "parameters": [230, 230, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 113, 13, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [230, 230, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 113, 13, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 13}, {"id": 26, "name": "EV026", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 90}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜私服像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6奇怪的梦(二)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/6奇怪的梦(二)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [237, 237, 0]}, {"code": 201, "indent": 1, "parameters": [0, 120, 14, 24, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 221, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "KAN_環境音02[カラオケ,ゲームセンター]", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "口交+娇喘6", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[5]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[KTV三人]"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 50 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人君也终于过来了呢,抱歉了呢桐人君,大家玩的这么开心"]}, {"code": 401, "indent": 1, "parameters": ["\\dac居然没有叫上你,不过谁让桐人君你睡得太死了,我们也没有办法呢~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚!亚丝娜,居然莉兹也,你们你们...和猪田..在做什么!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac啊,前辈好,我们当然是在唱歌呀,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac学姐们没有嫌弃我愿意和我唱歌,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac真是太好了,是还是第一次和女孩子一起唱歌呢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac这..这是哪门子唱歌?!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac怎么不是唱歌,刚刚我的浪叫声可是非常的好听呢~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac可惜桐人君来的太晚了点,不过你听莉兹现在正在吹着猪田的竖笛，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac咕噜咕噜的配乐声,多悦耳呀~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹......"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac咕噜~咕噜~亚丝娜真是的，刚才一直用下面的小嘴霸占着麦克风,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 现在终于~吧唧吧唧~轮到我了~猪田的这个，用来当竖笛也很棒呢~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac♥♥猪田的乐器果然比你这种闷骚男优秀太多了呢~咕噜咕噜~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田君虽然音感不怎么样,但是情绪总是很高涨呢，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac是和桐人君，桐人君完全相反的类似，尤其是猪田君的乐器~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac被它演奏的感觉是桐人君你完全无法让我体会到的♥♥~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不可能...这不可能!亚丝娜,莉兹...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac直叶:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哼,不仅迟到居然连睡觉还在叫着别的女人名字嘛!"]}, {"code": 231, "indent": 1, "parameters": [1, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">方块粉碎效果 : 图片[99] : 方块粉碎[2]"]}, {"code": 250, "indent": 1, "parameters": [{"name": "[3100] 综合音效库-玻璃粉碎 sml(glass-sm_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 242, "indent": 1, "parameters": [1]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 15}, {"id": 27, "name": "EV027", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 180}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜学校服", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11电车之猪"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 10, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 10 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 10]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 10 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜学校服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11电车之猪"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/11电车上亚丝娜和猪田发生事情的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 271, 4, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 271, 4, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 186, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜学校服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11电车之猪"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/11电车上亚丝娜和猪田发生事情的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 271, 4, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 271, 4, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜学校服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11电车之猪"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/11电车上亚丝娜和猪田发生事情的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 271, 4, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 271, 4, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 13}, {"id": 28, "name": "EV028", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 199}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜学校服", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11走廊偷窥(二)"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 5, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 5 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 5]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 5 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜学校服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11走廊偷窥(二)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/11桐人在公寓走廊偷窥猪田房间回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [232, 232, 0]}, {"code": 201, "indent": 1, "parameters": [0, 112, 2, 4, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 121, "indent": 1, "parameters": [232, 232, 0]}, {"code": 201, "indent": 1, "parameters": [0, 112, 2, 4, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 179, "switch1Valid": true, "switch2Id": 232, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜学校服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11走廊偷窥(二)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/11桐人在公寓走廊偷窥猪田房间回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [232, 232, 0]}, {"code": 201, "indent": 1, "parameters": [0, 112, 2, 4, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 121, "indent": 1, "parameters": [232, 232, 0]}, {"code": 201, "indent": 1, "parameters": [0, 112, 2, 4, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 232, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜学校服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11走廊偷窥(二)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/11桐人在公寓走廊偷窥猪田房间回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [232, 232, 0]}, {"code": 201, "indent": 1, "parameters": [0, 112, 2, 4, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 121, "indent": 1, "parameters": [232, 232, 0]}, {"code": 201, "indent": 1, "parameters": [0, 112, 2, 4, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 13}, {"id": 29, "name": "EV029", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 212}, "directionFix": true, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图滤镜 : 纯色滤镜 : 青色 : 155"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11哀伤恶灵"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/11哀伤恶灵战斗的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["进入战斗", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "进入战斗"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜情欲值 "]}, {"code": 102, "indent": 1, "parameters": [["情欲值低", "情欲值中", "情欲值高", "放弃回顾"], 3, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "情欲值低"]}, {"code": 122, "indent": 2, "parameters": [115, 115, 0, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "情欲值中"]}, {"code": 122, "indent": 2, "parameters": [115, 115, 0, 0, 50]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "情欲值高"]}, {"code": 122, "indent": 2, "parameters": [115, 115, 0, 0, 100]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "放弃回顾"]}, {"code": 122, "indent": 2, "parameters": [115, 115, 0, 0, 0]}, {"code": 119, "indent": 2, "parameters": ["放弃回顾"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 10, true]}, {"code": 129, "indent": 1, "parameters": [8, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 283, "indent": 1, "parameters": ["Grassland", "<PERSON>"]}, {"code": 121, "indent": 1, "parameters": [69, 69, 0]}, {"code": 231, "indent": 1, "parameters": [2, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[2] : 设置动画序列 : 动画序列[12]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[2] : 等待动画序列加载完成"]}, {"code": 301, "indent": 1, "parameters": [0, 4, false, false]}, {"code": 121, "indent": 1, "parameters": [69, 69, 1]}, {"code": 122, "indent": 1, "parameters": [115, 115, 0, 0, 0]}, {"code": 129, "indent": 1, "parameters": [1, 0, false]}, {"code": 129, "indent": 1, "parameters": [8, 1, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "放弃回顾"]}, {"code": 118, "indent": 1, "parameters": ["放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 17, "y": 13}, {"id": 30, "name": "EV030", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 221}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11树屋迷情"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/11晚上与亚丝娜分开后狩猎怪鸽和守卫树屋的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件(一)", "仅限事件(二)", "仅限事件(三)", "放弃回顾"], 4, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [233, 233, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [8, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 284, 21, 31, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件(一)"]}, {"code": 121, "indent": 1, "parameters": [233, 233, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [8, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 284, 25, 16, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "仅限事件(二)"]}, {"code": 121, "indent": 1, "parameters": [233, 233, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [8, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 279, 2, 4, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "仅限事件(三)"]}, {"code": 121, "indent": 1, "parameters": [233, 233, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [8, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 5]}, {"code": 201, "indent": 1, "parameters": [0, 279, 12, 9, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [4, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 13}, {"id": 31, "name": "EV031", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 221}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog日常服", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11树屋迷情"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 30]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog日常服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11树屋迷情"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/11晚上亚丝娜和猪田在树屋内的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件(一)", "仅限事件(二)", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [238, 238, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 241, "indent": 1, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 1, "parameters": [0, 128, 8, 22, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件(一)"]}, {"code": 121, "indent": 1, "parameters": [238, 238, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 4]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 241, "indent": 1, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 1, "parameters": [0, 328, 18, 9, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "仅限事件(二)"]}, {"code": 121, "indent": 1, "parameters": [238, 238, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 13]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 241, "indent": 1, "parameters": [{"name": "bensound-sexy", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 1, "parameters": [0, 328, 24, 9, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog日常服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11树屋迷情"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/11晚上亚丝娜和猪田在树屋内的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件(一)", "仅限事件(二)", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [238, 238, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 241, "indent": 1, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 1, "parameters": [0, 128, 8, 22, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件(一)"]}, {"code": 121, "indent": 1, "parameters": [238, 238, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 4]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 241, "indent": 1, "parameters": [{"name": "ERO_Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 1, "parameters": [0, 328, 18, 9, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "仅限事件(二)"]}, {"code": 121, "indent": 1, "parameters": [238, 238, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 13]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 241, "indent": 1, "parameters": [{"name": "bensound-sexy", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 1, "parameters": [0, 328, 24, 9, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 13}, {"id": 32, "name": "EV032", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 221}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 桐人视角"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-48,-18]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 13}, {"id": 33, "name": "EV033", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 221}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 亚丝娜视角"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-48,-18]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 13}, {"id": 34, "name": "EV034", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 221}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 桐人视角"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-48,-18]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 15}, {"id": 35, "name": "EV035", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 221}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 亚丝娜视角"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-48,-18]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 15}, {"id": 36, "name": "EV036", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 275}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog日常服", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/13林中吹箫"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 25]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog日常服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/13林中吹箫"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/13亚丝娜和猪田搭完帐篷以后发生事情（亚丝娜POV）的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [240, 240, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 331, 4, 4, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [240, 240, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 337, 4, 5, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog日常服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/13林中吹箫"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/13亚丝娜和猪田搭完帐篷以后发生事情（亚丝娜POV）的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [240, 240, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 331, 4, 4, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [240, 240, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 337, 4, 5, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 11}, {"id": 37, "name": "EV037", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 275}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 亚丝娜视角"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-48,-18]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 11}, {"id": 38, "name": "EV038", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 275}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog日常服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/13林中吹箫"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/13亚丝娜和猪田搭完帐篷以后发生事情的回想（桐人POV）,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [246, 246, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 275]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 0]}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 201, "indent": 1, "parameters": [0, 337, 26, 10, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [246, 246, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 276]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 0]}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 201, "indent": 1, "parameters": [0, 337, 26, 10, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 13}, {"id": 39, "name": "EV039", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 275}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 桐人视角"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[48,-18]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 13}, {"id": 40, "name": "EV040", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 300}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog日常服", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/14今夜无眠"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 30]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog日常服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/14今夜无眠"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/14桐人守夜时亚丝娜和猪田在帐篷里发生事情（亚丝娜POV）的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [247, 247, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 290, 15, 10, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [247, 247, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 290, 15, 10, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog日常服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/14今夜无眠"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/14桐人守夜时亚丝娜和猪田在帐篷里发生事情（亚丝娜POV）的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [247, 247, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 290, 15, 10, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 121, "indent": 1, "parameters": [247, 247, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 290, 15, 10, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 11}, {"id": 41, "name": "EV041", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 380}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/16胡闹厨房"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 30]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/16胡闹厨房"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/16亚丝娜和猪田在据点厨房里发生事情（亚丝娜POV）的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 121, "indent": 1, "parameters": [252, 252, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 365]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 201, "indent": 1, "parameters": [0, 305, 21, 33, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [252, 252, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 369]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 201, "indent": 1, "parameters": [0, 305, 11, 7, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/16胡闹厨房"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/16亚丝娜和猪田在据点厨房里发生事情（亚丝娜POV）的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 121, "indent": 1, "parameters": [252, 252, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 365]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 201, "indent": 1, "parameters": [0, 305, 21, 33, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [252, 252, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 369]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 201, "indent": 1, "parameters": [0, 305, 11, 7, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 11}, {"id": 42, "name": "EV042", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 390}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜私服像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/17奇怪的梦(三)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/17奇怪的梦(三)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [256, 256, 0]}, {"code": 121, "indent": 1, "parameters": [98, 98, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 389]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 201, "indent": 1, "parameters": [0, 340, 16, 28, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [256, 256, 0]}, {"code": 121, "indent": 1, "parameters": [98, 98, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 389]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 1]}, {"code": 201, "indent": 1, "parameters": [0, 340, 25, 32, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 11}, {"id": 43, "name": "EV043", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 418}, "directionFix": true, "image": {"tileId": 0, "characterName": "fog亚丝娜浴衣", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/19按摩(二)"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 25]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "fog亚丝娜浴衣", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/19按摩(二)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/19亚丝娜第二次按摩的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [254, 254, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 30, 8, 14, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [254, 254, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 30, 8, 14, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "fog亚丝娜浴衣", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/19按摩(二)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/19亚丝娜第二次按摩的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [254, 254, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 30, 8, 14, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [254, 254, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 30, 8, 14, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 11}, {"id": 44, "name": "EV044", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 418}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 亚丝娜视角"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-48,-18]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 11}, {"id": 45, "name": "EV045", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 120}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 亚丝娜视角"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-48,-18]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 15}, {"id": 46, "name": "EV046", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 399}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/18厅堂腋香"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 15]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 399}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/18厅堂腋香"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/18亚丝娜和猪田在据点客厅里发生事情的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [80, 80, 0]}, {"code": 121, "indent": 1, "parameters": [257, 257, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 396]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 201, "indent": 1, "parameters": [0, 309, 21, 32, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [80, 80, 0]}, {"code": 121, "indent": 1, "parameters": [257, 257, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 396]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 201, "indent": 1, "parameters": [0, 309, 21, 32, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 321, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 399}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/18厅堂腋香"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/18亚丝娜和猪田在据点客厅里发生事情的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [80, 80, 0]}, {"code": 121, "indent": 1, "parameters": [257, 257, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 396]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 201, "indent": 1, "parameters": [0, 309, 21, 32, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [80, 80, 0]}, {"code": 121, "indent": 1, "parameters": [257, 257, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 396]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 201, "indent": 1, "parameters": [0, 309, 21, 32, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 399}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/18厅堂腋香"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/18亚丝娜和猪田在据点客厅里发生事情的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [80, 80, 0]}, {"code": 121, "indent": 1, "parameters": [257, 257, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 396]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 201, "indent": 1, "parameters": [0, 309, 21, 32, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [80, 80, 0]}, {"code": 121, "indent": 1, "parameters": [257, 257, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 396]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 201, "indent": 1, "parameters": [0, 309, 21, 32, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 11}, {"id": 47, "name": "EV047", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 418}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog裸", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/20三人共浴"]}, {"code": 111, "indent": 0, "parameters": [0, 335, 0]}, {"code": 122, "indent": 1, "parameters": [186, 186, 0, 0, 5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [186, 186, 0, 0, 25]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog裸", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/20三人共浴"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/20亚丝娜在浴室发生事件的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [258, 258, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 108, "indent": 1, "parameters": ["关闭自动淡入"]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 309, 11, 29, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 121, "indent": 1, "parameters": [258, 258, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 108, "indent": 1, "parameters": ["关闭自动淡入"]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 309, 11, 29, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog裸", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/20三人共浴"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/20亚丝娜在浴室发生事件的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [258, 258, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 108, "indent": 1, "parameters": ["关闭自动淡入"]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 309, 11, 29, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 121, "indent": 1, "parameters": [258, 258, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 108, "indent": 1, "parameters": ["关闭自动淡入"]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 309, 11, 29, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 11}, {"id": 48, "name": "EV048", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 457}, "directionFix": true, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/21兽人"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/21兽人战斗的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["进入战斗", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "进入战斗"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜情欲值 "]}, {"code": 102, "indent": 1, "parameters": [["情欲值低", "情欲值中", "情欲值高", "放弃回顾"], 3, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "情欲值低"]}, {"code": 122, "indent": 2, "parameters": [300, 300, 0, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "情欲值中"]}, {"code": 122, "indent": 2, "parameters": [300, 300, 0, 0, 20]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "情欲值高"]}, {"code": 122, "indent": 2, "parameters": [300, 300, 0, 0, 40]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "放弃回顾"]}, {"code": 122, "indent": 2, "parameters": [300, 300, 0, 0, 0]}, {"code": 119, "indent": 2, "parameters": ["放弃回顾"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 10, true]}, {"code": 129, "indent": 1, "parameters": [8, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 283, "indent": 1, "parameters": ["DirtField", "Mine"]}, {"code": 121, "indent": 1, "parameters": [69, 69, 0]}, {"code": 231, "indent": 1, "parameters": [2, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 1, "parameters": [69, "", 0, 0, 346, 400, 100, 100, 0, 0]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[69] : 设置动画序列 : 动画序列[6]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[69] : 等待动画序列加载完成"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[2] : 设置动画序列 : 动画序列[12]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[2] : 等待动画序列加载完成"]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 1, false]}, {"code": 301, "indent": 1, "parameters": [0, 8, false, false]}, {"code": 121, "indent": 1, "parameters": [69, 69, 1]}, {"code": 122, "indent": 1, "parameters": [300, 300, 0, 0, 0]}, {"code": 129, "indent": 1, "parameters": [1, 0, false]}, {"code": 129, "indent": 1, "parameters": [8, 1, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "放弃回顾"]}, {"code": 118, "indent": 1, "parameters": ["放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 26, "y": 11}, {"id": 49, "name": "EV049", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 457}, "directionFix": true, "image": {"tileId": 0, "characterName": "People5sleep", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/21恶魔瓦拉克"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/21恶魔瓦拉克战斗的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 223, "indent": 1, "parameters": [[255, 255, 255, 0], 10, false]}, {"code": 129, "indent": 1, "parameters": [8, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 283, "indent": 1, "parameters": ["DirtField", "Mine"]}, {"code": 121, "indent": 1, "parameters": [69, 69, 0]}, {"code": 231, "indent": 1, "parameters": [2, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[2] : 设置动画序列 : 动画序列[12]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[2] : 等待动画序列加载完成"]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 1, false]}, {"code": 301, "indent": 1, "parameters": [0, 10, false, false]}, {"code": 121, "indent": 1, "parameters": [69, 69, 1]}, {"code": 122, "indent": 1, "parameters": [115, 115, 0, 0, 0]}, {"code": 129, "indent": 1, "parameters": [1, 0, false]}, {"code": 129, "indent": 1, "parameters": [8, 1, false]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [232]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 15, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 118, "indent": 1, "parameters": ["放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 3, "y": 9}, {"id": 50, "name": "EV050", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 500}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/28白日宣淫"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 30]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/28白日宣淫"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/28亚丝娜和猪田在FOG的旅馆中发生事情的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [269, 269, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 246, 14, 8, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [269, 269, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 4]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 246, 14, 8, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/28白日宣淫"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/28亚丝娜和猪田在FOG的旅馆中发生事情的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [269, 269, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 246, 14, 8, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [269, 269, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 4]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 246, 14, 8, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 9}, {"id": 51, "name": "EV051", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 468}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜私服像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/27叫醒口交"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/27亚丝娜给桐人叫醒口交的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [271, 271, 0]}, {"code": 121, "indent": 1, "parameters": [77, 77, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 467]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 201, "indent": 1, "parameters": [0, 252, 21, 9, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [271, 271, 0]}, {"code": 121, "indent": 1, "parameters": [77, 77, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 467]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 201, "indent": 1, "parameters": [0, 252, 21, 9, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 9}, {"id": 52, "name": "EV052", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 486}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜私服像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/27郎情妾意"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/27桐人和亚丝娜恩爱缠绵的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [265, 265, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 484]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([252, 1, 'A'], true);"]}, {"code": 201, "indent": 1, "parameters": [0, 252, 19, 16, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [265, 265, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 486]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([252, 1, 'A'], true);"]}, {"code": 201, "indent": 1, "parameters": [0, 252, 20, 16, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 9}, {"id": 53, "name": "EV053", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 502}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/27走廊服务"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/27亚丝娜在妓院走廊为猪田处理性欲的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [267, 267, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 502]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([94, 1, 'A'], true);"]}, {"code": 201, "indent": 1, "parameters": [0, 94, 17, 13, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [267, 267, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 502]}, {"code": 121, "indent": 1, "parameters": [368, 368, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([94, 1, 'A'], true);"]}, {"code": 201, "indent": 1, "parameters": [0, 94, 8, 8, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 9}, {"id": 54, "name": "EV054", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 581}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/1初施圣礼"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 30]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/1初施圣礼"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/1忏悔室中发生事情的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [274, 274, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 578]}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 355, 11, 11, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [274, 274, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 579]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 201, "indent": 1, "parameters": [0, 355, 11, 11, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/1初施圣礼"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/1忏悔室中发生事情的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [274, 274, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 578]}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 355, 11, 11, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [274, 274, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 579]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 201, "indent": 1, "parameters": [0, 355, 11, 11, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 9}, {"id": 55, "name": "EV055", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 567}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/30客室残影"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/30桐人在据点卧室看到残影幻像的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [273, 273, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 567]}, {"code": 201, "indent": 1, "parameters": [0, 308, 33, 16, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [273, 273, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 567]}, {"code": 201, "indent": 1, "parameters": [0, 308, 33, 19, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 9}, {"id": 56, "name": "EV056", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 623}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜 居家服", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/3黑丝美足"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 20]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜 居家服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/3黑丝美足"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/3亚丝娜给猪田足交的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [901, 901, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 363, 22, 10, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [901, 901, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 365, 16, 9, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 83, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜 居家服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/3黑丝美足"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/3亚丝娜给猪田足交的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [901, 901, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 363, 22, 10, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [901, 901, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 365, 16, 9, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 9}, {"id": 57, "name": "EV057", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 634}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/4猪圈偷欢"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 30]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/4猪圈偷欢"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/4亚丝娜和猪田在据点外帐篷做爱的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [902, 902, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 363, 20, 10, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [902, 902, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 301, 17, 8, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 83, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/4猪圈偷欢"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/4亚丝娜和猪田在据点外帐篷做爱的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [902, 902, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 363, 20, 10, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [902, 902, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 301, 17, 8, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 7}, {"id": 58, "name": "EV058", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 653}, "directionFix": true, "image": {"tileId": 0, "characterName": "sho<PERSON>hu", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/5灵石洞窟"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/5桐人在灵石洞窟中独自探索的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], -2, -1, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [4, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 121, "indent": 1, "parameters": [904, 904, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 651]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([171, 6, 'A'], false);"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([171, 6, 'B'], false);"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([171, 6, 'C'], false);"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([171, 6, 'D'], false);"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([171, 181, 'A'], false);"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([171, 217, 'A'], false);"]}, {"code": 655, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([171, 217, 'B'], false);"]}, {"code": 201, "indent": 1, "parameters": [0, 171, 26, 43, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [924, 924, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [199, 199, 0, 0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 171, 32, 25, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 403, "indent": 0, "parameters": [6, null]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 7}, {"id": 59, "name": "EV059", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 656}, "directionFix": true, "image": {"tileId": 0, "characterName": "sho<PERSON>hu", "direction": 4, "pattern": 1, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/5魔化植物"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/5魔化植物战斗的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["进入战斗", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "进入战斗"]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 10, true]}, {"code": 122, "indent": 1, "parameters": [300, 300, 0, 0, 20000]}, {"code": 129, "indent": 1, "parameters": [8, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 283, "indent": 1, "parameters": ["DirtField", "<PERSON>"]}, {"code": 121, "indent": 1, "parameters": [69, 69, 0]}, {"code": 121, "indent": 1, "parameters": [426, 426, 1]}, {"code": 231, "indent": 1, "parameters": [2, "", 0, 0, 640, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 1, "parameters": [69, "", 0, 0, 346, 400, 100, 100, 0, 0]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[69] : 设置动画序列 : 动画序列[6]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[69] : 等待动画序列加载完成"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[2] : 设置动画序列 : 动画序列[12]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[2] : 等待动画序列加载完成"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[2] : 设置动画序列 : 动画序列[31]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[2] : 等待动画序列加载完成"]}, {"code": 301, "indent": 1, "parameters": [0, 13, false, false]}, {"code": 121, "indent": 1, "parameters": [69, 69, 1]}, {"code": 122, "indent": 1, "parameters": [300, 300, 0, 0, 0]}, {"code": 129, "indent": 1, "parameters": [1, 0, false]}, {"code": 129, "indent": 1, "parameters": [8, 1, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "放弃回顾"]}, {"code": 118, "indent": 1, "parameters": ["放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 9, "y": 7}, {"id": 60, "name": "EV060", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 730}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜修女", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/8菊穴调教"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 15]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜修女", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/8菊穴调教"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/8猪田在教堂的密室里用拉珠调教亚丝娜肛门的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [912, 912, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 363, 21, 16, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [912, 912, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 355, 11, 24, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 83, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜修女", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/8菊穴调教"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/8猪田在教堂的密室里用拉珠调教亚丝娜肛门的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [912, 912, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 363, 21, 16, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [912, 912, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 355, 11, 24, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 7}, {"id": 61, "name": "EV061", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 747}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/9肛之过级"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 10]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/9肛之过级"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/9亚丝娜和猪田在勇气试炼的途中尝试肛交的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [913, 913, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 373, 50, 24, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [913, 913, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 373, 50, 24, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 83, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/9肛之过级"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/9亚丝娜和猪田在勇气试炼的途中尝试肛交的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [913, 913, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 373, 50, 24, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [913, 913, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 373, 50, 24, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 7}, {"id": 62, "name": "EV062", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 769}, "directionFix": true, "image": {"tileId": 0, "characterName": "fog亚丝娜浴衣", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/10按摩(三)"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 30]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "fog亚丝娜浴衣", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/10按摩(三)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/10亚丝娜第三次按摩的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [917, 917, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 30, 8, 14, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [917, 917, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 30, 8, 14, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 83, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "fog亚丝娜浴衣", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/10按摩(三)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/10亚丝娜第三次按摩的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [917, 917, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 30, 8, 14, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [917, 917, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 30, 8, 14, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 7}, {"id": 63, "name": "EV063", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 776}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜泳装", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/11玉足镇炎"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/11亚丝娜在试炼中用足交为桐人舒缓烦躁的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [921, 921, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 775]}, {"code": 201, "indent": 1, "parameters": [0, 371, 56, 37, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [921, 921, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 776]}, {"code": 201, "indent": 1, "parameters": [0, 371, 56, 37, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 7}, {"id": 64, "name": "EV064", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 792}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜试炼像素月牙", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/11仪式献礼"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 30]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜试炼像素月牙", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/11仪式献礼"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/11亚丝娜和猪田在求子仪式中通过一系列特定行为向女神献礼回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件(一)", "仅限事件(二)", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [919, 919, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 378, 19, 31, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件(一)"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [919, 919, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 379, 23, 20, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "仅限事件(二)"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [919, 919, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 4]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 379, 23, 20, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 83, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜试炼像素月牙", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/11仪式献礼"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/11亚丝娜和猪田在求子仪式中通过一系列特定行为向女神献礼回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件(一)", "仅限事件(二)", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [919, 919, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 378, 19, 31, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件(一)"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [919, 919, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 379, 23, 20, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "仅限事件(二)"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [919, 919, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 4]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 379, 23, 20, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 7}, {"id": 65, "name": "EV065", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 799}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜 居家服", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/11吮茎为凭"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 20]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜 居家服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/11吮茎为凭"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/11亚丝娜同意和猪田第二天进行肛交并为他口交的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [920, 920, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 363, 14, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [920, 920, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 363, 14, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 83, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜 居家服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/11吮茎为凭"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/11亚丝娜同意和猪田第二天进行肛交并为他口交的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [920, 920, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 363, 14, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [920, 920, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 363, 14, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 5}, {"id": 66, "name": "EV066", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 801}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜fog裸", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/11温存赎愧"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/11亚丝娜在FOG与桐人做爱的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [923, 923, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 800]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 310, 19, 20, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [923, 923, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 801]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 310, 19, 20, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 5}, {"id": 67, "name": "EV067", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 814}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜白衬衫单马尾像素", "direction": 8, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/12猪笼当备"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 35]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜白衬衫单马尾像素", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/12猪笼当备"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/12亚丝娜在性交中正式同意成为猪田炮友的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件(一)", "仅限事件(二)", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [922, 922, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 365, 10, 13, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件(一)"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [922, 922, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 365, 10, 13, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "仅限事件(二)"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [922, 922, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 4]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 365, 10, 13, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 83, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜白衬衫单马尾像素", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/12猪笼当备"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/12亚丝娜在性交中正式同意成为猪田炮友的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件(一)", "仅限事件(二)", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [922, 922, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 365, 10, 13, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件(一)"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [922, 922, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 365, 10, 13, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "仅限事件(二)"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [922, 922, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 4]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 365, 10, 13, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 5}, {"id": 68, "name": "EV068", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 814}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜兔女郎", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/13玉兔驾豚"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 30]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜兔女郎", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/13玉兔驾豚"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/13亚丝娜穿着兔女郎装乘骑猪田的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [926, 926, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 363, 18, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [926, 926, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 363, 18, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 5}, {"id": 69, "name": "EV069", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 862}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜女仆装", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/14婢姬侍寝"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 30]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 52}, "directionFix": true, "image": {"tileId": 0, "characterName": "亚丝娜女仆装", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/14婢姬侍寝"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/14亚丝娜穿着女仆装被猪田后入的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 121, "indent": 1, "parameters": [927, 927, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 363, 18, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [927, 927, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 363, 18, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 5}, null]}