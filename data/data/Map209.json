{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 16, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 66, "width": 30, "data": [2816, 2816, 2816, 2816, 2840, 2064, 2048, 2048, 2072, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2064, 2048, 2048, 2072, 3720, 3705, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2088, 2056, 2048, 2072, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2088, 2056, 2048, 2072, 2862, 3712, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 2064, 2048, 2072, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 2064, 2048, 2050, 2084, 3697, 3716, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2088, 2056, 2050, 2084, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2088, 2056, 2048, 2072, 3720, 3705, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 2088, 2056, 2072, 2856, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 2064, 2048, 2050, 2084, 3697, 3716, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 2064, 2050, 2069, 2093, 2832, 2816, 2820, 2844, 2824, 2816, 2816, 2840, 2064, 2048, 2048, 2072, 3720, 3718, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2064, 2048, 2072, 2859, 2825, 2816, 2840, 2906, 2856, 2824, 2816, 2840, 2064, 2048, 2048, 2072, 2859, 2838, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2082, 2049, 2048, 2050, 2084, 2832, 2816, 2840, 2881, 2900, 2832, 2816, 2840, 2088, 2056, 2048, 2050, 2084, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2064, 2048, 2048, 2048, 2072, 2832, 2816, 2840, 2880, 2888, 2832, 2816, 2818, 2852, 2064, 2048, 2048, 2072, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2064, 2048, 2048, 2052, 2086, 2832, 2816, 2840, 2904, 2889, 2832, 2816, 2816, 2840, 2064, 2048, 2048, 2072, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2082, 2049, 2048, 2048, 2072, 2850, 2817, 2816, 2818, 2852, 2908, 2832, 2816, 2816, 2840, 2064, 2048, 2048, 2050, 2084, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2064, 2048, 2048, 2048, 2072, 2832, 2816, 2816, 2816, 2818, 2836, 2817, 2816, 2816, 2840, 2064, 2048, 2048, 2048, 2072, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2088, 2056, 2048, 2048, 2072, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2064, 2048, 2048, 2048, 2072, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2822, 2861, 2064, 2048, 2048, 2072, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2082, 2049, 2048, 2048, 2052, 2086, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2082, 2049, 2048, 2048, 2072, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2064, 2048, 2048, 2048, 2072, 2858, 3290, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2064, 2048, 2048, 2048, 2072, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2082, 2049, 2048, 2048, 2048, 2072, 2848, 3280, 2832, 2816, 2816, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3870, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3138, 3140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3120, 3110, 3149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3122, 3142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3148, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3870, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3918, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 221, 0, 0, 0, 0, 0, 0, 364, 364, 221, 0, 221, 0, 364, 0, 364, 0, 0, 0, 0, 0, 0, 221, 363, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 364, 364, 365, 0, 0, 0, 0, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 367, 0, 0, 0, 0, 0, 0, 267, 364, 364, 364, 0, 0, 0, 0, 364, 0, 365, 0, 0, 0, 0, 0, 0, 0, 363, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 290, 0, 364, 364, 365, 0, 0, 0, 0, 364, 364, 0, 0, 0, 0, 0, 0, 275, 0, 364, 0, 0, 0, 364, 364, 0, 0, 0, 0, 0, 0, 0, 364, 364, 364, 364, 0, 0, 0, 364, 364, 367, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 364, 365, 364, 364, 0, 0, 364, 367, 365, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 364, 364, 0, 0, 0, 0, 0, 0, 0, 364, 364, 364, 364, 0, 0, 0, 363, 364, 364, 0, 0, 0, 0, 0, 0, 363, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 364, 364, 0, 0, 0, 0, 0, 364, 364, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 364, 364, 0, 0, 0, 0, 0, 364, 364, 0, 0, 0, 0, 0, 0, 0, 363, 0, 0, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 364, 0, 0, 0, 0, 364, 0, 364, 269, 0, 0, 0, 0, 0, 0, 0, 364, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 364, 287, 0, 0, 0, 364, 0, 364, 364, 0, 0, 0, 0, 0, 0, 0, 364, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 364, 364, 0, 0, 0, 364, 0, 364, 364, 0, 0, 0, 0, 0, 0, 364, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 361, 364, 364, 0, 0, 0, 364, 364, 365, 269, 0, 0, 0, 0, 0, 0, 364, 0, 0, 364, 364, 0, 0, 0, 0, 0, 0, 0, 364, 364, 361, 364, 0, 0, 0, 364, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 363, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 364, 361, 364, 364, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 364, 0, 0, 364, 364, 0, 0, 0, 364, 361, 364, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 364, 365, 269, 0, 221, 0, 0, 0, 267, 363, 365, 263, 261, 275, 270, 371, 364, 365, 269, 0, 0, 296, 297, 298, 291, 371, 364, 364, 364, 364, 364, 365, 301, 0, 0, 0, 0, 0, 267, 363, 367, 357, 269, 0, 275, 270, 363, 365, 263, 261, 0, 304, 305, 306, 275, 270, 363, 364, 364, 364, 364, 365, 301, 0, 0, 0, 280, 281, 282, 371, 374, 365, 263, 261, 0, 267, 371, 364, 357, 269, 0, 0, 0, 0, 0, 267, 371, 364, 364, 364, 364, 365, 263, 285, 0, 0, 288, 289, 275, 270, 363, 367, 357, 263, 261, 275, 270, 363, 365, 263, 285, 196, 0, 280, 281, 282, 270, 363, 364, 364, 364, 367, 357, 293, 0, 0, 296, 297, 298, 267, 371, 374, 367, 357, 263, 261, 267, 363, 367, 357, 269, 0, 0, 288, 289, 290, 267, 363, 364, 364, 364, 364, 365, 263, 261, 0, 304, 305, 306, 275, 270, 363, 364, 367, 357, 269, 267, 371, 374, 364, 287, 261, 0, 296, 297, 298, 267, 363, 364, 364, 364, 364, 367, 357, 269, 0, 12, 13, 0, 0, 267, 371, 374, 375, 373, 269, 275, 270, 371, 374, 357, 269, 0, 304, 305, 306, 267, 371, 364, 364, 364, 364, 364, 365, 269, 0, 0, 0, 0, 0, 275, 270, 363, 365, 271, 277, 0, 275, 270, 363, 365, 269, 0, 0, 0, 0, 275, 270, 363, 364, 364, 364, 375, 373, 269, 0, 0, 0, 0, 0, 0, 291, 363, 365, 269, 0, 0, 259, 262, 363, 365, 269, 0, 0, 0, 0, 0, 267, 371, 364, 364, 375, 373, 271, 277, 0, 0, 0, 0, 204, 259, 262, 363, 365, 269, 0, 0, 267, 355, 364, 365, 287, 261, 0, 0, 5, 0, 275, 270, 363, 364, 365, 271, 277, 0, 280, 281, 282, 0, 0, 267, 355, 364, 365, 263, 261, 0, 267, 363, 364, 367, 357, 269, 0, 0, 0, 0, 259, 286, 363, 364, 365, 269, 0, 0, 288, 289, 290, 0, 0, 267, 363, 364, 367, 357, 269, 0, 267, 363, 364, 375, 373, 269, 0, 155, 0, 0, 267, 355, 364, 364, 365, 263, 261, 0, 296, 297, 298, 0, 283, 262, 363, 364, 375, 373, 301, 283, 262, 363, 375, 373, 271, 277, 0, 163, 0, 0, 267, 363, 364, 364, 367, 357, 301, 0, 304, 305, 306, 0, 321, 355, 366, 364, 365, 271, 277, 291, 355, 364, 365, 271, 277, 0, 0, 0, 0, 0, 267, 371, 364, 364, 364, 365, 301, 259, 260, 260, 261, 0, 291, 363, 364, 375, 373, 301, 0, 291, 363, 364, 365, 269, 0, 0, 0, 0, 32, 0, 275, 270, 363, 364, 364, 365, 301, 267, 355, 357, 263, 189, 299, 363, 364, 365, 295, 277, 0, 291, 363, 364, 365, 269, 14, 0, 0, 0, 40, 0, 0, 267, 363, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "game_ma<PERSON><PERSON><PERSON><PERSON>_7_event40", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 12, "y": 11}, {"id": 2, "name": "EV002莉法", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 45, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 547}, "directionFix": false, "image": {"tileId": 0, "characterName": "莉法FOG(2)", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 15}, {"id": 3, "name": "EV003诗乃", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 45, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 547}, "directionFix": false, "image": {"tileId": 0, "characterName": "诗乃fog像素", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 12}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "Evil", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离4 : 触发独立开关 : D"]}, {"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离7 : 离开范围时自动OFF"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "Evil", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 117, "indent": 0, "parameters": [325]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 2, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 2, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage2", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 5}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 171}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [113, 113, 0, 3, 3, 4, 0]}, {"code": 111, "indent": 0, "parameters": [1, 113, 1, 195, 5]}, {"code": 122, "indent": 1, "parameters": [195, 195, 0, 3, 3, 4, 0]}, {"code": 122, "indent": 1, "parameters": [113, 113, 0, 3, 3, 4, 0]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置-玩家 : 文本[\\c[21]LevelUP] : 样式[1] : 弹道[1] : 持续时间[120]"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 356, "indent": 1, "parameters": [">地图临时漂浮文字 : 简单临时对象 : 位置-玩家 : 文本[Lv\\v[195]] : 样式[1] : 弹道[1] : 持续时间[120]"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 12, "y": 10}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage2", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 14}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage2", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 11}, {"id": 8, "name": "EV008盗贼", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Evil", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 2}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "Evil", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离4 : 触发独立开关 : D"]}, {"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离7 : 离开范围时自动OFF"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "Evil", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 117, "indent": 0, "parameters": [325]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 2, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 2, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage2", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 1}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage2", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 7}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage2", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 2}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage2", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 27, "y": 14}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "Evil", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离4 : 触发独立开关 : D"]}, {"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离7 : 离开范围时自动OFF"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 2, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 171}, "directionFix": false, "image": {"tileId": 0, "characterName": "Evil", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 117, "indent": 0, "parameters": [325]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 2, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 2, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage2", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 11}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 105, 8, 35, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 3, "y": 0}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 105, 19, 31, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 12, "y": 0}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 105, 31, 31, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 23, "y": 0}, {"id": 17, "name": "EV017", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [35]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 14, "parameters": [1, -2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [1, -2], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 212, "indent": 0, "parameters": [8, 11, false]}, {"code": 213, "indent": 0, "parameters": [8, 1, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": 0}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 15, "parameters": [35], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [35], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 40, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 16, "indent": null}, {"code": 41, "parameters": ["Damage2", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["Damage2", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 15, "parameters": [5], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["PushGab 43 \\c[8]给我站住！"]}, {"code": 213, "indent": 0, "parameters": [3, 3, true]}, {"code": 356, "indent": 0, "parameters": ["PushGab 71 \\c[8]你不去清怪，跑到我这边来干嘛？"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 213, "indent": 0, "parameters": [2, 5, true]}, {"code": 356, "indent": 0, "parameters": ["PushGab 43 \\c[8]我那边已经搞定了，不用你操心。比起这个破任务……"]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 356, "indent": 0, "parameters": ["PushGab 43 \\c[8]你到底想干什么？"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 213, "indent": 0, "parameters": [3, 3, true]}, {"code": 356, "indent": 0, "parameters": ["PushGab 71 \\c[8]想干什么？一时兴起罢了~"]}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["PushGab 43 \\c[8]呵！新任六兽一时兴起去讨伐盗贼？扯淡！"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [35], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [35], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["PushGab 71 \\c[8]没骗你哦，确实是一时兴起……"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 356, "indent": 0, "parameters": ["PushGab 71 \\c[8]对于把那条毒蛇迷得神魂颠倒的妓院头牌，我想搞好关系而已。"]}, {"code": 230, "indent": 0, "parameters": [270]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["PushGab 71 \\c[8]所以，别那么见外嘛，桐谷直叶~"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 213, "indent": 0, "parameters": [2, 6, true]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["PushGab 43 \\c[8]我和那家伙断掉很久了！给你独占！我不稀罕！"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [3, 5, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": ["PushGab 71 \\c[8]呵，独占他…？就算是，还需要你批准吗？"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["PushGab 71 \\c[8]只不过，之前累积的不爽总要想办法释放一下。"]}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 111, "indent": 0, "parameters": [1, 161, 0, 2, 1]}, {"code": 213, "indent": 1, "parameters": [3, 4, false]}, {"code": 356, "indent": 1, "parameters": ["PushGab 71 \\c[8]啊对了，你的哥哥，我也很感兴趣呢~"]}, {"code": 230, "indent": 1, "parameters": [150]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [2, 5, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 45, "parameters": ["$gameMap.event(3).setPriorityType(0);"], "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 37, "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(3).setPriorityType(0);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 37, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["PushGab 43 \\c[8]你这疯女人不要乱来！"]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 213, "indent": 0, "parameters": [3, 7, true]}, {"code": 356, "indent": 0, "parameters": ["PushGab 71 \\c[8]疯女人？我吗？比不过一天到晚想着侵犯哥哥的病娇妹妹吧~"]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 213, "indent": 0, "parameters": [2, 11, true]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["PushGab 43 \\c[8]你…！！！如果你觉得我好欺负，就大错特错了！"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 213, "indent": 0, "parameters": [3, 6, true]}, {"code": 356, "indent": 0, "parameters": ["PushGab 71 \\c[8]谁要欺负你了？"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 4, "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 356, "indent": 0, "parameters": ["PushGab 71 \\c[8]好了，快点回去你那条路吧。耽误了任务，损害的是你男人的身体。"]}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 213, "indent": 0, "parameters": [2, 7, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 14, "parameters": [-2, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [-2, -1], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 14, "parameters": [-2, -1], "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 37, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [-2, -1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 0, "parameters": [385, 385, 0]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 37, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 8}]}