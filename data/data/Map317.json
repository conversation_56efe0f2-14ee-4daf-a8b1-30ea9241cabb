{"autoplayBgm": false, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "spot_underground", "pan": 0, "pitch": 100, "volume": 45}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 40, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "!地下街", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 32, "width": 60, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 791, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 887, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 886, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 887, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 193, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 144, 145, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 78, 79, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 192, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 73, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 284, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 284, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 886, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 889, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 284, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 284, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 967, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 878, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 875, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 66, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 791, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 894, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 879, 0, 0, 0, 0, 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1013, 0, 0, 1013, 0, 0, 1013, 0, 0, 0, 0, 0, 0, 967, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 244, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 967, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 0, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 10, 10, 10, 10, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 10, 10, 10, 10, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 0, 0, 10, 10, 10, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 0, 0, 10, 0, 0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 0, 0, 10, 10, 10, 0, 0, 0, 0, 0, 10, 10, 10, 0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 10, 10, 0, 10, 10, 0, 10, 0, 10, 0, 0, 10, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 10, 10, 10, 10, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 317 2 地下街-光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 317 3 地下街-阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 241, "indent": 0, "parameters": [{"name": "spot_underground", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 223, "indent": 0, "parameters": [[-17, -68, 0, 51], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 298, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 121, "indent": 0, "parameters": [298, 298, 1]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 26, "y": 2}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 519}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 202, 47, 36, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 214, 47, 36, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 43, "y": 0}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 519}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 202, 47, 36, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 214, 47, 36, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 44, "y": 0}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 519}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 202, 47, 36, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 214, 47, 36, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 45, "y": 0}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 236}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : star : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 12"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 237}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 21}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 717, 0]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 499, 0]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<壮汉>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac级别不够。"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 41, "y": 22}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 519}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 202, 47, 36, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 214, 47, 36, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 46, "y": 0}, {"id": 8, "name": "EV008廉价妓女", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 236}, "directionFix": false, "image": {"tileId": 0, "characterName": "dansi2", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<站街妓女>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac像我这种生意不好的低级妓女，来街上拉客是很必要的。"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 1, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 242}, "directionFix": false, "image": {"tileId": 0, "characterName": "dansi2", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<站街妓女>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac像我这种生意不好的低级妓女，来街上拉客是很必要的。"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 243}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "dansi2", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 500, 4]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 495, 1]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).turnTowardCharacter($gameMap.event(this.eventId()));"]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 7]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 117, "indent": 3, "parameters": [243]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [8, 3, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [31, 3, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 242}, "directionFix": false, "image": {"tileId": 0, "characterName": "dansi2", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<站街妓女>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac像我这种生意不好的低级妓女，来街上拉客是很必要的。"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 19, "y": 23}, {"id": 9, "name": "EV009克莱因", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 242}, "directionFix": false, "image": {"tileId": 0, "characterName": "克莱因游戏", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 242}, "directionFix": false, "image": {"tileId": 0, "characterName": "克莱因游戏", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : Quest_A : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 11"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离7 : 触发独立开关 : D"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 242}, "directionFix": false, "image": {"tileId": 0, "characterName": "克莱因游戏", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 243]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 123, "indent": 0, "parameters": ["D", 1]}, {"code": 201, "indent": 0, "parameters": [0, 317, 14, 23, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 243}, "directionFix": false, "image": {"tileId": 0, "characterName": "克莱因游戏", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 244}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": false, "image": {"tileId": 0, "characterName": "克莱因游戏", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>持续动作 : 上下震动 : 周期[15] : 震动幅度[1]"]}, {"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离3 : 触发独立开关 : C"]}, {"code": 108, "indent": 0, "parameters": ["=>被触发 : 与玩家距离3 : 离开范围时自动OFF"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": false, "image": {"tileId": 0, "characterName": "克莱因游戏", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>持续动作 : 上下震动 : 周期[15] : 震动幅度[1]"]}, {"code": 111, "indent": 0, "parameters": [0, 365, 1]}, {"code": 355, "indent": 1, "parameters": ["$gamePlayer.turnTowardCharacter($gameMap.event(9));"]}, {"code": 213, "indent": 1, "parameters": [-1, 2, false]}, {"code": 122, "indent": 1, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈惊讶2", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](克莱因桑？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac他这是在干嘛……？)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](在偷窥吗？)"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [-1, 1, false]}, {"code": 121, "indent": 1, "parameters": [369, 369, 0]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈意外脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](手还在裤子里？！不是吧……！！！)"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 355, "indent": 1, "parameters": ["$gamePlayer.followers().follower(0).turnTowardCharacter($gameMap.event(9));"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 355, "indent": 1, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(3);"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 355, "indent": 1, "parameters": ["$gamePlayer.followers().follower(0).turnTowardPlayer();"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac学姐，你看那个红头发！好像正在当街打飞机耶！真牛……！"]}, {"code": 213, "indent": 1, "parameters": [-1, 12, false]}, {"code": 355, "indent": 1, "parameters": ["$gamePlayer.turnTowardCharacter($gamePlayer.followers().follower(0));"]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈焦急脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嘘！！！小声点！跟我们没关系，不要过去就是了！"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哦哦，好的好的。"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, false]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [365, 365, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 213, "indent": 1, "parameters": [-1, 8, false]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 500}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 242}, "directionFix": false, "image": {"tileId": 0, "characterName": "克莱因游戏", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 715}, "directionFix": false, "image": {"tileId": 0, "characterName": "克莱因游戏", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 716}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 722}, "directionFix": false, "image": {"tileId": 0, "characterName": "克莱因游戏", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 726}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 23}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 236}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 117, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([317, 18, 'A'], true);"]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(1);"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝默认", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君，进门的同时喝药水隐身。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac明白。"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["NF_REM_NPC 1"]}, {"code": 250, "indent": 0, "parameters": [{"name": "drink_maou17", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 250, "indent": 0, "parameters": [{"name": "drink_maou17", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 42, "parameters": [100], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [100], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 121, "indent": 0, "parameters": [298, 298, 0]}, {"code": 201, "indent": 0, "parameters": [0, 320, 15, 16, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 242}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 499, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac现在进入妓院调查吗？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, -1, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 205, "indent": 2, "parameters": [0, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 213, "indent": 2, "parameters": [-1, 8, false]}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).turnTowardPlayer();"]}, {"code": 230, "indent": 2, "parameters": [50]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(3);"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac学姐还是决定进去看看？"]}, {"code": 122, "indent": 2, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 2, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 2, "parameters": [2, "FOG明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 2, "parameters": [135]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac嗯……"]}, {"code": 401, "indent": 2, "parameters": ["\\dac只是以防万一。"]}, {"code": 235, "indent": 2, "parameters": [2]}, {"code": 205, "indent": 2, "parameters": [22, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 39, "indent": null}]}, {"code": 203, "indent": 2, "parameters": [22, 0, 11, 24, 8]}, {"code": 205, "indent": 2, "parameters": [22, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 2, "parameters": [{"code": 40, "indent": null}]}, {"code": 356, "indent": 2, "parameters": ["NF_REM_NPC 1"]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 16, "indent": null}]}, {"code": 231, "indent": 2, "parameters": [2, "FOG明日奈心事脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 2, "parameters": [135]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac进去前先隐身吧。"]}, {"code": 401, "indent": 2, "parameters": ["\\dac免得招惹不必要的麻烦。"]}, {"code": 235, "indent": 2, "parameters": [2]}, {"code": 205, "indent": 2, "parameters": [22, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 2, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac好嘞~"]}, {"code": 356, "indent": 2, "parameters": [">行走图额外位置偏移量 : 事件[22] : 像素偏移[0,-36] : 时间[30]"]}, {"code": 205, "indent": 2, "parameters": [22, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 34, "indent": null}]}, {"code": 212, "indent": 2, "parameters": [22, 45, false]}, {"code": 205, "indent": 2, "parameters": [22, {"list": [{"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [240], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [220], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [190], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [150], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [100], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 42, "parameters": [240], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 42, "parameters": [220], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 42, "parameters": [190], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 42, "parameters": [150], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 42, "parameters": [100], "indent": null}]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [240], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [220], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [190], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [150], "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 42, "parameters": [100], "indent": null}, {"code": 15, "parameters": [20], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 42, "parameters": [240], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 42, "parameters": [220], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 42, "parameters": [190], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 42, "parameters": [150], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 42, "parameters": [100], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [20], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 19, "indent": null}]}, {"code": 223, "indent": 2, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 122, "indent": 2, "parameters": [100, 100, 0, 0, 500]}, {"code": 250, "indent": 2, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 2, "parameters": [298, 298, 0]}, {"code": 201, "indent": 2, "parameters": [0, 320, 15, 16, 8, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 1, "indent": null}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 320, 15, 16, 8, 0]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 11, "y": 23}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage1", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<醉鬼>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我..我喝不下了..."]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage2", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 213, "indent": 0, "parameters": [0, 10, true]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 42, "y": 7}, {"id": 12, "name": "EV003紫烟", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 242}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$fsm_Object04", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[6,36]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 243}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 242}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$fsm_Object04", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[6,36]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 22}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 15}, "directionFix": false, "image": {"tileId": 0, "characterName": "RTPRecreation_VX2", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<粗犷的玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac性感的小妞~♥♥"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": false, "image": {"tileId": 0, "characterName": "RTPRecreation_VX2", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 500, 4]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 495, 1]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).turnTowardCharacter($gameMap.event(this.eventId()));"]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 1]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 213, "indent": 3, "parameters": [0, 4, false]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<粗犷的玩家>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac美女，你怎么和个肥猪在一起，"]}, {"code": 401, "indent": 3, "parameters": ["\\dac像我这样的帅哥才配得上你嘛！"]}, {"code": 213, "indent": 3, "parameters": [-1, 5, true]}, {"code": 122, "indent": 3, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 3, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac有些人外表稍差，但心地善良。"]}, {"code": 401, "indent": 3, "parameters": ["\\dac有些人金玉其外，却败絮其中。"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(3);"]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [0, 2, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<粗犷的玩家>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac什么里里外外的？啥意思？"]}, {"code": 213, "indent": 3, "parameters": [-1, 7, false]}, {"code": 205, "indent": 3, "parameters": [-1, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈叹气", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac没什么，只是拒绝你的邀请而已。"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 205, "indent": 3, "parameters": [-1, {"list": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}]}, {"code": 213, "indent": 3, "parameters": [0, 9, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<粗犷的玩家>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac哦哦，你是说暂时没时间对吧？"]}, {"code": 401, "indent": 3, "parameters": ["\\dac那等你有空了，随时来找我哦~"]}, {"code": 213, "indent": 3, "parameters": [-1, 6, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [0, 4, true]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [0, 8, true]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 45, "y": 11}, {"id": 14, "name": "EV014壮汉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "akunin2", "direction": 2, "pattern": 1, "characterIndex": 5}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 717, 0]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<壮汉>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac高级以上才可进入。"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 40, "y": 22}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 242}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 12, false]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(9).requestBalloon(1);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(9).requestBalloon(1);"], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac任务完成。抱歉久等了……"]}, {"code": 213, "indent": 0, "parameters": [-1, 2, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac怎么就你一个人，爱丽丝小姐呢？"]}, {"code": 213, "indent": 0, "parameters": [9, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac别提了，出了点意外，被妓院老板叫到办公室了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说是什么妓女等级考试。我去他妈的！"]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac会不会有危险？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac危险不至于。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那老头虽然猥琐，但一副无能为力的样子。"]}, {"code": 213, "indent": 0, "parameters": [-1, 9, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac办公室大致位置在哪？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我们也许可以通过窗户，或着隔着墙壁获得一些信息。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac估计没戏，老板办公室就是前台旁边那个门进去。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac位置肯定在妓院中心，哪会有窗户？"]}, {"code": 213, "indent": 0, "parameters": [-1, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这样吗……？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我还是想办法再进妓院看看……"]}, {"code": 213, "indent": 0, "parameters": [9, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不要再进去了吧……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac暴露了怎么办？那才真是前功尽弃了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人你太爱操心。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac即使是地下街，游戏的保护机制也没法突破。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝又异常免疫，应该没问题。"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但毕竟是敌人的地盘，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac任务途中出现这种意外，我觉得还是不能大意。"]}, {"code": 213, "indent": 0, "parameters": [9, 5, false]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那还能怎么办？爱丽丝自己都说她没事！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我总不能为了面子不顾大局地胡闹一番吧？"]}, {"code": 213, "indent": 0, "parameters": [-1, 12, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac抱歉……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我忽略了你才是最难受的……"]}, {"code": 213, "indent": 0, "parameters": [9, 11, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没事没事，刚才我那个态度也不是针对兄弟你……"]}, {"code": 213, "indent": 0, "parameters": [9, 5, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac而是生那个色老头的气！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac算了，没辙。说到底也不过是游戏而已！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac最坏最坏，爱丽丝也可以强制登出嘛~"]}, {"code": 213, "indent": 0, "parameters": [9, 9, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜应该还在等你回去吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你先收工啦，我在这里等她就好。"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我们毕竟是一个小队，共进退是最基本的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac万一真有需要，多个人，也多份保险。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我陪你一起等她。"]}, {"code": 213, "indent": 0, "parameters": [9, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，那行吧。"]}, {"code": 213, "indent": 0, "parameters": [9, 9, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac等的时候要不要也来一根？"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我虽然成年了，但还是学生，你记得的吧？"]}, {"code": 213, "indent": 0, "parameters": [9, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac忘了你妻管严。"]}, {"code": 213, "indent": 0, "parameters": [-1, 11, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我才不是什么妻管严！"]}, {"code": 213, "indent": 0, "parameters": [-1, 9, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不过说到这个……我要先去跟亚丝娜报备一下，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac之前说过会早点回去的。"]}, {"code": 121, "indent": 0, "parameters": [32, 32, 0]}, {"code": 111, "indent": 0, "parameters": [0, 22, 0]}, {"code": 356, "indent": 1, "parameters": [">地图永久漂浮文字 : 漂浮文字[2] : 修改样式属性-刷新内容文本"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [9, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这还不是妻管严……"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": 0}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 33, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "LNSM_SE08_Sense8", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 250, "indent": 0, "parameters": [{"name": "LNSM_SE08_Sense8", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 250, "indent": 0, "parameters": [{"name": "LNSM_SE08_Sense8", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 250, "indent": 0, "parameters": [{"name": "koukaongen_022", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 修改聚焦偏移 : 位置[336,0]"]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 500, 250, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 500, 250, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[11]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[地下街通讯正常]"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君！你准备回来了吗？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [-1, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还没有。出了点意外，比预想的要晚一些。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这样啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那大概什么时候结束呢？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在还不确定。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹和猪田呢？"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[地下街通讯骚扰]"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹刚刚走的。猪田……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田和莉兹一起走的。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这样……那亚丝娜，你不用等我了， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 先下线吧，早点休息。 "]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[地下街通讯正常]"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 先等一下。刚才我们几个讨论了明天的行程，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 本来准备你回来以后再跟你确认一下的。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我现在简单说一下，你方便吗？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，我现在有空，你说吧。"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[地下街通讯骚扰]"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac明天中午在据点集合，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们通过莉兹的传送道具去直接目的地。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我们中午一起吃饭吧？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}别闹啦~"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[地下街通讯正常]"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [-1, 2, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac别闹……？啊？阿姨连周末一起吃午饭也不同意吗？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呃，不是啦……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac明天的话，爸爸上午会带我去银座接见企划部的高层，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac中午要和他们一起吃饭。所以不能和你一起……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那个，桐人君，虽然是一个人吃， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还是要记得吃好一点哦~ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 为了增加沉浸感，我们打算中途都不下线， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 并且在FOG里过夜。 "]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好想法。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac Roger that。 "]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你今天如果弄晚了，明早好好睡个懒觉吧。 "]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[地下街通讯骚扰]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}唉……怎么又……？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [-1, 2, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac什么怎么了？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊，没什么，刚才我不小心把调料弄洒了…… "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人君，呃……我想下线之前先去洗个澡，免得耽误明天出发。 "]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，你去吧。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac提前晚安，桐人君~ "]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 250, "indent": 0, "parameters": [{"name": "koukaongen_004", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 修改聚焦偏移 : 位置[0,0]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 121, "indent": 0, "parameters": [32, 32, 1]}, {"code": 111, "indent": 0, "parameters": [0, 22, 0]}, {"code": 356, "indent": 1, "parameters": [">地图永久漂浮文字 : 漂浮文字[2] : 修改样式属性-刷新内容文本"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac回去找克莱因吧。"]}, {"code": 122, "indent": 0, "parameters": [79, 79, 0, 4, "\"亚丝娜的公寓\""]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 356, "indent": 0, "parameters": ["setBalloonLoop 9 0"]}, {"code": 108, "indent": 0, "parameters": ["开启克莱因范围触发"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([317, 9, 'A'], true);"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac危险吗……？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}爱丽丝如果正在被那个老头肏……唔……"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [9, 8, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [9, 15, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [9, 7, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": ["setBalloonLoop 9 11"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 243}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 203, "indent": 0, "parameters": [17, 0, 11, 24, 2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-17, -68, 0, 51], 60, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [17, 8, true]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 213, "indent": 0, "parameters": [-1, 10, false]}, {"code": 230, "indent": 0, "parameters": [80]}, {"code": 213, "indent": 0, "parameters": [9, 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["桐人", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人", 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [9, 4, false]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 14, "parameters": [0, 1], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝，你终于出来了！"]}, {"code": 213, "indent": 0, "parameters": [17, 7, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝失落脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这里说话不方便……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac先去临时据点……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯。"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [17, 17, 1]}, {"code": 201, "indent": 0, "parameters": [0, 250, 9, 14, 6, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 244}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 21}, {"id": 16, "name": "EV016舞厅门口舞女", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "<PERSON> a<PERSON>", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<兔女郎>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac请不要和我搭话，否则我们都会有危险……"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 42, "y": 22}, {"id": 17, "name": "EV017爱丽丝", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 243}, "directionFix": false, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 244}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 49, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 243}, "directionFix": false, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 715}, "directionFix": false, "image": {"tileId": 0, "characterName": "爱丽丝像素（面具）", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 2, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 59, "y": 0}, {"id": 18, "name": "EV018", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 236}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 236}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 236}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(2);"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 47 你们觉不觉得这个守门的和上次的感觉不大一样啊。"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 356, "indent": 0, "parameters": ["PushGab 10 可能是同样的衣服不同的人吧。"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(7);"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 47 不，虽然带了面具，但那双眼睛和之前的人一摸一样。"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(1).requestBalloon(3);"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 61 那大概是心情不一样吧？别多想了，不是什么大事~"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(8);"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 236}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 242}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 496}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [8, "", 0, 0, 346, 400, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[8] : 设置动画序列 : 动画序列[28]"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 317 2 地下街-光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 317 3 地下街-阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 356, "indent": 0, "parameters": ["SetArray 122 0 0"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 41, "parameters": ["亚丝娜", 4], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 121, "indent": 0, "parameters": [43, 43, 0]}, {"code": 203, "indent": 0, "parameters": [22, 0, 44, 5, 6]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[8] : 等待动画序列加载完成"]}, {"code": 241, "indent": 0, "parameters": [{"name": "spot_underground", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-17, -68, 0, 51], 30, true]}, {"code": 213, "indent": 0, "parameters": [22, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嘿嘿，学姐，顺利进来了哦~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac我很厉害吧？"]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈失望", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐，妓院就在那边，我带你过去。"]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 12, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈惊讶", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac等一下！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac去什么妓院啊？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君又不一定是去那里……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [22, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呃…不是吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人学长他一般都是直奔那里啊……"]}, {"code": 213, "indent": 0, "parameters": [-1, 7, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [22, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦嚯！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac刚才都是我瞎猜的，学姐你不要在意。"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈失落", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯……我们四处看看吧。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac好~"]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 玩家 : 移动到 : 相对坐标[0,0]"]}, {"code": 213, "indent": 0, "parameters": [22, 3, true]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 37, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 27 1"]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 2]}, {"code": 117, "indent": 0, "parameters": [110]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 497]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 497}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 498}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [5]}, {"code": 224, "indent": 0, "parameters": [[255, 136, 221, 170], 60, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "puu10", "volume": 60, "pitch": 90, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [8, "", 0, 0, 346, 400, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">图片动态遮罩板A : 简单透视镜 : 图片[8] : 样式[20]"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动态遮罩板A : 图片[8] : 启用该插件的动态遮罩板"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[8] : 设置动画序列 : 动画序列[28]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[8] : 播放简单状态元集合 : 集合[骚扰闪图]"]}, {"code": 231, "indent": 0, "parameters": [5, "剧情小图背景3", 0, 0, -816, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [6, "剧情小图背景1", 0, 0, -29, -7, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [7, "剧情小图背景2", 0, 0, 29, 7, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [6, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 0, "parameters": [7, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 0, "parameters": [8, 0, 0, 0, 375, 407, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 0, "parameters": [5, 0, 0, 0, -20, 0, 100, 100, 255, 0, 15, true]}, {"code": 232, "indent": 0, "parameters": [5, 0, 0, 0, 20, 0, 100, 100, 255, 0, 30, false]}, {"code": 232, "indent": 0, "parameters": [6, 0, 0, 0, 58, 14, 100, 100, 0, 0, 45, false]}, {"code": 232, "indent": 0, "parameters": [7, 0, 0, 0, -29, -7, 100, 100, 0, 0, 45, false]}, {"code": 232, "indent": 0, "parameters": [5, 0, 0, 0, 836, 0, 100, 100, 0, 0, 45, false]}, {"code": 232, "indent": 0, "parameters": [8, 0, 0, 0, 404, 414, 100, 100, 0, 0, 60, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 235, "indent": 0, "parameters": [5]}, {"code": 235, "indent": 0, "parameters": [6]}, {"code": 235, "indent": 0, "parameters": [7]}, {"code": 235, "indent": 0, "parameters": [8]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).turnTowardPlayer();"]}, {"code": 117, "indent": 0, "parameters": [122]}, {"code": 117, "indent": 0, "parameters": [131]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 213, "indent": 0, "parameters": [-1, 5, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.turnTowardCharacter($gamePlayer.followers().follower(0));"]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈new", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG明日奈气鼓鼓.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田君！刚才过来的路上我就说过了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我现在没心情陪你恶作剧！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(12);"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac咿！学姐对不起！刚刚完全是无意识的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在开始保证注意！别生气，求你了……！"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 5], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈妥协", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](算了，明明是我拜托猪田君帮忙的， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在不是对他这种孩子气的恶作剧生气的时候……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜", 4], "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(7);"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac噗咿……"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈心事脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac抱歉…不该对你那么凶……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唉……但是现在不是闹着玩的时候。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 11, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈掩饰", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我等会再补偿你吧……总之，先忍忍。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(4);"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac好耶！"]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 499]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 499}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 27, "y": 2}, {"id": 19, "name": "EV019", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 242}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 爱心 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 7"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 243}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 242}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 爱心 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 4"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 18, "y": 14}, {"id": 20, "name": "EV020", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 242}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 245, "indent": 0, "parameters": [{"name": "D_激しめにフェラ2", "volume": 50, "pitch": 100, "pan": -20}]}, {"code": 356, "indent": 0, "parameters": ["audiosource bgs 0"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 243}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 242}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 245, "indent": 0, "parameters": [{"name": "D_激しめにフェラ2", "volume": 50, "pitch": 100, "pan": -20}]}, {"code": 356, "indent": 0, "parameters": ["audiosource bgs 0"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 19, "y": 15}, {"id": 21, "name": "EV021", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 242}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [6, -1, 4]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 117, "indent": 1, "parameters": [207]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null}]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 117, "indent": 1, "parameters": [6]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 242}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [6, -1, 4]}, {"code": 241, "indent": 1, "parameters": [{"name": "spot_underground", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[14]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[爱丽丝口交影绘]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 243}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 242}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [6, -1, 4]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 117, "indent": 1, "parameters": [207]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null}]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 117, "indent": 1, "parameters": [6]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [6, -1, 4]}, {"code": 241, "indent": 1, "parameters": [{"name": "spot_underground", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "D_激しめにフェラ2", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[14]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[爱丽丝口交影绘]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 18, "y": 15}, {"id": 22, "name": "EV022猪田", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 43, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头游戏服", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 59, "y": 1}, {"id": 23, "name": "EV023站街妓女", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 236}, "directionFix": false, "image": {"tileId": 0, "characterName": "dansi2", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<站街妓女>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac5000一次，30000包夜~"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 245}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": false, "image": {"tileId": 0, "characterName": "dansi2", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 500, 4]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 495, 1]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).turnTowardCharacter($gameMap.event(this.eventId()));"]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 5]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.followers().follower(0).turnTowardCharacter($gameMap.event(23));"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<站街妓女>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac8000一次，50000包夜~"]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(6);"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac\\}这种货色也好意思涨价？不知羞耻！"]}, {"code": 213, "indent": 3, "parameters": [-1, 2, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.turnTowardCharacter($gamePlayer.followers().follower(0));"]}, {"code": 122, "indent": 3, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 3, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈惊讶2", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac猪田君？你说什么？"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(12);"]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac我说…我说就算是游戏，裸体站在这里拉客也太不知羞耻了！"]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈失落", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac确实。"]}, {"code": 401, "indent": 3, "parameters": ["\\dac但可能是她有自己的苦衷吧……"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 205, "indent": 3, "parameters": [0, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 3, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 213, "indent": 3, "parameters": [0, 5, false]}, {"code": 205, "indent": 3, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 45, "parameters": ["$gamePlayer.turnTowardCharacter($gameMap.event(23));"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 3, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 45, "parameters": ["$gamePlayer.turnTowardCharacter($gameMap.event(23));"], "indent": null}]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<站街妓女>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac苦衷你妈啊！老娘就是喜欢做爱喜欢赚钱？要你管！"]}, {"code": 401, "indent": 3, "parameters": ["\\dac我站街怎么了？当妓女怎么了？比你这假正经的绿茶婊强！ "]}, {"code": 213, "indent": 3, "parameters": [-1, 6, true]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac呃……"]}, {"code": 401, "indent": 3, "parameters": ["\\dac猪田君，我们走吧……"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [0, 5, false]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<站街妓女>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac假正经的绿茶婊能不能死远点，在我这里晃来晃去，"]}, {"code": 401, "indent": 3, "parameters": ["\\dac想抢我生意啊？"]}, {"code": 213, "indent": 3, "parameters": [-1, 6, false]}, {"code": 205, "indent": 3, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<站街妓女>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac8000一次，50000包夜~"]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 236}, "directionFix": false, "image": {"tileId": 0, "characterName": "dansi2", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<站街妓女>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac5000一次，30000包夜~"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 13}, {"id": 24, "name": "EV024影像店门口NPCD", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor2", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<地下街玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac低级通行证没有人权啊。"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor2", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 500, 4]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 495, 1]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).turnTowardCharacter($gameMap.event(this.eventId()));"]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 6]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 117, "indent": 3, "parameters": [242]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [24, 3, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [25, 3, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 10}, {"id": 25, "name": "EV025影像店门口NPCE", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor2", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<地下街玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唉,想要高级证啊……"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor2", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 500, 4]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 495, 1]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).turnTowardCharacter($gameMap.event(this.eventId()));"]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 6]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 117, "indent": 3, "parameters": [242]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [24, 3, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [25, 3, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 10}, {"id": 26, "name": "EV026酒馆门口NPCA", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "akunin3", "direction": 6, "pattern": 1, "characterIndex": 5}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<地下街玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac最近真不爽。"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "akunin3", "direction": 6, "pattern": 1, "characterIndex": 5}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 500, 4]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 495, 1]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).turnTowardCharacter($gameMap.event(this.eventId()));"]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 2]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 117, "indent": 3, "parameters": [241]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [26, 4, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [27, 3, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 31, "y": 11}, {"id": 27, "name": "EV027酒馆门口NPCB", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "akunin3", "direction": 4, "pattern": 1, "characterIndex": 6}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<地下街玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac得想办法找点乐子……"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "akunin3", "direction": 4, "pattern": 1, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 500, 4]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 495, 1]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).turnTowardCharacter($gameMap.event(this.eventId()));"]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 2]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 117, "indent": 3, "parameters": [241]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [26, 4, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [27, 3, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 32, "y": 11}, {"id": 28, "name": "EV028树下NPCC", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "akunin3", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<地下街玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac赚钱！想办法赚钱！"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": false, "image": {"tileId": 0, "characterName": "akunin3", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 500, 4]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 495, 1]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).turnTowardCharacter($gameMap.event(this.eventId()));"]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 3]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 213, "indent": 3, "parameters": [28, 5, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<地下街玩家C>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac拉客不用来找老子！"]}, {"code": 401, "indent": 3, "parameters": ["\\dac老子现在根本没钱买屄！"]}, {"code": 213, "indent": 3, "parameters": [-1, 7, false]}, {"code": 122, "indent": 3, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 3, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈鄙夷", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac放尊重点。我不是妓女。"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 213, "indent": 3, "parameters": [28, 2, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<地下街玩家C>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac那你凑过来干嘛？还带着个肥猪，不是你的龟公吗？"]}, {"code": 213, "indent": 3, "parameters": [-1, 5, false]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac你…！！！"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 213, "indent": 3, "parameters": [28, 5, false]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<地下街玩家C>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac干嘛？嘴巴张那么大，要免费给我口交啊？"]}, {"code": 401, "indent": 3, "parameters": ["\\dac不是就快滚！"]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.followers().follower(0).turnTowardPlayer();"]}, {"code": 213, "indent": 3, "parameters": [-1, 5, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(12);"]}, {"code": 205, "indent": 3, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 45, "parameters": ["$gamePlayer.turnTowardCharacter($gamePlayer.followers().follower(0));"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 3, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 45, "parameters": ["$gamePlayer.turnTowardCharacter($gamePlayer.followers().follower(0));"], "indent": null}]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac学姐，不要跟这种人一般见识。"]}, {"code": 401, "indent": 3, "parameters": ["\\dac不值得。"]}, {"code": 213, "indent": 3, "parameters": [-1, 7, true]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈凛然脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac嗯……"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [0, 7, true]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 27, "y": 8}, {"id": 29, "name": "EV022空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 19, "y": 12}, {"id": 30, "name": "EV030药店门口NPC", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Evil", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<地下街玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嘿嘿，高价买的药水果然有效~"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Evil", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<地下街玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嘿嘿，高价买的药水果然有效~"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 33}, {"id": 31, "name": "EV031嫖客", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 242}, "directionFix": false, "image": {"tileId": 0, "characterName": "Evil", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<地下街玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac今天我要继续嫖妓嫖到脚软！"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 243}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "NPC02", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 500, 4]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 495, 1]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).turnTowardCharacter($gameMap.event(this.eventId()));"]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 7]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 117, "indent": 3, "parameters": [243]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [8, 3, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [31, 3, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 242}, "directionFix": false, "image": {"tileId": 0, "characterName": "Evil", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<地下街玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac今天我要继续嫖妓嫖到脚软！"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 23}, {"id": 32, "name": "EV032醉酒大叔", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 15}, "directionFix": true, "image": {"tileId": 0, "characterName": "<PERSON><PERSON><PERSON>", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<醉醺醺的大叔>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗝~酒好喝，女人的骚水更好喝，哈哈哈！！！"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "<PERSON><PERSON><PERSON>", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 500, 4]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 495, 1]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).turnTowardCharacter($gameMap.event(this.eventId()));"]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 4]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 213, "indent": 3, "parameters": [32, 7, false]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<醉醺醺的大叔>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac唉！活着还有什么意思啊！"]}, {"code": 213, "indent": 3, "parameters": [-1, 2, false]}, {"code": 122, "indent": 3, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 3, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈担忧2", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac大叔，你怎么了？"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 213, "indent": 3, "parameters": [32, 12, false]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<醉醺醺的大叔>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac我工作丢了，老婆跟别人跑了，女儿也不认我了。"]}, {"code": 401, "indent": 3, "parameters": ["\\dac我真的不想活了！"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<醉醺醺的大叔>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac小姑娘，看你交了个丑肥…老实巴交的男友，你一定是个人美心善的好人！"]}, {"code": 401, "indent": 3, "parameters": ["\\dac求求你，帮我买一杯酒吧，再陪我聊聊天吧！我想一醉不醒！"]}, {"code": 213, "indent": 3, "parameters": [-1, 12, false]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac大叔，喝醉也不是办法呀！"]}, {"code": 401, "indent": 3, "parameters": ["\\dac我帮你买点解酒茶吧……"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(5);"]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.followers().follower(0).turnTowardPlayer();"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac学姐，刚才都是假话！他就是想骗酒骗炮！"]}, {"code": 401, "indent": 3, "parameters": ["\\dac你千万别被他骗了！"]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.turnTowardCharacter($gamePlayer.followers().follower(0));"]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈担忧", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac诶？不会吧……"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 213, "indent": 3, "parameters": [32, 5, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 355, "indent": 3, "parameters": ["$gameMap.event(32).turnTowardCharacter($gamePlayer.followers().follower(0));"]}, {"code": 205, "indent": 3, "parameters": [32, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 3, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<醉醺醺的大叔>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac我向美女讨点酒喝，关你个肥仔沸羊羊乜啊事？！"]}, {"code": 401, "indent": 3, "parameters": ["\\dac本来如果事成了，都还能分你点好处，你嘴贱个什么劲？"]}, {"code": 205, "indent": 3, "parameters": [32, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 3, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 25, "indent": null}]}, {"code": 213, "indent": 3, "parameters": [-1, 7, false]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.turnTowardCharacter($gameMap.event(32));"]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac大叔，原来你真的是骗人的。"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 213, "indent": 3, "parameters": [32, 7, false]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<醉醺醺的大叔>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac不给酒喝就别站在这里碍眼！"]}, {"code": 401, "indent": 3, "parameters": ["\\dac和你的肥仔龟男滚蛋！"]}, {"code": 213, "indent": 3, "parameters": [-1, 6, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [0, 5, true]}, {"code": 213, "indent": 3, "parameters": [-1, 6, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 4}, {"id": 33, "name": "EV033酒馆入口", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 499, 0]}, {"code": 111, "indent": 1, "parameters": [0, 358, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210](没有必要再进去了。)"]}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 100, 15, 18, 8, 0]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 59, "y": 2}, {"id": 34, "name": "EV034", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 358, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 499}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 317 2 地下街-光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 317 3 地下街-阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 203, "indent": 0, "parameters": [22, 0, 40, 9, 4]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 241, "indent": 0, "parameters": [{"name": "spot_underground", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-17, -68, 0, 51], 30, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [22, 12, false]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐，没事吧？那个家伙没把你怎么样吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac如果他欺负了你，我一定饶不了他！"]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈感动", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没事的。谢谢你，猪田君。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[22] : 像素偏移[-12,0] : 时间[30]"]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗅嗅……"]}, {"code": 213, "indent": 0, "parameters": [22, 3, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[22] : 像素偏移[0,0] : 时间[15]"]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}\\c[8]时间这么短，身上也没怪味，嗯，应该确实没有发生什么……"]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈惊讶2", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac什么？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [22, 12, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我是说地下街有怪味……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不管这个了！我们走吧~"]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 37, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 27 1"]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 28, "y": 2}, {"id": 35, "name": "EV035", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 97, 0, 7, 0]}, {"code": 111, "indent": 1, "parameters": [1, 96, 0, 8, 0]}, {"code": 111, "indent": 2, "parameters": [1, 100, 0, 716, 0]}, {"code": 205, "indent": 3, "parameters": [14, {"list": [{"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 97, 0, 6, 0]}, {"code": 111, "indent": 1, "parameters": [1, 96, 0, 28, 0]}, {"code": 356, "indent": 2, "parameters": [">位置与位移 : 批量事件[6,8,10,11,13,13,14,16,23,24,25,26,27,28,30,31,32] : 移动到 : 位置[59,0]"]}, {"code": 203, "indent": 2, "parameters": [77, 0, 32, 33, 0]}, {"code": 203, "indent": 2, "parameters": [78, 0, 35, 33, 0]}, {"code": 203, "indent": 2, "parameters": [79, 0, 38, 33, 0]}, {"code": 203, "indent": 2, "parameters": [82, 0, 7, 21, 0]}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 96, 0, 27, 0]}, {"code": 356, "indent": 2, "parameters": [">位置与位移 : 批量事件[2,3,4,7] : 移动到 : 位置[59,0]"]}, {"code": 111, "indent": 2, "parameters": [0, 371, 1]}, {"code": 203, "indent": 3, "parameters": [80, 0, 30, 34, 6]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 111, "indent": 2, "parameters": [1, 100, 0, 500, 4]}, {"code": 111, "indent": 3, "parameters": [1, 100, 0, 495, 1]}, {"code": 203, "indent": 4, "parameters": [30, 0, 50, 2, 0]}, {"code": 203, "indent": 4, "parameters": [33, 0, 39, 9, 0]}, {"code": 203, "indent": 4, "parameters": [41, 0, 9, 9, 0]}, {"code": 203, "indent": 4, "parameters": [46, 0, 17, 9, 0]}, {"code": 203, "indent": 4, "parameters": [14, 0, 51, 0, 0]}, {"code": 203, "indent": 4, "parameters": [16, 0, 51, 0, 0]}, {"code": 203, "indent": 4, "parameters": [9, 0, 7, 21, 8]}, {"code": 203, "indent": 4, "parameters": [51, 0, 41, 21, 0]}, {"code": 203, "indent": 4, "parameters": [52, 0, 30, 35, 6]}, {"code": 203, "indent": 4, "parameters": [53, 0, 29, 35, 6]}, {"code": 203, "indent": 4, "parameters": [54, 0, 28, 35, 6]}, {"code": 203, "indent": 4, "parameters": [55, 0, 29, 34, 6]}, {"code": 203, "indent": 4, "parameters": [56, 0, 28, 34, 6]}, {"code": 203, "indent": 4, "parameters": [57, 0, 27, 34, 6]}, {"code": 203, "indent": 4, "parameters": [58, 0, 27, 36, 6]}, {"code": 203, "indent": 4, "parameters": [59, 0, 29, 36, 6]}, {"code": 203, "indent": 4, "parameters": [60, 0, 27, 33, 6]}, {"code": 203, "indent": 4, "parameters": [61, 0, 28, 36, 6]}, {"code": 203, "indent": 4, "parameters": [62, 0, 27, 37, 0]}, {"code": 203, "indent": 4, "parameters": [74, 0, 31, 31, 0]}, {"code": 205, "indent": 4, "parameters": [74, {"list": [{"code": 19, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 4, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 203, "indent": 4, "parameters": [75, 0, 34, 31, 0]}, {"code": 205, "indent": 4, "parameters": [75, {"list": [{"code": 19, "indent": null}, {"code": 15, "parameters": [6], "indent": null}, {"code": 16, "indent": null}, {"code": 15, "parameters": [6], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 4, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [6], "indent": null}]}, {"code": 203, "indent": 4, "parameters": [76, 0, 37, 31, 0]}, {"code": 205, "indent": 4, "parameters": [76, {"list": [{"code": 19, "indent": null}, {"code": 15, "parameters": [8], "indent": null}, {"code": 16, "indent": null}, {"code": 15, "parameters": [8], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 4, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [8], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [8], "indent": null}]}, {"code": 205, "indent": 4, "parameters": [80, {"list": [{"code": 45, "parameters": ["$gameMap.event(80).requestBalloon(8);"], "indent": null}, {"code": 15, "parameters": [480], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 4, "parameters": [{"code": 45, "parameters": ["$gameMap.event(80).requestBalloon(8);"], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [480], "indent": null}]}, {"code": 119, "indent": 4, "parameters": ["结束"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 26, "y": 1}, {"id": 36, "name": "EV022空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 13}, {"id": 37, "name": "EV022空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 14}, {"id": 38, "name": "EV022空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 15}, {"id": 39, "name": "EV022空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 16}, {"id": 40, "name": "EV022空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 17}, {"id": 41, "name": "EV041影像店入口", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 109, 10, 16, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 59, "y": 3}, {"id": 42, "name": "EV022空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 18}, {"id": 43, "name": "EV022离开回想", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 0, "characterIndex": 7}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac是否退出回想？"]}, {"code": 102, "indent": 0, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "是"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 84, 5, 15, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "否"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 20, "y": 19}, {"id": 44, "name": "EV022空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 244, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 19}, {"id": 45, "name": "EV045", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 361, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 499}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 317 2 地下街-光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 317 3 地下街-阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 205, "indent": 0, "parameters": [41, {"list": [{"code": 42, "parameters": [0], "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [22, 0, 9, 9, 2]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 33, "indent": null}, {"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 241, "indent": 0, "parameters": [{"name": "spot_underground", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-17, -68, 0, 51], 30, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": -20}]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 事件[22] : 移动显现 : 时间[30] : 方向角度[270] : 移动距离[24]"]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [22, 7, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}\\c[8]妈的！买断个片子怎么这么贵！\\{"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}\\c[8]那个时候还没觉得是自己的东西……真是悔不当初！"]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [22, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐，不好意思！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac因为这种事情让你等这么久。"]}, {"code": 213, "indent": 0, "parameters": [-1, 11, false]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不要紧。男孩子嘛……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我们走吧。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 37, "indent": null}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 27 1"]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 事件[41] : 移动显现 : 时间[60] : 方向角度[90] : 移动距离[0]"]}, {"code": 205, "indent": 0, "parameters": [41, {"list": [{"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 29, "y": 2}, {"id": 46, "name": "EV046破屋入口", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 499, 0]}, {"code": 111, "indent": 1, "parameters": [0, 363, 0]}, {"code": 213, "indent": 2, "parameters": [-1, 7, false]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210](不想再进去了！)"]}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 102, 15, 16, 8, 0]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 59, "y": 4}, {"id": 47, "name": "EV047亚丝娜", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "亚丝娜", "direction": 6, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 58, "y": 1}, {"id": 48, "name": "EV048镜头墙1", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>地图镜头 : 设置镜头墙 : 左"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 52, "y": 8}, {"id": 49, "name": "EV049", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 510}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 317 2 地下街-光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 317 3 地下街-阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [47, 0, 16, 25, 6]}, {"code": 356, "indent": 0, "parameters": [">多帧行走图 : 事件[47] : 固定帧 : 1"]}, {"code": 203, "indent": 0, "parameters": [22, 0, 17, 25, 6]}, {"code": 356, "indent": 0, "parameters": [">多帧行走图 : 事件[22] : 固定帧 : 3"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [10, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.target(47,2,1);"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-17, -68, 0, 51], 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac学姐，你大概是应激反应了……"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 511]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([316, 1, 'A'], true);"]}, {"code": 201, "indent": 0, "parameters": [0, 317, 33, 9, 0, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 511}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [5]}, {"code": 203, "indent": 0, "parameters": [47, 0, 33, 9, 6]}, {"code": 356, "indent": 0, "parameters": [">多帧行走图 : 事件[47] : 固定帧 : 3"]}, {"code": 203, "indent": 0, "parameters": [22, 0, 34, 9, 6]}, {"code": 356, "indent": 0, "parameters": [">多帧行走图 : 事件[22] : 固定帧 : 1"]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.target(47,2,1);"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-17, -68, 0, 51], 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac放心，我会照顾好你的……"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 512]}, {"code": 121, "indent": 0, "parameters": [298, 298, 0]}, {"code": 201, "indent": 0, "parameters": [0, 316, 17, 14, 4, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 512}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 516}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 317 2 地下街-无光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 317 3 地下街-阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 203, "indent": 0, "parameters": [51, 0, 41, 21, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 37, "indent": null}, {"code": 34, "indent": null}, {"code": 36, "indent": null}, {"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "spot_underground", "volume": 20, "pitch": 105, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-17, -34, 0, 0], 30, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 212, "indent": 0, "parameters": [-1, 207, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 玩家 : 移动显现 : 时间[30] : 方向角度[0] : 移动距离[0]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](嗯？已经是上午了？！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我在那个空间里感觉并没有那么久啊！)"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 2], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](是因为空间内部数据量过大， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac导致的时间流速与现实不同吗？)"]}, {"code": 213, "indent": 0, "parameters": [-1, 7, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](可恶！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac计划全乱了，希望亚丝娜没有生气才好。)"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [-1, 9, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](不过……既然那个组织确实和须乡的研究有关，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那就必须早日铲除掉他们！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](今天的行动是必要的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这也是为了亚丝娜。)"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](希望这些数据能如爱丽丝小姐的预期，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac成为给他们定罪的关键证据。)"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 517]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 201, "indent": 0, "parameters": [0, 250, 9, 14, 6, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 517}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 518}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 317 2 地下街-无光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 317 3 地下街-阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 356, "indent": 0, "parameters": ["SetArray 122 0 0"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "spot_underground", "volume": 20, "pitch": 105, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-17, -34, 0, 0], 30, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [122]}, {"code": 117, "indent": 0, "parameters": [131]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](白天的地下街真是冷清……)"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [6, -1, 4]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](我刚才跟亚丝娜说了正在和克莱因夫妻处理事情，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac如果现在立刻去找她，反而搞得当时像是在掩饰什么似的。)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](不过还是确认一下她的位置吧……)"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](下线了？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不会生气了吧……？)"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](应该不会，亚丝娜才不是那种斤斤计较的女生。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可能是京子阿姨有事找她吧。那我现在怎么安排好呢……)"]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](也许是累过头了，我现在反而一点困意也没有……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac出了地下街以后随便逛逛吧~)"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 519]}, {"code": 117, "indent": 0, "parameters": [154]}, {"code": 117, "indent": 0, "parameters": [132]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 519}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 30, "y": 2}, {"id": 50, "name": "EV050道具店入口", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 111, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 342, 9, 15, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 59, "y": 5}, {"id": 51, "name": "EV051告示", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 224}, "directionFix": true, "image": {"tileId": 0, "characterName": "!adult2", "direction": 4, "pattern": 0, "characterIndex": 4}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[3,-9]"]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 520, 2]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 515, 1]}, {"code": 213, "indent": 2, "parameters": [-1, 8, false]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[2]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 2, "parameters": ["\\dac【暂停营业】\\dac "]}, {"code": 401, "indent": 2, "parameters": ["\\dac舞娘专业技能培训中\\dac"]}, {"code": 401, "indent": 2, "parameters": ["\\dac营业时间另待通知\\dac"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 16, "indent": null}]}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 499, 0]}, {"code": 355, "indent": 1, "parameters": ["$gamePlayer.followers().follower(0).turnTowardCharacter($gameMap.event(this.eventId()));"]}, {"code": 122, "indent": 1, "parameters": [123, 123, 0, 0, 9]}, {"code": 355, "indent": 1, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 1, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 1, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 1, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 1, "parameters": ["} else {"]}, {"code": 655, "indent": 1, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 1, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 1, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 111, "indent": 1, "parameters": [0, 355, 1]}, {"code": 213, "indent": 2, "parameters": [-1, 8, false]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[2]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 2, "parameters": ["\\dac【暂停营业】\\dac "]}, {"code": 401, "indent": 2, "parameters": ["\\dac舞娘专业技能培训中\\dac"]}, {"code": 401, "indent": 2, "parameters": ["\\dac营业时间另待通知\\dac"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(3);"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac学姐，这里是舞厅，桐人学长他确实喜欢……"]}, {"code": 401, "indent": 2, "parameters": ["\\dac啊啊，没什么没什么！我们要进去看看吗？"]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 16, "indent": null}]}, {"code": 122, "indent": 2, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 2, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 2, "parameters": [2, "FOG明日奈回应", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 2, "parameters": [135]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac门上的告示说今天这里不营业，桐人君自然不在里面。"]}, {"code": 401, "indent": 2, "parameters": ["\\dac我们不用进去了。"]}, {"code": 235, "indent": 2, "parameters": [2]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac嗯，好。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 213, "indent": 2, "parameters": [-1, 8, false]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[2]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 2, "parameters": ["\\dac【暂停营业】\\dac "]}, {"code": 401, "indent": 2, "parameters": ["\\dac舞娘专业技能培训中\\dac"]}, {"code": 401, "indent": 2, "parameters": ["\\dac营业时间另待通知\\dac"]}, {"code": 356, "indent": 2, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 58, "y": 0}, {"id": 52, "name": "EV052", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor1", "direction": 6, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 59, "y": 39}, {"id": 53, "name": "EV053", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "Actor3", "direction": 6, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 58, "y": 39}, {"id": 54, "name": "EV054", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "akunin2", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 57, "y": 39}, {"id": 55, "name": "EV055蚯蚓沃德", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "akunin3", "direction": 6, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 59, "y": 38}, {"id": 56, "name": "EV056", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "akunin3", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 58, "y": 38}, {"id": 57, "name": "EV057", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "akunin3", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 57, "y": 38}, {"id": 58, "name": "EV058", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": false, "image": {"tileId": 0, "characterName": "jp_character2", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 59, "y": 37}, {"id": 59, "name": "EV059", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "<PERSON><PERSON><PERSON>", "direction": 6, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 58, "y": 37}, {"id": 60, "name": "EV060", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "<PERSON><PERSON><PERSON>", "direction": 6, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 57, "y": 37}, {"id": 61, "name": "EV061", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "<PERSON><PERSON><PERSON>", "direction": 6, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 59, "y": 36}, {"id": 62, "name": "EV062狗洞触发点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 500, 4]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 495, 1]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 8]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 108, "indent": 3, "parameters": ["关闭狗洞对话"]}, {"code": 355, "indent": 3, "parameters": ["$gameSelfSwitches.setValue([317, 63, 'B'], true);"]}, {"code": 213, "indent": 3, "parameters": [-1, 2, false]}, {"code": 122, "indent": 3, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 3, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈惊讶2", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac为什么这里这么多人排队……？"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 108, "indent": 3, "parameters": ["开启换人"]}, {"code": 355, "indent": 3, "parameters": ["$gameSelfSwitches.setValue([317, 70, 'A'], true);"]}, {"code": 205, "indent": 3, "parameters": [0, {"list": [{"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 3, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": 0}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 3, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 19, "indent": null}]}, {"code": 121, "indent": 3, "parameters": [369, 369, 0]}, {"code": 213, "indent": 3, "parameters": [-1, 15, true]}, {"code": 231, "indent": 3, "parameters": [2, "fog亚丝娜惊讶脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac这些人…！"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac真是不知羞耻！"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac我听说，有些男玩家会用非法手段建个女号，然后……"]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈意外脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac还有这种事？！"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac但我猜桐人学长不会干这种事~"]}, {"code": 205, "indent": 3, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 3, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac那还用说吗？！"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈凛然脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac赶紧离开吧，我都快吐出来了！"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 205, "indent": 3, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 2, "indent": null}]}, {"code": 205, "indent": 3, "parameters": [0, {"list": [{"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 38, "indent": null}]}, {"code": 355, "indent": 3, "parameters": ["$gameSelfSwitches.setValue([317, 63, 'B'], false);"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 500}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 58, "y": 36}, {"id": 63, "name": "EV063", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 499}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 198, 0, 30, 1]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 23, 1]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 30, 1]}, {"code": 108, "indent": 2, "parameters": ["关闭狗洞排队气泡"]}, {"code": 355, "indent": 2, "parameters": ["$gameSelfSwitches.setValue([317, 64, 'A'], true);"]}, {"code": 117, "indent": 2, "parameters": [240]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var waitFrames = Math.floor(Math.random() * 180) + 120;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 523}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 28, "y": 1}, {"id": 64, "name": "EV064", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["RandomNumber 124 125"]}, {"code": 111, "indent": 0, "parameters": [1, 125, 0, 0, 0]}, {"code": 108, "indent": 1, "parameters": ["数组抽尽重置"]}, {"code": 356, "indent": 1, "parameters": ["SetArray 124 1 10"]}, {"code": 356, "indent": 1, "parameters": ["RandomNumber 124 125"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 125, 0, 1, 0]}, {"code": 213, "indent": 1, "parameters": [52, 8, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 125, 0, 2, 0]}, {"code": 213, "indent": 1, "parameters": [53, 8, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 125, 0, 3, 0]}, {"code": 213, "indent": 1, "parameters": [54, 8, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 125, 0, 4, 0]}, {"code": 213, "indent": 1, "parameters": [55, 8, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 125, 0, 5, 0]}, {"code": 213, "indent": 1, "parameters": [56, 8, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 125, 0, 6, 0]}, {"code": 213, "indent": 1, "parameters": [57, 8, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 125, 0, 7, 0]}, {"code": 213, "indent": 1, "parameters": [58, 8, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 125, 0, 8, 0]}, {"code": 213, "indent": 1, "parameters": [59, 8, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 125, 0, 9, 0]}, {"code": 213, "indent": 1, "parameters": [60, 8, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 125, 0, 10, 0]}, {"code": 213, "indent": 1, "parameters": [61, 8, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["var waitFrames = Math.floor(Math.random() * 90) + 30;"]}, {"code": 655, "indent": 0, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 523}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 27, "y": 1}, {"id": 65, "name": "EV065镜头墙2", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>地图镜头 : 设置镜头墙 : 左"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 52, "y": 18}, {"id": 66, "name": "EV066镜头墙3", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>地图镜头 : 设置镜头墙 : 左"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 52, "y": 30}, {"id": 67, "name": "EV067狗洞妓女1", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$狗洞-屁股", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,98]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 515}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$狗洞-洞", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,98]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 32, "y": 31}, {"id": 68, "name": "EV068狗洞妓女2", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 515}, "directionFix": true, "image": {"tileId": 0, "characterName": "$狗洞-漏精", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,53]"]}, {"code": 108, "indent": 0, "parameters": ["=>多帧行走图 : 帧数 : 12"]}, {"code": 108, "indent": 0, "parameters": ["=>多帧行走图 : 初始帧 : 1"]}, {"code": 108, "indent": 0, "parameters": ["=>多帧行走图 : 动画帧间隔 : 6"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 515}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$狗洞-洞", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,98]"]}, {"code": 108, "indent": 0, "parameters": ["=>多帧行走图 : 帧数 : 3"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 35, "y": 31}, {"id": 69, "name": "EV069狗洞妓女3", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$狗洞-扭屁股", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,98]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 515}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$狗洞-洞", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,98]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 38, "y": 31}, {"id": 70, "name": "EV070", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 371, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 499}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 499, 0]}, {"code": 111, "indent": 1, "parameters": [0, 371, 1]}, {"code": 203, "indent": 2, "parameters": [81, 0, 35, 33, 0]}, {"code": 205, "indent": 2, "parameters": [81, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 39, "indent": null}]}, {"code": 356, "indent": 2, "parameters": [">行走图额外位置偏移量 : 事件[81] : 像素偏移[0,-34] : 时间[1]"]}, {"code": 205, "indent": 2, "parameters": [75, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 205, "indent": 2, "parameters": [75, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 16, "indent": null}]}, {"code": 356, "indent": 2, "parameters": [">持续动作 : 事件[75] : 左右震动 : 持续时间[60] : 周期[15] : 震动幅度[1]"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 250, "indent": 2, "parameters": [{"name": "Equip2", "volume": 40, "pitch": 80, "pan": 20}]}, {"code": 205, "indent": 2, "parameters": [81, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 2, "parameters": [{"code": 40, "indent": null}]}, {"code": 205, "indent": 2, "parameters": [75, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 39, "indent": null}]}, {"code": 213, "indent": 2, "parameters": [81, 4, true]}, {"code": 356, "indent": 2, "parameters": [">行走图额外位置偏移量 : 事件[81] : 像素偏移[0,0] : 时间[60]"]}, {"code": 205, "indent": 2, "parameters": [81, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 2, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 1, "indent": null}]}, {"code": 230, "indent": 2, "parameters": [15]}, {"code": 213, "indent": 2, "parameters": [80, 3, false]}, {"code": 230, "indent": 2, "parameters": [45]}, {"code": 205, "indent": 2, "parameters": [80, {"list": [{"code": 36, "indent": null}, {"code": 37, "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 29, "parameters": [2], "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 2, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 205, "indent": 2, "parameters": [81, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": 0}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 356, "indent": 2, "parameters": [">消失动作 : 事件[81] : 纵向挤扁 : 时间[60] : 纵向比例[1.5]"]}, {"code": 356, "indent": 2, "parameters": [">行走图额外位置偏移量 : 事件[80] : 像素偏移[0,-34] : 时间[60]"]}, {"code": 121, "indent": 2, "parameters": [371, 371, 0]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 230, "indent": 2, "parameters": [10]}, {"code": 250, "indent": 2, "parameters": [{"name": "Equip1", "volume": 40, "pitch": 70, "pan": 20}]}, {"code": 230, "indent": 2, "parameters": [30]}, {"code": 205, "indent": 2, "parameters": [75, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 2, "parameters": [{"code": 40, "indent": null}]}, {"code": 205, "indent": 2, "parameters": [80, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 2, "parameters": [75, {"list": [{"code": 19, "indent": null}, {"code": 15, "parameters": [7], "indent": null}, {"code": 16, "indent": null}, {"code": 15, "parameters": [7], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 2, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [7], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [7], "indent": null}]}, {"code": 355, "indent": 2, "parameters": ["$gameSelfSwitches.setValue([317, 63, 'B'], false);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 523}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 29, "y": 1}, {"id": 71, "name": "EV071", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door3", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 32, "y": 32}, {"id": 72, "name": "EV072", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door3", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 35, "y": 32}, {"id": 73, "name": "EV073", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door3", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 38, "y": 32}, {"id": 74, "name": "EV074狗洞嫖客1", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$狗洞-蓝发嫖客", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[48,98]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 57, "y": 33}, {"id": 75, "name": "EV075狗洞嫖客2", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$狗洞-武道家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[48,98]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 371, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$狗洞-战士嫖客", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[48,98]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 58, "y": 33}, {"id": 76, "name": "EV076狗洞嫖客3", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$狗洞-红发", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[48,98]"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 19, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 59, "y": 33}, {"id": 77, "name": "EV077精液大", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$精液1", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 213, "indent": 0, "parameters": [-1, 7, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 57, "y": 34}, {"id": 78, "name": "EV078厕纸", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!adult1", "direction": 4, "pattern": 0, "characterIndex": 7}, "list": [{"code": 213, "indent": 0, "parameters": [-1, 7, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 58, "y": 34}, {"id": 79, "name": "EV079避孕套", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!adult2", "direction": 4, "pattern": 2, "characterIndex": 6}, "list": [{"code": 213, "indent": 0, "parameters": [-1, 7, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 59, "y": 34}, {"id": 80, "name": "EV080战士嫖客", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "RTPRecreation_XP ver 3", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 58, "y": 35}, {"id": 81, "name": "EV081武道家嫖客", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "RTPRecreation_XP ver 3", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 59, "y": 35}, {"id": 82, "name": "EV082精液小", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "$精液2", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 56, "y": 34}, {"id": 83, "name": "EV083", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 716}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">地图镜头 : 修改聚焦偏移 : 位置[0,0]"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 356, "indent": 0, "parameters": ["LAYER 317 2 地下街-光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 317 3 地下街-阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 129, "indent": 0, "parameters": [63, 0, false]}, {"code": 129, "indent": 0, "parameters": [4, 1, false]}, {"code": 117, "indent": 0, "parameters": [23]}, {"code": 203, "indent": 0, "parameters": [0, 0, 36, 22, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 17, "indent": null}, {"code": 41, "parameters": ["克莱因游戏", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["克莱因游戏", 0], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [85, 0, 23, 24, 6]}, {"code": 205, "indent": 0, "parameters": [85, {"list": [{"code": 41, "parameters": ["桐人女装", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人女装", 0], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [9, 0, 25, 24, 6]}, {"code": 203, "indent": 0, "parameters": [17, 0, 24, 25, 6]}, {"code": 203, "indent": 0, "parameters": [84, 0, 26, 23, 0]}, {"code": 203, "indent": 0, "parameters": [86, 0, 26, 24, 0]}, {"code": 203, "indent": 0, "parameters": [87, 0, 26, 25, 0]}, {"code": 203, "indent": 0, "parameters": [88, 0, 26, 26, 0]}, {"code": 203, "indent": 0, "parameters": [14, 0, 41, 22, 0]}, {"code": 203, "indent": 0, "parameters": [89, 0, 37, 23, 0]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 玩家 : 移动到 : 位置[30,24]"]}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 固定看向 : 事件[83]"]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.target(83,1.5,1);"]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 715]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 241, "indent": 0, "parameters": [{"name": "spot_underground", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["SetArray 122 0 0"]}, {"code": 223, "indent": 0, "parameters": [[-17, -68, 0, 51], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 3, "indent": 0}, {"code": 3, "indent": 0}, {"code": 3, "indent": 0}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 3, "indent": 0}, {"code": 3, "indent": null}, {"code": 3, "indent": 0}, {"code": 3, "indent": 0}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [9, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呼……"]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我！准备好了！我！准备好了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 准备开始任务啦！"]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你们呢？虽然时间还早……"]}, {"code": 213, "indent": 0, "parameters": [17, 8, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [9, 2, true]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人老弟，你干嘛啊？扭扭捏捏的！紧张？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 有啥好紧张的？你又不是第一次出任务！"]}, {"code": 205, "indent": 0, "parameters": [85, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.restore(120);"]}, {"code": 205, "indent": 0, "parameters": [85, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 3, "indent": 0}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [85, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 与其说紧张，不如说是尴尬……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我之前怎么都没想到会让我女装。"]}, {"code": 213, "indent": 0, "parameters": [9, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 穿都穿了，现在还说这个干啥？"]}, {"code": 213, "indent": 0, "parameters": [17, 11, true]}, {"code": 231, "indent": 0, "parameters": [2, "爱丽丝面具立绘", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人，我知道有点勉强你，但为了任务，也只能委屈你……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [85, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯，我明白。"]}, {"code": 213, "indent": 0, "parameters": [9, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那就按计划执行！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我打头，你们也跟紧一点。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在就进去吗？似乎还没到内应出来接我们的预定时间。"]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们的伪装超完美，根本不需要内应也能混进去！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 肯定不会有问题！"]}, {"code": 213, "indent": 0, "parameters": [85, 8, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [17, 6, true]}, {"code": 231, "indent": 0, "parameters": [2, "爱丽丝面具立绘", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 阿克，稍微沉着稳重些。小心被敌人看出破绽。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我知道的啦！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别担心了！我们赶紧进去吧~"]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 41, "parameters": ["克莱因游戏", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["克莱因游戏", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [85, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 48 1"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).setDirection(6);"]}, {"code": 205, "indent": 0, "parameters": [85, {"list": [{"code": 3, "indent": 0}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 96 1"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(1).setDirection(6);"]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 717]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 717}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 37, 0]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 718]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 718}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 3, "indent": 0}, {"code": 3, "indent": 0}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 事件[14] : 移动显现 : 时间[10] : 方向角度[0] : 移动距离[0]"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [14, 7, false]}, {"code": 230, "indent": 0, "parameters": [25]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<壮汉>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}\\c[8]嘁！这些白痴来早了啊……"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 23, 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).setDirection(8);"]}, {"code": 655, "indent": 0, "parameters": ["$gamePlayer.followers().follower(1).setDirection(8);"]}, {"code": 203, "indent": 0, "parameters": [17, 0, 41, 24, 8]}, {"code": 355, "indent": 0, "parameters": ["$gameVariables.setValue(197, $gamePlayer.followers().follower(1).x);"]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 40, 0]}, {"code": 203, "indent": 1, "parameters": [85, 0, 40, 24, 8]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 203, "indent": 1, "parameters": [85, 0, 41, 25, 8]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [85, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 大哥，这是我的通行证，最高等级的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可以进入对吧！"]}, {"code": 356, "indent": 0, "parameters": ["NF_REM_NPC 1"]}, {"code": 213, "indent": 0, "parameters": [14, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<壮汉>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 最高等级的贵宾，一只手就能数过来。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我之前可没有见过你。"]}, {"code": 213, "indent": 0, "parameters": [-1, 12, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个嘛，我也是最近才升级的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 大哥，看证不看人，这是最基本的规则吧！"]}, {"code": 213, "indent": 0, "parameters": [14, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<壮汉>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 今天特殊。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 再过半个小时，影狐大人要在里面招待贵客，可不敢怠慢！"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这就对上了！我就是给影狐大人送新舞女来的！"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你看这两个妞，还不错吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 虽然不知道舞场其他女人能卖到什么价钱，但她们一个是我的老婆，一个是我的妹妹，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 都是有正经职业的良家女子，和普通的妓女可是天壤之别！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<壮汉>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是嘛？我看看……"]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 37, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<壮汉>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 面具掀开！"]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 44, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 150, "pan": 0}], "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 150, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [14, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<壮汉>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 金发的这个还行，波大水灵，是我的菜……"]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 44, "parameters": [{"name": "Equip3", "volume": 90, "pitch": 150, "pan": 0}], "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "Equip3", "volume": 90, "pitch": 150, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 40, 0]}, {"code": 205, "indent": 1, "parameters": [14, {"list": [{"code": 2, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 205, "indent": 1, "parameters": [14, {"list": [{"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [14, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<壮汉>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但黑发地味文科妹有点离谱啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 妆都没画？服装也不对！逗我玩呢！"]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 45, "parameters": ["$gameMap.event(17).turnTowardCharacter($gameMap.event(14));"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(17).turnTowardCharacter($gameMap.event(14));"], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "爱丽丝面具立绘", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 让您见笑了。她还是实习阶段。今天主要是我给影狐大人表演，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 她在一旁观摩学习，也兼职端茶倒水之类的。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [14, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<壮汉>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 也就是个丫鬟。那正好，小丫头，给我跑个腿！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 影像店的老鬼说好了今天有新片送我，但现在都没动静，去问问。"]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 45, "parameters": ["$gameMap.event(17).turnTowardCharacter($gameMap.event(85));"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(17).turnTowardCharacter($gameMap.event(85));"], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "爱丽丝面具立绘", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐子，你去吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们在这里等你……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [85, 8, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 40, 5]}, {"code": 205, "indent": 1, "parameters": [14, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [85, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 37, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 40, 0]}, {"code": 205, "indent": 1, "parameters": [14, {"list": [{"code": 1, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 205, "indent": 1, "parameters": [14, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(17).turnTowardCharacter($gameMap.event(14));"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(17).turnTowardCharacter($gameMap.event(14));"], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<壮汉>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你们也不能闲着！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 给那位大人表演，可要好好检查一番才行。跟我过来！"]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 7, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [17, 8, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 解除固定看向"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 719]}, {"code": 117, "indent": 0, "parameters": [154]}, {"code": 129, "indent": 0, "parameters": [96, 0, false]}, {"code": 129, "indent": 0, "parameters": [63, 1, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 解除固定看向"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 201, "indent": 0, "parameters": [0, 109, 10, 16, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 719}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 722}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[14]"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 356, "indent": 0, "parameters": ["LAYER 317 2 地下街-光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 317 3 地下街-阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 117, "indent": 0, "parameters": [23]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [17, 0, 35, 16, 8]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 41, "parameters": ["爱丽丝像素", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["爱丽丝像素", 0], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[17] : 像素偏移[0,-12] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [14, 0, 35, 17, 8]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[14] : 像素偏移[0,-24] : 时间[1]"]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [9, 0, 34, 17, 6]}, {"code": 111, "indent": 0, "parameters": [0, 916, 0]}, {"code": 119, "indent": 1, "parameters": ["回想"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 设置声音 : 默认声音"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dDMS[慢]\\dac 与此同时……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dDMS[慢]\\dac 地下街"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 默认模式"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 关闭声音"]}, {"code": 118, "indent": 0, "parameters": ["回想"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-17, -68, 0, 51], 60, true]}, {"code": 213, "indent": 0, "parameters": [9, 7, true]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 胖哥，可以了吧，你已经摸了半天了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这可是本人宝贵的赚钱工具啊！"]}, {"code": 213, "indent": 0, "parameters": [14, 5, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<壮汉>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这是基本程序，要被重要人士裸体使用，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我当然要确保表演者没有私藏什么危险的道具！"]}, {"code": 213, "indent": 0, "parameters": [14, 8, true]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[14] : 像素偏移[0,0] : 时间[30]"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [9, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 结束了？"]}, {"code": 213, "indent": 0, "parameters": [14, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<壮汉>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么可能？体外检查只是最基本的，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 接下来我还要检查小穴……"]}, {"code": 213, "indent": 0, "parameters": [14, 9, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<壮汉>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不……那个还是留到以后再享受。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 先从嘴巴开始检查，把手臂放到背后，张开嘴！"]}, {"code": 213, "indent": 0, "parameters": [9, 5, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [17, 8, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 723]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 723}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[14]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[爱丽丝舞厅催眠口交]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘4", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦吼，好久没遇到能把我整根肉棒吞进去的女人了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我这个号基本上90%的时间都在站岗，可是没时间洗鸡巴的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么样？包皮垢好吃吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜呜……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 味道挺怪的吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我听其他妓女说，好像是酸味的，哈哈哈哈~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啾……嗯……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 喂，别光吸，舌头也动起来啊。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 克莱因:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真是下作，居然利用守卫的职务之便……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我拿的可是高等级通行证啊！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿，不爽啊？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不爽也憋着，这就是老子的权力！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 更何况你看舞女小姐这个投入的样子，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 想必是很喜欢嘴里的美味吧！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 肿么……嗯……可能……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这样啊。那含着不喜欢的鸡巴，应该相当难受吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可为什么口水流了那么多呢？哈哈哈！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 克莱因:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这家伙……居然一边要求女性含自己的肉棒，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 一边还羞辱她，混蛋……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 克莱因:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱…艾莉儿，赶紧让他射精了事吧……！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦哦，真不错啊。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就这么弄，再稍微快一点。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 老子的先走汁都流出来了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好像要射了……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 克莱因:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那就赶紧射出来！射啊！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈哈，才不急！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘴里没有危险物品，确认！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 接着是下面！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 克莱因:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜呃……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, true]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "bob_cloth1", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[爱丽丝舞厅催眠后入]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 70, "pan": 0}], "indent": null}, {"code": 15, "parameters": [32], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 35, "pitch": 70, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [32], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 小穴能插这么深呢！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜哦哦！真的超正的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊…啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 喂，真的舍得自己这么极品的老婆出来卖啊？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你这种大王八，也是不多见呢！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 克莱因:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜…呜呜……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 克莱因:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 给我快点结束……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还有！不许内射！不然……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 去你妈的！你个屌毛以为自己是个什么人物？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别打扰老子的兴致！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦哦……他……是担心影狐…哈啊…大人那边……不好交代……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊啊…我不要紧…哈啊啊……您继续……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哼！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还是美女比较识相。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯啊啊啊~~！好，爽……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我知道你现在很爽，可声音是不是太大了啊？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你老公还在旁边呢？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没，没事的……这只是检查，是为了工作，所以……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以，就没问题了是吧？呵呵。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你听到了吧？这可是你老婆自己说的哦~"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 克莱因:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可恶！呜呜……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呼啊啊啊……啊啊啊啊，嗯啊啊……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 热热的……大鸡巴，好舒服……啊，那里……特别有感觉……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那里是哪里？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 里面，往里面一些……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那里，被鸡鸡搅动之后……感觉都要去了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊啊啊，呀啊啊啊……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 到了，舒服的地方……鸡鸡顶着的那里……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊，啊啊啊啊，去，去了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯啊啊啊啊啊啊~~~！\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊，啊啊啊……好爽……已经去了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔嗯，被夹得好爽……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过，不要以为我这就会放过你……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好爽，好爽喔……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 大鸡巴，我好喜欢……呼啊啊啊……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啧啧……你老公听到这句话或许会哭出来哦？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在，和他没有关系……呼啊，啊啊啊嗯……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你只需要集中精神检查，就行了……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 克莱因:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊啊，嗯啊啊啊，呀啊啊啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 又要去了……嗯啊啊啊啊……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看你都已经高潮得停不下来了，还能继续吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 当然能了……继续用你的大鸡巴检查我的骚穴吧……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 再激烈一些也没关系的……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呼！在你骚逼绵软肉壁的连续攻势下，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就算是老子，也要扛不住了，我……要射了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呼啊啊啊……内射太舒服了……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 克莱因:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我都说了不可以内射……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 算了算了，现在检查可以结束了吧？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 想得美！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不是还有一个洞没有检查吗？"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[爱丽丝舞厅肛交催眠前]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 44, "parameters": [{"name": "ブチュ２", "volume": 35, "pitch": 70, "pan": 0}], "indent": null}, {"code": 15, "parameters": [44], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ブチュ２", "volume": 35, "pitch": 70, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [44], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊，啊啊……好痛…！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦哦~ 真紧啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 放松点，越紧张越痛哦~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜……说得轻松……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈哈！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 肛交的初体验感觉如何？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 身体…啊呃……像要被撕裂开一样……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜…不用管我，你继续吧！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈哈，你这不挺精神的嘛。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不错哦。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 克莱因:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你这混蛋，竟然伤害我最珍贵的……货物。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你！别说了…我可以…忍耐……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 吼吼吼！这眼神真是太赞了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 屈辱地张开双腿的样子简直让人欲罢不能啊！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜……啊……呜……"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 30, false]}, {"code": 231, "indent": 0, "parameters": [100, "爱丽丝舞厅催眠一", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 25, true]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过，我也不是什么恶魔。小美女刚才用骚屄也伺候得我不错。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以，我来帮你舒服一点吧，婊子骑士……"]}, {"code": 241, "indent": 0, "parameters": [{"name": "催眠H事件主动", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 108, "indent": 0, "parameters": ["催眠CG"]}, {"code": 231, "indent": 0, "parameters": [98, "爱丽丝舞厅催眠二", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 232, "indent": 0, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 25, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 欸？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}你天性淫乱，明明是第一次肛交，却舒服的不得了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}肉棒对直肠的刺激，让你变得更加敏感，让你成为了快感的俘虏。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}明白了吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}明白。我天性淫乱，喜欢肛交，肉棒刺激直肠会让我更敏感，成为快感得俘虏。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}很好。等会继续，可要叫得好听点哦。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我知道了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\}清醒过来吧。"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 30, false]}, {"code": 245, "indent": 0, "parameters": [{"name": "hikaru-adlib-10", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[爱丽丝舞厅肛交催眠后]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 44, "parameters": [{"name": "ブチュ２", "volume": 35, "pitch": 70, "pan": 0}], "indent": null}, {"code": 15, "parameters": [44], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ブチュ２", "volume": 35, "pitch": 70, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [44], "indent": null}]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [98]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊\\i[90]　啊！！！\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呼\\i[90]　啊\\i[90]　不，不行\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好舒服\\i[90] 这是什么\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 从来没试过\\i[90]"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 克莱因:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱…艾莉儿？你不觉得痛了吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你不用装舒服的……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你这个逼人又在说什么操蛋话？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你老婆爽了你反而不开心了？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊，呼…没有装……嗯嗯\\i[90] "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 突然舒服起来了……嗯\\i[90] 真的好舒服！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不错，总算变直率了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 太怪了，居然……居然会这么舒服……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 克莱因:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 艾莉儿……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 每当他往前顶，我都感觉爽到升天……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈哈，老子比你老公强多了吧？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 克莱因:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别说了，赶紧…结束吧……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 再敢废话一句，我就废了你们的通行证！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你…不要再打扰我们了……呜啊\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊~~~呜呜\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 说得好，给你奖励！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜呜\\i[90]　啊啊……\\i[90] 呜呜\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看招！看招！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯呀啊啊，啊啊啊……鸡巴…太爽啦！\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我快要上瘾了……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦吼？上瘾了？那你老公怎么办？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 当然，老公是最重要的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但我现在只想尽情地高潮……\\i[90]"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 克莱因:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呜呜……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 菊穴舒服得快要化开了…… "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呼啊啊啊，啊啊啊啊，再多次也会去的……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 肠道又夹紧了哦~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是因为守卫大人的腰动得太狂野了……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这样，只会高潮的……鸡鸡再用力些，捅最里面……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我不行了\\i[90]忍不住了~\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 高潮了\\i[90] 最猛烈的高潮来了\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦哦~ 居然边收缩边颤抖，就那么舒服吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这种能把女人变成废人的感觉……不舒服……就怪了\\i[90]"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 克莱因:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么会……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 肛交…真的好舒服！\\i[90]\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 壮汉:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 喜欢就好！你那拼命忍耐高潮的样子，真的很可爱哦，艾莉儿酱。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我也差不多该拿出真本领，好好射一次了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 欸……？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊！！！！\\i[90] 啊啊\\i[90] 去了！啊！\\i[90]"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[爱丽丝舞厅肛交中出]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "hikaru adlib-02_", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 44, "parameters": [{"name": "ブチュ２", "volume": 35, "pitch": 70, "pan": 0}], "indent": null}, {"code": 15, "parameters": [28], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ブチュ２", "volume": 35, "pitch": 70, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [28], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [204]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精-06", "volume": 45, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 111, "indent": 0, "parameters": [0, 916, 0]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 1, 82]}, {"code": 121, "indent": 1, "parameters": [916, 916, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 84, 11, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 121, "indent": 1, "parameters": [17, 17, 1]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 724]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 109, 9, 9, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 724}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 725}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.restore(1);"]}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 修改聚焦偏移 : 位置[0,0]"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 解除固定看向"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 317 2 地下街-光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 317 3 地下街-阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 203, "indent": 0, "parameters": [17, 0, 34, 24, 6]}, {"code": 203, "indent": 0, "parameters": [9, 0, 34, 25, 6]}, {"code": 203, "indent": 0, "parameters": [14, 0, 36, 24, 2]}, {"code": 205, "indent": 0, "parameters": [55, {"list": [{"code": 36, "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [55, 0, 36, 25, 4]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": 0}, {"code": 3, "indent": 0}, {"code": 3, "indent": 0}, {"code": 3, "indent": 0}, {"code": 3, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-17, -68, 0, 51], 60, true]}, {"code": 213, "indent": 0, "parameters": [55, 3, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [55, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<蚯蚓沃德>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是啦，杜克，他们的确是福克西介绍来的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 客人已经在等了，放他们进去。"]}, {"code": 213, "indent": 0, "parameters": [14, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<壮汉杜克>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 行，我知道了。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [55, {"list": [{"code": 37, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [14, 1, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<壮汉杜克>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 正好，地味女也回来了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 影片交给我。"]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 15, "parameters": [45], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [45], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 16, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<壮汉杜克>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好啦，你们可以进去了。"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 3, "indent": 0}, {"code": 3, "indent": 0}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [25], "indent": null}, {"code": 29, "parameters": [2], "indent": null}, {"code": 3, "indent": 0}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [25], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 15, "parameters": [35], "indent": null}, {"code": 29, "parameters": [2], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": 0}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [35], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 29, "parameters": [2], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": 0}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 全图事件 : 立即归位"]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.target(14,2,1);"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 玩家 : 移动到 : 位置[42,23]"]}, {"code": 203, "indent": 0, "parameters": [14, 0, 42, 23, 8]}, {"code": 203, "indent": 0, "parameters": [90, 0, 41, 23, 2]}, {"code": 205, "indent": 0, "parameters": [90, {"list": [{"code": 42, "parameters": [0], "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 16, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-17, -68, 0, 51], 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<壮汉杜克？>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 联系杜克，休息结束了，让他回来上班。"]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 2, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 212, "indent": 0, "parameters": [90, 208, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 事件[14] : 直接消失 : 时间[15]"]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 事件[90] : 移动显现 : 时间[15] : 方向角度[0] : 移动距离[0]"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "Flash3", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 0, "parameters": [[34, 136, 34, 170], 60, false]}, {"code": 356, "indent": 0, "parameters": [">方块粉碎效果 : 事件[90] : 方块粉碎[16]"]}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.restore(1);"]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 726]}, {"code": 201, "indent": 0, "parameters": [0, 107, 29, 38, 6, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 726}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 31, "y": 2}, {"id": 84, "name": "EV084空气墙1", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 717}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [509]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 50, "y": 20}, {"id": 85, "name": "EV085桐人", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "桐人", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 58, "y": 3}, {"id": 86, "name": "EV086空气墙2", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 717}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [509]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 50, "y": 21}, {"id": 87, "name": "EV087空气墙3", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 717}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [509]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 50, "y": 22}, {"id": 88, "name": "EV088空气墙4", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 717}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [509]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 50, "y": 23}, {"id": 89, "name": "EV089酒瓶", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 911, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 59, "y": 31}, {"id": 90, "name": "EV090影狐", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "福克西（影狐斯卡利）", "direction": 8, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 59, "y": 28}, null]}