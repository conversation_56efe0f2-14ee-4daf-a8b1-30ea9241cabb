{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 25, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 6, "width": 25, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6931, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6933, 0, 0, 0, 0, 0, 6931, 6935, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6370, 6928, 0, 0, 0, 0, 0, 6928, 6374, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6376, 6928, 0, 0, 0, 0, 0, 6928, 6380, 4146, 4132, 4132, 4132, 4132, 4132, 4132, 4132, 4132, 4132, 4132, 4132, 4132, 4132, 4132, 4132, 4148, 6928, 0, 0, 0, 0, 0, 6928, 4146, 4113, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4136, 6928, 0, 0, 0, 0, 0, 6928, 4128, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4136, 6928, 0, 0, 0, 0, 0, 6928, 4152, 4120, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4136, 6928, 0, 0, 0, 0, 0, 6937, 6933, 4128, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4136, 6928, 0, 0, 0, 0, 0, 0, 6928, 4128, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4136, 6928, 0, 0, 0, 0, 0, 0, 6928, 4128, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4136, 6928, 0, 0, 0, 0, 0, 0, 6928, 4128, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4136, 6928, 0, 0, 0, 0, 0, 0, 6928, 4128, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4136, 6928, 0, 0, 0, 0, 0, 0, 6928, 4128, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4136, 6928, 0, 0, 0, 0, 0, 0, 6928, 4128, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4136, 6928, 0, 0, 0, 0, 0, 0, 6928, 4128, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4136, 6928, 0, 0, 0, 0, 0, 0, 6928, 4128, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4136, 6928, 0, 0, 0, 0, 0, 0, 6928, 4128, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4136, 6928, 0, 0, 0, 0, 0, 0, 6928, 4128, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4136, 6928, 0, 0, 0, 0, 0, 0, 6928, 4128, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4136, 6928, 0, 0, 0, 0, 0, 0, 6928, 4128, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4112, 4136, 6928, 0, 0, 0, 0, 0, 0, 6928, 4152, 4140, 4140, 4140, 4140, 4140, 4140, 4140, 4140, 4140, 4140, 4140, 4140, 4140, 4140, 4140, 4150, 6928, 0, 0, 0, 0, 0, 0, 6937, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6929, 6935, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3570, 3556, 3556, 3556, 3556, 3556, 3572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3552, 3536, 3536, 3536, 3536, 3536, 3560, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3552, 3536, 3536, 3536, 3536, 3536, 3560, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3552, 3536, 3536, 3536, 3536, 3536, 3560, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3552, 3536, 3536, 3536, 3536, 3536, 3560, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3552, 3536, 3536, 3536, 3536, 3536, 3560, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3552, 3536, 3536, 3536, 3536, 3536, 3560, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3552, 3536, 3536, 3536, 3536, 3536, 3560, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3552, 3536, 3536, 3536, 3536, 3536, 3560, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3576, 3564, 3564, 3564, 3564, 3564, 3574, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 917, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 901, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 0, 0, 0, 0, 0, 0, 908, 909, 910, 0, 0, 0, 0, 0, 0, 830, 0, 0, 0, 0, 0, 0, 0, 0, 303, 0, 0, 0, 0, 0, 0, 916, 919, 918, 0, 0, 0, 0, 0, 0, 838, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 303, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 840, 0, 0, 0, 0, 0, 0, 0, 841, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 848, 0, 0, 0, 0, 0, 0, 0, 849, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 840, 0, 0, 0, 0, 0, 0, 0, 841, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 848, 0, 0, 0, 0, 0, 0, 0, 849, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 840, 0, 0, 0, 0, 0, 0, 0, 841, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 848, 0, 0, 0, 0, 0, 0, 0, 849, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 477}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [5]}, {"code": 129, "indent": 0, "parameters": [69, 0, false]}, {"code": 111, "indent": 0, "parameters": [4, 1, 0]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [4, 60, 0]}, {"code": 129, "indent": 1, "parameters": [60, 1, false]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 39, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 33, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 45, "parameters": ["$gameMap.event(2).requestBalloon(8);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(3).requestBalloon(8);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(2).requestBalloon(8);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(3).requestBalloon(8);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 设置声音 : 默认声音"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dDMS[慢]\\dac2026年6月27日 下午"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dDMS[慢]\\dac未知-会议室"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 关闭声音"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 默认模式"]}, {"code": 241, "indent": 0, "parameters": [{"name": "The Godfather Theme Song", "volume": 20, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 40, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [70], "indent": null}, {"code": 40, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [70], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<使徒>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac就是这里了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac离正式开会还有一会，黑卡蒂大人，您请自便。"]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 478]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 108, "indent": 0, "parameters": ["对话数组"]}, {"code": 356, "indent": 0, "parameters": ["SetArray 122 0 0"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 478}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 479}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [2, 0, 16, 10, 4]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[2] : 像素偏移[-6,6] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 36, "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [3, 0, 16, 13, 4]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[3] : 像素偏移[-6,6] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 36, "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 45, "parameters": ["$gameMap.event(3).requestBalloon(10);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(3).requestBalloon(10);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 36, "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 36, "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 36, "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 213, "indent": 0, "parameters": [6, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<白鹭>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac相信大家都在梦域中收到Administrator的通知了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac黑卡蒂小姐是新的六兽候补，今天会议第一项就是关于她的加入。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我反对~"]}, {"code": 213, "indent": 0, "parameters": [4, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<黑熊安德烈>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你反对？！这可是Administrator的意思。"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[2] : 像素偏移[0,0] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac主上也只是让我们讨论。没有说必须赞成吧！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac刚加入的新人就能晋升六兽，怎么想都不合理不是吗？"]}, {"code": 213, "indent": 0, "parameters": [5, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<秃鹫德雷克>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那不也正说明了主上对于黑卡蒂小姐的青睐？"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac青睐？\\.不要擅自解读Administrator大人的意思！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac也许主上只是心血来潮也不一定。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但既然主上给了她面试机会，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac当个普通使徒确实委屈，来做我的副手吧~"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [6, 2, 2]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在的六兽运作得挺好，再多一个干部完全没有必要！"]}, {"code": 213, "indent": 0, "parameters": [4, 5, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<黑熊安德烈>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac斯卡利，你这说的是什么话！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac六兽现在只有五个，加人本来就是名正言顺的。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呵，六兽这个名字你安德烈不是一直觉得傻逼吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在开始要求名正言顺了。"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac别以为我不知道，你就是眼红我现在同时统领情报和行动队！"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<黑熊安德烈>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哼，不要把别人想得和你一样幼稚。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你不幼稚，那你把手下的生意分给她一些不就行了！ "]}, {"code": 213, "indent": 0, "parameters": [5, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<秃鹫德雷克>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那似乎不合适吧！术业有专攻。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac白鹭在会前发的简报，相信大家都看过了。"]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [5, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<秃鹫德雷克>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 她在FOG的绿林射术积分已经霸榜13周之久，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 目前仍在继续。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<秃鹫德雷克>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac前段时间开始玩直播后，短时间内已经有了数十万的订阅，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还有了注册人数上千的粉丝会，里面不乏科技界的大佬。"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<秃鹫德雷克>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac所以，无论是虚拟世界，还是现实世界的成就，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac都证明黑卡蒂小姐可以胜任行动队长一职。"]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哼，年纪轻轻的小丫头。这些也算成就？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac算个屁！"]}, {"code": 213, "indent": 0, "parameters": [4, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<黑熊安德烈>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那之前的狂鲨，说起来加入教会前也只是个校园混混。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但就是你斯卡利说的：年轻人有冲劲，敢打敢拼，不像老人家畏首畏尾。"]}, {"code": 213, "indent": 0, "parameters": [5, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<秃鹫德雷克>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这个我也记得。不过后来狂鲨被抓，却又是你反对去捞他。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac还说做行动队长就是耗材，你影狐临时兼任队长，有了合适的新人就换上。"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[0,0] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, -1], "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<黑熊安德烈>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac而现在合适的新人出现了，你霸着权不交，有点包藏祸心的嫌疑了吧。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哼！你们两个一唱一和的，搞得像我真的有问题一样！"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不捞狂鲨也赖我？你们谁有这个能耐？？不是还要靠我和古蛇？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我们评估了，不划算。现在反对小丫头当干部，只是不信任她的能力！有什么私心？"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac古蛇老哥，别装睡了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你也说个话！"]}, {"code": 213, "indent": 0, "parameters": [3, 3, true]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<古蛇班纳特>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呵呵，斯卡利老弟，这次老哥不撑你了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<古蛇班纳特>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac开会前见到黑卡蒂小姐的那一刻起，我就已经想好啦~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这样面容姣好，前凸后翘的美人，完全就是特别行动队队长的最佳人选！"]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [8, 6, true]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [3, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<古蛇班纳特>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac小卡蒂~ 老哥我到时候会动用警界的人脉，给你弄一套特别行动服，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac一定性感又好用！哈哈哈哈~"]}, {"code": 213, "indent": 0, "parameters": [8, 7, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [2, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}这死老头！白鹭也是……对科研以外的一切，永远漠不关心的样子。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac行！我答应了。给你安德烈一个面子。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac等会我的提案，你也顾全大局不要反对才好。"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<黑熊安德烈>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac象征队长权力的配枪移交一下吧！"]}, {"code": 213, "indent": 0, "parameters": [2, 5, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哼！"]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [8, 12, true]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [3], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [6], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 0, "parameters": [1, "诗乃口罩为难", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [167]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac枪……\\|"]}, {"code": 401, "indent": 0, "parameters": ["\\dac必须要是枪吗？"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 213, "indent": 0, "parameters": [6, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<白鹭>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没有规定必须。"]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [8, 11, true]}, {"code": 122, "indent": 0, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 0, "parameters": [1, "诗乃口罩岔开", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [167]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可以换成弩吗？我觉得……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac特别行动队用弩更符合形象一些。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<秃鹫德雷克>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac行，那我去安排……"]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<古蛇班纳特>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac有道理！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac黑卡蒂小妹妹说得太有道理了！"]}, {"code": 213, "indent": 0, "parameters": [3, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<古蛇班纳特>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这点小事就不麻烦秃鹫老弟了，我会用人脉弄一把高端的警用弩，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac就当作见面礼物，小妹妹，不要推辞哦~"]}, {"code": 213, "indent": 0, "parameters": [6, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<白鹭>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac代号。"]}, {"code": 213, "indent": 0, "parameters": [2, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呵，我看过她的游戏人物，是个蓝毛。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这个丫头又特立独行，像个猫一样，就叫蓝猫吧。"]}, {"code": 213, "indent": 0, "parameters": [8, 5, false]}, {"code": 122, "indent": 0, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 0, "parameters": [1, "诗乃口罩不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [167]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我不要！"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 213, "indent": 0, "parameters": [4, 9, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<黑熊安德烈>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那就叫苍虎吧。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac一个代号有什么好纠结的！就这个吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac赶紧下一议案！"]}, {"code": 213, "indent": 0, "parameters": [5, 2, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<秃鹫德雷克>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还有议案？开会前怎么没有提前沟通？"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在就是沟通。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐谷和人记忆扫描计划！"]}, {"code": 213, "indent": 0, "parameters": [8, 6, false]}, {"code": 213, "indent": 0, "parameters": [6, 8, false]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 213, "indent": 0, "parameters": [5, 1, true]}, {"code": 213, "indent": 0, "parameters": [4, 5, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<黑熊安德烈>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这个之前不是已经否定过了吗？！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac虽然白鹭上次弃权，但她也说过SEED对于降临不是必要的。"]}, {"code": 213, "indent": 0, "parameters": [2, 5, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac上次是上次！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac之前最大的问题，只是SAO归还者对我们的技术有免疫力，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你们大可以说无影响全局……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac而这次FOG更新延期，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac则彻底暴露了我们在新世界构建方面有着致命的效率低下问题！"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac如果游戏世界都这么费劲，以后的现实化映射怎么办？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不去搞SEED，我看降临就是遥遥无期！"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac白鹭，我知道你自尊心很强，也不屑茅场的成果。但这一次……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你还怎么否认目前我们的技术存在缺陷？"]}, {"code": 213, "indent": 0, "parameters": [6, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<白鹭>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac见仁见智吧。"]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac感谢你以大局为重，没有罔顾事实。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac所以SEED就是必须的！"]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<古蛇班纳特>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac安德烈小老弟，我也觉得SEED是必须的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac须乡那边的进展不是很顺利啊，那家伙对于合作的开价太高了。"]}, {"code": 213, "indent": 0, "parameters": [5, 6, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<秃鹫德雷克>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac他想当什么精灵王奥伯龙，在我们之上。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac他还以为自己是结城家的继承人吗？简直是精神错乱。"]}, {"code": 213, "indent": 0, "parameters": [2, 1, false]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦，秃鹫，你也听说了？"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<秃鹫德雷克>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我不专门搞情报，但也是有些信息源的。"]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<秃鹫德雷克>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac所以你们又把主意打到了桐谷身上，最后接触希兹克利夫的人。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但他可是生还者的英雄，后续影响我觉得不好控制。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 7, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还是这个说辞。我上次就说过了，生还者才多少人？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac政府也刻意掩盖了他的功绩，知道他是英雄的民众，少之又少。"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac一开始我也没打算弄他，但后来他自投罗网、身陷局中，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac面对神赐良机，岂有浪费之理？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac而且这一次，我已经设计好了周密的计划，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐谷断然不会有半点怀疑。"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac然后你们还会说：”茅场不一定跟桐谷提过SEED“……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但我也还是那句话：不尝试又怎么知道？"]}, {"code": 213, "indent": 0, "parameters": [2, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac反对的理由还有没有新鲜的？"]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 19, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<黑熊安德烈>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac有。"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<黑熊安德烈>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你说桐人入局是神赐的机会？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不见得吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<黑熊安德烈>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac上次你提出这个计划以后，我就找机会问过Administrator，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac她暗示并不支持你的冒进行为。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<黑熊安德烈>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你现在还继续强推这个计划，根本就是在违背神的旨意！"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呵呵，你是什么时候问的？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我承认之前好几次主上都没有支持……"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但现在不同了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我昨晚又请示了，Administrator大人的回复是：随便。"]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<黑熊安德烈>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那就等我见了Administrator以后再说。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac女神又不是你想见就能见！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac有时候隔天，有时候几个星期。那他妈等到什么时候？"]}, {"code": 213, "indent": 0, "parameters": [3, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<古蛇班纳特>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac影狐是我们中最虔诚的，他万万不会假传女神的懿旨。"]}, {"code": 213, "indent": 0, "parameters": [5, 8, false]}, {"code": 230, "indent": 0, "parameters": [35]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [35]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<秃鹫德雷克>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac既然我们谁也说服不了谁，老规矩，先投票吧。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac又投票？我们2对2，白鹭那家伙总是弃权，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac也就是把决定权交给一个新加入的小丫头？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac第一个议案，我已经妥协了，这次就必须听我的！ "]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 0, "parameters": [1, "诗乃口罩淡漠", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [167]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我支持扫描桐谷和人的记忆。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 213, "indent": 0, "parameters": [2, 1, false]}, {"code": 213, "indent": 0, "parameters": [3, 4, false]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [5, 1, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [6, 8, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<黑熊安德烈>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac什么？！"]}, {"code": 213, "indent": 0, "parameters": [5, 8, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦！小丫头，不对，苍虎，有魄力！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我对你刮目相看了！"]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 0, "parameters": [1, "诗乃口罩默认", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [167]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我也希望降临能够早日实现。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 122, "indent": 0, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 0, "parameters": [1, "诗乃口罩随意", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [167]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但我的支持有两个条件：只能扫描SAO最后阶段的记忆，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac而且行动要隐秘，不能引发桐谷的怀疑。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 213, "indent": 0, "parameters": [2, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没必要吧？！"]}, {"code": 122, "indent": 0, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 0, "parameters": [1, "诗乃口罩嘲弄", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [167]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac是吗？"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 0, "parameters": [1, "诗乃口罩敷衍", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [167]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐谷和人，这个击败过希兹克利夫的男人……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我曾经在梦域和女神交流过他。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac以我的理解，女神暗示了他是特殊的存在，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac也是女神最看重的神选……"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 213, "indent": 0, "parameters": [6, 8, false]}, {"code": 213, "indent": 0, "parameters": [8, 3, false]}, {"code": 122, "indent": 0, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 0, "parameters": [1, "诗乃口罩斥责", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [167]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac作为最笃信女神的使徒，女神对桐谷和人的态度，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac影狐大人不可能不知道吧？"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 213, "indent": 0, "parameters": [2, 7, true]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 0, "parameters": [1, "诗乃口罩嘲弄", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [167]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac所以，处理和他相关的事项，谨慎小心一点，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不是应该的吗？还是说……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac影狐大人觉得女神的态度无足轻重呢？"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 213, "indent": 0, "parameters": [2, 5, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [50]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呵呵呵……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我之前就说了，我安排了周密的计划，怎么就不谨慎了呢？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这次我的棋子会引导他自愿进入FOG里的记忆扫描机器，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac扫描大脑的时候，他还会以为是在调查我们教会的情报呢！"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不过，我一直都是一个爱护后辈的好大哥嘛！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac苍虎妹妹，今天哥给你这个面子。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac扫描也仅限和茅场，也就是希兹克利夫有关的场景。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不会造成伤害，没有任何可疑。没问题了吧？"]}, {"code": 122, "indent": 0, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 0, "parameters": [1, "诗乃口罩淡漠", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [167]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呵呵~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac一开始的计划就该这么安排才对。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 213, "indent": 0, "parameters": [2, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}\\c[8]贱婊子……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那看来我们达成共识了。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac对吧，安德烈？"]}, {"code": 213, "indent": 0, "parameters": [4, 7, true]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<黑熊安德烈>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac投票都出结果了，我还说什么。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac行，那就这样！散会！"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 480]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 129, "indent": 0, "parameters": [1, 0, false]}, {"code": 129, "indent": 0, "parameters": [69, 1, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 249, "indent": 0, "parameters": [{"name": "LNSM_ME06_Sleep2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 121, "indent": 0, "parameters": [298, 298, 0]}, {"code": 201, "indent": 0, "parameters": [0, 327, 16, 11, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 480}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 6, "y": 2}, {"id": 2, "name": "EV002福克西", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "福克西2", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 117, "indent": 0, "parameters": [237]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 18, "y": 10}, {"id": 3, "name": "EV003古蛇", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "People1(vx)", "direction": 4, "pattern": 1, "characterIndex": 6}, "list": [{"code": 117, "indent": 0, "parameters": [237]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 19, "y": 10}, {"id": 4, "name": "EV004黑熊", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "六兽黑熊", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[6,6]"]}, {"code": 111, "indent": 0, "parameters": [6, -1, 6]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "A", 1]}, {"code": 205, "indent": 2, "parameters": [0, {"list": [{"code": 36, "indent": null}, {"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 25, "indent": null}]}, {"code": 213, "indent": 2, "parameters": [4, 1, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<戴面具的男子>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac黑卡蒂小姐，你好。你就是顶替狂鲨的人选啊，百闻不如一见。"]}, {"code": 401, "indent": 2, "parameters": ["\\dac嗯，犀利而坚定的眼神，我相信你能胜任。"]}, {"code": 213, "indent": 2, "parameters": [-1, 2, true]}, {"code": 122, "indent": 2, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 2, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 2, "parameters": [1, "诗乃口罩默认", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 2, "parameters": [167]}, {"code": 232, "indent": 2, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac你是？"]}, {"code": 235, "indent": 2, "parameters": [1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<戴面具的男子>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac抱歉，忘记自我介绍了。我是六兽之一的黑熊。"]}, {"code": 401, "indent": 2, "parameters": ["\\dac教会非要大家使用动物起代号。但我觉得挺傻的，你叫我安德烈吧。"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<黑熊安德烈>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac我在教会里主要是负责财务。主要的正规产业也是我在统筹经营。"]}, {"code": 401, "indent": 2, "parameters": ["\\dac以后关于特别行动队的经费，我们肯定少不了交流，希望能友好合作。"]}, {"code": 122, "indent": 2, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 2, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 2, "parameters": [1, "诗乃口罩淡漠", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 2, "parameters": [167]}, {"code": 232, "indent": 2, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac但愿如此。"]}, {"code": 235, "indent": 2, "parameters": [1]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 205, "indent": 2, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 35, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 35, "indent": null}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 213, "indent": 2, "parameters": [-1, 8, true]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 10}, {"id": 5, "name": "EV005秃鹫", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "gnr_shop001", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 117, "indent": 0, "parameters": [5]}, {"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[6,6]"]}, {"code": 111, "indent": 0, "parameters": [6, -1, 6]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "A", 1]}, {"code": 205, "indent": 2, "parameters": [0, {"list": [{"code": 36, "indent": null}, {"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 25, "indent": null}]}, {"code": 213, "indent": 2, "parameters": [0, 3, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<和蔼的中年人>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac你好。我是六兽的秃鹫，也可以叫我德里克。"]}, {"code": 401, "indent": 2, "parameters": ["\\dac你是安保部……唔，我的意思是特别行动队，你是新队长候选吧？"]}, {"code": 213, "indent": 2, "parameters": [-1, 2, true]}, {"code": 122, "indent": 2, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 2, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 2, "parameters": [1, "诗乃口罩默认", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 2, "parameters": [167]}, {"code": 232, "indent": 2, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac刚才说的安保部是？"]}, {"code": 235, "indent": 2, "parameters": [1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<秃鹫德里克>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac呵呵呵，我平时是在大公司里工作的，"]}, {"code": 401, "indent": 2, "parameters": ["\\dac习惯把教会的一些机构叫成公司里的部门名称。别见怪。"]}, {"code": 122, "indent": 2, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 2, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 2, "parameters": [1, "诗乃口罩默认", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 2, "parameters": [167]}, {"code": 232, "indent": 2, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac不会。"]}, {"code": 401, "indent": 2, "parameters": ["\\dac但和其他人相比，大叔你看起来很普通。"]}, {"code": 235, "indent": 2, "parameters": [1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<秃鹫德里克>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac呵呵，确实。想了解六兽的情况吗？"]}, {"code": 401, "indent": 2, "parameters": ["\\dac有些人喜欢保密，但我觉得六兽间不该有太多的秘密。"]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 119, "indent": 2, "parameters": ["问答"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 118, "indent": 2, "parameters": ["问答"]}, {"code": 205, "indent": 2, "parameters": [0, {"list": [{"code": 36, "indent": null}, {"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 25, "indent": null}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<秃鹫德里克>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac你如果想了解其他干部的情况，可以直接问我，知无不答。"]}, {"code": 118, "indent": 2, "parameters": ["选项"]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["// 检查数组中是否同时存在21、22、23、24和25"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(21) && array122.includes(22) && array122.includes(23) &&"]}, {"code": 655, "indent": 2, "parameters": ["    array122.includes(24) && array122.includes(25)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(356, true); // 同时存在则开启开关356"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(356, false); // 不同时存在则关闭开关356"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 356, 1]}, {"code": 102, "indent": 3, "parameters": [["黑熊", "秃鹫", "古蛇", "影狐", "白鹭", "取消"], 5, -1, 1, 1]}, {"code": 402, "indent": 3, "parameters": [0, "黑熊"]}, {"code": 122, "indent": 4, "parameters": [123, 123, 0, 0, 21]}, {"code": 355, "indent": 4, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 4, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 4, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 4, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 4, "parameters": ["} else {"]}, {"code": 655, "indent": 4, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 4, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 4, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 4, "parameters": ["}"]}, {"code": 111, "indent": 4, "parameters": [0, 355, 1]}, {"code": 101, "indent": 5, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 5, "parameters": ["\\nc<秃鹫德里克>"]}, {"code": 401, "indent": 5, "parameters": ["\\dac他没说过自己的身份，安德烈估计也是化名吧。"]}, {"code": 401, "indent": 5, "parameters": ["\\dac我知道的情况并不多。但是个好人。"]}, {"code": 101, "indent": 5, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 5, "parameters": ["\\nc<秃鹫德里克>"]}, {"code": 401, "indent": 5, "parameters": ["\\dac他经营管理挺有一套的，把教会的产业都打理的很好。"]}, {"code": 401, "indent": 5, "parameters": ["\\dac也许是某个大公司的高管。"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 118, "indent": 5, "parameters": ["重复话题"]}, {"code": 122, "indent": 5, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 5, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 5, "parameters": [1, "诗乃口罩淡漠", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 5, "parameters": [167]}, {"code": 232, "indent": 5, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 5, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 5, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 5, "parameters": ["\\dac\\c[210](已经聊过这个人的情况了……)"]}, {"code": 235, "indent": 5, "parameters": [1]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 119, "indent": 4, "parameters": ["选项"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [1, "秃鹫"]}, {"code": 122, "indent": 4, "parameters": [123, 123, 0, 0, 22]}, {"code": 355, "indent": 4, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 4, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 4, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 4, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 4, "parameters": ["} else {"]}, {"code": 655, "indent": 4, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 4, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 4, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 4, "parameters": ["}"]}, {"code": 111, "indent": 4, "parameters": [0, 355, 1]}, {"code": 101, "indent": 5, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 5, "parameters": ["\\nc<秃鹫德里克>"]}, {"code": 401, "indent": 5, "parameters": ["\\dac我啊？教会里管货的。平时是大公司的中层干部。"]}, {"code": 401, "indent": 5, "parameters": ["\\dac我其实不太愿意当高管的，责任大，风险高。 "]}, {"code": 101, "indent": 5, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 5, "parameters": ["\\nc<秃鹫德里克>"]}, {"code": 401, "indent": 5, "parameters": ["\\dac但你懂的吧，加入教会的目的，大家都是有想要实现的梦想的。"]}, {"code": 401, "indent": 5, "parameters": ["\\dac我虽然自己没什么想要的，但是……"]}, {"code": 101, "indent": 5, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 5, "parameters": ["\\nc<秃鹫德里克>"]}, {"code": 401, "indent": 5, "parameters": ["\\dac为了我那个封闭了内心的女儿，希望Administrator降临后，"]}, {"code": 401, "indent": 5, "parameters": ["\\dac能创造一个让她更加开心的世界。"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 119, "indent": 5, "parameters": ["重复话题"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 119, "indent": 4, "parameters": ["选项"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [2, "古蛇"]}, {"code": 122, "indent": 4, "parameters": [123, 123, 0, 0, 23]}, {"code": 355, "indent": 4, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 4, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 4, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 4, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 4, "parameters": ["} else {"]}, {"code": 655, "indent": 4, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 4, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 4, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 4, "parameters": ["}"]}, {"code": 111, "indent": 4, "parameters": [0, 355, 1]}, {"code": 101, "indent": 5, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 5, "parameters": ["\\nc<秃鹫德里克>"]}, {"code": 401, "indent": 5, "parameters": ["\\dac是我们里面年纪最大的。他大概是希望降临以后能重回青春吧。"]}, {"code": 401, "indent": 5, "parameters": ["\\dac据说是个警方的高管来着。主管教会的色情产业。"]}, {"code": 101, "indent": 5, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 5, "parameters": ["\\nc<秃鹫德里克>"]}, {"code": 401, "indent": 5, "parameters": ["\\dac他和影狐关系比较好，对我们剩下的人都很冷漠。"]}, {"code": 401, "indent": 5, "parameters": ["\\dac所以我不太清楚他别的事情。"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 119, "indent": 5, "parameters": ["重复话题"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 119, "indent": 4, "parameters": ["选项"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [3, "影狐"]}, {"code": 122, "indent": 4, "parameters": [123, 123, 0, 0, 24]}, {"code": 355, "indent": 4, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 4, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 4, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 4, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 4, "parameters": ["} else {"]}, {"code": 655, "indent": 4, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 4, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 4, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 4, "parameters": ["}"]}, {"code": 111, "indent": 4, "parameters": [0, 355, 1]}, {"code": 101, "indent": 5, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 5, "parameters": ["\\nc<秃鹫德里克>"]}, {"code": 401, "indent": 5, "parameters": ["\\dac主管情报。狂鲨被抓了以后，特别行动队也是他在兼任临时队长。"]}, {"code": 401, "indent": 5, "parameters": ["\\dac挺有想法的一个年轻人。能力很强，擅长易容和潜入，但自视甚高。"]}, {"code": 101, "indent": 5, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 5, "parameters": ["\\nc<秃鹫德里克>"]}, {"code": 401, "indent": 5, "parameters": ["\\dac对于降临，他的态度是最激进的。和安德烈那家伙很不对付。"]}, {"code": 401, "indent": 5, "parameters": ["\\dac因为安德烈性格特别谨慎，他希望一步步推进降临计划。"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 119, "indent": 5, "parameters": ["重复话题"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 119, "indent": 4, "parameters": ["选项"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [4, "白鹭"]}, {"code": 122, "indent": 4, "parameters": [123, 123, 0, 0, 25]}, {"code": 355, "indent": 4, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 4, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 4, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 4, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 4, "parameters": ["} else {"]}, {"code": 655, "indent": 4, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 4, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 4, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 4, "parameters": ["}"]}, {"code": 111, "indent": 4, "parameters": [0, 355, 1]}, {"code": 101, "indent": 5, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 5, "parameters": ["\\nc<秃鹫德里克>"]}, {"code": 401, "indent": 5, "parameters": ["\\dac那个小姑娘，呃，其实算是老姑娘了，"]}, {"code": 401, "indent": 5, "parameters": ["\\dac平时不怎么说话，负责科研的。"]}, {"code": 101, "indent": 5, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 5, "parameters": ["\\nc<秃鹫德里克>"]}, {"code": 401, "indent": 5, "parameters": ["\\dac她研发出来的东西简直像是魔法，很厉害的。"]}, {"code": 401, "indent": 5, "parameters": ["\\dac其他的事情，就不清楚了。"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 119, "indent": 5, "parameters": ["重复话题"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 119, "indent": 4, "parameters": ["选项"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [5, "取消"]}, {"code": 118, "indent": 4, "parameters": ["取消"]}, {"code": 122, "indent": 4, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 4, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 4, "parameters": [1, "诗乃口罩随意", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 4, "parameters": [167]}, {"code": 232, "indent": 4, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 4, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 4, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 4, "parameters": ["\\dac谢谢，暂时就这些。"]}, {"code": 235, "indent": 4, "parameters": [1]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 404, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [-1, 8, false]}, {"code": 122, "indent": 3, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 3, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 3, "parameters": [1, "诗乃口罩默认", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [167]}, {"code": 232, "indent": 3, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac\\c[210](应该问不出更多东西了……)"]}, {"code": 235, "indent": 3, "parameters": [1]}, {"code": 119, "indent": 3, "parameters": ["取消"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 205, "indent": 2, "parameters": [0, {"list": [{"code": 18, "indent": null}, {"code": 35, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 35, "indent": null}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 13}, {"id": 6, "name": "EV006白鹭", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "六兽白鹤", "direction": 4, "pattern": 2, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-6,6]"]}, {"code": 111, "indent": 0, "parameters": [6, -1, 4]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "A", 1]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<白鹭>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac我……不想聊天。"]}, {"code": 401, "indent": 2, "parameters": ["\\dac坐着等开会吧。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 213, "indent": 2, "parameters": [-1, 8, true]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 16}, {"id": 7, "name": "EV007使徒", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "SF_Monster", "direction": 6, "pattern": 1, "characterIndex": 7}, "list": [{"code": 117, "indent": 0, "parameters": [5]}, {"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<使徒>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac您还有什么疑问吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我会尽量回答的。"]}, {"code": 118, "indent": 0, "parameters": ["选项"]}, {"code": 355, "indent": 0, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 0, "parameters": ["// 检查数组中是否同时存在11、12、13和14"]}, {"code": 655, "indent": 0, "parameters": ["if (array122.includes(11) && array122.includes(12) && array122.includes(13) && array122.includes(14)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(356, true); // 同时存在则开启开关356"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(356, false); // 不同时存在则关闭开关356"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 356, 1]}, {"code": 102, "indent": 1, "parameters": [["关于教会", "关于使徒", "关于六兽", "关于降临", "   取消"], 1, -1, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "关于教会"]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 11]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<使徒>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac公理教会是我们的实际存在，但仅限于内部表述。"]}, {"code": 401, "indent": 3, "parameters": ["\\dac在公开场合，我们还是以RATH的名义进行社会活动。"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 118, "indent": 3, "parameters": ["重复话题"]}, {"code": 122, "indent": 3, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 3, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 3, "parameters": [1, "诗乃口罩默认", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [167]}, {"code": 232, "indent": 3, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac\\c[210](已经聊过这个话题了……)"]}, {"code": 235, "indent": 3, "parameters": [1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["选项"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "关于使徒"]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 12]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<使徒>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac使徒都是在梦域中得到女神召见的幸运之人。"]}, {"code": 401, "indent": 3, "parameters": ["\\dac承蒙女神恩宠，自愿为女神效力。"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<使徒>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac但不是所有人都有这样的天命，"]}, {"code": 401, "indent": 3, "parameters": ["\\dac也不是所有人都能做出正确的选择。"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<使徒>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac无法将梦域记忆带入现世之人，以及不理解女神伟大之人，"]}, {"code": 401, "indent": 3, "parameters": ["\\dac便没有成为使徒的资格。"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 119, "indent": 3, "parameters": ["重复话题"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["选项"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [2, "关于六兽"]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 13]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<使徒>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac六兽是教会的六大干部。按影狐大人的解释，"]}, {"code": 401, "indent": 3, "parameters": ["\\dac这是要我们使徒遵循女神大人的指示，像野兽一样尽情释放欲望。"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<使徒>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac每个干部都有分管的领域。"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<使徒>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac黑熊大人负责财务和产业，秃鹫大人负责后勤。"]}, {"code": 401, "indent": 3, "parameters": ["\\dac古蛇大人负责风俗业，白鹭大人是科研组的负责人。"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<使徒>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac负责暴力行动的狂鲨大人因为行事过于鲁莽，前段时间又因为小事被抓， "]}, {"code": 401, "indent": 3, "parameters": ["\\dac已经被组织放弃了。现在这部分工作由情报影狐大人临时兼管。"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<使徒>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac负责行动队的干部更换频率很高，普遍在任时间不长。"]}, {"code": 401, "indent": 3, "parameters": ["\\dac就像是这个岗位背负的诅咒一样…啊不对，应该说是天命。"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<使徒>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac如果您能正式成为六兽，不出意外，行动队应该就是您来负责了。"]}, {"code": 401, "indent": 3, "parameters": ["\\dac祝愿您能武运昌隆，常胜久安。"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 119, "indent": 3, "parameters": ["重复话题"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["选项"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [3, "关于降临"]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 14]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 213, "indent": 3, "parameters": [0, 6, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<使徒>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac呃……您不知道降临吗？"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<使徒>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac降临，就是迎接Administrator从梦域来到现世。"]}, {"code": 401, "indent": 3, "parameters": ["\\dac是我们所有使徒的共同愿望，其中又以影狐大人最为狂热。"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<使徒>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac因为降临后，Administrator会将这个世界改造成天堂，"]}, {"code": 401, "indent": 3, "parameters": ["\\dac并且还会实现有重大贡献使徒的一个愿望。"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<使徒>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac那个…虽然我是一个普通使徒，而您可能即将成为六兽……"]}, {"code": 401, "indent": 3, "parameters": ["\\dac但就算有些僭越，我还是觉得有必要说……"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<使徒>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac但还是建议您闲暇之余研读一下教义，"]}, {"code": 401, "indent": 3, "parameters": ["\\dac不然，对于您日后的领导工作也是不利的。"]}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 119, "indent": 3, "parameters": ["重复话题"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["选项"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [4, "   取消"]}, {"code": 118, "indent": 2, "parameters": ["取消"]}, {"code": 122, "indent": 2, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 2, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 2, "parameters": [1, "诗乃口罩默认", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 2, "parameters": [167]}, {"code": 232, "indent": 2, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac暂时没问题了。"]}, {"code": 235, "indent": 2, "parameters": [1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 213, "indent": 1, "parameters": [-1, 8, false]}, {"code": 122, "indent": 1, "parameters": [131, 131, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 1, "parameters": [1, "诗乃口罩默认", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [167]}, {"code": 232, "indent": 1, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](应该问不出更多东西了……)"]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 119, "indent": 1, "parameters": ["取消"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 6}, {"id": 8, "name": "EV008诗乃座位", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[6,6]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac等待到会议开始？"]}, {"code": 102, "indent": 0, "parameters": [["是", "否"], 1, 0, 2, 1]}, {"code": 402, "indent": 0, "parameters": [0, "是"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 479]}, {"code": 201, "indent": 1, "parameters": [0, 359, 8, 16, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "否"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 479}, "directionFix": false, "image": {"tileId": 0, "characterName": "诗乃像素", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[6,6]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 16}, null]}