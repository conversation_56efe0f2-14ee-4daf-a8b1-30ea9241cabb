{"autoplayBgm": true, "autoplayBgs": false, "battleback1Name": "Grassland", "battleback2Name": "Forest", "bgm": {"name": "ayato_02_Stopped clock", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 23, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": true, "tilesetId": 57, "width": 40, "data": [3248, 3248, 3248, 3272, 6048, 6032, 6032, 6032, 6032, 6036, 6060, 6060, 6060, 6060, 6060, 6060, 6060, 6040, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3248, 3248, 3248, 3272, 6048, 6032, 6032, 6036, 6060, 6070, 2947, 2945, 2934, 2932, 2932, 2932, 2948, 6072, 6040, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3248, 3248, 3248, 3272, 6048, 6032, 6036, 6070, 2946, 2933, 2951, 2862, 2952, 2940, 2940, 2920, 2914, 2948, 6072, 6040, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3248, 3248, 3248, 3272, 6048, 6036, 6070, 2946, 2917, 2950, 2994, 2980, 2980, 2980, 2996, 2952, 2920, 2914, 2948, 6048, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3248, 3248, 3248, 3272, 6050, 6070, 2946, 2917, 2950, 2994, 2965, 2988, 2988, 2988, 2970, 2996, 2952, 2920, 2936, 6048, 6032, 6036, 6060, 6060, 6060, 6040, 6032, 6032, 6032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3248, 3248, 3248, 3272, 6064, 2946, 2913, 2936, 2995, 2989, 2998, 2850, 2836, 2852, 3000, 2990, 2997, 2952, 2937, 6048, 6036, 6070, 2907, 2886, 2900, 6072, 6060, 6040, 6032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3248, 3248, 3248, 3272, 6076, 2952, 2940, 2950, 3004, 2850, 2836, 2817, 2816, 2818, 2836, 2852, 3001, 2997, 2944, 6072, 6070, 2906, 2858, 2904, 2894, 2886, 2900, 6072, 6060, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3248, 3248, 3248, 3272, 2859, 2849, 2849, 2849, 2838, 2817, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 3004, 2953, 2957, 2907, 2903, 2848, 3003, 3005, 2904, 2894, 2897, 2897, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3276, 3276, 3276, 3278, 3281, 3281, 3281, 3285, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2837, 2849, 2849, 2849, 2838, 2819, 2852, 2910, 3678, 2850, 2836, 2836, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2849, 2849, 2849, 2849, 2849, 2861, 3002, 3289, 3285, 2856, 2824, 2816, 2816, 2816, 2816, 2820, 2844, 2854, 2995, 2993, 3005, 2834, 2844, 2826, 2836, 2836, 2817, 2820, 2844, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6052, 6052, 6052, 6052, 6052, 6068, 2992, 2910, 3280, 2906, 2832, 2816, 2816, 2816, 2816, 2840, 2907, 2901, 3004, 2851, 2849, 2855, 3678, 2856, 2844, 2824, 2820, 2854, 2898, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6032, 6032, 6032, 6032, 6032, 6056, 2977, 2996, 3280, 2908, 2856, 2844, 2844, 2844, 2828, 2846, 2861, 2908, 3678, 2848, 6066, 6068, 2862, 3675, 3677, 2834, 2854, 2907, 2893, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6032, 6032, 6032, 6032, 6032, 6056, 2976, 2984, 3289, 3281, 3270, 3268, 3268, 3284, 2860, 3282, 3268, 3268, 3284, 2848, 6048, 6034, 6068, 2859, 2849, 2855, 6066, 6052, 6052, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6032, 6032, 6032, 6032, 6032, 6056, 2976, 2984, 2862, 2910, 3288, 3276, 3276, 3278, 3270, 3249, 3248, 3248, 3272, 2848, 6048, 6032, 6034, 6052, 6052, 6052, 6033, 6032, 6032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6032, 6032, 6032, 6032, 6032, 6056, 3000, 2970, 2996, 2850, 2837, 2849, 2838, 2852, 3288, 3256, 3252, 3276, 3286, 2860, 6048, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6032, 6032, 6032, 6032, 6032, 6034, 6068, 3000, 2985, 2856, 2854, 2906, 2832, 2840, 2906, 3288, 3286, 2859, 2861, 6066, 6033, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6032, 6032, 6032, 6032, 6032, 6032, 6034, 6068, 3001, 3005, 3678, 2896, 2834, 2854, 2905, 2909, 2862, 6066, 6052, 6033, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6034, 6052, 6068, 2858, 2908, 2848, 6066, 6052, 6052, 6052, 6033, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6056, 2848, 3290, 2848, 6048, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6056, 2848, 3280, 2848, 6048, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6056, 2848, 3280, 2848, 6048, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6056, 2848, 3280, 2848, 6048, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6056, 2848, 3280, 2848, 6048, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 6032, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4206, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4206, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4203, 4205, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3098, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3088, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3088, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3088, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3088, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3088, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3088, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3088, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3088, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3088, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 267, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 267, 0, 0, 0, 0, 0, 0, 257, 256, 256, 257, 256, 256, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 267, 0, 0, 0, 0, 256, 256, 273, 272, 274, 273, 274, 274, 272, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 364, 364, 364, 364, 0, 0, 256, 274, 272, 0, 0, 0, 0, 0, 0, 0, 274, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 267, 0, 0, 257, 274, 0, 0, 0, 0, 0, 0, 0, 0, 0, 315, 318, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 256, 273, 6, 0, 0, 0, 0, 0, 820, 821, 0, 0, 0, 0, 0, 0, 374, 364, 364, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 274, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 295, 294, 363, 326, 308, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 835, 836, 0, 0, 0, 0, 0, 256, 257, 327, 299, 363, 0, 316, 326, 328, 329, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 843, 844, 0, 0, 0, 0, 0, 272, 273, 317, 341, 332, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 283, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 291, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 285, 0, 364, 0, 0, 283, 284, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 367, 364, 364, 366, 284, 284, 291, 355, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 365, 287, 286, 355, 356, 356, 366, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 365, 311, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 367, 0, 0, 366, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 367, 357, 0, 0, 0, 0, 0, 3, 0, 0, 987, 0, 286, 363, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 356, 366, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 365, 311, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 367, 0, 0, 0, 366, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 374, 364, 375, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 295, 294, 371, 372, 373, 295, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 295, 336, 327, 326, 337, 339, 308, 327, 326, 294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 375, 372, 372, 372, 374, 0, 0, 295, 327, 344, 317, 315, 345, 347, 316, 317, 315, 326, 294, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 365, 271, 276, 270, 0, 0, 295, 327, 317, 0, 809, 810, 811, 812, 813, 0, 0, 283, 286, 0, 0, 364, 364, 364, 364, 364, 0, 0, 0, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 365, 269, 0, 267, 0, 295, 327, 317, 0, 816, 817, 818, 819, 830, 831, 0, 0, 291, 355, 366, 0, 372, 372, 374, 364, 364, 364, 364, 0, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 365, 269, 0, 267, 0, 319, 317, 6, 0, 824, 825, 826, 827, 828, 829, 0, 0, 291, 371, 372, 0, 280, 281, 282, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 365, 269, 0, 275, 276, 277, 0, 3, 0, 832, 833, 834, 792, 778, 837, 838, 1, 307, 337, 338, 339, 288, 289, 290, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 365, 269, 0, 0, 0, 0, 0, 0, 1, 840, 841, 842, 800, 786, 845, 846, 0, 315, 345, 346, 347, 296, 297, 298, 374, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 365, 263, 284, 284, 284, 284, 285, 0, 0, 848, 849, 850, 851, 852, 853, 854, 2, 0, 0, 0, 0, 304, 305, 306, 363, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 367, 0, 0, 0, 0, 0, 287, 285, 0, 856, 857, 858, 859, 860, 861, 862, 0, 0, 0, 283, 284, 320, 260, 324, 366, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 367, 357, 293, 0, 0, 865, 866, 867, 868, 869, 870, 0, 0, 283, 286, 0, 356, 356, 366, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 364, 365, 301, 0, 0, 873, 874, 875, 876, 877, 0, 0, 0, 291, 355, 366, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 364, 367, 325, 285, 37, 0, 0, 0, 0, 0, 995, 973, 995, 299, 363, 364, 0, 364, 364, 364, 364, 364, 364, 0, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 364, 364, 365, 293, 45, 0, 0, 0, 38, 39, 0, 283, 284, 324, 366, 364, 0, 364, 364, 364, 364, 0, 0, 0, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 364, 364, 365, 301, 1, 0, 0, 0, 46, 47, 0, 321, 355, 366, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 364, 367, 325, 320, 285, 0, 283, 284, 284, 284, 286, 363, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 364, 367, 357, 293, 0, 291, 355, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 365, 301, 0, 291, 363, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 365, 301, 0, 291, 363, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 365, 301, 0, 291, 363, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 365, 301, 0, 291, 363, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 365, 301, 0, 291, 363, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": false, "variableValue": 220}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 223, "indent": 0, "parameters": [[-68, -68, 0, 68], 1, false]}, {"code": 356, "indent": 0, "parameters": ["particle set 雨 weather"]}, {"code": 231, "indent": 0, "parameters": [60, "白色线条864-576x1008", 0, 0, 864, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [48, "黑幕", 0, 0, 864, 0, 40, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [49, "", 0, 0, 864, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [49, 0, 0, 0, 864, 0, 100, 100, 255, 0, 30, false]}, {"code": 231, "indent": 0, "parameters": [50, "", 0, 0, 864, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 播放简单状态元集合 : 集合[木屋正常位素股影绘]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 播放简单状态元集合 : 集合[木屋正常位素股]"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 11}, {"id": 2, "name": "狂战士", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor1", "direction": 6, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"tileId": 0, "characterName": "Damage1", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 230, "indent": 0, "parameters": [30]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 本事件 : 横向挤扁 : 时间[60] : 横向比例[1.5]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}], "x": 9, "y": 12}, {"id": 3, "name": "死亡骑士", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor2", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"tileId": 0, "characterName": "Damage1", "direction": 4, "pattern": 1, "characterIndex": 2}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 本事件 : 横向挤扁 : 时间[60] : 横向比例[1.5]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}], "x": 8, "y": 11}, {"id": 4, "name": "剧情事件", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 300, 1]}, {"code": 121, "indent": 1, "parameters": [70, 70, 0]}, {"code": 111, "indent": 1, "parameters": [0, 22, 0]}, {"code": 356, "indent": 2, "parameters": [">地图永久漂浮文字 : 漂浮文字[1] : 清除"]}, {"code": 356, "indent": 2, "parameters": [">地图永久漂浮文字 : 漂浮文字[2] : 清除"]}, {"code": 356, "indent": 2, "parameters": [">高级变量框 : 框设置[1] : 隐藏"]}, {"code": 356, "indent": 2, "parameters": [">高级变量框 : 框设置[2] : 隐藏"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">菜单GIF : GIF[23] : 隐藏"]}, {"code": 356, "indent": 0, "parameters": [">菜单GIF : GIF[21] : 显示"]}, {"code": 356, "indent": 0, "parameters": ["particle set 寂静场域 event:7"]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [15, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 111, "indent": 0, "parameters": [0, 233, 1]}, {"code": 122, "indent": 1, "parameters": [31, 31, 1, 0, 3]}, {"code": 122, "indent": 1, "parameters": [33, 33, 1, 0, 1]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 102], 30, false]}, {"code": 356, "indent": 1, "parameters": ["infotext : 11 : 1 : 590 : 140 : 28 : 150 : 0 : 1 : 0 : 亚  丝  娜  信  息  更  新"]}, {"code": 356, "indent": 1, "parameters": ["addLog 亚丝娜的经验似乎发生了一些变化..."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 15, "parameters": [15], "indent": 0}, {"code": 17, "indent": null}, {"code": 15, "parameters": [25], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [25], "indent": 0}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [25], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [25], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [14, 2, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<术士玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 奇怪那2个人的气息好像突然消失了?"]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<盗贼玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 估计是用了什么隐匿类的技能吧，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这样看对方似乎也发现我们了。"]}, {"code": 213, "indent": 0, "parameters": [16, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<盗贼玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过这里能藏身的区域不是显而易见嘛。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别傻站着，跟我上来。"]}, {"code": 213, "indent": 0, "parameters": [15, 1, false]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [15, {"list": [{"code": 4, "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<暗杀者玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 等等…有别的气息接近，就快要过来了。"]}, {"code": 213, "indent": 0, "parameters": [16, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<盗贼玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哼，估计是其他收到消息过来的玩家，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那就你们守住梯子，我去收拾藏起来的家伙。"]}, {"code": 213, "indent": 0, "parameters": [15, 8, false]}, {"code": 213, "indent": 0, "parameters": [14, 8, false]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [15, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 19, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[16] : 像素偏移[0,-24] : 时间[20]"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 213, "indent": 0, "parameters": [16, 8, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [16, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<骑士玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 妈的，不应该啊，这门应该是很容易撬开才对……"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac小屋那边有其他玩家……"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": 0}, {"code": 3, "indent": 0}, {"code": 3, "indent": 0}, {"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 喂喂，大家聚集在这里是有什么好事吗?"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 也算上我一个嘛。"]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [15, 5, false]}, {"code": 205, "indent": 0, "parameters": [15, {"list": [{"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<暗杀者玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac小子，我们”弑神★堕天”在这里办事，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 识相点赶紧给我滚远点。"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](果然是看到系统消息过来抢素材的嘛，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 什么奇葩公会，给我增加莫名其妙的麻烦，可恶！ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没看到亚丝娜和猪田，他们应该还在树屋里面……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 弑神★堕天？呵呵~"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 14, "parameters": [3, -3], "indent": null}, {"code": 37, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [3, -3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [15, 1, false]}, {"code": 213, "indent": 0, "parameters": [14, 1, false]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [15, {"list": [{"code": 25, "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 25, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [16, 12, false]}, {"code": 212, "indent": 0, "parameters": [16, 27, false]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 225, "indent": 0, "parameters": [7, 5, 15, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[16] : 像素偏移[0,0] : 时间[15]"]}, {"code": 225, "indent": 0, "parameters": [2, 6, 30, false]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 34, "indent": null}, {"code": 14, "parameters": [0, 3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 3], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 1]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 抱歉了，今天无论是弑神堕天还是杀猪升天，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这里都禁止通行。"]}, {"code": 205, "indent": 0, "parameters": [15, {"list": [{"code": 3, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [14, 12, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<术士玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac好...好快的速度！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这小子，是是是什么人？"]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 2]}, {"code": 213, "indent": 0, "parameters": [16, 7, false]}, {"code": 205, "indent": 0, "parameters": [15, {"list": [{"code": 34, "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<盗贼玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咳咳，妈的别怕他，我们这边可是有三个人，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 让这个装逼的家伙见识见识弑神★堕天的厉害。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 3], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [16, 1, false]}, {"code": 213, "indent": 0, "parameters": [14, 1, false]}, {"code": 213, "indent": 0, "parameters": [15, 12, false]}, {"code": 212, "indent": 0, "parameters": [-1, 2, false]}, {"code": 205, "indent": 0, "parameters": [15, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 35, "indent": null}, {"code": 32, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 36, "indent": null}, {"code": 31, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 32, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 31, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 13, "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 19, "indent": null}, {"code": 13, "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那就来试试呗~ 不过我也提醒你们： "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 刚才两下都是用的剑鞘，现在我可要拔剑了。 "]}, {"code": 213, "indent": 0, "parameters": [15, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<暗杀者玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 刚才一击我血槽就见底了，还是剑鞘……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这是什么怪物，点子太硬了，保命要紧！ "]}, {"code": 205, "indent": 0, "parameters": [15, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<术士玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我大姨妈来了，我我我也撤了！"]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [16, 5, false]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<盗贼玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你们两个没义气的家伙！ "]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在就剩你一个了，来单挑啊~ "]}, {"code": 213, "indent": 0, "parameters": [16, 12, false]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 19, "indent": null}, {"code": 13, "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<盗贼玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你、你别嚣张！我们只是公会负责侦察的小队，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 有本事你你你别跑，公会主力大哥会来收拾你的！"]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 37, "indent": null}, {"code": 33, "indent": null}, {"code": 29, "parameters": [5], "indent": 0}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 3]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([279, 27, 'C'], true);"]}, {"code": 356, "indent": 0, "parameters": ["particle clear 闪电"]}, {"code": 223, "indent": 0, "parameters": [[-68, -68, 0, 68], 15, false]}, {"code": 232, "indent": 0, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 0, 0, 15, false]}, {"code": 224, "indent": 0, "parameters": [[255, 136, 221, 170], 60, false]}, {"code": 250, "indent": 0, "parameters": [{"name": "puu10", "volume": 60, "pitch": 90, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [68, "", 0, 0, 346, 400, 100, 100, 255, 0]}, {"code": 356, "indent": 0, "parameters": [">图片动态遮罩板A : 简单透视镜 : 图片[68] : 样式[20]"]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动态遮罩板A : 图片[68] : 启用该插件的动态遮罩板"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[68] : 设置动画序列 : 动画序列[6]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[68] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[68] : 播放简单状态元集合 : 集合[木屋插入闪图]"]}, {"code": 231, "indent": 0, "parameters": [65, "剧情小图背景3", 0, 0, -816, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [66, "剧情小图背景1", 0, 0, -29, -7, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [67, "剧情小图背景2", 0, 0, 29, 7, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [66, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 0, "parameters": [67, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 0, "parameters": [68, 0, 0, 0, 375, 407, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 0, "parameters": [65, 0, 0, 0, -20, 0, 100, 100, 255, 0, 15, true]}, {"code": 232, "indent": 0, "parameters": [65, 0, 0, 0, 20, 0, 100, 100, 255, 0, 30, false]}, {"code": 232, "indent": 0, "parameters": [66, 0, 0, 0, 58, 14, 100, 100, 0, 0, 100, false]}, {"code": 232, "indent": 0, "parameters": [67, 0, 0, 0, -29, -7, 100, 100, 0, 0, 100, false]}, {"code": 232, "indent": 0, "parameters": [65, 0, 0, 0, 836, 0, 100, 100, 0, 0, 100, false]}, {"code": 232, "indent": 0, "parameters": [68, 0, 0, 0, 404, 414, 100, 100, 0, 0, 130, false]}, {"code": 230, "indent": 0, "parameters": [70]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 60, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 播放简单状态元集合 : 集合[木屋正常位影绘]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 播放简单状态元集合 : 集合[木屋正常位]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 235, "indent": 0, "parameters": [65]}, {"code": 235, "indent": 0, "parameters": [66]}, {"code": 235, "indent": 0, "parameters": [67]}, {"code": 235, "indent": 0, "parameters": [68]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([279, 27, 'C'], false);"]}, {"code": 111, "indent": 0, "parameters": [0, 233, 1]}, {"code": 122, "indent": 1, "parameters": [31, 31, 1, 0, 10]}, {"code": 122, "indent": 1, "parameters": [32, 32, 1, 0, 1]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 102], 30, false]}, {"code": 356, "indent": 1, "parameters": ["infotext : 11 : 1 : 590 : 140 : 28 : 150 : 0 : 1 : 0 : 亚  丝  娜  信  息  更  新"]}, {"code": 356, "indent": 1, "parameters": ["addLog 亚丝娜的经验似乎发生了一些变化..."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac逗比公会，逃跑倒是蛮有一套的。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac去找亚丝娜吧~"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "pierrot_door_key", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["PushGab 27 \\}\\c[8]切，可恶……"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([279, 27, 'D'], true);"]}, {"code": 356, "indent": 0, "parameters": ["particle clear 闪电"]}, {"code": 223, "indent": 0, "parameters": [[-68, -68, 0, 68], 15, false]}, {"code": 232, "indent": 0, "parameters": [48, 0, 0, 0, 864, 0, 100, 100, 0, 0, 30, false]}, {"code": 232, "indent": 0, "parameters": [49, 0, 0, 0, 864, 0, 100, 100, 0, 0, 30, false]}, {"code": 232, "indent": 0, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 0, 0, 10, false]}, {"code": 232, "indent": 0, "parameters": [60, 0, 0, 0, 864, 0, 100, 100, 0, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯?门锁着?"]}, {"code": 250, "indent": 0, "parameters": [{"name": "modernday_door_knock_02", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "modernday_door_knock_02", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "modernday_door_knock_02", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["particle clear 寂静场域"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 27 \\}\\c[8]学姐，那个……桐人学长好像在外面叫我们。"]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac似乎没反应？可能是以为还是之前那帮人……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜？是我？能听到吗？"]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 23\\}\\c[8]诶？！"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [-1, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还是没注意到嘛，没办法，发个信息吧…"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 44, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}], "indent": null}, {"code": 41, "parameters": ["!fsm_Door01b", 6], "indent": null}, {"code": 17}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["!fsm_Door01b", 6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 213, "indent": 0, "parameters": [7, 12, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜~"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 4]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([279, 27, 'D'], false);"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([279, 27, 'B'], true);"]}, {"code": 356, "indent": 0, "parameters": ["particle clear 雨"]}, {"code": 356, "indent": 0, "parameters": ["particle clear 闪电"]}, {"code": 223, "indent": 0, "parameters": [[-68, -68, 0, 68], 1, false]}, {"code": 235, "indent": 0, "parameters": [48]}, {"code": 235, "indent": 0, "parameters": [49]}, {"code": 235, "indent": 0, "parameters": [50]}, {"code": 235, "indent": 0, "parameters": [60]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[木屋开门待机]"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐…桐人君！你，你，你回来了？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，抱歉是不是稍微有点晚，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过外面那群家伙我已经收拾掉了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你们可以放心出来了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac外面的家伙？什么家伙？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就是刚刚准备来抢素材的玩家，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 难道你们在木屋里都没注意到刚才发生的事吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯…可能是因为打雷和隔音的关系吧…"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且我也在那…那个…专心帮猪田治疗。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 治疗？有那么严重嘛，中了个普攻而已， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且已经过去挺久了吧。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 似..似乎因为那个异常状态的原因， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 让他回想起SAO那个时期的事了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 现在似乎非常害怕，我现在正在帮他恢复……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac SAO时期的事啊，猪田他会PTSD确实可以理解， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我也进来一起帮忙吧。 "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诶…不…不用了，这个技能释放期间必须专注才行， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 锁门也是为了被打扰…而且桐人君是猪田君在SAO里的偶像。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 他现在不敢面对你，你进来恐怕只会起副作用…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我是他的偶像？平时倒是没怎么看出来…… "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这样的话，好吧，那就只能交给你了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对了亚丝娜你刚刚看见系统的广播了吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 额…好像确实有听到广播， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但是因为我在专心帮猪头治疗所以…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac噔噔，那你看这个！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这…这是传说级食材？桐人君你是怎么得到的？ 是那个怪物的掉落？ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人君这么执着这个任务，还有之前和利兹的悄悄话， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这一切难..难道就是为了这个…为了我？ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯~毕竟亚丝娜你之前有提过SAO的料理是你一生宝贵的回忆，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以这次就打算给你个惊喜，也才一直没告诉你真相。 "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](debuff逐渐失效，亚丝娜的心情也好了起来。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 当然最关键还是我拿到食材解除了误会。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 刚才选择去追鸽子，真是太正确了。 )"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 原来……是这样。桐人君…抱歉，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我之前还错怪你了。你真好，果然我…"]}, {"code": 250, "indent": 0, "parameters": [{"name": "あぁん！", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 パンパン中 35 100 0"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 60, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[木屋开门后入]"]}, {"code": 111, "indent": 0, "parameters": [0, 233, 1]}, {"code": 122, "indent": 1, "parameters": [31, 31, 1, 0, 10]}, {"code": 122, "indent": 1, "parameters": [32, 32, 1, 0, 1]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 102], 30, false]}, {"code": 356, "indent": 1, "parameters": ["infotext : 11 : 1 : 590 : 140 : 28 : 150 : 0 : 1 : 0 : 亚  丝  娜  信  息  更  新"]}, {"code": 356, "indent": 1, "parameters": ["addLog 亚丝娜的经验似乎发生了一些变化..."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊~~~"]}, {"code": 245, "indent": 0, "parameters": [{"name": "捂嘴娇喘", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜？这么了？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我…！唔~我没事，刚才突然脚一软，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可能治疗用太多魔力导致虚弱了…哦…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是这样嘛？说起来，你的脸也好红啊。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不要勉强自己，果然我还是进来帮忙吧。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不！不行….那..那个…"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我稍微休息一下就好了……唔…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac黑暗督军玩家:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 找到了！和那几个家伙说的一样，他们就在那个木屋里，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 给我围好了，千万不要让他们有机会逃跑，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可抢夺的时间估计不到半小时了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那三个混蛋…果然应该把他们送回复活点的才对。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜你进去把门锁上好好休息，这边就交给我吧。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯…你小心点…唔…"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嘿嘿放心吧，我不会让任何人从这里踏进一步的。"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 246, "indent": 0, "parameters": [1]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["!fsm_Door01b", 1], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["!fsm_Door01b", 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([279, 27, 'B'], false);"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([279, 27, 'C'], true);"]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 5]}, {"code": 111, "indent": 0, "parameters": [0, 233, 0]}, {"code": 111, "indent": 1, "parameters": [0, 300, 1]}, {"code": 121, "indent": 2, "parameters": [70, 70, 1]}, {"code": 111, "indent": 2, "parameters": [0, 22, 0]}, {"code": 356, "indent": 3, "parameters": [">地图永久漂浮文字 : 漂浮文字[1] : 创建 : 样式[1]"]}, {"code": 356, "indent": 3, "parameters": [">地图永久漂浮文字 : 漂浮文字[2] : 创建 : 样式[3]"]}, {"code": 356, "indent": 3, "parameters": [">高级变量框 : 框设置[1] : 显示"]}, {"code": 356, "indent": 3, "parameters": [">高级变量框 : 框设置[2] : 显示"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 355, "indent": 2, "parameters": ["$gameSelfSwitches.setValue([279, 27, 'C'], false);"]}, {"code": 356, "indent": 2, "parameters": ["particle clear 雨"]}, {"code": 356, "indent": 2, "parameters": ["particle clear 闪电"]}, {"code": 122, "indent": 2, "parameters": [199, 199, 0, 0, 0]}, {"code": 129, "indent": 2, "parameters": [1, 0, false]}, {"code": 129, "indent": 2, "parameters": [8, 1, false]}, {"code": 201, "indent": 2, "parameters": [0, 81, 20, 14, 2, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 300, 1]}, {"code": 121, "indent": 1, "parameters": [70, 70, 0]}, {"code": 111, "indent": 1, "parameters": [0, 22, 0]}, {"code": 356, "indent": 2, "parameters": [">地图永久漂浮文字 : 漂浮文字[1] : 清除"]}, {"code": 356, "indent": 2, "parameters": [">地图永久漂浮文字 : 漂浮文字[2] : 清除"]}, {"code": 356, "indent": 2, "parameters": [">高级变量框 : 框设置[1] : 隐藏"]}, {"code": 356, "indent": 2, "parameters": [">高级变量框 : 框设置[2] : 隐藏"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["particle set 雨 weather"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([279, 27, 'B'], false);"]}, {"code": 231, "indent": 0, "parameters": [60, "白色线条864-576x1008", 0, 0, 864, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [48, "黑幕", 0, 0, 864, 0, 40, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [49, "", 0, 0, 864, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [49, 0, 0, 0, 864, 0, 100, 100, 255, 0, 30, false]}, {"code": 231, "indent": 0, "parameters": [50, "", 0, 0, 864, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 设置动画序列 : 动画序列[24]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[49] : 播放简单状态元集合 : 集合[木屋后背位影绘]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[50] : 播放简单状态元集合 : 集合[木屋后背位]"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([279, 27, 'C'], false);"]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个数量……哈哈，有意思。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 希望能别让我太过无聊。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 14, "parameters": [-1, 3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [-1, 3], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{谁都别想上去！"]}, {"code": 213, "indent": 0, "parameters": [19, 5, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<黑暗督军玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 臭小子居然敢说大话，大伙都一起上！"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [68, 68, 0]}, {"code": 356, "indent": 0, "parameters": ["particle clear 雨"]}, {"code": 235, "indent": 0, "parameters": [48]}, {"code": 235, "indent": 0, "parameters": [49]}, {"code": 235, "indent": 0, "parameters": [50]}, {"code": 235, "indent": 0, "parameters": [60]}, {"code": 132, "indent": 0, "parameters": [{"name": "SAO_Battle_BGM_Swordland_Kirito", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 301, "indent": 0, "parameters": [0, 7, false, false]}, {"code": 132, "indent": 0, "parameters": [{"name": "B - THE MARS LIGHT", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 121, "indent": 0, "parameters": [70, 70, 1]}, {"code": 111, "indent": 0, "parameters": [0, 22, 0]}, {"code": 356, "indent": 1, "parameters": [">地图永久漂浮文字 : 漂浮文字[1] : 创建 : 样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">地图永久漂浮文字 : 漂浮文字[2] : 创建 : 样式[3]"]}, {"code": 356, "indent": 1, "parameters": [">高级变量框 : 框设置[1] : 显示"]}, {"code": 356, "indent": 1, "parameters": [">高级变量框 : 框设置[2] : 显示"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([279, 27, 'A'], false);"]}, {"code": 121, "indent": 0, "parameters": [68, 68, 1]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 玩家 : 移动到 : 位置[11,9]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[7] : 像素偏移[0,0] : 时间[1]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [2, 0, 8, 12, 0]}, {"code": 203, "indent": 0, "parameters": [8, 0, 16, 9, 0]}, {"code": 203, "indent": 0, "parameters": [3, 0, 8, 7, 0]}, {"code": 203, "indent": 0, "parameters": [20, 0, 17, 10, 0]}, {"code": 203, "indent": 0, "parameters": [22, 0, 15, 15, 0]}, {"code": 203, "indent": 0, "parameters": [25, 0, 9, 15, 0]}, {"code": 203, "indent": 0, "parameters": [23, 0, 16, 13, 0]}, {"code": 203, "indent": 0, "parameters": [19, 0, 10, 9, 0]}, {"code": 203, "indent": 0, "parameters": [18, 0, 11, 13, 8]}, {"code": 203, "indent": 0, "parameters": [21, 0, 12, 13, 8]}, {"code": 203, "indent": 0, "parameters": [17, 0, 12, 14, 8]}, {"code": 203, "indent": 0, "parameters": [6, 0, 10, 13, 8]}, {"code": 203, "indent": 0, "parameters": [24, 0, 12, 15, 8]}, {"code": 203, "indent": 0, "parameters": [21, 0, 10, 15, 8]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 233, 1]}, {"code": 122, "indent": 1, "parameters": [32, 32, 1, 0, 4]}, {"code": 122, "indent": 1, "parameters": [41, 41, 1, 0, 7]}, {"code": 122, "indent": 1, "parameters": [31, 31, 1, 0, 14]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 102], 30, false]}, {"code": 356, "indent": 1, "parameters": ["infotext : 11 : 1 : 590 : 140 : 28 : 150 : 0 : 1 : 0 : 亚  丝  娜  信  息  更  新"]}, {"code": 356, "indent": 1, "parameters": ["addLog 亚丝娜的经验似乎发生了一些变化..."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 212, "indent": 0, "parameters": [19, 7, false]}, {"code": 205, "indent": 0, "parameters": [19, {"list": [{"code": 14, "parameters": [-1, 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [-1, 2], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 3, "indent": null}, {"code": 38, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [6, 12, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<佣兵玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这是什么怪物…一个人顶住了我们所有人！"]}, {"code": 213, "indent": 0, "parameters": [17, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<符文骑士玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac别……别害怕，他已经到极限了，那可是秘银之羽！"]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac极限？有胆量的可以在上来试试。"]}, {"code": 111, "indent": 0, "parameters": [0, 233, 0]}, {"code": 111, "indent": 1, "parameters": [0, 300, 1]}, {"code": 223, "indent": 2, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 223, "indent": 2, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 356, "indent": 2, "parameters": ["particle clear 雨"]}, {"code": 356, "indent": 2, "parameters": ["particle clear 闪电"]}, {"code": 122, "indent": 2, "parameters": [199, 199, 0, 0, 0]}, {"code": 129, "indent": 2, "parameters": [1, 0, false]}, {"code": 129, "indent": 2, "parameters": [8, 1, false]}, {"code": 201, "indent": 2, "parameters": [0, 81, 20, 14, 2, 0]}, {"code": 115, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [24, 7, false]}, {"code": 213, "indent": 0, "parameters": [21, 6, false]}, {"code": 205, "indent": 0, "parameters": [21, {"list": [{"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](虽然大话说出去了，但果然数量还是有点太多，这些逗比的配合也意外的不错……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不知道亚丝娜那边好了没，情况不对估计得靠猪头的技能隐身跑路了)"]}, {"code": 213, "indent": 0, "parameters": [17, 12, false]}, {"code": 213, "indent": 0, "parameters": [18, 5, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<剑圣玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac只剩几分钟了，这是最后的机会了，妈的，准备上……"]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 7]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 356, "indent": 0, "parameters": ["PushGab 61 喂喂，桐人老弟，这种事情不叫上老哥我也太不讲义气了吧！"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "A", 1]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac克莱因！？你怎么突然过来了？"]}, {"code": 213, "indent": 0, "parameters": [26, 3, false]}, {"code": 205, "indent": 0, "parameters": [26, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那种事情之后在说吧，这时候还是先解决目前的这些家伙吧，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哟，接下来由我来做你们的对手。"]}, {"code": 213, "indent": 0, "parameters": [21, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<牧师玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac怎……怎么办，好好像又来了一个好强的家伙。"]}, {"code": 213, "indent": 0, "parameters": [24, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<魔法师玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac虽然秘银之羽很值钱，但是被杀的损失也相当大啊……"]}, {"code": 213, "indent": 0, "parameters": [6, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<佣兵玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且那个家伙就是个怪物。"]}, {"code": 213, "indent": 0, "parameters": [17, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<符文骑士玩家>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac果然已经不行了……还是撤退吧……"]}, {"code": 205, "indent": 0, "parameters": [21, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [26, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac什么啊，没骨气的家伙！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还想久违的和桐人老弟并肩战斗呢。"]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac克莱因多谢了，不过你是怎么知道的。"]}, {"code": 205, "indent": 0, "parameters": [26, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 莉兹这个小丫头消息真是灵通得很，你们一被围攻， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 她就收到了情报。然后她就联系了我，拜托我过来救场。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我反正没啥事，就立刻动身过来了。 "]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这次谢啦！也亏是莉兹那家伙，意外的靠谱。"]}, {"code": 111, "indent": 0, "parameters": [0, 190, 0]}, {"code": 213, "indent": 1, "parameters": [-1, 2, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 对了，我晚上在商店街看到你和爱丽丝小姐好像去约会来着，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 怎么还有空登录游戏？ "]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 213, "indent": 1, "parameters": [-1, 2, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不过你赶过来，没耽误你什么事情吧？ "]}, {"code": 213, "indent": 1, "parameters": [26, 7, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac赶过来帮你没耽误我什么事。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac因为之前已经被别人耽误过了！"]}, {"code": 213, "indent": 1, "parameters": [-1, 1, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac啊？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac今天去约爱丽丝大人吃饭，好不容易让她答应了……"]}, {"code": 213, "indent": 1, "parameters": [-1, 3, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac那不是好事吗？"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [26, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别提了，饭吃到一半，就被她领导电话叫走了。说是经过总部核查， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 她中午拷问犯人得到的口供大多数都是真的，而且挺有深挖的价值， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 让她加班进一步审讯。不知道要到几点。我白准备了套子，烦死了。"]}, {"code": 111, "indent": 0, "parameters": [0, 189, 0]}, {"code": 213, "indent": 1, "parameters": [-1, 8, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](中午的审讯？是那个自首的凡人吧。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 周一警方的行动刚结束，今天就有条大鱼自首……)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 213, "indent": 1, "parameters": [-1, 2, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac审讯？中午的犯人？"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac一个自首的家伙，被警方前段时间的行动吓出来的。"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](周一警方的行动刚结束，今天就有条大鱼自首……)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 102, "indent": 0, "parameters": [["看来行动确实成功", "总感觉哪里不太对"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "看来行动确实成功"]}, {"code": 213, "indent": 1, "parameters": [-1, 3, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 看来之前周一的行动确实起到了震慑作用，有了情报， "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我们以后的调查会轻松不少。而且你也不用伤心， "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 爱丽丝事业顺利，才更有心情谈恋爱嘛。"]}, {"code": 213, "indent": 1, "parameters": [26, 6, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 承你吉言喽……"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "总感觉哪里不太对"]}, {"code": 121, "indent": 1, "parameters": [191, 191, 0]}, {"code": 213, "indent": 1, "parameters": [-1, 7, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 总感觉哪里不太对，周一警方才有所行动，按我理解，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 地下势力肯定会低调行事、严管手下，并且密切关注警方动向。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 怎么会在这种时候给自首的机会？还掌握有用的情报？"]}, {"code": 213, "indent": 1, "parameters": [26, 6, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你操心太多啦……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你也算是爱丽丝的半个男朋友了，我建议你也关注一下那个自首的家伙。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 如果他真有什么阴谋，你能英雄救美，不也就早日转正了？"]}, {"code": 213, "indent": 1, "parameters": [26, 9, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac这倒是，感谢兄弟提醒！"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 别傻站在下面了，我们到树屋里休息会吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜也在，你们很久没见了，一起叙叙旧。 "]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 17, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [26, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜也在啊，是好久没见了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过你们居然没一起战斗倒是挺稀奇的。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这个说来话长……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嘛~ 先进木屋再说吧。"]}, {"code": 250, "indent": 0, "parameters": [{"name": "微信新消息-ipone-短视频音效-微信提醒_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [26, 1, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [26, 8, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [26, 4, false]}, {"code": 205, "indent": 0, "parameters": [26, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 45, "parameters": ["$gamePlayer.requestBalloon(6);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gamePlayer.requestBalloon(6);"], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦他妈的耶！"]}, {"code": 213, "indent": 0, "parameters": [26, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<克莱因>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 抱歉抱歉，我要走了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝大人加班结束，喊我送她回家。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 改日有空再聚，拜拜！"]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 事件[26] : 纵向挤扁 : 时间[60] : 纵向比例[1.5]"]}, {"code": 111, "indent": 0, "parameters": [0, 233, 1]}, {"code": 122, "indent": 1, "parameters": [58, 58, 1, 0, 50]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 102], 30, false]}, {"code": 356, "indent": 1, "parameters": ["infotext : 11 : 1 : 590 : 140 : 28 : 150 : 0 : 1 : 0 : 亚  丝  娜  信  息  更  新"]}, {"code": 356, "indent": 1, "parameters": ["addLog 亚丝娜的情感发生了一点变化...."]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac克莱因这家伙，真是在靠谱和不靠谱间反复横跳。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac去找亚丝娜吧。"]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 9]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 7}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [26, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 45, "parameters": ["$gamePlayer.requestBalloon(1);"], "indent": 0}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 37, "indent": 0}, {"code": 29, "parameters": [6], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gamePlayer.requestBalloon(1);"], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [26, {"list": [{"code": 4, "indent": null}, {"code": 32, "indent": null}, {"code": 34, "indent": null}, {"code": 4, "indent": null}, {"code": 31, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 32, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 31, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 212, "indent": 0, "parameters": [18, 8, false]}, {"code": 205, "indent": 0, "parameters": [18, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 8]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 9}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 11}, {"id": 5, "name": "ID005门", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Door01b", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac门锁打开了。"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 修改聚焦偏移 : 位置[0,0]"]}, {"code": 201, "indent": 0, "parameters": [0, 280, 8, 14, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 8}, {"id": 6, "name": "佣兵", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor1", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true}], "x": 15, "y": 12}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 129, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 218}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-12,-12]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 129, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 9}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : star : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 7}, {"id": 8, "name": "武器大师", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor1", "direction": 4, "pattern": 1, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"tileId": 0, "characterName": "Damage1", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 230, "indent": 0, "parameters": [30]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 本事件 : 横向挤扁 : 时间[60] : 横向比例[1.5]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}], "x": 13, "y": 12}, {"id": 9, "name": "空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 8}, {"id": 10, "name": "空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 9}, {"id": 11, "name": "空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 10}, {"id": 12, "name": "空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 11}, {"id": 13, "name": "空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 7}, {"id": 14, "name": "ID014术士", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor3", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 16}, {"id": 15, "name": "ID015暗杀者", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Evil", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 16}, {"id": 16, "name": "ID016盗贼", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor3", "direction": 8, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 1}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage1", "direction": 6, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor3", "direction": 8, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 15}, {"id": 17, "name": "符文骑士", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor1", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true}], "x": 8, "y": 9}, {"id": 18, "name": "剑圣", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"tileId": 0, "characterName": "Evil", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 8}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage2", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 230, "indent": 0, "parameters": [120]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 本事件 : 横向挤扁 : 时间[60] : 横向比例[1.5]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": false}], "x": 13, "y": 14}, {"id": 19, "name": "黑暗督军", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"tileId": 0, "characterName": "Evil", "direction": 8, "pattern": 1, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 6}, "directionFix": true, "image": {"tileId": 0, "characterName": "Damage2", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 230, "indent": 0, "parameters": [30]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 本事件 : 横向挤扁 : 时间[60] : 横向比例[1.5]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": false}], "x": 11, "y": 14}, {"id": 20, "name": "盗贼", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor1", "direction": 6, "pattern": 1, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"tileId": 0, "characterName": "Damage1", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 本事件 : 横向挤扁 : 时间[60] : 横向比例[1.5]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 7, "y": 10}, {"id": 21, "name": "牧师", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor2", "direction": 4, "pattern": 1, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true}], "x": 14, "y": 13}, {"id": 22, "name": "魔物猎人", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor2", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"tileId": 0, "characterName": "Damage1", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 230, "indent": 0, "parameters": [30]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 本事件 : 横向挤扁 : 时间[60] : 横向比例[1.5]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}], "x": 12, "y": 13}, {"id": 23, "name": "魔导士", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor2", "direction": 8, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"tileId": 0, "characterName": "Damage1", "direction": 6, "pattern": 1, "characterIndex": 2}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 本事件 : 横向挤扁 : 时间[60] : 横向比例[1.5]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}], "x": 10, "y": 14}, {"id": 24, "name": "魔法师", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor3", "direction": 8, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true}], "x": 12, "y": 15}, {"id": 25, "name": "魔剑士", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"tileId": 0, "characterName": "Actor2", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"tileId": 0, "characterName": "Damage1", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 本事件 : 横向挤扁 : 时间[60] : 横向比例[1.5]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0, "parameters": []}], "repeat": false, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 4, "walkAnime": true}], "x": 10, "y": 13}, {"id": 26, "name": "克莱因", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 7}, "directionFix": false, "image": {"tileId": 0, "characterName": "克莱因游戏", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 5, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 22}, {"id": 27, "name": "打雷特效", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 10]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 60) + 120;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 102], 10, false]}, {"code": 232, "indent": 1, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 255, 0, 10, false]}, {"code": 356, "indent": 1, "parameters": ["particle set 闪电 screen 闪电 above"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 30) + 1;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 1, "parameters": ["particle clear 闪电"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 15, false]}, {"code": 232, "indent": 1, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 0, 0, 15, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 90) + 240;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 7, 2]}, {"code": 111, "indent": 1, "parameters": [1, 77, 0, 2, 1]}, {"code": 223, "indent": 2, "parameters": [[0, 0, 0, 102], 3, false]}, {"code": 356, "indent": 2, "parameters": ["particle set 闪电 screen 闪电 above"]}, {"code": 232, "indent": 2, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 255, 0, 3, false]}, {"code": 355, "indent": 2, "parameters": ["var waitFrames = Math.floor(Math.random() * 2) + 3;"]}, {"code": 655, "indent": 2, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 2, "parameters": ["particle clear 闪电"]}, {"code": 230, "indent": 2, "parameters": [15]}, {"code": 223, "indent": 2, "parameters": [[-68, -68, 0, 68], 10, false]}, {"code": 232, "indent": 2, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 0, 0, 15, false]}, {"code": 355, "indent": 2, "parameters": ["var waitFrames = Math.floor(Math.random() * 90) + 180;"]}, {"code": 655, "indent": 2, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 7, 3]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 102], 3, false]}, {"code": 356, "indent": 1, "parameters": ["particle set 闪电 screen 闪电 above"]}, {"code": 232, "indent": 1, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 255, 0, 3, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 2) + 3;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 1, "parameters": ["particle clear 闪电"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 10, false]}, {"code": 232, "indent": 1, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 0, 0, 15, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 3) + 14;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 102], 5, false]}, {"code": 356, "indent": 1, "parameters": ["particle set 闪电 screen 闪电 above"]}, {"code": 232, "indent": 1, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 255, 0, 5, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 5) + 5;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 1, "parameters": ["particle clear 闪电"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 15, false]}, {"code": 232, "indent": 1, "parameters": [50, 0, 0, 0, 864, 0, 100, 100, 0, 0, 15, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 120) + 180;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 10]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 60) + 120;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 102], 10, false]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 30) + 1;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 15, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 90) + 240;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 7, 2]}, {"code": 111, "indent": 1, "parameters": [1, 77, 0, 2, 1]}, {"code": 223, "indent": 2, "parameters": [[0, 0, 0, 102], 3, false]}, {"code": 355, "indent": 2, "parameters": ["var waitFrames = Math.floor(Math.random() * 2) + 3;"]}, {"code": 655, "indent": 2, "parameters": ["this.wait(waitFrames);"]}, {"code": 230, "indent": 2, "parameters": [15]}, {"code": 223, "indent": 2, "parameters": [[-68, -68, 0, 68], 10, false]}, {"code": 355, "indent": 2, "parameters": ["var waitFrames = Math.floor(Math.random() * 90) + 180;"]}, {"code": 655, "indent": 2, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 7, 3]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 102], 3, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 2) + 3;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 10, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 3) + 14;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 102], 5, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 5) + 5;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 15, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 120) + 180;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "C", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "D", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 1, 10]}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 2, 0]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 60) + 120;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 102], 10, false]}, {"code": 356, "indent": 1, "parameters": ["particle set 闪电 screen 闪电 above"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 30) + 1;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 1, "parameters": ["particle clear 闪电"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 15, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 90) + 240;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 7, 2]}, {"code": 111, "indent": 1, "parameters": [1, 77, 0, 2, 1]}, {"code": 223, "indent": 2, "parameters": [[0, 0, 0, 102], 3, false]}, {"code": 356, "indent": 2, "parameters": ["particle set 闪电 screen 闪电 above"]}, {"code": 355, "indent": 2, "parameters": ["var waitFrames = Math.floor(Math.random() * 2) + 3;"]}, {"code": 655, "indent": 2, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 2, "parameters": ["particle clear 闪电"]}, {"code": 230, "indent": 2, "parameters": [15]}, {"code": 223, "indent": 2, "parameters": [[-68, -68, 0, 68], 10, false]}, {"code": 355, "indent": 2, "parameters": ["var waitFrames = Math.floor(Math.random() * 90) + 180;"]}, {"code": 655, "indent": 2, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 77, 0, 7, 3]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 102], 3, false]}, {"code": 356, "indent": 1, "parameters": ["particle set 闪电 screen 闪电 above"]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 2) + 3;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 1, "parameters": ["particle clear 闪电"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 10, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 3) + 14;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 102], 5, false]}, {"code": 356, "indent": 1, "parameters": ["particle set 闪电 screen 闪电 above"]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 5) + 5;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 356, "indent": 1, "parameters": ["particle clear 闪电"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 15, false]}, {"code": 355, "indent": 1, "parameters": ["var waitFrames = Math.floor(Math.random() * 120) + 180;"]}, {"code": 655, "indent": 1, "parameters": ["this.wait(waitFrames);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 199, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 12}]}