{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "S - かおりとぬくもり", "pan": 0, "pitch": 100, "volume": 25}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 30, "note": "=>自定义照明:临时锁定:开启\n=>自定义照明:临时锁定:黑暗层透明度:100", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "!神社", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 31, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5, 6, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 5, 5, 5, 1, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 6, 5, 5, 1, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 1, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 5, 5, 5, 5, 5, 5, 6, 5, 5, 1, 5, 5, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 79 2 神社-夜晚光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 79 3 神社-夜晚阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 356, "indent": 0, "parameters": ["particle set 灯 region:6 灯 above"]}, {"code": 356, "indent": 0, "parameters": ["particle set 传送 event:2 传送 above"]}, {"code": 356, "indent": 0, "parameters": ["particle set 回想点 region:1 回想点 above"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 50, 1]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([79, 12, 'A'], true);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([79, 12, 'A'], false);"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[9] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[12] : 强制刷新文本"]}, {"code": 223, "indent": 0, "parameters": [[-34, -34, 0, 34], 1, true]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 355, 1]}, {"code": 111, "indent": 1, "parameters": [4, 4, 0]}, {"code": 129, "indent": 2, "parameters": [4, 0, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [4, 7, 0]}, {"code": 129, "indent": 2, "parameters": [7, 1, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [4, 14, 0]}, {"code": 129, "indent": 2, "parameters": [14, 1, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [4, 1, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 129, "indent": 2, "parameters": [1, 0, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 198, 0, 7, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 42, "parameters": [0], "indent": null}, {"code": 16, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[9] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[12] : 强制刷新文本"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 466, 1]}, {"code": 117, "indent": 1, "parameters": [24]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 198, 0, 7, 0]}, {"code": 356, "indent": 1, "parameters": [">显现动作 : 玩家 : 纵向冒出 : 时间[60] : 纵向比例[1.5]"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 107, 1, 100, 5]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([80, 3, 'A'], true);"]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([80, 2, 'A'], true);"]}, {"code": 122, "indent": 1, "parameters": [107, 107, 0, 1, 100]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 15, "y": 3}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 68}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 102, "indent": 0, "parameters": [["\\c[210]离开这个空间...", "\\c[210]继续留在这里"], 1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\c[210]离开这个空间..."]}, {"code": 356, "indent": 1, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 356, "indent": 1, "parameters": [">消失动作 : 玩家 : 纵向挤扁 : 时间[60] : 纵向比例[1.5]"]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 180, true]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 1, true]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 42, "parameters": [255], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "Item3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 受到奇怪力量的影响, 现在桐人的潜意识也会提升不安值。同时，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 周围人的不安情绪也能被他所吸收。但不用担心不安过多会让他"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 爆炸。每日结束进入这个“真实的梦境”，他所积蓄的所有不安都"]}, {"code": 401, "indent": 1, "parameters": ["\\dac会被伟大的Administrator所吸收、净化。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 作为回赠，Administrator将允许桐人花费积累的不安解锁一些错过的，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 或者原本看不见的“现实”。以这个为目标,在现实或者游戏中拼劲全力"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 吸纳负面情绪吧！作为神的使者，度化苦难众生的无尽烦恼。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac 赞美Administrator！"]}, {"code": 129, "indent": 1, "parameters": [7, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 33, 22, 8, 4, 0]}, {"code": 241, "indent": 1, "parameters": [{"name": "夜晚", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\c[210]继续留在这里"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 90}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 102, "indent": 0, "parameters": [["\\c[210]离开这个空间...", "\\c[210]继续留在这里"], 1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\c[210]离开这个空间..."]}, {"code": 356, "indent": 1, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 1, "parameters": [1, "葵妮拉羞辱", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["葵妮拉默认.png"]}, {"code": 232, "indent": 1, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<Administrator>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac要离开了吗?看起来今天晚上还能做个好梦呢~"]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">消失动作 : 玩家 : 纵向挤扁 : 时间[60] : 纵向比例[1.5]"]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 120, true]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 121, "indent": 1, "parameters": [21, 21, 1]}, {"code": 129, "indent": 1, "parameters": [12, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 201, "indent": 1, "parameters": [0, 33, 21, 8, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\c[210]继续留在这里"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 120}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 102, "indent": 0, "parameters": [["\\c[210]离开这个空间...", "\\c[210]继续留在这里"], 1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\c[210]离开这个空间..."]}, {"code": 356, "indent": 1, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 356, "indent": 1, "parameters": [">消失动作 : 玩家 : 纵向挤扁 : 时间[60] : 纵向比例[1.5]"]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 120, true]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 129, "indent": 1, "parameters": [7, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 201, "indent": 1, "parameters": [0, 33, 21, 8, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\c[210]继续留在这里"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 125}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 102, "indent": 0, "parameters": [["\\c[210]离开这个空间...", "\\c[210]继续留在这里"], 1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\c[210]离开这个空间..."]}, {"code": 356, "indent": 1, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 356, "indent": 1, "parameters": [">消失动作 : 玩家 : 纵向挤扁 : 时间[60] : 纵向比例[1.5]"]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 120, true]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 121, "indent": 1, "parameters": [21, 21, 1]}, {"code": 121, "indent": 1, "parameters": [76, 76, 0]}, {"code": 121, "indent": 1, "parameters": [71, 71, 0]}, {"code": 117, "indent": 1, "parameters": [91]}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 243, 4]}, {"code": 129, "indent": 2, "parameters": [7, 0, false]}, {"code": 129, "indent": 2, "parameters": [1, 1, false]}, {"code": 201, "indent": 2, "parameters": [0, 33, 21, 8, 4, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 121, "indent": 2, "parameters": [75, 75, 0]}, {"code": 201, "indent": 2, "parameters": [0, 142, 22, 8, 4, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\c[210]继续留在这里"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 329}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 102, "indent": 0, "parameters": [["\\c[210]离开这个空间...", "\\c[210]继续留在这里"], 1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\c[210]离开这个空间..."]}, {"code": 356, "indent": 1, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 356, "indent": 1, "parameters": [">消失动作 : 玩家 : 纵向挤扁 : 时间[60] : 纵向比例[1.5]"]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 120, true]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 117, "indent": 1, "parameters": [91]}, {"code": 117, "indent": 1, "parameters": [11]}, {"code": 117, "indent": 1, "parameters": [141]}, {"code": 121, "indent": 1, "parameters": [95, 95, 1]}, {"code": 121, "indent": 1, "parameters": [91, 91, 1]}, {"code": 121, "indent": 1, "parameters": [77, 77, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 249, "indent": 1, "parameters": [{"name": "Inn", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [180]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 354, 1]}, {"code": 117, "indent": 2, "parameters": [16]}, {"code": 117, "indent": 2, "parameters": [160]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 201, "indent": 1, "parameters": [0, 142, 22, 8, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\c[210]继续留在这里"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 869}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[18]本次更新到此结束，敬请期待下次更新。\\|"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 469, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 868}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 0, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{回到主菜单？"]}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 102, "indent": 0, "parameters": [["是", "否"], 1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "是"]}, {"code": 121, "indent": 1, "parameters": [469, 469, 1]}, {"code": 354, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "否"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 15, "y": 6}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 4, "pattern": 1, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,-24]"]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 838, 2]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 801, 1]}, {"code": 213, "indent": 2, "parameters": [-1, 8, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 401, "indent": 2, "parameters": ["\\dac 门锁着，里面似乎空无一人。 "]}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 226, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "pierrot_door_key", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 213, "indent": 1, "parameters": [-1, 8, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac 门锁着，里面似乎有人在谈话。 "]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "沉重的开门", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 80, 19, 15, 4, 0]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 5, "y": 16}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 4, "pattern": 1, "characterIndex": 7}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 838, 2]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 801, 1]}, {"code": 213, "indent": 2, "parameters": [-1, 8, false]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 401, "indent": 2, "parameters": ["\\dac 门锁着，里面似乎空无一人。 "]}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 226, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "pierrot_door_key", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 213, "indent": 1, "parameters": [-1, 8, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac 门锁着，里面似乎有人在谈话。 "]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "沉重的开门", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 80, 19, 15, 4, 0]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 5, "y": 15}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Crystal", "direction": 6, "pattern": 0, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 亚丝娜＆猪田"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 81, 14, 19, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 13}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Crystal", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 莉兹＆京子"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 82, 14, 19, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 16}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Crystal", "direction": 6, "pattern": 0, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 直叶＆诗乃"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 83, 14, 19, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 19}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Crystal", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 爱丽丝＆其他"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 84, 14, 19, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 22}, {"id": 9, "name": "真实世界", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door2", "direction": 8, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : \\c[23]原初"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<???>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac需要在这个世界中看见更清晰的真实吗?\\c[101](某一些事件将更加清晰)"]}, {"code": 102, "indent": 0, "parameters": [["我想面对更真实的世界", "我想这个世界保持原貌"], -1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "我想面对更真实的世界"]}, {"code": 121, "indent": 1, "parameters": [19, 19, 0]}, {"code": 117, "indent": 1, "parameters": [7]}, {"code": 117, "indent": 1, "parameters": [15]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "我想这个世界保持原貌"]}, {"code": 121, "indent": 1, "parameters": [19, 19, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 19, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door2", "direction": 6, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : \\c[24]真实"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<???>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac需要在这个世界中看见更清晰的真实吗?\\c[101](某一些事件将更加清晰)"]}, {"code": 102, "indent": 0, "parameters": [["我想面对更真实的世界", "我想这个世界保持原貌"], -1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "我想面对更真实的世界"]}, {"code": 121, "indent": 1, "parameters": [19, 19, 0]}, {"code": 117, "indent": 1, "parameters": [7]}, {"code": 117, "indent": 1, "parameters": [15]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "我想这个世界保持原貌"]}, {"code": 121, "indent": 1, "parameters": [19, 19, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 10}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 67}, "directionFix": false, "image": {"tileId": 0, "characterName": "葵妮拉像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 230, "indent": 0, "parameters": [15]}, {"code": 212, "indent": 0, "parameters": [-1, 138, false]}, {"code": 230, "indent": 0, "parameters": [40]}, {"code": 122, "indent": 0, "parameters": [72, 72, 1, 1, 73]}, {"code": 122, "indent": 0, "parameters": [73, 73, 0, 0, 0]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 2, 0]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac不知为何，心情感觉轻松许多……"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[12] : 强制刷新文本"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 1"]}, {"code": 231, "indent": 0, "parameters": [1, "葵妮拉坏笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["葵妮拉默认.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<？？？>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac好像醒了呢?辛苦了~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac今天也上演了不错的剧情呢~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac真可惜，明明差一点就能发现真相了。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 231, "indent": 0, "parameters": [1, "葵妮拉微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["葵妮拉默认.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<？？？>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嘛,不过这也不是什么要紧事,凡人本就难以看清事物的真实，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac就像我的本名,也并不是“Administrator”。"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 231, "indent": 0, "parameters": [1, "葵妮拉玩弄", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["葵妮拉默认.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<Administrator>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哎呀,抱歉,抱歉自顾自地说了这么多,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你现在大概是一头雾水吧,如果有什么问题的话现在都可以问我哦~"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 118, "indent": 0, "parameters": ["葵妮拉问题"]}, {"code": 231, "indent": 0, "parameters": [1, "葵妮拉坏笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["葵妮拉默认.png"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<Administrator>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那么有趣的家伙你有什么想问的事吗?"]}, {"code": 235, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 102, "indent": 0, "parameters": [["\\c[210]你是谁?", "if(s[133])\\c[210]我为什么会在这里?", "if(s[134])\\c[210]这里是什么地方?", "if(s[135])\\c[210]我该怎么离开这里?", "if(s[136])\\c[210]没有什么想问的了..."], -1, 0, 1, 0]}, {"code": 402, "indent": 0, "parameters": [0, "\\c[210]你是谁?"]}, {"code": 231, "indent": 1, "parameters": [1, "葵妮拉疑惑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["葵妮拉默认.png"]}, {"code": 232, "indent": 1, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<Administrator>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哈?你这个家伙是笨蛋嘛,这个地方是神社吧,那我肯定就是"]}, {"code": 401, "indent": 1, "parameters": ["\\dac你们凡人口中的神明啊,别问这种愚蠢的问题。"]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 121, "indent": 1, "parameters": [133, 133, 0]}, {"code": 119, "indent": 1, "parameters": ["葵妮拉问题"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "if(s[133])\\c[210]我为什么会在这里?"]}, {"code": 231, "indent": 1, "parameters": [1, "葵妮拉普通", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["葵妮拉默认.png"]}, {"code": 232, "indent": 1, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<Administrator>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac唔..这个的原因就比较复杂了，不过姑且我还是稍微解释下吧~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<Administrator>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac作为神明，偶尔会对凡间有趣的故事多一些关注，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac和你们脑波的频率调频，给你们一些特殊的启示。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac也就你们凡人所谓“闪灵”、“缪斯”、“第六感”之类的东西。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<Administrator>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac亦或者，部分幸运儿，因为情绪的波动，激素的分泌，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac偶然地达成同调，在睡眠时落入了神的领域"]}, {"code": 401, "indent": 1, "parameters": ["\\dac这些人就会来到你现在所处之处，这个真实的梦境."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<Administrator>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac每个人和神的相性都不同，有些人转瞬就会忘记，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac有些人则会清晰地铭刻在心。"]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 231, "indent": 1, "parameters": [1, "葵妮拉安心", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["葵妮拉默认.png"]}, {"code": 232, "indent": 1, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<Administrator>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不过从你对我的启示几乎无感的迟钝来看，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac你似乎是属于相性比较差的那一型，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不过，这反而更加有趣。所以也不必在意。"]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 121, "indent": 1, "parameters": [134, 134, 0]}, {"code": 119, "indent": 1, "parameters": ["葵妮拉问题"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "if(s[134])\\c[210]这里是什么地方?"]}, {"code": 231, "indent": 1, "parameters": [1, "葵妮拉羞辱", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["葵妮拉默认.png"]}, {"code": 232, "indent": 1, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<Administrator>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac这个空间中，保存着过去的回忆，可以随意查看，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac也有你未能触及的真实，用一点负面情绪做钥匙就能打开，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac在保持同调的限时内，自由探索吧，如果有兴趣的话。"]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 231, "indent": 1, "parameters": [1, "葵妮拉坏笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["葵妮拉默认.png"]}, {"code": 232, "indent": 1, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<Administrator>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不过你醒来的时候，大概率全部都会忘记就是了~"]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 121, "indent": 1, "parameters": [135, 135, 0]}, {"code": 119, "indent": 1, "parameters": ["葵妮拉问题"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "if(s[135])\\c[210]我该怎么离开这里?"]}, {"code": 231, "indent": 1, "parameters": [1, "葵妮拉焦急", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["葵妮拉默认.png"]}, {"code": 232, "indent": 1, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<Administrator>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac诶,你可是好不容易才来到这里,这么就想着离开没关系吗?"]}, {"code": 401, "indent": 1, "parameters": ["\\dac全知全能的漂亮女神可是正在大发善意解答你这个凡人的问题"]}, {"code": 401, "indent": 1, "parameters": ["\\dac耶,我才不会告诉你这种不懂的感恩的家伙从身后的空间里就能离开呢~哼!"]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 121, "indent": 1, "parameters": [136, 136, 0]}, {"code": 119, "indent": 1, "parameters": ["葵妮拉问题"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [4, "if(s[136])\\c[210]没有什么想问的了..."]}, {"code": 231, "indent": 1, "parameters": [1, "葵妮拉微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["葵妮拉默认.png"]}, {"code": 232, "indent": 1, "parameters": [1, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<Administrator>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac看样子经过本女神的精心解答你已经完全理解了，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac接下来我还有作为神明必须进行的工作，所以闲聊就到此"]}, {"code": 401, "indent": 1, "parameters": ["\\dac为止吧,和我说的一样你可以自由的在这个空间里探索~"]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 本事件 : 纵向挤扁 : 时间[60] : 纵向比例[1.5]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 68]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 68}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 7}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 68}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 73, 0, 0, 3]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 212, "indent": 1, "parameters": [-1, 138, false]}, {"code": 230, "indent": 1, "parameters": [40]}, {"code": 122, "indent": 1, "parameters": [72, 72, 1, 1, 73]}, {"code": 122, "indent": 1, "parameters": [73, 73, 0, 0, 0]}, {"code": 111, "indent": 1, "parameters": [1, 72, 0, 50, 1]}, {"code": 355, "indent": 2, "parameters": ["$gameSelfSwitches.setValue([79, 12, 'A'], true);"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": [">事件漂浮文字 : 事件[12] : 强制刷新文本"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 0]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac不知为何，心情感觉轻松许多……"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 14, "y": 3}, {"id": 12, "name": "不安总量", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door2", "direction": 4, "pattern": 0, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 不安总量：\\c[113]\\v[72]"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 偏移 : 0 : -2"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Door2", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 不安总量：\\c[113]\\v[72]"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 偏移 : 0 : -2"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 10}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 869}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 14, "parameters": [-1, 0], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [-1, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这次更新的话到这里结束了,上次更新本来以为2个月能做完"]}, {"code": 401, "indent": 0, "parameters": ["\\dac自己预想的重要内容，但是经过整理果然还是需要做的准备"]}, {"code": 401, "indent": 0, "parameters": ["\\dac实在太多了。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac只能退尔其次避免大家也等待太久，所以先进行了这次"]}, {"code": 401, "indent": 0, "parameters": ["\\dac小的更新，对前面的一些事件细节进行了一些优化,也补充了"]}, {"code": 401, "indent": 0, "parameters": ["\\dac一些遗漏的回想和一点之前事件的CG。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac以及花心思制作了一些大家可能比较感兴趣的PLAY,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 希望大家能够喜欢。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac之前进行的问答QA,我也收到了大家对于游戏的各种意见和"]}, {"code": 401, "indent": 0, "parameters": ["\\dac各式各样的建议,有不少对我对后续内容的制作有很多帮助，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac以后有机会也会多和大家进行这种交流。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac以及很多人关注的一个问题是后续作品的考量。在下个月的更新"]}, {"code": 401, "indent": 0, "parameters": ["\\dac之后，sao也会进入终章阶段，不出意外今年会全部完成。所以"]}, {"code": 401, "indent": 0, "parameters": ["\\dac做完这次重要的更新，我可能也会开始进行新作的一些筹备工作。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac到时候也可能会开始比较频繁的征询大家对于题材和故事以及"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 人设的想法，希望大家到时候也能多多帮忙提供建议。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 最后是自己比较私人的一些碎碎念。各种各样的原因吧，工作，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 生活环境，游戏更新等等，最近压力也是比较大。压力大，就容"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 易失眠，抵抗力下降，再加上长时间劳累，生病的次数也在增加。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就算是这样，却也经常被人说是借口和懒惰；因为没有在某些人预"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 想的时间更新就被说“真该死”；让人觉得有些难过。人与人的悲欢"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 并不相通，之前也说过，经常会有“干脆停下来吧！”的想法。"]}, {"code": 231, "indent": 0, "parameters": [1, "doro_origin", 1, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过好在我收到更多的是鼓励和支持的言语，让心情能够恢复过来。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 以及以及，经常刷一刷doro的视频，用doro的表情，也是非常治愈的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 下周可能去看看心理医生，希望能让自己更开心一些。"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, false]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.target(13,2,30);"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 225, "indent": 0, "parameters": [9, 9, 30, true]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 需要我解锁之前版本全部回想（不包括此次更新）吗？"]}, {"code": 102, "indent": 0, "parameters": [["是", "否"], 1, 0, 2, 1]}, {"code": 402, "indent": 0, "parameters": [0, "是"]}, {"code": 117, "indent": 1, "parameters": [94]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "否"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac以上。"]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.restore(15);"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 14, "parameters": [1, 0], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [1, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 121, "indent": 0, "parameters": [1000, 1000, 0]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 4, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1000, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 695}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这次更新的话到这里结束了,上次更新本来以为2个月能做完"]}, {"code": 401, "indent": 0, "parameters": ["\\dac自己预想的重要内容，但是经过整理果然还是需要做的准备"]}, {"code": 401, "indent": 0, "parameters": ["\\dac实在太多了。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac只能退尔其次避免大家也等待太久，所以先进行了这次"]}, {"code": 401, "indent": 0, "parameters": ["\\dac小的更新，对前面的一些事件细节进行了一些优化,也补充了"]}, {"code": 401, "indent": 0, "parameters": ["\\dac一些遗漏的回想和一点之前事件的CG。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac以及花心思制作了一些大家可能比较感兴趣的PLAY,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 希望大家能够喜欢。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac之前进行的问答QA,我也收到了大家对于游戏的各种意见和"]}, {"code": 401, "indent": 0, "parameters": ["\\dac各式各样的建议,有不少对我对后续内容的制作有很多帮助，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac以后有机会也会多和大家进行这种交流。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac以及很多人关注的一个问题是后续作品的考量。在下个月的更新"]}, {"code": 401, "indent": 0, "parameters": ["\\dac之后，sao也会进入终章阶段，不出意外今年会全部完成。所以"]}, {"code": 401, "indent": 0, "parameters": ["\\dac做完这次重要的更新，我可能也会开始进行新作的一些筹备工作。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac到时候也可能会开始比较频繁的征询大家对于题材和故事以及"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 人设的想法，希望大家到时候也能多多帮忙提供建议。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 最后是自己比较私人的一些碎碎念。各种各样的原因吧，工作，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 生活环境，游戏更新等等，最近压力也是比较大。压力大，就容"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 易失眠，抵抗力下降，再加上长时间劳累，生病的次数也在增加。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就算是这样，却也经常被人说是借口和懒惰；因为没有在某些人预"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 想的时间更新就被说“真该死”；让人觉得有些难过。人与人的悲欢"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 并不相通，之前也说过，经常会有“干脆停下来吧！”的想法。"]}, {"code": 231, "indent": 0, "parameters": [1, "doro_origin", 1, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过好在我收到更多的是鼓励和支持的言语，让心情能够恢复过来。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 以及以及，经常刷一刷doro的视频，用doro的表情，也是非常治愈的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 下周可能去看看心理医生，希望能让自己更开心一些。"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, false]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 需要我解锁之前版本全部回想（不包括此次更新）吗？"]}, {"code": 102, "indent": 0, "parameters": [["是", "否"], 1, 0, 2, 1]}, {"code": 402, "indent": 0, "parameters": [0, "是"]}, {"code": 117, "indent": 1, "parameters": [94]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "否"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 469, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 695}, "directionFix": false, "image": {"tileId": 0, "characterName": "animo", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这次更新的话到这里结束了,上次更新本来以为2个月能做完"]}, {"code": 401, "indent": 0, "parameters": ["\\dac自己预想的重要内容，但是经过整理果然还是需要做的准备"]}, {"code": 401, "indent": 0, "parameters": ["\\dac实在太多了。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac只能退尔其次避免大家也等待太久，所以先进行了这次"]}, {"code": 401, "indent": 0, "parameters": ["\\dac小的更新，对前面的一些事件细节进行了一些优化,也补充了"]}, {"code": 401, "indent": 0, "parameters": ["\\dac一些遗漏的回想和一点之前事件的CG。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac以及花心思制作了一些大家可能比较感兴趣的PLAY,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 希望大家能够喜欢。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac之前进行的问答QA,我也收到了大家对于游戏的各种意见和"]}, {"code": 401, "indent": 0, "parameters": ["\\dac各式各样的建议,有不少对我对后续内容的制作有很多帮助，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac以后有机会也会多和大家进行这种交流。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac以及很多人关注的一个问题是后续作品的考量。在下个月的更新"]}, {"code": 401, "indent": 0, "parameters": ["\\dac之后，sao也会进入终章阶段，不出意外今年会全部完成。所以"]}, {"code": 401, "indent": 0, "parameters": ["\\dac做完这次重要的更新，我可能也会开始进行新作的一些筹备工作。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac到时候也可能会开始比较频繁的征询大家对于题材和故事以及"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 人设的想法，希望大家到时候也能多多帮忙提供建议。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 最后是自己比较私人的一些碎碎念。各种各样的原因吧，工作，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 生活环境，游戏更新等等，最近压力也是比较大。压力大，就容"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 易失眠，抵抗力下降，再加上长时间劳累，生病的次数也在增加。"]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就算是这样，却也经常被人说是借口和懒惰；因为没有在某些人预"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 想的时间更新就被说“真该死”；让人觉得有些难过。人与人的悲欢"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 并不相通，之前也说过，经常会有“干脆停下来吧！”的想法。"]}, {"code": 231, "indent": 0, "parameters": [1, "doro_origin", 1, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过好在我收到更多的是鼓励和支持的言语，让心情能够恢复过来。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 以及以及，经常刷一刷doro的视频，用doro的表情，也是非常治愈的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 下周可能去看看心理医生，希望能让自己更开心一些。"]}, {"code": 232, "indent": 0, "parameters": [1, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, false]}, {"code": 101, "indent": 0, "parameters": ["Nature", 2, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc鸽子"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 需要我解锁之前版本全部回想（不包括此次更新）吗？"]}, {"code": 102, "indent": 0, "parameters": [["是", "否"], 1, 0, 2, 1]}, {"code": 402, "indent": 0, "parameters": [0, "是"]}, {"code": 117, "indent": 1, "parameters": [94]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "否"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 10}, {"id": 14, "name": "英文版更新状态", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [99]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 3}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.target(9,2,30); "]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 0]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 左侧的光球"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 互动选择真实世界，可让部分回想内容更加清晰"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 同时更新亚丝娜的最新状态"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.target(12,2,30); "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 0]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 右侧的光球"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 记录了可以用于回想事件解锁的存量不安能量 "]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.restore(30);"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 469, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 8}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 243, 4]}, {"code": 111, "indent": 1, "parameters": [1, 178, 0, 1, 5]}, {"code": 356, "indent": 2, "parameters": [">标题平铺GIF : 平铺GIF[1] : 显示"]}, {"code": 356, "indent": 2, "parameters": [">标题平铺GIF : 平铺GIF[2] : 隐藏"]}, {"code": 122, "indent": 2, "parameters": [178, 178, 0, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 178, 0, 2, 5]}, {"code": 356, "indent": 2, "parameters": [">标题平铺GIF : 平铺GIF[2] : 显示"]}, {"code": 356, "indent": 2, "parameters": [">标题平铺GIF : 平铺GIF[1] : 隐藏"]}, {"code": 122, "indent": 2, "parameters": [178, 178, 0, 0, 2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 999, 4]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 14, "y": 1}, null]}