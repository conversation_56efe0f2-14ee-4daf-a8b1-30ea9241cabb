[null, {"id": 1, "animationId": -1, "damage": {"critical": true, "elementId": -1, "formula": "a.atk * 3 - b.def * 1.5", "type": 1, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 0, "value1": 1, "value2": 0}], "hitType": 1, "iconIndex": 76, "message1": "的攻击！", "message2": "", "mpCost": 0, "name": "攻击", "note": "1 号技能会在选择“攻击”指令时使用。", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 10}, {"id": 2, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 2, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 81, "message1": "正在保护自己。", "message2": "", "mpCost": 0, "name": "防御", "note": "1 号技能会在选择“防御”指令时使用。", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 2000, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 10}, {"id": 3, "animationId": -1, "damage": {"critical": true, "elementId": -1, "formula": "a.atk * 4 - b.def * 2", "type": 1, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 0, "value1": 1, "value2": 0}], "hitType": 1, "iconIndex": 76, "message1": " attacks!", "message2": "", "mpCost": 0, "name": "双重攻击", "note": "", "occasion": 1, "repeats": 2, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 5}, {"id": 4, "animationId": -1, "damage": {"critical": true, "elementId": -1, "formula": "a.atk * 4 - b.def * 2", "type": 1, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 0, "value1": 1, "value2": 0}], "hitType": 1, "iconIndex": 76, "message1": " attacks!", "message2": "", "mpCost": 0, "name": "双重攻击", "note": "", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 4, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 5}, {"id": 5, "animationId": -1, "damage": {"critical": true, "elementId": -1, "formula": "a.atk * 4 - b.def * 2", "type": 1, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 0, "value1": 1, "value2": 0}], "hitType": 1, "iconIndex": 76, "message1": " attacks!", "message2": "", "mpCost": 0, "name": "乱击", "note": "", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 5, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 4}, {"id": 6, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 41, "dataId": 0, "value1": 0, "value2": 0}], "hitType": 0, "iconIndex": 82, "message1": " flees.", "message2": "", "mpCost": 0, "name": "逃跑", "note": "", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 7, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 81, "message1": " waits.", "message2": "", "mpCost": 0, "name": "观察", "note": "", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 0, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 10}, {"id": 8, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 9, "animationId": 37, "damage": {"critical": false, "elementId": -1, "formula": "a.atk * 1", "type": 1, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 80, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 78, "message1": "使用了%1！", "message2": "", "mpCost": 10, "name": "战吼", "note": "", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 2, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 10, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 11, "animationId": 97, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 44, "dataId": 130, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 70, "message1": "", "message2": "", "mpCost": 10, "name": "炫目之光", "note": "<Cooldown: 6>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 12, "animationId": 99, "damage": {"critical": false, "elementId": 11, "formula": "a.mdf * 2", "type": 1, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 94, "message1": "", "message2": "", "mpCost": 10, "name": "光魔法", "note": "<Cooldown: 3>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 13, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 14, "animationId": 46, "damage": {"critical": false, "elementId": 0, "formula": "a.mdf * 2", "type": 3, "variance": 20}, "description": "Restore HP to all allies.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 0, "iconIndex": 72, "message1": "使用了%1！", "message2": "", "mpCost": 8, "name": "自我再生", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 15, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 16, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 17, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 18, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 19, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 20, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "<Cleric Skills>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 21, "animationId": 46, "damage": {"critical": false, "elementId": 0, "formula": "a.mdf * 2", "type": 3, "variance": 20}, "description": "Restore HP to all allies.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 0, "iconIndex": 72, "message1": "", "message2": "", "mpCost": 8, "name": "Heal Wounds", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 22, "animationId": 97, "damage": {"critical": false, "elementId": 11, "formula": "a.mat * 4", "type": 1, "variance": 20}, "description": "Deal heavy light damage to a single foe.\n\\c[27]\\bpdamage[400]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 70, "message1": "", "message2": "", "mpCost": 6, "name": "Holy Light", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait: 10\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 23, "animationId": 53, "damage": {"critical": false, "elementId": 0, "formula": "a.mdf * 2", "type": 0, "variance": 20}, "description": "Augment a single ally's MDF for \\c[27]\\bpturn[2]\\c[0] turns.", "effects": [{"code": 31, "dataId": 5, "value1": 2, "value2": 0}], "hitType": 0, "iconIndex": 37, "message1": "", "message2": "", "mpCost": 6, "name": "Sheltering Veil", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 24, "animationId": 96, "damage": {"critical": false, "elementId": 11, "formula": "a.mat * 2", "type": 1, "variance": 20}, "description": "Deal light damage to all foes.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 70, "message1": "", "message2": "", "mpCost": 9, "name": "Luminescence", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait: 20\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 25, "animationId": 41, "damage": {"critical": false, "elementId": 0, "formula": "a.mdf * 4", "type": 3, "variance": 20}, "description": "Restore HP to all allies.\n\\c[27]\\bpdamage[400]%\\c[0] Multiplier.", "effects": [], "hitType": 0, "iconIndex": 72, "message1": "", "message2": "", "mpCost": 25, "name": "Heal More", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 26, "animationId": 53, "damage": {"critical": false, "elementId": 0, "formula": "a.mdf * 2", "type": 0, "variance": 20}, "description": "Grant a single ally the ability to reflect elemental attacks.\nReflects \\c[27]\\bpturn[1]\\c[0] spell\\bp[s].", "effects": [{"code": 21, "dataId": 21, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 222, "message1": "", "message2": "", "mpCost": 22, "name": "Reflective Veil", "note": "<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 27, "animationId": 43, "damage": {"critical": false, "elementId": 0, "formula": "Math.max(1, b.mhp / 3 * a.bp)", "type": 3, "variance": 0}, "description": "Revive all incapacitated allies.", "effects": [], "hitType": 0, "iconIndex": 72, "message1": "", "message2": "", "mpCost": 50, "name": "Revive", "note": "<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 10, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 28, "animationId": 51, "damage": {"critical": false, "elementId": 0, "formula": "a.mdf * 2", "type": 0, "variance": 20}, "description": "\\c[27]Divine Skill:\\c[0] For 3 turns, skills performed by a single chosen\nally will trigger twice. \\}(Except divine skills)\\{", "effects": [{"code": 21, "dataId": 22, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 109, "message1": "", "message2": "", "mpCost": 30, "name": "Holy Auspices", "note": "<Divine>\n<Require 3 BP>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 29, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 30, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "<Scholar Skills>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 31, "animationId": 67, "damage": {"critical": false, "elementId": 7, "formula": "a.mdf * 2", "type": 1, "variance": 20}, "description": "Deal fire damage to all foes.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 64, "message1": "", "message2": "", "mpCost": 8, "name": "Fireball", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: target\nwait: 10\naction effect\nImmortal: target, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 2, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 32, "animationId": 71, "damage": {"critical": false, "elementId": 8, "formula": "a.mdf * 2", "type": 1, "variance": 20}, "description": "Deal ice damage to all foes.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 65, "message1": "", "message2": "", "mpCost": 8, "name": "Icewind", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: target\nwait: 10\naction effect\nImmortal: target, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 2, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 33, "animationId": 76, "damage": {"critical": false, "elementId": 9, "formula": "a.mdf * 2", "type": 1, "variance": 20}, "description": "Deal lightning damage to all foes.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 66, "message1": "", "message2": "", "mpCost": 8, "name": "Lightning Bolt", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: target\nwait: 10\naction effect\nImmortal: target, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 2, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 34, "animationId": 122, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Reveal \\c[27]\\bpanalyze[1]\\c[0] weakness\\bp[es] and HP of a single foe.", "effects": [], "hitType": 0, "iconIndex": 79, "message1": "", "message2": "", "mpCost": 1, "name": "Analyze", "note": "<Analyze Weakness: 1>\n\n<Boost Analyze>\n\n<Before Eval>\nvar text = '<CENTER>' + target.name() + '\\'s HP: ' + Yanfly.Util.toGroup(target.hp) + '/' + Yanfly.Util.toGroup(target.mhp);\nBattleManager.addText(text, 60);\n</Before Eval>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nwait: 60\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 2, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 35, "animationId": 66, "damage": {"critical": false, "elementId": 7, "formula": "a.mdf * 2", "type": 1, "variance": 20}, "description": "Deal fire damage to all foes twice.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 64, "message1": "", "message2": "", "mpCost": 22, "name": "Fire Storm", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait: 20\naction effect\nanimation 107: targets\nwait: 20\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 2, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 36, "animationId": 4, "damage": {"critical": false, "elementId": 8, "formula": "a.mdf * 2", "type": 1, "variance": 20}, "description": "Deal ice damage to all foes twice.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 65, "message1": "", "message2": "", "mpCost": 22, "name": "Blizzard", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\naction animation: targets\nwait: 10\naction effect\nanimation 72: targets\nwait: 20\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 2, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 37, "animationId": 77, "damage": {"critical": false, "elementId": 9, "formula": "a.mdf * 2", "type": 1, "variance": 20}, "description": "Deal lightning damage to all foes twice.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 66, "message1": "", "message2": "", "mpCost": 22, "name": "Lightning Blast", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\naction animation: targets\nwait: 20\naction effect\nwait: 20\nanimation 15: targets\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 2, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 38, "animationId": 51, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "\\c[27]Divine Skill:\\c[0] For 3 turns, skills cast by an ally that usually target \nall foes will instead be focused on a single foe at increased intensity.", "effects": [{"code": 21, "dataId": 23, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 108, "message1": "", "message2": "", "mpCost": 30, "name": "Enlightenment", "note": "<Divine>\n<Require 3 BP>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 2, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 39, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 40, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "<Merchant Skills>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 3, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 41, "animationId": 2, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Collect money from a single foe. Accuracy depends on foe's\ncurrent HP percentage. Accuracy \\c[27]+\\bp=0[5]\\bp=1[33]\\bp=2[67]\\bp=3[100]%\\c[0].", "effects": [], "hitType": 0, "iconIndex": 314, "message1": "", "message2": "", "mpCost": 2, "name": "Collect", "note": "<Before Eval>\nvar text = '<CENTER>';\nif (!target._isMoneyCollected && target.isEnemy()) {\n    var rate = [0.05, 0.33, 0.67, 1.0][user.bp] + 1 - target.hpRate();\n    if (Math.random() < rate) {\n        target._isMoneyCollected = true;\n        SoundManager.playShop();\n        var gold = target.gold();\n        $gameParty.gainGold(gold);\n        text += 'Collected ' + gold + ' ' + TextManager.currencyUnit;\n    } else {\n        text += 'Failed to collect';\n    }\n} else {\n    text += 'Already collected'\n}\ntext += ' from ' + target.name() + '!';\nBattleManager.addText(text, 60);\n</Before Eval>\n\n<whole action>\nperform start\nmotion standby: user\nwait for movement\nface user: targets\n</whole action>\n\n<target action>\nmotion skill: user\nwait: 10\naction animation: target\nwait: 10\naction effect\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 1, "stypeId": 3, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 42, "animationId": 92, "damage": {"critical": false, "elementId": 10, "formula": "a.mat * 4", "type": 1, "variance": 20}, "description": "Deal heavy wind damage to a single foe.\n\\c[27]\\bpdamage[400]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 69, "message1": "", "message2": "", "mpCost": 7, "name": "Tradewinds", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait: 10\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 3, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 43, "animationId": 41, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Restore one's own HP and SP, and cure status ailments.", "effects": [{"code": 22, "dataId": 6, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 24, "message1": "", "message2": "", "mpCost": 0, "name": "Rest", "note": "<Before Eval>\nvar hp = target.mhp * 0.25 * (user.bp + 1);\nvar mp = target.mmp * 0.25 * (user.bp + 1);\ntarget.gainHp(Math.round(hp));\ntarget.gainMp(Math.round(mp));\ntarget.startDamagePopup();\n</Before Eval>\n\n<whole action>\nmotion skill: user\nwait: 10\naction animation: targets\nwait: 10\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 3, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 44, "animationId": 91, "damage": {"critical": false, "elementId": 10, "formula": "a.mat * 2", "type": 1, "variance": 20}, "description": "Deal wind damage to all foes.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 69, "message1": "", "message2": "", "mpCost": 10, "name": "Trade Tempest", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait: 10\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 3, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 45, "animationId": 51, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "<PERSON> \\c[27]\\bpeffect[1]\\c[0] BP to an ally \\}(cannot target self)\\{.", "effects": [], "hitType": 0, "iconIndex": 160, "message1": "", "message2": "", "mpCost": 3, "name": "Donate BP", "note": "<Target BP: +1>\n<Boost BP Effect>\n<Bypass Target Change>\n\n<Select Conditions>\nNot User\n</Select Conditions>\n\n<Before Eval>\nvar bp = user.bp + 1;\ntarget.result().mpDamage = bp;\ntarget.startDamagePopup();\ntarget.clearResult();\n</Before Eval>\n\n<whole action>\nmotion spell: user\n</whole action>\n\n<target action>\naction animation: target\nwait: 10\naction effect\nImmortal: target, false\nwait: 10\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 3, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 46, "animationId": 12, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Dodge \\c[27]\\bpEffect[1]\\c[0] physical attack\\bp[s] with 100% success rate.", "effects": [{"code": 21, "dataId": 24, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 82, "message1": "", "message2": "", "mpCost": 3, "name": "Sidestep", "note": "<whole action>\nmotion skill: user\nwait: 10\naction animation: target\nwait: 10\naction effect\nImmortal: target, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 3, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 47, "animationId": 6, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Pay money to summon hired help to the battlefield.\n", "effects": [], "hitType": 0, "iconIndex": 314, "message1": "", "message2": "", "mpCost": 0, "name": "<PERSON><PERSON>", "note": "<Extra Skill List: 151, 152, 153, 154, 155>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 3, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 48, "animationId": 115, "damage": {"critical": false, "elementId": 0, "formula": "(a.atk + a.mat) * 6", "type": 1, "variance": 20}, "description": "\\c[27]Divine Skill:\\c[0] Unleash a non-elemental attack on a single foe and\nreceive money equivalent to the damage dealt.", "effects": [], "hitType": 2, "iconIndex": 116, "message1": "", "message2": "", "mpCost": 30, "name": "<PERSON>er <PERSON>", "note": "<Divine>\n<Require 3 BP>\n<Boost Damage>\n\n<Post-Damage Eval>\n$gameParty.gainGold(value);\nSoundManager.playShop();\nvar text = '<CENTER>Gained ' + value + ' ' + TextManager.currencyUnit + '!';\nBattleManager.addText(text, 60);\n</Post-Damage Eval>\n\n<whole action>\nmotion skill: user\n</whole action>\n\n<target action>\nanimation 122: target\nwait for animation\naction animation: target\nwait for animation\nanimation 126: target\nwait: 48\naction effect\nImmortal: target, false\n</target action>\n\n<follow action>\nwait for animation\n</follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 3, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 49, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 50, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "<Warrior Skills>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 4, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 51, "animationId": 6, "damage": {"critical": true, "elementId": 1, "formula": "a.atk * 2", "type": 1, "variance": 20}, "description": "Attack all foes with a sword.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 1, "iconIndex": 97, "message1": "", "message2": "", "mpCost": 9, "name": "Level Slash", "note": "<Boost Damage>\n<Switch to Weapon: Sword>\n\n<whole action>\nmotion attack: user\nwait: 10\naction animation: target\nwait: 10\naction effect\nImmortal: target, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 1, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 4, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 52, "animationId": 51, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Increase the user's ATK for \\c[27]\\bpTurn[3]\\c[0] turns.", "effects": [{"code": 31, "dataId": 2, "value1": 3, "value2": 0}], "hitType": 0, "iconIndex": 34, "message1": "", "message2": "", "mpCost": 4, "name": "Abide", "note": "<Boost Turns>\n\n<whole action>\nmotion skill: user\nwait: 10\naction animation: target\nwait: 10\naction effect\nImmortal: target, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 4, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 53, "animationId": 11, "damage": {"critical": true, "elementId": 2, "formula": "a.atk * 2", "type": 1, "variance": 20}, "description": "Attack a single foe with a spear, and act earlier on your next turn.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 1, "iconIndex": 107, "message1": "", "message2": "", "mpCost": 6, "name": "<PERSON><PERSON><PERSON>", "note": "<Boost Damage>\n<Switch to Weapon: Spear>\n\n<whole action>\nperform start\nmotion standby: user\nwait for movement\nface user: targets\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\nanimation 38: target\nattack animation: target\nwait: 10\naction effect\nImmortal: targets, false\nwait: 10\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 2, "requiredWtypeId2": 0, "scope": 1, "speed": 2, "stypeId": 4, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 54, "animationId": 3, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Become more readily targeted by foes for \\c[27]\\bpTurn[3]\\c[0] turns.", "effects": [{"code": 21, "dataId": 25, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 128, "message1": "", "message2": "", "mpCost": 4, "name": "Incite", "note": "<Boost Turns>\n\n<whole action>\nmotion skill: user\nwait: 10\naction animation: target\nwait: 10\naction effect\nImmortal: target, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 4, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 55, "animationId": 7, "damage": {"critical": true, "elementId": 1, "formula": "a.atk * 4", "type": 1, "variance": 20}, "description": "Unleash a heavy sword attack on a single foe.\n\\c[27]\\bpdamage[400]%\\c[0] Multiplier.", "effects": [], "hitType": 1, "iconIndex": 97, "message1": "", "message2": "", "mpCost": 12, "name": "Cross Strike", "note": "<Boost Damage>\n<Switch to Weapon: Sword>\n\n<whole action>\nperform start\nmotion standby: user\nwait for movement\nface user: targets\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\nattack animation: target\nwait: 10\naction effect\nImmortal: targets, false\nwait: 10\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 1, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 4, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 56, "animationId": 53, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Increase the user's DEF for \\c[27]\\bpTurn[3]\\c[0] turns.", "effects": [{"code": 31, "dataId": 3, "value1": 3, "value2": 0}], "hitType": 0, "iconIndex": 35, "message1": "", "message2": "", "mpCost": 4, "name": "Stout Wall", "note": "<Boost Turns>\n\n<whole action>\nmotion skill: user\nwait: 10\naction animation: target\nwait: 10\naction effect\nImmortal: target, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 4, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 57, "animationId": -1, "damage": {"critical": true, "elementId": 2, "formula": "a.atk", "type": 1, "variance": 20}, "description": "Attack random foes with a spear 5 to 10 times.\n\\c[27]\\bpdamage[100]%\\c[0] Multiplier.", "effects": [], "hitType": 0, "iconIndex": 107, "message1": "", "message2": "", "mpCost": 20, "name": "Thousand Spears", "note": "<Boost Damage>\n<Switch to Weapon: Spear>\n\n<Custom Target Text>\ntext = '5 to 10 Random Enemies';\n</Custom Target Text>\n\n<Custom Target Eval>\nvar times = 5 + Math.randomInt(6);\nvar members = foes.aliveMembers();\nwhile (times--) {\n    var member = members[Math.floor(Math.random() * members.length)];\n    targets.push(member);\n}\n</Custom Target Eval>\n\n<whole action>\nperform start\nmotion standby: user\nwait for movement\nface user: targets\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\nanimation 38: target\nattack animation: target\nwait: 10\naction effect\n</target action>\n\n<follow action>\nImmortal: targets, false\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 2, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 4, "successRate": 80, "tpCost": 0, "tpGain": 0}, {"id": 58, "animationId": 10, "damage": {"critical": true, "elementId": 1, "formula": "a.atk * 16", "type": 1, "variance": 20}, "description": "\\c[27]Divine Skill:\\c[0] Unleash a tremendously powerful sword attack\non a single foe.", "effects": [], "hitType": 1, "iconIndex": 112, "message1": "", "message2": "", "mpCost": 30, "name": "Thunderclap", "note": "<Divine>\n<Require 3 BP>\n<Boost Damage>\n<Switch to Weapon: Sword>\n\n<whole action>\nanimation 76: user\nwait for animation\nanimation 77: user\nwait for animation\nanimation 51: user\nwait for animation\nmove user: targets, front, 20\nmotion standby: user\nwait for movement\nface user: targets\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\naction animation: target\nwait: 10\naction effect\nImmortal: targets, false\nwait: 10\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 1, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 4, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 59, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 60, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "<Dancer Skills>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 5, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 61, "animationId": 51, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Augment a single ally's ATK for \\c[27]\\bpturn[2]\\c[0] turns.", "effects": [{"code": 31, "dataId": 2, "value1": 2, "value2": 0}], "hitType": 0, "iconIndex": 34, "message1": "", "message2": "", "mpCost": 4, "name": "Lion Dance", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 5, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 62, "animationId": 102, "damage": {"critical": false, "elementId": 12, "formula": "a.mat * 4", "type": 1, "variance": 20}, "description": "Deal heavy dark damage to a single foe.\n\\c[27]\\bpdamage[400]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 71, "message1": "", "message2": "", "mpCost": 7, "name": "月光圆舞曲", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait: 10\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 5, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 63, "animationId": 51, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Augment a single ally's MAT for \\c[27]\\bpturn[2]\\c[0] turns.", "effects": [{"code": 31, "dataId": 4, "value1": 2, "value2": 0}], "hitType": 0, "iconIndex": 36, "message1": "", "message2": "", "mpCost": 4, "name": "Peacock Strut", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 5, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 64, "animationId": 53, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Augment a single ally's DEF for \\c[27]\\bpturn[2]\\c[0] turns.", "effects": [{"code": 31, "dataId": 3, "value1": 2, "value2": 0}], "hitType": 0, "iconIndex": 35, "message1": "", "message2": "", "mpCost": 4, "name": "<PERSON>le Dance", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 5, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 65, "animationId": 101, "damage": {"critical": false, "elementId": 12, "formula": "a.mat * 2", "type": 1, "variance": 20}, "description": "Deal dark damage to all foes.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 71, "message1": "", "message2": "", "mpCost": 10, "name": "夜之颂歌", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait: 10\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 5, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 66, "animationId": 51, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Augment a single ally's AGI for \\c[27]\\bpturn[2]\\c[0] turns.", "effects": [{"code": 31, "dataId": 6, "value1": 2, "value2": 0}], "hitType": 0, "iconIndex": 38, "message1": "", "message2": "", "mpCost": 4, "name": "Panther Dance", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 5, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 67, "animationId": 52, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Cause a curious effect to occur \\c[27]\\bprepeat[1]\\c[0] time\\bp[s].", "effects": [], "hitType": 0, "iconIndex": 14, "message1": "", "message2": "", "mpCost": 25, "name": "Bewildering Grace", "note": "<BP Repeat>\n<Bypass Target Change>\n\n<setup action>\n</setup action>\n\n<whole action>\neval: $gameVariables.setValue(4, BattleManager._subject.bp)\n</whole action>\n\n<target action>\nclear battle log\ndisplay action\nmotion victory: user\naction animation: user\nwait for animation\nif $gameVariables.value(4) === 0\n  eval: console.log(0)\n  common event: 1\nelse if $gameVariables.value(4) === 1\n  eval: console.log(1)\n  common event: 2\nelse if $gameVariables.value(4) === 2\n  eval: console.log(2)\n  common event: 3\nelse if $gameVariables.value(4) === 3\n  eval: console.log(3)\n  common event: 4\nend\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 5, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 68, "animationId": 51, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "\\c[27]Divine Skill:\\c[0] For 3 turns, skills performed by a single ally that \nusually select one target will affect all targets instead. \\}(Except divine skills)\\{", "effects": [{"code": 21, "dataId": 26, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 149, "message1": "", "message2": "", "mpCost": 30, "name": "Spread the Love", "note": "<Divine>\n<Require 3 BP>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 5, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 69, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 70, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "<Apothecary Skills>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 6, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 71, "animationId": 41, "damage": {"critical": false, "elementId": 0, "formula": "a.mdf * 4", "type": 3, "variance": 20}, "description": "Restore HP to a single ally.\n\\c[27]\\bpdamage[400]%\\c[0] Multiplier.", "effects": [], "hitType": 0, "iconIndex": 72, "message1": "", "message2": "", "mpCost": 4, "name": "First Aid", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 6, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 72, "animationId": 71, "damage": {"critical": false, "elementId": 8, "formula": "a.mat * 4", "type": 1, "variance": 20}, "description": "Deal heavy ice damage to a single foe.\n\\c[27]\\bpdamage[400]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 65, "message1": "", "message2": "", "mpCost": 7, "name": "I<PERSON><PERSON>", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait: 10\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 6, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 73, "animationId": 45, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Cure a single ally of all status ailments, and then render them \nimmune to further ailments for \\c[27]\\bpturn[2]\\c[0] turns.", "effects": [{"code": 22, "dataId": 6, "value1": 1, "value2": 0}, {"code": 22, "dataId": 7, "value1": 1, "value2": 0}, {"code": 22, "dataId": 8, "value1": 1, "value2": 0}, {"code": 22, "dataId": 9, "value1": 1, "value2": 0}, {"code": 22, "dataId": 10, "value1": 1, "value2": 0}, {"code": 22, "dataId": 11, "value1": 1, "value2": 0}, {"code": 22, "dataId": 12, "value1": 1, "value2": 0}, {"code": 21, "dataId": 27, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 241, "message1": "", "message2": "", "mpCost": 10, "name": "Rehabilitate", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 6, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 74, "animationId": 7, "damage": {"critical": true, "elementId": 4, "formula": "a.atk * 4", "type": 1, "variance": 20}, "description": "Unleash an axe attack on a single foe.\n\\c[27]\\bpdamage[400]%\\c[0] Multiplier.", "effects": [], "hitType": 1, "iconIndex": 99, "message1": "", "message2": "", "mpCost": 8, "name": "Amputation", "note": "<Boost Damage>\n<Switch to Weapon: Axe>\n\n<whole action>\nperform start\nmotion standby: user\nwait for movement\nface user: targets\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\nattack animation: target\nwait: 10\naction effect\nImmortal: targets, false\nwait: 10\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 4, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 6, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 75, "animationId": 59, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Poison a single foe for \\c[27]\\bpturn[2]\\c[0] turns.", "effects": [{"code": 21, "dataId": 6, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 2, "message1": "", "message2": "", "mpCost": 6, "name": "Empoison", "note": "<Boost Turns>\n\n<whole action>\nmotion skill: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 6, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 76, "animationId": 49, "damage": {"critical": false, "elementId": 0, "formula": "Math.max(1, b.mhp / 2 * (a.bp + 1))", "type": 1, "variance": 0}, "description": "Revive a single incapacitated ally.", "effects": [], "hitType": 0, "iconIndex": 72, "message1": "", "message2": "", "mpCost": 16, "name": "Vivify", "note": "<whole action>\nmotion skill: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 9, "speed": 0, "stypeId": 6, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 77, "animationId": 9, "damage": {"critical": true, "elementId": 4, "formula": "a.atk * (2 + (1 - a.hpRate()) * 4)", "type": 1, "variance": 20}, "description": "Attack all foes with an axe, dealing damage inversely proportional \nto your current HP. \\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 1, "iconIndex": 99, "message1": "", "message2": "", "mpCost": 16, "name": "Last Stand", "note": "<Boost Damage>\n<Switch to Weapon: Axe>\n\n<whole action>\nperform start\nwait: 20\nmotion standby: user\nwait for movement\nface user: targets\nmotion attack: user\nwait: 10\nattack animation: targets\nwait: 10\naction effect\nImmortal: targets, false\n</whole action>\n\n<target action>\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 4, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 6, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 78, "animationId": 51, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "\\c[27]Divine Skill:\\c[0] For 3 turns, items used by a single choosen ally \nwill affect all.", "effects": [{"code": 21, "dataId": 28, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 147, "message1": "", "message2": "", "mpCost": 30, "name": "Charity", "note": "<Divine>\n<Require 3 BP>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 6, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 79, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 80, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "<Thief Skills>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 7, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 81, "animationId": 38, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Steal an item from a single foe.\nAccuracy \\c[27]+\\bp=0[5]\\bp=1[33]\\bp=2[67]\\bp=3[100]%\\c[0].", "effects": [], "hitType": 0, "iconIndex": 142, "message1": "", "message2": "", "mpCost": 2, "name": "Steal", "note": "<Before Eval>\nvar text = '<CENTER>';\nif (!target._isItemStolen && target.isEnemy()) {\n    var rate = [0.05, 0.33, 0.67, 1.0][user.bp] + 1 - target.hpRate();\n    var items = [];\n    for (var i = 0; i < target.enemy().dropItems.length; i++) {\n        var dropItem = target.enemy().dropItems[i];\n        if (dropItem.kind === 1) {\n            items.push($dataItems[dropItem.dataId]);\n        } else if (dropItem.kind === 2) {\n            items.push($dataWeapons[dropItem.dataId]);\n        } else if (dropItem.kind === 3) {\n            items.push($dataArmors[dropItem.dataId]);\n        }\n    }\n    if (Math.random() < rate && items.length > 0) {\n        target._isItemStolen = true;\n        SoundManager.playUseItem();\n        var item = items[Math.floor(Math.random() * items.length)];\n        $gameParty.gainItem(item);\n        text += 'Stole ' + item.name;\n    } else {\n        text += 'Failed to steal';\n    }\n} else {\n    text += 'Already stolen'\n}\ntext += ' from ' + target.name() + '!';\nBattleManager.addText(text, 60);\n</Before Eval>\n\n<whole action>\nperform start\nmotion standby: user\nwait for movement\nface user: targets\n</whole action>\n\n<target action>\nmotion skill: user\nwait: 10\naction animation: target\nwait: 10\naction effect\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 7, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 82, "animationId": 67, "damage": {"critical": false, "elementId": 7, "formula": "a.mat * 4", "type": 1, "variance": 20}, "description": "Deal heavy fire damage to a single foe.\n\\c[27]\\bpdamage[400]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 64, "message1": "", "message2": "", "mpCost": 7, "name": "Wildfire", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait: 10\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 7, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 83, "animationId": -1, "damage": {"critical": true, "elementId": 3, "formula": "a.atk", "type": 1, "variance": 20}, "description": "Attack a single foe twice with a dagger, and steal HP equivalent to half of the damage dealt.\n\\c[27]\\bpdamage[100]%\\c[0] Multiplier.", "effects": [], "hitType": 1, "iconIndex": 96, "message1": "使用了%1！", "message2": "", "mpCost": 6, "name": "生命窃取", "note": "<Boost Damage>\n<Switch to Weapon: Dagger>\n<HP Life Steal: 50%>\n\n<whole action>\nperform start\nmotion standby: user\nwait for movement\nface user: target\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\nattack animation: target\nwait: 10\naction effect\nmotion attack: user\nwait: 10\nattack animation: target\nwait: 10\naction effect\nImmortal: targets, false\nwait for animation\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 3, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 7, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 84, "animationId": 54, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Reduce a single foe's ATK for \\c[27]\\bpturn[2]\\c[0] turns.", "effects": [{"code": 32, "dataId": 2, "value1": 2, "value2": 0}], "hitType": 0, "iconIndex": 50, "message1": "", "message2": "", "mpCost": 4, "name": "<PERSON><PERSON><PERSON> Foe", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 7, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 85, "animationId": 54, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Reduce a single foe's <PERSON><PERSON> for \\c[27]\\bpturn[2]\\c[0] turns.", "effects": [{"code": 32, "dataId": 3, "value1": 2, "value2": 0}], "hitType": 0, "iconIndex": 51, "message1": "", "message2": "", "mpCost": 4, "name": "Armor Corrosive", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 7, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 86, "animationId": -1, "damage": {"critical": true, "elementId": 3, "formula": "a.atk", "type": 1, "variance": 20}, "description": "Attack a single foe twice with a dagger, and steal MP equivalent to 5% of the damage dealt.\n\\c[27]\\bpdamage[100]%\\c[0] Multiplier.", "effects": [], "hitType": 1, "iconIndex": 96, "message1": "", "message2": "", "mpCost": 6, "name": "能量窃取", "note": "<Boost Damage>\n<Switch to Weapon: Dagger>\n<MP Life Steal: 5%>\n\n<whole action>\nperform start\nmotion standby: user\nwait for movement\nface user: target\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\nattack animation: target\nwait: 10\naction effect\nmotion attack: user\nwait: 10\nattack animation: target\nwait: 10\naction effect\nImmortal: targets, false\nwait for animation\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 3, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 7, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 87, "animationId": 45, "damage": {"critical": false, "elementId": 0, "formula": "a.mp * 0.5 * (a.bp + 1)", "type": 4, "variance": 0}, "description": "Bestow SP equivalent to \\c[27]\\bpEffect[50]%\\c[0] of one's current MP to a single ally.", "effects": [], "hitType": 0, "iconIndex": 165, "message1": "", "message2": "", "mpCost": 0, "name": "Share MP", "note": "<Target BP: +1>\n<Boost BP Effect>\n<Bypass Target Change>\n\n<Select Conditions>\nNot User\n</Select Conditions>\n\n<After Eval>\nvar mp = Math.floor(user.mp * 0.5 * (user.bp + 1));\nuser.gainMp(-mp);\nuser.startDamagePopup();\nuser.clearResult();\n</After Eval>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 7, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 88, "animationId": 8, "damage": {"critical": true, "elementId": 3, "formula": "(a.atk + a.agi) * 4", "type": 1, "variance": 20}, "description": "\\c[27]Divine Skill:\\c[0] Attack all foes with a dagger, dealing damage proportional to your speed.", "effects": [], "hitType": 1, "iconIndex": 123, "message1": "", "message2": "", "mpCost": 30, "name": "Reckoning", "note": "<Divine>\n<Require 3 BP>\n<Boost Damage>\n<Switch to Weapon: Dagger>\n\n<whole action>\nperform start\nwait: 20\nmotion standby: user\nwait for movement\nface user: targets\n</whole action>\n\n<target action>\nanimation 40: target\nwait: 40\nanimation 3: user\nmotion attack: user\nwait: 10\nattack animation: target\nwait: 10\naction effect\nImmortal: targets, false\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 3, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 7, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 89, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 90, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "<Hunter Skills>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 8, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 91, "animationId": 11, "damage": {"critical": true, "elementId": 5, "formula": "a.atk", "type": 1, "variance": 20}, "description": "Attack random foes 5 to 8 times with a bow.\n\\c[27]\\bpdamage[100]%\\c[0] Multiplier.", "effects": [], "hitType": 1, "iconIndex": 102, "message1": "", "message2": "", "mpCost": 8, "name": "Rain of Arrows", "note": "<Boost Damage>\n<Switch to Weapon: Bow>\n\n<Custom Target Text>\ntext = '5 to 8 Random Enemies';\n</Custom Target Text>\n\n<Custom Target Eval>\nvar times = 3 + Math.randomInt(6);\nvar members = foes.aliveMembers();\nwhile (times--) {\n    var member = members[Math.floor(Math.random() * members.length)];\n    targets.push(member);\n}\n</Custom Target Eval>\n\n<whole action>\nmotion standby: user\nface user: targets\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\nanimation 6: target\nwait: 10\naction animation: target\nwait: 10\naction effect\n</target action>\n\n<follow action>\nImmortal: targets, false\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 5, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 8, "successRate": 80, "tpCost": 0, "tpGain": 0}, {"id": 92, "animationId": 12, "damage": {"critical": true, "elementId": 5, "formula": "a.atk * 2", "type": 1, "variance": 20}, "description": "Deal critical damage with a bow to a single foe.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 1, "iconIndex": 102, "message1": "", "message2": "", "mpCost": 10, "name": "True Strike", "note": "<Critical Rate: 100%>\n<Boost Damage>\n<Switch to Weapon: Bow>\n\n<whole action>\nface user: targets\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\nanimation 38: target\nwait: 10\naction animation: target\nwait: 10\naction effect\nImmortal: targets, false\nwait: 10\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 5, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 8, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 93, "animationId": 76, "damage": {"critical": false, "elementId": 9, "formula": "a.mat * 4", "type": 1, "variance": 20}, "description": "Deal heavy lightning damage to a single foe.\n\\c[27]\\bpdamage[400]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 66, "message1": "", "message2": "", "mpCost": 7, "name": "Thunderbird", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait: 10\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 8, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 94, "animationId": 55, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Causes a single foe to act last for \\c[27]\\bpturn[2]\\c[0] turns unless they are \nrecovering from Break.", "effects": [{"code": 21, "dataId": 13, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 54, "message1": "", "message2": "", "mpCost": 6, "name": "<PERSON><PERSON><PERSON>", "note": "<Boost Turns>\n<OTB Target Current Turn: -100>\n<OTB Target Next Turn: -100>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 8, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 95, "animationId": 11, "damage": {"critical": true, "elementId": 5, "formula": "a.atk * 2", "type": 1, "variance": 20}, "description": "Attack a single foe with a bow. Otherwise, lethal attacks will\ninstead leave the target with 1 HP. \\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 1, "iconIndex": 102, "message1": "", "message2": "", "mpCost": 4, "name": "Mercy Strike", "note": "<Boost Damage>\n<Switch to Weapon: Bow>\n\n<Pre-Damage Eval>\nvalue = Math.min(target.hp - 1, value);\n</Pre-Damage Eval>\n\n<whole action>\nface user: targets\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\nanimation 38: target\nwait: 10\naction animation: target\nwait: 10\naction effect\nImmortal: targets, false\nwait: 10\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 5, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 8, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 96, "animationId": 11, "damage": {"critical": true, "elementId": 5, "formula": "a.atk", "type": 1, "variance": 20}, "description": "Attack all foes 5 to 8 times with a bow.\n\\c[27]\\bpdamage[100]%\\c[0] Multiplier.", "effects": [], "hitType": 1, "iconIndex": 102, "message1": "", "message2": "", "mpCost": 24, "name": "Arrow Storm", "note": "<Boost Damage>\n<Switch to Weapon: Bow>\n\n<whole action>\nface user: targets\neval: $gameVariables.setValue(1, Math.randomInt(4))\n\nif $gameVariables.value(1) >= 1\n  motion attack: user\n  wait: 10\n  animation 6: targets\n  wait: 10\n  action animation: targets\n  wait: 10\n  action effect\nend\n\nif $gameVariables.value(1) >= 2\n  motion attack: user\n  wait: 10\n  animation 6: targets\n  wait: 10\n  action animation: targets\n  wait: 10\n  action effect\nend\n\nif $gameVariables.value(1) >= 3\n  motion attack: user\n  wait: 10\n  animation 6: targets\n  wait: 10\n  action animation: targets\n  wait: 10\n  action effect\nend\n\nmotion attack: user\nwait: 10\nanimation 6: targets\nwait: 10\naction animation: targets\nwait: 10\naction effect\n\nmotion attack: user\nwait: 10\nanimation 6: targets\nwait: 10\naction animation: targets\nwait: 10\naction effect\n\nmotion attack: user\nwait: 10\nanimation 6: targets\nwait: 10\naction animation: targets\nwait: 10\naction effect\n\nmotion attack: user\nwait: 10\nanimation 6: targets\nwait: 10\naction animation: targets\nwait: 10\naction effect\n\nmotion attack: user\nwait: 10\nanimation 6: targets\nwait: 10\naction animation: targets\nwait: 10\naction effect\nImmortal: targets, false\n</whole action>\n\n<target action>\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 5, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 8, "successRate": 50, "tpCost": 0, "tpGain": 0}, {"id": 97, "animationId": 47, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Increase all allies' critical rate and accuracy for \\c[27]\\bpturn[2]\\c[0] turns.", "effects": [{"code": 21, "dataId": 29, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 225, "message1": "", "message2": "", "mpCost": 8, "name": "Take Aim", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 8, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 98, "animationId": 11, "damage": {"critical": true, "elementId": 5, "formula": "a.atk * 8", "type": 1, "variance": 20}, "description": "\\c[27]Divine Skill:\\c[0] Unleash a highly powerful bow attack on all foes.", "effects": [], "hitType": 1, "iconIndex": 103, "message1": "", "message2": "", "mpCost": 30, "name": "Hunter's Rage", "note": "<Divine>\n<Require 3 BP>\n<Boost Damage>\n<Switch to Weapon: Bow>\n\n<whole action>\nface user: targets\nanimation 51: user\nwait for animation\nmotion attack: user\nwait: 10\nanimation 29: targets\nwait: 60\naction animation: targets\nwait: 10\naction effect\nImmortal: targets, false\n</whole action>\n\n<target action>\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 5, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 8, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 99, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 100, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "<Starseer Skills>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 9, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 101, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "a.mat * 2", "type": 1, "variance": 20}, "description": "Deal wind, light, and dark damage to all foes.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 78, "message1": "", "message2": "", "mpCost": 35, "name": "Shooting Stars", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\nanimation 92: targets\nwait: 10\nforce element: wind\naction effect\nwait: 40\nanimation 96: targets\nwait: 20\nforce element: light\naction effect\nwait: 40\nanimation 101: targets\nwait: 10\nforce element: dark\naction effect\nclear element\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 9, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 102, "animationId": 49, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Allow a single ally to gain 2 BP per turn for \\c[27]\\bpturn[2]\\c[0] turns.", "effects": [{"code": 21, "dataId": 30, "value1": 1, "value2": 0}], "hitType": 2, "iconIndex": 160, "message1": "", "message2": "", "mpCost": 25, "name": "BP Boost", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 9, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 103, "animationId": 5, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Increase a single ally's critical hit rate for \\c[27]\\bpturn[2]\\c[0] turns.", "effects": [{"code": 21, "dataId": 31, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 76, "message1": "", "message2": "", "mpCost": 25, "name": "Divination", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 9, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 104, "animationId": 52, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Augment a single ally's DEF, MDF, AGI, and EVA for \\c[27]\\bpturn[2]\\c[0] turns.", "effects": [{"code": 31, "dataId": 3, "value1": 2, "value2": 0}, {"code": 31, "dataId": 5, "value1": 2, "value2": 0}, {"code": 31, "dataId": 6, "value1": 2, "value2": 0}, {"code": 21, "dataId": 32, "value1": 1, "value2": 0}], "hitType": 2, "iconIndex": 79, "message1": "", "message2": "", "mpCost": 25, "name": "<PERSON><PERSON>", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 9, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 105, "animationId": 97, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "For \\c[27]\\bpturn[2]\\c[0] turns, rend a single foe unable to receive buffs or\na single ally immune to debuffs.", "effects": [], "hitType": 2, "iconIndex": 75, "message1": "", "message2": "", "mpCost": 25, "name": "Intervention", "note": "<Enemy or Actor Select>\n\n<Boost Turns>\n\n<Before Eval>\nif (target.isActor() === user.isActor()) {\n  target.addState(34);\n} else {\n  target.addState(33);\n}\n</Before Eval>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 9, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 106, "animationId": 43, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "<PERSON> HP regeneration to all allies for \\c[27]\\bpturn[2]\\c[0] turns.", "effects": [{"code": 21, "dataId": 35, "value1": 1, "value2": 0}], "hitType": 2, "iconIndex": 72, "message1": "", "message2": "", "mpCost": 30, "name": "Ethereal Healing", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 9, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 107, "animationId": 49, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Grant a single ally the ability to counter physical damage \\c[27]\\bpturn[1]\\c[0] time\\bp[s].", "effects": [{"code": 21, "dataId": 36, "value1": 1, "value2": 0}], "hitType": 2, "iconIndex": 77, "message1": "", "message2": "", "mpCost": 30, "name": "<PERSON>'s Reflection", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 9, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 108, "animationId": 108, "damage": {"critical": false, "elementId": 0, "formula": "a.mat * 8", "type": 1, "variance": 20}, "description": "\\c[27]Divine Skill:\\c[0] Unleash a non-elemental attack on all foes that deals \ndamage proportional to the party's current BP.", "effects": [], "hitType": 2, "iconIndex": 118, "message1": "", "message2": "", "mpCost": 50, "name": "<PERSON><PERSON><PERSON>", "note": "<Divine>\n<Require 3 BP>\n<Boost Damage>\n\n<Pre-Damage Eval>\nvar members = user.friendsUnit().aliveMembers();\nvar rate = 1;\nfor (var i = 0; i < members.length; i++) {\n  var member = members[i];\n  if (!!member && member.storedBP() > 0) {\n    rate += 0.50 * member.storedBP();\n  }\n}\nvalue = Math.round(rate * value);\n</Pre-Damage Eval>\n\n<whole action>\nanimation 133: user\nmotion spell: user\nwait for animation\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\n</whole action>\n\n<target action>\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 9, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 109, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 110, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "<Runelord Skills>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 10, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 111, "animationId": 3, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Weapons the user attacks with will deal additional fire damage\nfor \\c[27]\\bpturn[3]\\c[0] turns.", "effects": [{"code": 21, "dataId": 37, "value1": 1, "value2": 0}], "hitType": 2, "iconIndex": 64, "message1": "", "message2": "", "mpCost": 15, "name": "Fire Rune", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 10, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 112, "animationId": 4, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Weapons the user attacks with will deal additional ice damage\nfor \\c[27]\\bpturn[3]\\c[0] turns.", "effects": [{"code": 21, "dataId": 38, "value1": 1, "value2": 0}], "hitType": 2, "iconIndex": 65, "message1": "", "message2": "", "mpCost": 15, "name": "Ice Rune", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 10, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 113, "animationId": 5, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Weapons the user attacks with will deal additional lightning damage\nfor \\c[27]\\bpturn[3]\\c[0] turns.", "effects": [{"code": 21, "dataId": 39, "value1": 1, "value2": 0}], "hitType": 2, "iconIndex": 66, "message1": "", "message2": "", "mpCost": 15, "name": "Thunder Rune", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 10, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 114, "animationId": 92, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Weapons the user attacks with will deal additional wind damage\nfor \\c[27]\\bpturn[3]\\c[0] turns.", "effects": [{"code": 21, "dataId": 40, "value1": 1, "value2": 0}], "hitType": 2, "iconIndex": 69, "message1": "", "message2": "", "mpCost": 15, "name": "Wind Rune", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 10, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 115, "animationId": 97, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Weapons the user attacks with will deal additional light damage\nfor \\c[27]\\bpturn[3]\\c[0] turns.", "effects": [{"code": 21, "dataId": 41, "value1": 1, "value2": 0}], "hitType": 2, "iconIndex": 70, "message1": "", "message2": "", "mpCost": 15, "name": "Light Rune", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 10, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 116, "animationId": 102, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Weapons the user attacks with will deal additional dark damage\nfor \\c[27]\\bpturn[3]\\c[0] turns.", "effects": [{"code": 21, "dataId": 42, "value1": 1, "value2": 0}], "hitType": 2, "iconIndex": 71, "message1": "", "message2": "", "mpCost": 15, "name": "<PERSON> Rune", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 10, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 117, "animationId": 2, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "For \\c[27]\\bpturn[3]\\c[0] turns, skills that normally target yourself alone will \ntarget your allies as well.", "effects": [{"code": 21, "dataId": 43, "value1": 1, "value2": 0}], "hitType": 2, "iconIndex": 75, "message1": "", "message2": "", "mpCost": 25, "name": "Transfer Rune", "note": "<Boost Turns>\n<Bypass Target Change>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 10, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 118, "animationId": 101, "damage": {"critical": false, "elementId": 0, "formula": "a.mat * 16", "type": 1, "variance": 20}, "description": "\\c[27]Divine Skill:\\c[0] Deal damage from each of the six elements (fire, ice, \nlightning, wind, light, and dark) to a single foe.", "effects": [], "hitType": 2, "iconIndex": 119, "message1": "", "message2": "", "mpCost": 50, "name": "Element Blades", "note": "<Divine>\n<Require 3 BP>\n<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\nanimation 8: targets\nwait: 10\nforce element: fire\naction effect\nwait for animation\nmotion spell: user\nwait: 10\nanimation 9: targets\nwait: 20\nforce element: ice\naction effect\nwait for animation\nmotion spell: user\nwait: 10\nanimation 10: targets\nwait: 10\nforce element: lightning\naction effect\nwait for animation\nmotion spell: user\nwait: 10\nanimation 92: targets\nwait: 20\nforce element: wind\naction effect\nwait for animation\nmotion spell: user\nwait: 10\nanimation 97: targets\nwait: 20\nforce element: light\naction effect\nwait for animation\nmotion spell: user\nwait: 10\nanimation 101: targets\nwait: 20\nforce element: dark\naction effect\nImmortal: targets, false\nclear element\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 10, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 119, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 120, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "<Warmaster Skills>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 11, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 121, "animationId": -1, "damage": {"critical": true, "elementId": 1, "formula": "a.atk * 1.5", "type": 1, "variance": 20}, "description": "Unleash 5 to 10 sword attacks against random foes.\n\\c[27]\\bpdamage[150]%\\c[0] Multiplier.", "effects": [], "hitType": 1, "iconIndex": 97, "message1": "", "message2": "", "mpCost": 35, "name": "Guardian Liondog", "note": "<Boost Damage>\n<Switch to Weapon: Sword>\n\n<Custom Target Text>\ntext = '5 to 10 Random Enemies';\n</Custom Target Text>\n\n<Custom Target Eval>\nvar times = 5 + Math.randomInt(6);\nvar members = foes.aliveMembers();\nwhile (times--) {\n    var member = members[Math.floor(Math.random() * members.length)];\n    targets.push(member);\n}\n</Custom Target Eval>\n\n<whole action>\nperform start\nwait: 20\nmotion standby: user\nwait for movement\nface user: targets\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\nanimation 39: target\naction animation: target\nwait: 10\naction effect\n</target action>\n\n<follow action>\nImmortal: targets, false\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 1, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 11, "successRate": 90, "tpCost": 0, "tpGain": 0}, {"id": 122, "animationId": -1, "damage": {"critical": true, "elementId": 2, "formula": "a.atk * 6", "type": 1, "variance": 20}, "description": "Unleash a spear attack on a single foe.\n\\c[27]\\bpdamage[600]%\\c[0] Multiplier.", "effects": [], "hitType": 1, "iconIndex": 107, "message1": "", "message2": "", "mpCost": 35, "name": "<PERSON><PERSON>'s Horn", "note": "<Boost Damage>\n<Switch to Weapon: Spear>\n\n<whole action>\nperform start\nmotion standby: user\nwait for movement\nface user: targets\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\nanimation 39: target\nattack animation: target\nwait: 10\naction effect\nImmortal: targets, false\nwait: 10\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 2, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 11, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 123, "animationId": -1, "damage": {"critical": true, "elementId": 3, "formula": "a.atk * 4", "type": 1, "variance": 20}, "description": "Unleash a dagger attack on all foes.\n\\c[27]\\bpdamage[400]%\\c[0] Multiplier.", "effects": [], "hitType": 1, "iconIndex": 96, "message1": "", "message2": "", "mpCost": 35, "name": "八咫乌", "note": "<Boost Damage>\n<Switch to Weapon: Dagger>\n\n<whole action>\nperform start\nwait: 20\nmotion standby: user\nwait for movement\nface user: targets\nmotion attack: user\nwait: 10\nanimation 39: targets\nattack animation: targets\nwait: 10\naction effect\nImmortal: targets, false\n</whole action>\n\n<target action>\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 3, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 11, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 124, "animationId": -1, "damage": {"critical": true, "elementId": 4, "formula": "a.atk * 4", "type": 1, "variance": 20}, "description": "Unleash an axe attack on all foes.\n\\c[27]\\bpdamage[400]%\\c[0] Multiplier.", "effects": [], "hitType": 1, "iconIndex": 99, "message1": "", "message2": "", "mpCost": 35, "name": "Tiger Rage", "note": "<Boost Damage>\n<Switch to Weapon: Axe>\n\n<whole action>\nperform start\nwait: 20\nmotion standby: user\nwait for movement\nface user: targets\nmotion attack: user\nwait: 10\nanimation 39: targets\nattack animation: targets\nwait: 10\naction effect\nImmortal: targets, false\n</whole action>\n\n<target action>\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 4, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 11, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 125, "animationId": -1, "damage": {"critical": true, "elementId": 5, "formula": "a.atk * 6", "type": 1, "variance": 20}, "description": "Unleash a bow attack on a single foe.\n\\c[27]\\bpdamage[600]%\\c[0] Multiplier.", "effects": [], "hitType": 1, "iconIndex": 102, "message1": "", "message2": "", "mpCost": 35, "name": "Phoenix Storm", "note": "<Boost Damage>\n<Switch to Weapon: Bow>\n\n<whole action>\nperform start\nmotion standby: user\nwait for movement\nface user: targets\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\nanimation 39: targets\nattack animation: target\nwait: 10\naction effect\nImmortal: targets, false\nwait: 10\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 5, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 11, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 126, "animationId": 0, "damage": {"critical": true, "elementId": 6, "formula": "a.atk * 4", "type": 1, "variance": 20}, "description": "Unleash a staff attack on all foes.\n\\c[27]\\bpdamage[400]%\\c[0] Multiplier.", "effects": [], "hitType": 1, "iconIndex": 101, "message1": "", "message2": "", "mpCost": 35, "name": "Fox Spirit", "note": "<Boost Damage>\n<Switch to Weapon: Staff>\n\n<whole action>\nperform start\nwait: 20\nmotion standby: user\nwait for movement\nface user: targets\nmotion attack: user\nwait: 10\nanimation 39: targets\nattack animation: targets\nwait: 10\naction effect\nImmortal: targets, false\n</whole action>\n\n<target action>\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 6, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 11, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 127, "animationId": -1, "damage": {"critical": true, "elementId": -1, "formula": "a.atk * 8", "type": 1, "variance": 20}, "description": "Unleash an attack on a single foe using a weapon of your choice. \nThe weapon will be destroyed. \\c[27]\\bpdamage[800]%\\c[0] Multiplier.", "effects": [], "hitType": 1, "iconIndex": 167, "message1": "", "message2": "", "mpCost": 35, "name": "Nightmare Chimera", "note": "<Boost Damage>\n<Require Any Weapon>\n<Destroy Weapon>\n\n<whole action>\nperform start\nmotion standby: user\nwait for movement\nface user: target\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\nanimation 55: target\nattack animation: target\nwait: 10\naction effect\nImmortal: targets, false\nwait: 10\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 11, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 128, "animationId": 6, "damage": {"critical": true, "elementId": -1, "formula": "a.atk * 8", "type": 1, "variance": 20}, "description": "\\c[27]<PERSON> Skill:\\c[0] Wield six weapons to unleash six attacks on all foes.", "effects": [], "hitType": 1, "iconIndex": 98, "message1": "", "message2": "", "mpCost": 50, "name": "War Buster", "note": "<Divine>\n<Require 3 BP>\n<Boost Damage>\n<Require Weapon Types: 1, 2, 3, 4, 5, 6>\n\n<whole action>\nWeapon Swap: user, Sword\nMotion Attack: user\nAnimation 9: targets\nWait: 10\nAction Effect: targets\nWait: 40\nWeapon Swap: user, Bow\nMotion Attack: user\nWait: 5\nAnimation 38: targets\nWait: 10\nAction Effect: targets\nWait: 40\nWeapon Swap: user, Axe\nMotion Attack: user\nWait: 5\nAnimation 18: targets\nWait: 10\nAction Effect: targets\nWait: 40\nWeapon Swap: user, Dagger\nMotion Attack: user\nWait: 5\nAnimation 13: targets\nWait: 10\nAction Effect: targets\nWait: 40\nWeapon Swap: user, Staff\nMotion Attack: user\nWait: 5\nAnimation 66: targets\nWait: 10\nAction Effect: targets\nWait: 40\nWeapon Swap: user, Spear\nMotion Attack: user\nWait: 5\nAnimation 15: targets\nWait: 10\nAction Effect: targets\nImmortal: targets, false\nWait for Animation\n</whole action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 11, "successRate": 100, "tpCost": 0, "tpGain": 10}, {"id": 129, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 130, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "<Sorcerer Skills>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 12, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 131, "animationId": 67, "damage": {"critical": false, "elementId": 7, "formula": "a.mat * 2", "type": 1, "variance": 20}, "description": "Deal fire damage to all foes 3 times.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 64, "message1": "", "message2": "", "mpCost": 36, "name": "<PERSON><PERSON><PERSON>", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\nanimation 67: targets\nwait: 10\naction effect\nwait: 40\nanimation 66: targets\nwait: 10\naction effect\nwait: 40\nanimation 107: targets\nwait: 10\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 12, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 132, "animationId": 4, "damage": {"critical": false, "elementId": 8, "formula": "a.mat * 2", "type": 1, "variance": 20}, "description": "Deal ice damage to all foes 3 times.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 65, "message1": "", "message2": "", "mpCost": 36, "name": "Glacies Claudere", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\nanimation 4: targets\nwait: 10\naction effect\nwait: 40\nanimation 72: targets\nwait: 10\naction effect\nwait: 40\nanimation 71: targets\nwait: 10\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 12, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 133, "animationId": 5, "damage": {"critical": false, "elementId": 9, "formula": "a.mat * 2", "type": 1, "variance": 20}, "description": "Deal lightning damage to all foes 3 times.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 66, "message1": "", "message2": "", "mpCost": 36, "name": "<PERSON><PERSON><PERSON>", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\nanimation 5: targets\nwait: 10\naction effect\nwait: 40\nanimation 76: targets\nwait: 10\naction effect\nwait: 40\nanimation 77: targets\nwait: 10\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 12, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 134, "animationId": 91, "damage": {"critical": false, "elementId": 10, "formula": "a.mat * 2", "type": 1, "variance": 20}, "description": "Deal wind damage to all foes 3 times.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 69, "message1": "", "message2": "", "mpCost": 36, "name": "<PERSON><PERSON><PERSON>", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\nanimation 91: targets\nwait: 10\naction effect\nwait: 40\nanimation 92: targets\nwait: 10\naction effect\nwait: 40\nanimation 93: targets\nwait: 10\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 12, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 135, "animationId": 96, "damage": {"critical": false, "elementId": 11, "formula": "a.mat * 2", "type": 1, "variance": 20}, "description": "Deal light damage to all foes 3 times.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 70, "message1": "", "message2": "", "mpCost": 36, "name": "<PERSON><PERSON>", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\nanimation 97: targets\nwait: 10\naction effect\nwait: 40\nanimation 98: targets\nwait: 10\naction effect\nwait: 50\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 12, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 136, "animationId": 102, "damage": {"critical": false, "elementId": 12, "formula": "a.mat * 2", "type": 1, "variance": 20}, "description": "Deal dark damage to all foes 3 times.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 71, "message1": "", "message2": "", "mpCost": 36, "name": "Tenebrae Operire", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\nanimation 102: targets\nwait: 10\naction effect\nwait: 40\nanimation 103: targets\nwait: 10\naction effect\nwait: 50\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 12, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 137, "animationId": -1, "damage": {"critical": false, "elementId": 6, "formula": "a.atk * 4", "type": 1, "variance": 20}, "description": "Unleash a powerful staff attack on a single foe that also reduces the\nfoe's MDF for \\c[27]\\bpturn[2]\\c[0] turns. \\c[27]\\bpdamage[400]%\\c[0] Multiplier.", "effects": [{"code": 32, "dataId": 5, "value1": 2, "value2": 0}], "hitType": 1, "iconIndex": 53, "message1": "", "message2": "", "mpCost": 20, "name": "Elemental Break", "note": "<Boost Damage>\n<Boost Turns>\n<Switch to Weapon: Staff>\n\n<whole action>\nmotion standby: user\nwait for movement\nface user: targets\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\nanimation 54: target\naction animation: target\nwait: 10\naction effect\nImmortal: targets, false\nwait: 10\n</target action>\n\n<follow action>\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 6, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 12, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 138, "animationId": 49, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "\\c[27]Divine Skill:\\c[0] For 3 turns, elemental attacks performed by a\nsingle chosen ally will hit for critical damage.", "effects": [{"code": 21, "dataId": 44, "value1": 1, "value2": 0}], "hitType": 2, "iconIndex": 121, "message1": "", "message2": "", "mpCost": 50, "name": "Spellmaster", "note": "<Divine>\n<Require 3 BP>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 12, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 139, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 140, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "<Rune Effects>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 12, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 141, "animationId": 8, "damage": {"critical": false, "elementId": 7, "formula": "a.mat", "type": 1, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 64, "message1": "", "message2": "", "mpCost": 0, "name": "Fiery Pursuit", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 142, "animationId": 9, "damage": {"critical": false, "elementId": 8, "formula": "a.mat", "type": 1, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 65, "message1": "", "message2": "", "mpCost": 0, "name": "<PERSON><PERSON>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 143, "animationId": 10, "damage": {"critical": false, "elementId": 9, "formula": "a.mat", "type": 1, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 66, "message1": "", "message2": "", "mpCost": 0, "name": "Shocking Pursuit", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 144, "animationId": 92, "damage": {"critical": false, "elementId": 10, "formula": "a.mat", "type": 1, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 69, "message1": "", "message2": "", "mpCost": 0, "name": "Windy Pursuit", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 145, "animationId": 97, "damage": {"critical": false, "elementId": 11, "formula": "a.mat", "type": 1, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 70, "message1": "", "message2": "", "mpCost": 0, "name": "Bright Pursuit", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 146, "animationId": 102, "damage": {"critical": false, "elementId": 12, "formula": "a.mat", "type": 1, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 71, "message1": "", "message2": "", "mpCost": 0, "name": "Shadowy Pursuit", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 147, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 148, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 149, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 150, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "<Hired Help>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 151, "animationId": 6, "damage": {"critical": false, "elementId": 1, "formula": "15", "type": 1, "variance": 20}, "description": "Unleash \\c[27]\\bpRepeat[1]\\c[0] sword attack\\bp[s] on all foes, and \naugment DEF for all allies for \\c[27]\\bpTurn[1]\\c[0] turns.", "effects": [], "hitType": 0, "iconIndex": 97, "message1": "", "message2": "", "mpCost": 0, "name": "Mercenary", "note": "<Custom Requirement>\nvalue = $gameParty.gold() > 150;\n</Custom Requirement>\n\n<Custom Execution>\n$gameParty.loseGold(150);\n</Custom Execution>\n\n<Custom Cost Display>\n150\\c[27]\\G\\c[0]\n</Custom Cost Display>\n\n<whole action>\nanimation 127: user, mirror\nmotion spell: user\nwait: 30\nif BattleManager._subject.bp >= 0\n  action animation: targets\n  wait: 10\n  action effect\nend\nif BattleManager._subject.bp >= 1\n  action animation: targets\n  wait: 10\n  action effect\nend\nif BattleManager._subject.bp >= 2\n  action animation: targets\n  wait: 10\n  action effect\nend\nif BattleManager._subject.bp >= 3\n  action animation: targets\n  wait: 10\n  action effect\nend\n</whole action>\n\n<target action>\n</target action>\n\n<follow action>\nanimation 53: all friends\nif BattleManager._subject.bp === 0\n  add DEF buff: all friends, 1\nelse if BattleManager._subject.bp === 1\n  add DEF buff: all friends, 2\nelse if BattleManager._subject.bp === 2\n  add DEF buff: all friends, 3\nelse if BattleManager._subject.bp === 3\n  add DEF buff: all friends, 4\nend\nwait: 90\n</follow action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 152, "animationId": 7, "damage": {"critical": false, "elementId": 4, "formula": "80", "type": 1, "variance": 20}, "description": "Unleash an axe attack on all foes.", "effects": [], "hitType": 0, "iconIndex": 99, "message1": "", "message2": "", "mpCost": 0, "name": "Bandit", "note": "<Custom Requirement>\nvalue = $gameParty.gold() > 800;\n</Custom Requirement>\n\n<Custom Execution>\n$gameParty.loseGold(800);\n</Custom Execution>\n\n<Custom Cost Display>\n800\\c[27]\\G\\c[0]\n</Custom Cost Display>\n\n<whole action>\nanimation 128: user, mirror\nmotion spell: user\nwait: 30\nif BattleManager._subject.bp >= 0\n  action animation: targets\n  wait: 10\n  action effect\nend\nif BattleManager._subject.bp >= 1\n  action animation: targets\n  wait: 10\n  action effect\nend\nif BattleManager._subject.bp >= 2\n  action animation: targets\n  wait: 10\n  action effect\nend\nif BattleManager._subject.bp >= 3\n  action animation: targets\n  wait: 10\n  action effect\nend\n</whole action>\n\n<target action>\n</target action>\n\n<follow action>\n</follow action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 153, "animationId": 2, "damage": {"critical": false, "elementId": 3, "formula": "250", "type": 1, "variance": 20}, "description": "Unleash a dagger attack on all foes that has a chance of \ninflicting status ailments.", "effects": [], "hitType": 0, "iconIndex": 96, "message1": "", "message2": "", "mpCost": 0, "name": "Dancer", "note": "<Custom Requirement>\nvalue = $gameParty.gold() > 2500;\n</Custom Requirement>\n\n<Custom Execution>\n$gameParty.loseGold(2500);\n</Custom Execution>\n\n<Custom Cost Display>\n2,500\\c[27]\\G\\c[0]\n</Custom Cost Display>\n\n<whole action>\nanimation 129: user, mirror\nmotion spell: user\nwait: 30\nif BattleManager._subject.bp >= 0\n  action animation: targets\n  wait: 15\n  action effect\nend\nif BattleManager._subject.bp >= 1\n  action animation: targets\n  wait: 15\n  action effect\nend\nif BattleManager._subject.bp >= 2\n  action animation: targets\n  wait: 15\n  action effect\nend\nif BattleManager._subject.bp >= 3\n  action animation: targets\n  wait: 15\n  action effect\nend\n</whole action>\n\n<target action>\n</target action>\n\n<follow action>\n</follow action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 154, "animationId": 97, "damage": {"critical": false, "elementId": 11, "formula": "1000", "type": 1, "variance": 20}, "description": "Deal light damage to all foes.", "effects": [], "hitType": 0, "iconIndex": 70, "message1": "", "message2": "", "mpCost": 0, "name": "Cleric", "note": "<Custom Requirement>\nvalue = $gameParty.gold() > 10000;\n</Custom Requirement>\n\n<Custom Execution>\n$gameParty.loseGold(10000);\n</Custom Execution>\n\n<Custom Cost Display>\n10,000\\c[27]\\G\\c[0]\n</Custom Cost Display>\n\n<whole action>\nanimation 130: user, mirror\nmotion spell: user\nwait: 30\nif BattleManager._subject.bp >= 0\n  action animation: targets\n  wait: 30\n  action effect\nend\nif BattleManager._subject.bp >= 1\n  action animation: targets\n  wait: 30\n  action effect\nend\nif BattleManager._subject.bp >= 2\n  action animation: targets\n  wait: 30\n  action effect\nend\nif BattleManager._subject.bp >= 3\n  action animation: targets\n  wait: 30\n  action effect\nend\n</whole action>\n\n<target action>\n</target action>\n\n<follow action>\n</follow action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 155, "animationId": 10, "damage": {"critical": false, "elementId": 1, "formula": "3000", "type": 1, "variance": 20}, "description": "Unleash a powerful sword attack on all foes.", "effects": [], "hitType": 0, "iconIndex": 97, "message1": "", "message2": "", "mpCost": 0, "name": "<PERSON>", "note": "<Custom Requirement>\nvalue = $gameParty.gold() > 30000;\n</Custom Requirement>\n\n<Custom Execution>\n$gameParty.loseGold(30000);\n</Custom Execution>\n\n<Custom Cost Display>\n30,000\\c[27]\\G\\c[0]\n</Custom Cost Display>\n\n<whole action>\nanimation 131: user, mirror\nmotion spell: user\nwait: 30\nif BattleManager._subject.bp >= 0\n  action animation: targets\n  wait: 10\n  action effect\nend\nif BattleManager._subject.bp >= 1\n  action animation: targets\n  wait: 10\n  action effect\nend\nif BattleManager._subject.bp >= 2\n  action animation: targets\n  wait: 10\n  action effect\nend\nif BattleManager._subject.bp >= 3\n  action animation: targets\n  wait: 10\n  action effect\nend\n</whole action>\n\n<target action>\n</target action>\n\n<follow action>\n</follow action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 156, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 157, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 158, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 159, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 160, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "<Bewildering Grace>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 161, "animationId": 62, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 11, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "Your foe appears drowsy", "note": "<Bypass Target Change>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 3, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 162, "animationId": 41, "damage": {"critical": false, "elementId": 0, "formula": "400", "type": 3, "variance": 0}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "You recover a small amount of HP", "note": "<Bypass Target Change>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 163, "animationId": 93, "damage": {"critical": false, "elementId": 10, "formula": "a.mat", "type": 1, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "A Tempest Howls", "note": "<Bypass Target Change>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 164, "animationId": 53, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 31, "dataId": 3, "value1": 2, "value2": 0}], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "Strengthen Defenses", "note": "<Bypass Target Change>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 165, "animationId": 45, "damage": {"critical": false, "elementId": 0, "formula": "50", "type": 4, "variance": 0}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "MP Recovered", "note": "<Bypass Target Change>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 166, "animationId": 51, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "BP Recovered", "note": "<Bypass Target Change>\n<Target BP: +1>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 167, "animationId": 76, "damage": {"critical": false, "elementId": 9, "formula": "a.mat * 4", "type": 1, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "A bolt of lightning strikes the enemy", "note": "<Bypass Target Change>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 3, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 168, "animationId": 47, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "JP x2", "note": "<Bypass Target Change>\n<JP x2>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 169, "animationId": 47, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "EXP x2", "note": "<Bypass Target Change>\n<EXP x2>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 170, "animationId": 52, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "BP Restored", "note": "<Bypass Target Change>\n<Target BP: +5>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 171, "animationId": 99, "damage": {"critical": false, "elementId": 11, "formula": "a.mat", "type": 1, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "Light cascades down from above", "note": "<Bypass Target Change>", "occasion": 0, "repeats": 3, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 172, "animationId": 53, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 21, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "You are protected by a mysterious power", "note": "<Bypass Target Change>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 173, "animationId": 47, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "JP x5", "note": "<Bypass Target Change>\n<JP x5>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 174, "animationId": 47, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "EXP x5", "note": "<Bypass Target Change>\n<EXP x5>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 175, "animationId": 41, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 22, "dataId": 1, "value1": 1, "value2": 0}, {"code": 22, "dataId": 6, "value1": 1, "value2": 0}, {"code": 22, "dataId": 7, "value1": 1, "value2": 0}, {"code": 22, "dataId": 8, "value1": 1, "value2": 0}, {"code": 22, "dataId": 9, "value1": 1, "value2": 0}, {"code": 22, "dataId": 10, "value1": 1, "value2": 0}, {"code": 22, "dataId": 11, "value1": 1, "value2": 0}, {"code": 22, "dataId": 12, "value1": 1, "value2": 0}, {"code": 34, "dataId": 0, "value1": 1, "value2": 0}, {"code": 34, "dataId": 1, "value1": 1, "value2": 0}, {"code": 34, "dataId": 2, "value1": 1, "value2": 0}, {"code": 34, "dataId": 3, "value1": 1, "value2": 0}, {"code": 34, "dataId": 4, "value1": 1, "value2": 0}, {"code": 34, "dataId": 5, "value1": 1, "value2": 0}, {"code": 34, "dataId": 6, "value1": 1, "value2": 0}, {"code": 34, "dataId": 7, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "Full Party Restore", "note": "<Bypass Target Change>\n\n<Custom Target Eval>\ntargets = $gameParty.members();\n</Custom Target Eval>\n\n<Before Eval>\ntarget.gainHp(target.mhp);\ntarget.gainMp(target.mmp);\n</Before Eval>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 176, "animationId": 105, "damage": {"critical": false, "elementId": 0, "formula": "a.mat * 24", "type": 1, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "A Monster Appears", "note": "<Bypass Target Change>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 177, "animationId": 52, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "Transformation", "note": "<Bypass Target Change>\n\n<Before Eval>\nif (target.isEnemy() && !target.enemy().note.match(/<Boss>/i)) {\n  target.transform(5);\n}\n</Before Eval>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 3, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 178, "animationId": 43, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "JP x100", "note": "<Bypass Target Change>\n<JP x100>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 179, "animationId": 43, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "EXP x100", "note": "<Bypass Target Change>\n<EXP x100>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 180, "animationId": 54, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 32, "dataId": 3, "value1": 2, "value2": 0}, {"code": 32, "dataId": 5, "value1": 2, "value2": 0}], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "Weaken Defenses", "note": "<Bypass Target Change>\n\n<whole action>\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 181, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "The dance has no effect", "note": "<Bypass Target Change>\n\n<Target Action>\nWait: 60\n</Target Action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 182, "animationId": 35, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 7, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "You are enveloped in a mist...", "note": "<Bypass Target Change>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 183, "animationId": 66, "damage": {"critical": false, "elementId": 7, "formula": "a.mat", "type": 1, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "The ground explodes", "note": "<Bypass Target Change>\n\n<whole action>\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 184, "animationId": 54, "damage": {"critical": false, "elementId": 0, "formula": "b.mp", "type": 2, "variance": 0}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "MP reduced to 0", "note": "<Bypass Target Change>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 185, "animationId": 59, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 6, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "You are enveloped in poison...", "note": "<Bypass Target Change>\n\n<whole action>\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>\n\n<Before Eval>\ntarget.addState(6);\ntarget._stateTurns[6] = 9;\n</Before Eval>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 186, "animationId": 54, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 5, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "Your items lie scattered on the ground", "note": "<Bypass Target Change>\n\n<whole action>\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 187, "animationId": 54, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "BP Depleted", "note": "<Bypass Target Change>\n<Target BP: -5>\n\n<whole action>\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 188, "animationId": 41, "damage": {"critical": false, "elementId": 0, "formula": "b.mhp - b.hp", "type": 1, "variance": 0}, "description": "", "effects": [{"code": 31, "dataId": 3, "value1": 2, "value2": 0}, {"code": 31, "dataId": 4, "value1": 2, "value2": 0}, {"code": 31, "dataId": 6, "value1": 2, "value2": 0}], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "Restore Opponent", "note": "<Bypass Target Change>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 189, "animationId": 106, "damage": {"critical": false, "elementId": 0, "formula": "b.hp - 1", "type": 1, "variance": 0}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "Weaken Party", "note": "<Bypass Target Change>\n\n<whole action>\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 190, "animationId": 65, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 21, "dataId": 1, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "Unwanted Sacrifice", "note": "<Bypass Target Change>\n\n<Custom Target Eval>\nvar members = friends.aliveMembers();\nvar index = targets.indexOf(user);\nmembers.splice(index, 1);\nvar targets = [members[Math.floor(Math.random() * members.length)]];\n</Custom Target Eval>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 1, "successRate": 50, "tpCost": 0, "tpGain": 0}, {"id": 191, "animationId": 107, "damage": {"critical": false, "elementId": 0, "formula": "a.mat * 8", "type": 1, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "You are enveloped in flames...", "note": "<Bypass Target Change>\n\n<whole action>\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 192, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 193, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 194, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 195, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "<Misc Skills>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 196, "animationId": 97, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 79, "message1": "", "message2": "", "mpCost": 0, "name": "Study Foe", "note": "<Analyze Weakness: 1>\n\n<whole action>\nanimation 51: user\nmotion standby: user\nwait for animation\naction animation: targets\nwait for animation\neval: BattleManager.revealWeakness(1)\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 2, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 197, "animationId": 22, "damage": {"critical": false, "elementId": 0, "formula": "a.friendsUnit().aliveMembers().reduce(function(r, member) { return r + member.atk }, 0) * 2", "type": 1, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 77, "message1": "", "message2": "", "mpCost": 0, "name": "All Out Attack", "note": "<Divine>\n<Critical Rate: 100%>\n\n<Custom Select Condition>\ncondition = target.isStateAffected(4);\n</Custom Select Condition>\n\n<Custom Target Eval>\nvar members = foes.aliveMembers();\nfor (var i = 0; i < members.length; i++) {\n  var member = members[i];\n  if (!!member && member.isStateAffected(4)) {\n    targets.push(member);\n  }\n}\n</Custom Target Eval>\n\n<setup action>\nclear battle log\ndisplay action\nimmortal: targets, true\n</setup action>\n\n<whole action>\nmotion victory: user\nwait: 60\nmove friends: targets, center, 30\njump friends: 200%, 30\nwait: 15\nanimation 136: targets\nwait: 15\naction animation: targets\nani wait: 20\naction effect\nImmortal: targets, false\n</whole action>\n\n<target action>\n</target action>\n\n<follow action>\njump friends: 200%, 60\nmove friends: return, 60\nwait: 60\nwait for animation\n</follow action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 14, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 198, "animationId": 22, "damage": {"critical": false, "elementId": 0, "formula": "a.friendsUnit().aliveMembers().reduce(function(r, member) { return r + member.atk }, 0) * 2", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 14, "message1": "", "message2": "", "mpCost": 0, "name": "All Out Attack Prompt", "note": "<setup action>\ncommon event: 5\n</setup action>\n\n<whole action>\n</whole action>\n\n<target action>\n</target action>\n\n<follow action>\n</follow action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 0, "speed": 0, "stypeId": 14, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 199, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 200, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "<Time Blade>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 13, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 201, "animationId": 2, "damage": {"critical": true, "elementId": 1, "formula": "a.atk", "type": 1, "variance": 20}, "description": "Attack a single foe \\c[27]\\bpRepeat[1]\\c[0] time\\bp[s] with a sword.\nEach hit delays struck foe for the current and next turns.", "effects": [], "hitType": 1, "iconIndex": 97, "message1": " attacks!", "message2": "", "mpCost": 15, "name": "Delay Strike", "note": "<Cast Animation: 55>\n\n<BP Repeat>\n<Switch to Weapon: Sword>\n<OTB Target Current Turn: -1>\n<OTB Target Next Turn: -1>\n\n<whole action>\nperform start\nmotion standby: user\nwait for movement\nface user: target\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\nattack animation: target\nwait: 10\naction animation: target\naction effect\nwait: 10\n</target action>\n\n<follow action>\nwait for animation\n</follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 1, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 13, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 202, "animationId": 2, "damage": {"critical": true, "elementId": 6, "formula": "a.atk", "type": 1, "variance": 20}, "description": "Attack a single foe \\c[27]\\bpRepeat[1]\\c[0] time\\bp[s] with a staff.\nUser gains an additional action for next turn.", "effects": [], "hitType": 1, "iconIndex": 101, "message1": " attacks!", "message2": "", "mpCost": 15, "name": "Added Hit", "note": "<Cast Animation: 55>\n\n<BP Repeat>\n<Switch to Weapon: Staff>\n<OTB User Add Next Turn Actions: 1>\n\n<whole action>\nperform start\nmotion standby: user\nwait for movement\nface user: target\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\nattack animation: target\nwait: 10\naction animation: target\naction effect\nwait: 10\n</target action>\n\n<follow action>\nwait for animation\n</follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 6, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 13, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 203, "animationId": 106, "damage": {"critical": false, "elementId": 0, "formula": "(b.isActor() || !b.enemy().note.match(/<Boss>/i)) ? (b.hp * [0.25,0.33,0.5,0.66][a.bp]) : (a.mat * 4 * (a.bp + 1))", "type": 1, "variance": 0}, "description": "Reduce a single foe's current HP by \\c[27]\\bp=0[25]\\bp=1[33]\\bp=2[50]\\bp=3[66]%\\c[0].\nIf the foe is a boss, deal non-elemental damage instead. \\c[27]\\bpdamage[400]%\\c[0] Multiplier.", "effects": [], "hitType": 0, "iconIndex": 161, "message1": "", "message2": "", "mpCost": 15, "name": "Gravity", "note": "<Cast Animation: 55>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 13, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 204, "animationId": 51, "damage": {"critical": false, "elementId": 0, "formula": "b.mmp * 0.25", "type": 4, "variance": 0}, "description": "Restore \\c[27]\\bpDamage[25]%\\c[0] MP to a single ally and grant one\nadditional action for the current turn. \\}(cannot target self)\\{.", "effects": [], "hitType": 0, "iconIndex": 160, "message1": "", "message2": "", "mpCost": 15, "name": "Entrust Now", "note": "<Cast Animation: 55>\n<Bypass Target Change>\n<BP Damage>\n<OTB Target Add Current Turn Actions: 1>\n\n<Select Conditions>\nNot User\n</Select Conditions>\n\n<whole action>\nmotion spell: user\n</whole action>\n\n<target action>\naction animation: target\nwait: 10\naction effect\nImmortal: target, false\nwait for animation\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 13, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 205, "animationId": 51, "damage": {"critical": false, "elementId": 0, "formula": "b.mmp * 0.25", "type": 4, "variance": 20}, "description": "Restore \\c[27]\\bpDamage[25]%\\c[0] MP to a single ally and grant two \nadditional actions for the next turn. \\}(cannot target self)\\{.", "effects": [], "hitType": 0, "iconIndex": 165, "message1": "", "message2": "", "mpCost": 35, "name": "Entrust Later", "note": "<Cast Animation: 55>\n<Bypass Target Change>\n<BP Damage>\n<OTB Target Add Next Turn Actions: 2>\n\n<Select Conditions>\nNot User\n</Select Conditions>\n\n<whole action>\nmotion spell: user\n</whole action>\n\n<target action>\naction animation: target\nwait: 10\naction effect\nImmortal: target, false\nwait for animation\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 13, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 206, "animationId": 52, "damage": {"critical": false, "elementId": 0, "formula": "a.mdf * 2", "type": 0, "variance": 20}, "description": "Selected ally gains additional actions for \\c[27]\\bpturn[2]\\c[0] turns.", "effects": [{"code": 21, "dataId": 45, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 73, "message1": "", "message2": "", "mpCost": 35, "name": "<PERSON><PERSON>", "note": "<Cast Animation: 55>\n<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 13, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 207, "animationId": 106, "damage": {"critical": false, "elementId": 0, "formula": "a.mat * (4 + (BattleManager._nextTurnActionBattlers.indexOf(b) + 1) * 2)", "type": 1, "variance": 0}, "description": "Inflict heavy non-elemental damage to a foe. The further back the foe's\nposition is next turn, the more damage the foe receives. \\c[27]\\bpdamage[400]%\\c[0] Multiplier.", "effects": [], "hitType": 0, "iconIndex": 167, "message1": "", "message2": "", "mpCost": 35, "name": "Pressurize", "note": "<Cast Animation: 55>\n<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 13, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 208, "animationId": 49, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "\\c[27]Divine Skill:\\c[0] User gains 2 additional actions for the current turn.", "effects": [], "hitType": 2, "iconIndex": 111, "message1": "", "message2": "", "mpCost": 50, "name": "Overrealm", "note": "<Divine>\n<Cast Animation: 55>\n<Require 3 BP>\n<OTB Target Add Current Turn Actions: 2>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 13, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 209, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 210, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "<Trickster>", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 14, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 211, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "Jump into the air for 2 turns before attacking a single foe with a spear\nwith a critical hit. \\c[27]\\bpdamage[400]%\\c[0] Multiplier.", "effects": [], "hitType": 0, "iconIndex": 107, "message1": "", "message2": "", "mpCost": 15, "name": "Jump", "note": "// Action Sequences\n// Setup: Jump animation.\n<Setup Action>\ndisplay action\ncamera focus: user\nzoom: 150%\nperform start\nwait: 60\nse: Wind1, 80, 150\nfloat user: 495%, 20\nopacity user: 0%, 20\nwait for float\n</Setup Action>\n\n// Whole: Ensure nothing happens.\n<Whole Action>\n</Whole Action>\n\n// Target: Flag the target.\n<Target Action>\naction effect\n</Target Action>\n\n// Follow: Apply Jump state to user.\n<Follow Action>\nadd state 46: user\n</Follow Action>\n\n// Finish: Without landing.\n<Finish Action>\nclear battle log\nreset camera\nreset zoom\nwait for camera\nwait for zoom\n</Finish Action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 2, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 14, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 212, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "User's basic attacks are infused with a random element\n(Fire, Ice, Lightning, Wind, Light, or Dark) for \\c[27]\\bpturn[3]\\c[0] turns.", "effects": [], "hitType": 2, "iconIndex": 76, "message1": "", "message2": "", "mpCost": 15, "name": "Infusion", "note": "<Boost Turns>\n\n<After Eval>\nvar index = Math.randomInt(6);\nvar stateId = 47 + index;\nvar animationId = [67, 71, 76, 92, 97, 101][index];\ntarget.addState(stateId);\ntarget.startAnimation(animationId);\n</After Eval>\n\n<Follow Action>\nWait for Animation\n</Follow Action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 11, "speed": 0, "stypeId": 14, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 213, "animationId": 52, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "For \\c[27]\\bpturn[3]\\c[0] turns, selected ally stops regenerating BP, but critical hits will cause \nselected ally gain 2 BP each time. <PERSON>'s critical hit rate is increased by +50%.", "effects": [{"code": 21, "dataId": 53, "value1": 1, "value2": 0}], "hitType": 2, "iconIndex": 160, "message1": "", "message2": "", "mpCost": 15, "name": "Critical Booster", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 14, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 214, "animationId": 52, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "For \\c[27]\\bpturn[3]\\c[0] turns, selected ally stops regenerating BP, but hitting an enemy's\nweakness will cause selected ally gain 2 BP each time.", "effects": [{"code": 21, "dataId": 54, "value1": 1, "value2": 0}], "hitType": 2, "iconIndex": 166, "message1": "", "message2": "", "mpCost": 15, "name": "Weakness Booster", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 14, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 215, "animationId": 52, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "For \\c[27]\\bpturn[3]\\c[0] turns, selected ally will gain an additional action upon landing a critical hit on\na foe (once per turn per foe this way). <PERSON>'s critical hit rate is increased by +50%.", "effects": [{"code": 21, "dataId": 56, "value1": 1, "value2": 0}], "hitType": 2, "iconIndex": 163, "message1": "", "message2": "", "mpCost": 35, "name": "Critical Exploiter", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 14, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 216, "animationId": 52, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "For \\c[27]\\bpturn[3]\\c[0] turns, selected ally will gain an additional action upon hitting a foe's weakness\n(once per turn per foe this way).", "effects": [{"code": 21, "dataId": 58, "value1": 1, "value2": 0}], "hitType": 2, "iconIndex": 169, "message1": "", "message2": "", "mpCost": 35, "name": "Weakness Exploiter", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 14, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 217, "animationId": 52, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "For \\c[27]\\bpturn[3]\\c[0] turns, damage dealt by selected ally inflict significantly more damage based\non how much he or she Boosts.", "effects": [{"code": 21, "dataId": 59, "value1": 1, "value2": 0}], "hitType": 2, "iconIndex": 162, "message1": "", "message2": "", "mpCost": 35, "name": "BP Eater", "note": "<Boost Turns>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 14, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 218, "animationId": 65, "damage": {"critical": false, "elementId": 0, "formula": "(b.isActor() || !b.enemy().note.match(/<Boss>/i)) ? (b.mhp) : (a.mat * 4 * (a.bp + 1))", "type": 1, "variance": 20}, "description": "\\c[27]Divine Skill:\\c[0] Inflict fatal damage to all non-boss foes in Break.\nInflict non-elemental damage to bosses in Break.", "effects": [], "hitType": 0, "iconIndex": 31, "message1": "", "message2": "", "mpCost": 50, "name": "Break Death", "note": "<Divine>\n<Require 3 BP>\n<Bypass Damage Cap>\n\n<Custom Select Condition>\ncondition = target.isStateAffected(4);\n</Custom Select Condition>\n\n<Custom Target Eval>\nvar members = foes.aliveMembers();\nfor (var i = 0; i < members.length; i++) {\n  var member = members[i];\n  if (!!member && member.isStateAffected(4)) {\n    targets.push(member);\n  }\n}\n</Custom Target Eval>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: targets\nwait for animation\naction effect\nImmortal: targets, false\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 14, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 219, "animationId": 0, "damage": {"critical": false, "elementId": 2, "formula": "a.atk * 4", "type": 1, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 107, "message1": "", "message2": "", "mpCost": 0, "name": "Land", "note": "<Boost Damage>\n<Critical Rate: 100%>\n<Switch to Weapon: Spear>\n\n// Action Sequence\n// Setup: Setup landing spot.\n<Setup Action>\nclear battle log\ndisplay action\nimmortal: targets, true\nmove user: target, base, 1\n</Setup Action>\n\n// Whole: Land and deal damage to all targets.\n<Whole Action>\ncamera focus: target\nzoom: 150%\nwait: 60\nmotion attack: user\nopacity user: 100%, 1\nfloat user: 0, 20\nwait: 10\nanimation 2: targets\naction effect\nshake screen: 5, 5, 5\nwait for animation\n</Whole Action>\n\n// Target: Ensure nothing happens.\n<Target Action>\n</Target Action>\n\n// Follow: Reset everything.\n<Follow Action>\nreset camera\nreset zoom\njump user: 200%, 30\nmove user: return, 30\nwait for movement\n</Follow Action>", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 0, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 220, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 221, "animationId": 91, "damage": {"critical": false, "elementId": 7, "formula": "a.atk * 3 * (a.isStateAffected(41) ? 2 : 1)", "type": 1, "variance": 20}, "description": "【单体敌人】\n给予敌人风属性伤害", "effects": [], "hitType": 1, "iconIndex": 69, "message1": "施放了%1！", "message2": "", "mpCost": 0, "name": "尘卷风", "note": "<setup action>\ndisplay action\nimmortal: target, true\nperform start\n</setup action>\n\n<target action>\nmotion attack: user\nwait: 12\naction animation\nwait for animation\naction effect\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 2, "successRate": 100, "tpCost": 0, "tpGain": 5}, {"id": 222, "animationId": 94, "damage": {"critical": false, "elementId": 7, "formula": "a.atk * 3 * (a.isStateAffected(41) ? 2 : 1)", "type": 1, "variance": 20}, "description": "【全体敌人】\n给予敌人风属性伤害", "effects": [], "hitType": 1, "iconIndex": 69, "message1": "施放了%1！", "message2": "", "mpCost": 0, "name": "龙卷风", "note": "<setup action>\ndisplay action\nimmortal: target, true\nperform start\n</setup action>\n\n<whole action>\nmotion skill: user\nwait: 12\naction animation\nwait for animation\naction effect\n</whole action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 2, "successRate": 100, "tpCost": 0, "tpGain": 2}, {"id": 223, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 224, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 225, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 226, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 227, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 228, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 229, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 230, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 231, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 232, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 233, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 234, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 235, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 236, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 237, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 238, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 239, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 240, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 241, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 242, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 243, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 244, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 245, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 246, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 247, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 248, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 249, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 250, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "桐谷和人技能", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 251, "animationId": 12, "damage": {"critical": true, "elementId": 1, "formula": "a.atk * 32", "type": 1, "variance": 20}, "description": "单手直剑基本技、上段突进技。快速积攒TP。", "effects": [], "hitType": 1, "iconIndex": 97, "message1": "", "message2": "", "mpCost": 0, "name": "音速冲击", "note": "", "occasion": 1, "repeats": 1, "requiredWtypeId1": 1, "requiredWtypeId2": 0, "scope": 1, "speed": 100, "stypeId": 1, "successRate": 100, "tpCost": 5, "tpGain": 50}, {"id": 252, "animationId": 175, "damage": {"critical": true, "elementId": 1, "formula": "a.atk * 16", "type": 1, "variance": 20}, "description": "水平二连击技", "effects": [], "hitType": 1, "iconIndex": 97, "message1": "", "message2": "", "mpCost": 0, "name": "魔剑侵袭", "note": "<Boost Damage>\n<Switch to Weapon: Sword>\n\n<whole action>\nmotion attack: user\nwait: 10\naction animation: target\nwait: 2\naction effect\nwait: 2\naction effect\nImmortal: target, false\nwait for animation\n</whole action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 1, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 10, "tpGain": 0}, {"id": 253, "animationId": 0, "damage": {"critical": true, "elementId": 1, "formula": "a.atk * 10", "type": 1, "variance": 20}, "description": "垂直方向四连击", "effects": [], "hitType": 1, "iconIndex": 97, "message1": "", "message2": "", "mpCost": 0, "name": "垂直四方斩", "note": "<Boost Damage>\n<Switch to Weapon: Sword>\n\n<Cooldown: 0>\n\n<whole action>\nperform start\nmotion standby: user\nwait for movement\nface user: targets\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\nanimation 174: target\nwait: 3\naction effect\nwait: 6\naction effect\nwait: 6\naction effect\nwait: 6\naction effect\nImmortal: targets, false\nwait: 10\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 1, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 25, "tpGain": 0}, {"id": 254, "animationId": 0, "damage": {"critical": true, "elementId": 1, "formula": "a.atk * 4", "type": 1, "variance": 20}, "description": "十六连击二刀流上位剑技", "effects": [], "hitType": 0, "iconIndex": 97, "message1": "", "message2": "", "mpCost": 0, "name": "天外飞仙", "note": "<Action Cutin>\n\n<Boost Damage>\n<Switch to Weapon: Spear>\n\n<Cooldown: 0>\n\n<Custom Target Eval>\nvar times = 8;\nvar members = foes.aliveMembers();\nwhile (times--) {\n    var member = members[Math.floor(Math.random() * members.length)];\n    targets.push(member);\n}\n</Custom Target Eval>\n\n<target action>\nmotion attack: user\nwait: 10\nanimation 173: target\nwait: 2\naction effect\nwait: 2\naction effect\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 1, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 35, "tpGain": 0}, {"id": 255, "animationId": 0, "damage": {"critical": true, "elementId": 1, "formula": "a.atk* 5", "type": 1, "variance": 20}, "description": "十六连击二刀流上位剑技强化版", "effects": [], "hitType": 0, "iconIndex": 97, "message1": "", "message2": "", "mpCost": 0, "name": "星爆气流斩", "note": "<Action Cutin>\n\n<Boost Damage>\n<Switch to Weapon: Spear>\n\n<Cooldown: 0>\n\n<Custom Target Eval>\nvar times = 8;\nvar members = foes.aliveMembers();\nwhile (times--) {\n    var member = members[Math.floor(Math.random() * members.length)];\n    targets.push(member);\n}\n</Custom Target Eval>\n\n<target action>\nmotion attack: user\nwait: 10\nanimation 173: target\nwait: 2\naction effect\nwait: 2\naction effect\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 1, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 50, "tpGain": 0}, {"id": 256, "animationId": 0, "damage": {"critical": true, "elementId": 1, "formula": "a.atk* 5", "type": 1, "variance": 20}, "description": "二十七连击二刀流最上位剑技", "effects": [], "hitType": 0, "iconIndex": 97, "message1": "", "message2": "", "mpCost": 0, "name": "日蚀", "note": "<Action Cutin>\n\n<Boost Damage>\n<Switch to Weapon: Spear>\n\n<Cooldown: 1>\n\n<Custom Target Eval>\nvar times = 9;\nvar members = foes.aliveMembers();\nwhile (times--) {\n    var member = members[Math.floor(Math.random() * members.length)];\n    targets.push(member);\n}\n</Custom Target Eval>\n\n<target action>\nmotion attack: user\nwait: 10\nanimation 172: target\nwait: 3\naction effect\nwait: 2\naction effect\nwait: 3\naction effect\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 1, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 75, "tpGain": 0}, {"id": 257, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "对亚丝娜提出援助需求!", "effects": [{"code": 44, "dataId": 14, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 800, "message1": "", "message2": "", "mpCost": 0, "name": "请求亚丝娜的援助", "note": "<Cooldown: 3>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 0, "speed": 100, "stypeId": 2, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 258, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 259, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 260, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 261, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 262, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "亚丝娜技能", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 263, "animationId": 71, "damage": {"critical": false, "elementId": 8, "formula": "a.mdf * 2", "type": 1, "variance": 20}, "description": "Deal ice damage to all foes.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 65, "message1": "", "message2": "", "mpCost": 15, "name": "小型水魔法", "note": "<Boost Damage>\n\n<whole action>\nmotion spell: user\nwait: 10\naction animation: target\nwait: 10\naction effect\nImmortal: target, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>\n\n", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 264, "animationId": 4, "damage": {"critical": false, "elementId": 8, "formula": "a.mdf * 1.5", "type": 1, "variance": 20}, "description": "Deal ice damage to all foes twice.\n\\c[27]\\bpdamage[200]%\\c[0] Multiplier.", "effects": [], "hitType": 2, "iconIndex": 65, "message1": "", "message2": "", "mpCost": 30, "name": "大型水系魔法", "note": "<Boost Damage>\n\n<Cooldown: 3>\n\n<whole action>\nmotion spell: user\naction animation: targets\nwait: 10\naction effect\nanimation 72: targets\nwait: 20\naction effect\nImmortal: targets, false\nwait for animation\n</whole action>\n\n<target action>\n</target action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 265, "animationId": 41, "damage": {"critical": false, "elementId": 0, "formula": "a.mdf * 4", "type": 3, "variance": 20}, "description": "大幅度恢复全体队友的HP。\n\\c[27]\\bpdamage[400]%\\c[0] Multiplier.", "effects": [], "hitType": 0, "iconIndex": 72, "message1": "", "message2": "", "mpCost": 30, "name": "圣洁治愈", "note": "\n\n<Cooldown: 3>\n\n", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 266, "animationId": 51, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 31, "dataId": 2, "value1": 2, "value2": 0}, {"code": 31, "dataId": 3, "value1": 2, "value2": 0}, {"code": 31, "dataId": 4, "value1": 2, "value2": 0}, {"code": 31, "dataId": 5, "value1": 2, "value2": 0}, {"code": 31, "dataId": 6, "value1": 2, "value2": 0}, {"code": 31, "dataId": 7, "value1": 2, "value2": 0}], "hitType": 0, "iconIndex": 80, "message1": "", "message2": "", "mpCost": 30, "name": "群体强化", "note": "\n\n<Cooldown: 5>\n\n", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 8, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 267, "animationId": 51, "damage": {"critical": false, "elementId": 0, "formula": "a.mdf * 2", "type": 0, "variance": 20}, "description": "在2回合内，使一名队友释放技能时能追加一次该技能。 \\}(Except divine skills)\\{", "effects": [{"code": 21, "dataId": 22, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 109, "message1": "", "message2": "", "mpCost": 50, "name": "神之力量", "note": "<Cooldown: 7>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 7, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 268, "animationId": 26, "damage": {"critical": true, "elementId": 1, "formula": "a.atk* 2", "type": 1, "variance": 20}, "description": "最强的十一连击原创剑技", "effects": [], "hitType": 0, "iconIndex": 97, "message1": "", "message2": "", "mpCost": 0, "name": "圣母圣咏", "note": "<Boost Damage>\n<Switch to Weapon: Spear>\n\n<Custom Target Eval>\nvar times = 5 + Math.randomInt(4);\nvar members = foes.aliveMembers();\nwhile (times--) {\n    var member = members[Math.floor(Math.random() * members.length)];\n    targets.push(member);\n}\n</Custom Target Eval>\n\n<whole action>\nperform start\nmotion standby: user\nwait for movement\nface user: targets\n</whole action>\n\n<target action>\nmotion attack: user\nwait: 10\nanimation 22: target\nattack animation: target\nwait: 10\naction effect\n</target action>\n\n<follow action>\nImmortal: targets, false\nwait for animation\n<follow action>", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 100, "tpGain": 0}, {"id": 269, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 270, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 271, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 272, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 273, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "怪物技能", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 274, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "被痛苦的噩梦折磨!", "effects": [{"code": 44, "dataId": 32, "value1": 1, "value2": 0}], "hitType": 0, "iconIndex": 26, "message1": "", "message2": "", "mpCost": 0, "name": "噩梦", "note": "", "occasion": 1, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 0, "speed": 0, "stypeId": 3, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 275, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [{"code": 32, "dataId": 3, "value1": 10, "value2": 0}], "hitType": 0, "iconIndex": 59, "message1": "", "message2": "", "mpCost": 50, "name": "装甲灭破", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 2, "speed": 0, "stypeId": 4, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 276, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 277, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 278, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 279, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 280, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 281, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 282, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 283, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 284, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 285, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 286, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 287, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 288, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 289, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 290, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 291, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 292, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 293, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 294, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 295, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 296, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 297, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 298, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 299, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}, {"id": 300, "animationId": 0, "damage": {"critical": false, "elementId": 0, "formula": "0", "type": 0, "variance": 20}, "description": "", "effects": [], "hitType": 0, "iconIndex": 0, "message1": "", "message2": "", "mpCost": 0, "name": "", "note": "", "occasion": 0, "repeats": 1, "requiredWtypeId1": 0, "requiredWtypeId2": 0, "scope": 1, "speed": 0, "stypeId": 1, "successRate": 100, "tpCost": 0, "tpGain": 0}]