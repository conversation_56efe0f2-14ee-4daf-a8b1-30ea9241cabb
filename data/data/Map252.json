{"autoplayBgm": false, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 25, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "!公寓房间-白天", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 8, "width": 30, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0, 5, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 0, 5, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 324}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 329}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 111, "indent": 0, "parameters": [0, 77, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 298, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 121, "indent": 0, "parameters": [298, 298, 1]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 16, "y": 2}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 2, "characterIndex": 5}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 11, "y": 9}, {"id": 3, "name": "EV003桐人裸", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 122}, "directionFix": false, "image": {"tileId": 0, "characterName": "桐人裸", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true}], "x": 28, "y": 23}, {"id": 4, "name": "EV004亚丝娜", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 44, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "亚丝娜私服像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 44, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 484}, "directionFix": false, "image": {"tileId": 0, "characterName": "亚丝娜私服像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 121, "indent": 0, "parameters": [71, 71, 0]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 526, 0]}, {"code": 355, "indent": 1, "parameters": ["$gamePlayer.followers().follower(0).turnTowardCharacter($gameMap.event(this.eventId()));"]}, {"code": 122, "indent": 1, "parameters": [123, 123, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 1, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 1, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 1, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 1, "parameters": ["} else {"]}, {"code": 655, "indent": 1, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 1, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 1, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 111, "indent": 1, "parameters": [0, 355, 1]}, {"code": 213, "indent": 2, "parameters": [-1, 5, true]}, {"code": 122, "indent": 2, "parameters": [133, 133, 0, 0, 2]}, {"code": 355, "indent": 2, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 2, "parameters": [2, "直叶不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 2, "parameters": [168]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210](哥哥还没醒呢，居然有心情做饭？点外卖不行吗？"]}, {"code": 401, "indent": 2, "parameters": ["\\dac果然是心思已经不在哥哥身上了！)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210](这个出轨女，要不是刚刚通过监控看到，我是真不敢相信！"]}, {"code": 401, "indent": 2, "parameters": ["\\dac居然会和那种丑男有不清不楚的关系……)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210](也不知道有多久了，只能说伪装的真好……)"]}, {"code": 235, "indent": 2, "parameters": [2]}, {"code": 231, "indent": 2, "parameters": [2, "直叶凛然", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 2, "parameters": [168]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210](好得让我恶心！)"]}, {"code": 235, "indent": 2, "parameters": [2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 213, "indent": 2, "parameters": [-1, 5, true]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 486, 4]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 484, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac亚丝娜……"]}, {"code": 356, "indent": 2, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 118, "indent": 2, "parameters": ["开头"]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["if (Array.from({ length: 10 }, (_, i) => i + 1).every(num => array122.includes(num))) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(356, true); // 同时存在则开启开关356"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(356, false); // 不同时存在则关闭开关356"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 356, 1]}, {"code": 102, "indent": 3, "parameters": [["接吻", "聊天", "做爱", "取消"], 1, -1, 1, 1]}, {"code": 402, "indent": 3, "parameters": [0, "接吻"]}, {"code": 122, "indent": 4, "parameters": [123, 123, 0, 0, 1]}, {"code": 355, "indent": 4, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 4, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 4, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 4, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 4, "parameters": ["} else {"]}, {"code": 655, "indent": 4, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 4, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 4, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 4, "parameters": ["}"]}, {"code": 111, "indent": 4, "parameters": [0, 355, 1]}, {"code": 213, "indent": 5, "parameters": [-1, 11, true]}, {"code": 101, "indent": 5, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 5, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 5, "parameters": ["\\dac亚丝娜，那个…嗯……我能亲你吗……？"]}, {"code": 213, "indent": 5, "parameters": [4, 6, false]}, {"code": 122, "indent": 5, "parameters": [95, 95, 0, 0, 2]}, {"code": 355, "indent": 5, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 5, "parameters": [2, "明日奈苦笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 5, "parameters": [135]}, {"code": 232, "indent": 5, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 5, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 5, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 5, "parameters": ["\\dac傻瓜，这种事情不要问出口呀~ "]}, {"code": 235, "indent": 5, "parameters": [2]}, {"code": 213, "indent": 5, "parameters": [4, 4, true]}, {"code": 205, "indent": 5, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 5, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}]}, {"code": 505, "indent": 5, "parameters": [{"code": 33, "indent": null}]}, {"code": 111, "indent": 5, "parameters": [6, 4, 8]}, {"code": 356, "indent": 6, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[0,-24] : 时间[30]"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 111, "indent": 5, "parameters": [6, 4, 4]}, {"code": 356, "indent": 6, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[-24,0] : 时间[30]"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 111, "indent": 5, "parameters": [6, 4, 6]}, {"code": 356, "indent": 6, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[24,0] : 时间[30]"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 117, "indent": 5, "parameters": [122]}, {"code": 122, "indent": 5, "parameters": [39, 39, 1, 0, 1]}, {"code": 122, "indent": 5, "parameters": [15, 15, 1, 0, 1]}, {"code": 231, "indent": 5, "parameters": [2, "明日奈接吻", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 5, "parameters": [135]}, {"code": 232, "indent": 5, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 5, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 5, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 5, "parameters": ["\\dac啾~\\i[90]"]}, {"code": 235, "indent": 5, "parameters": [2]}, {"code": 213, "indent": 5, "parameters": [-1, 4, false]}, {"code": 230, "indent": 5, "parameters": [30]}, {"code": 356, "indent": 5, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[0,0] : 时间[30]"]}, {"code": 230, "indent": 5, "parameters": [30]}, {"code": 205, "indent": 5, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 5, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}]}, {"code": 505, "indent": 5, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 5, "parameters": [4, 11, true]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 411, "indent": 4, "parameters": []}, {"code": 101, "indent": 5, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 5, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 5, "parameters": ["\\dac\\c[210](刚刚才亲过的，再提这个要求，不太好意思啊……)"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 412, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [1, "聊天"]}, {"code": 118, "indent": 4, "parameters": ["对话开头"]}, {"code": 102, "indent": 4, "parameters": [["谈论朋友", "谈论FOG", "谈论最近的事情", "返回"], 1, -1, 1, 1]}, {"code": 402, "indent": 4, "parameters": [0, "谈论朋友"]}, {"code": 118, "indent": 5, "parameters": ["对话开头-朋友"]}, {"code": 102, "indent": 5, "parameters": [["莉兹", "诗乃", "猪田", "返回"], 1, -1, 1, 1]}, {"code": 402, "indent": 5, "parameters": [0, "莉兹"]}, {"code": 122, "indent": 6, "parameters": [123, 123, 0, 0, 2]}, {"code": 355, "indent": 6, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 6, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 6, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 6, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 6, "parameters": ["} else {"]}, {"code": 655, "indent": 6, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 6, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 6, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 6, "parameters": ["}"]}, {"code": 111, "indent": 6, "parameters": [0, 355, 1]}, {"code": 213, "indent": 7, "parameters": [-1, 7, false]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac唉，我现在已经完全不知道怎么和莉兹相处了。"]}, {"code": 401, "indent": 7, "parameters": ["\\dac感觉自从她转学过来，性格变得越来越极端，情绪也很不稳定。"]}, {"code": 213, "indent": 7, "parameters": [4, 6, false]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈叹气", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac感觉莉兹单独和我在一起的时候挺正常的，只是在和你……"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 213, "indent": 7, "parameters": [4, 9, true]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈担忧", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac不会吧……"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 213, "indent": 7, "parameters": [-1, 2, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac怎么了？"]}, {"code": 213, "indent": 7, "parameters": [4, 7, false]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈妥协", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac没，没什么。以后有空我会和莉兹好好聊一下的。"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 118, "indent": 7, "parameters": ["重复对话"]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac\\c[210](已经聊过这个话题了……)"]}, {"code": 119, "indent": 7, "parameters": ["对话开头"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 119, "indent": 6, "parameters": ["对话开头-朋友"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 402, "indent": 5, "parameters": [1, "诗乃"]}, {"code": 122, "indent": 6, "parameters": [123, 123, 0, 0, 3]}, {"code": 355, "indent": 6, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 6, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 6, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 6, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 6, "parameters": ["} else {"]}, {"code": 655, "indent": 6, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 6, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 6, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 6, "parameters": ["}"]}, {"code": 111, "indent": 6, "parameters": [0, 355, 1]}, {"code": 213, "indent": 7, "parameters": [-1, 2, false]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac感觉最近你和朝田诗乃经常发信息聊天来着，"]}, {"code": 401, "indent": 7, "parameters": ["\\dac你们有那么多话题说吗？"]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈理解", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac都是些很正常的女孩子间的话题。因为是刚交的朋友，"]}, {"code": 401, "indent": 7, "parameters": ["\\dac很多事情都从来没聊过，所以一开始的交流会稍微密集一点。"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 213, "indent": 7, "parameters": [-1, 8, true]}, {"code": 230, "indent": 7, "parameters": [15]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac这样吗？她有没有聊过什么奇怪的话题……？"]}, {"code": 213, "indent": 7, "parameters": [4, 2, false]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈失望", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac奇怪的话题？没有吧……"]}, {"code": 401, "indent": 7, "parameters": ["\\dac桐人君，我感觉你对诗乃好像有点戒备的样子。"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac也没有啦……可能，只是还不太熟悉吧。"]}, {"code": 213, "indent": 7, "parameters": [4, 3, false]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac她在FOG中的战力也很高哦，以后有空了安排你们切磋一下，"]}, {"code": 401, "indent": 7, "parameters": ["\\dac通过战斗也许你们能熟络起来也不一定~"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 213, "indent": 7, "parameters": [-1, 6, false]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac哈…哈…哈……"]}, {"code": 401, "indent": 7, "parameters": ["\\dac可以一试。"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 119, "indent": 7, "parameters": ["重复对话"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 119, "indent": 6, "parameters": ["对话开头-朋友"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 402, "indent": 5, "parameters": [2, "猪田"]}, {"code": 122, "indent": 6, "parameters": [123, 123, 0, 0, 4]}, {"code": 355, "indent": 6, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 6, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 6, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 6, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 6, "parameters": ["} else {"]}, {"code": 655, "indent": 6, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 6, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 6, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 6, "parameters": ["}"]}, {"code": 111, "indent": 6, "parameters": [0, 355, 1]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac感觉最近都没怎么见到猪田……"]}, {"code": 213, "indent": 7, "parameters": [4, 11, false]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac他可能有他的事吧。"]}, {"code": 401, "indent": 7, "parameters": ["\\dac桐人君，明天想吃牛排还是汉堡肉？"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 213, "indent": 7, "parameters": [-1, 3, false]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac感觉都挺不错诶！"]}, {"code": 401, "indent": 7, "parameters": ["\\dac我想想……汉堡肉吧~ 好久没吃了。"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 119, "indent": 7, "parameters": ["重复对话"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 119, "indent": 6, "parameters": ["对话开头-朋友"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 402, "indent": 5, "parameters": [3, "返回"]}, {"code": 119, "indent": 6, "parameters": ["对话开头"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 404, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 402, "indent": 4, "parameters": [1, "谈论FOG"]}, {"code": 118, "indent": 5, "parameters": ["对话开头-FOG"]}, {"code": 102, "indent": 5, "parameters": [["开服延迟", "更新内容", "游戏性爱", "返回"], 1, -1, 1, 1]}, {"code": 402, "indent": 5, "parameters": [0, "开服延迟"]}, {"code": 122, "indent": 6, "parameters": [123, 123, 0, 0, 5]}, {"code": 355, "indent": 6, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 6, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 6, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 6, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 6, "parameters": ["} else {"]}, {"code": 655, "indent": 6, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 6, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 6, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 6, "parameters": ["}"]}, {"code": 111, "indent": 6, "parameters": [0, 355, 1]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac这次更新延迟真是离谱，居然延误了4天。"]}, {"code": 401, "indent": 7, "parameters": ["\\dac已经是预定更新时间的两倍了。"]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac你说会不会和新铺设的设备有关？"]}, {"code": 213, "indent": 7, "parameters": [4, 11, false]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac有可能。但目前新设备的报告里没提延期的事，"]}, {"code": 401, "indent": 7, "parameters": ["\\dac而关于延期原因的报告也还没有出来，可能周一才有结论。"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac果然我们公司的情报不太行啊，"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 119, "indent": 7, "parameters": ["重复对话"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 119, "indent": 6, "parameters": ["对话开头-FOG"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 402, "indent": 5, "parameters": [1, "更新内容"]}, {"code": 122, "indent": 6, "parameters": [123, 123, 0, 0, 6]}, {"code": 355, "indent": 6, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 6, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 6, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 6, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 6, "parameters": ["} else {"]}, {"code": 655, "indent": 6, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 6, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 6, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 6, "parameters": ["}"]}, {"code": 111, "indent": 6, "parameters": [0, 355, 1]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac根据我们市场部周五刚出的调研结果，从RATH前导宣传用词基本可以确定，"]}, {"code": 401, "indent": 7, "parameters": ["\\dac FOG将允许玩家导入自己编写的人工智能程式了。"]}, {"code": 213, "indent": 7, "parameters": [4, 9, true]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac说到人工智能……"]}, {"code": 401, "indent": 7, "parameters": ["\\dac桐人君，你和我想的一样吗？"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac结衣，对吧？"]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈沉思", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac嗯，你说有没有可能……"]}, {"code": 401, "indent": 7, "parameters": ["\\dac把她从你的旧款的NERvGear内存导入FOG呢？"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 213, "indent": 7, "parameters": [-1, 3, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac嘿嘿，其实我早就计划好了！"]}, {"code": 401, "indent": 7, "parameters": ["\\dac只要RATH没有虚假宣传，我有九成以上的把握。"]}, {"code": 213, "indent": 7, "parameters": [4, 1, false]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈激动", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac真的？！那真是太好了！"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac ALO时期我就有过类似的打算，可惜被须乡事件打乱了计划。"]}, {"code": 401, "indent": 7, "parameters": ["\\dac后来RECT加强风控封闭了系统，就彻底不行了。"]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac难得又有了机会我一定会全力去把握的。"]}, {"code": 213, "indent": 7, "parameters": [4, 4, false]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈感动", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac想到要能再看到结衣，啊，感觉眼泪都要出来了~"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac还有一个小时就开服，这次应该不会再继续推迟了。"]}, {"code": 401, "indent": 7, "parameters": ["\\dac上线以后的第一件事，我就去教堂开启生育子女的任务~"]}, {"code": 213, "indent": 7, "parameters": [4, 3, true]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 119, "indent": 7, "parameters": ["重复对话"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 119, "indent": 6, "parameters": ["对话开头-FOG"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 402, "indent": 5, "parameters": [2, "游戏性爱"]}, {"code": 122, "indent": 6, "parameters": [123, 123, 0, 0, 7]}, {"code": 355, "indent": 6, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 6, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 6, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 6, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 6, "parameters": ["} else {"]}, {"code": 655, "indent": 6, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 6, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 6, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 6, "parameters": ["}"]}, {"code": 111, "indent": 6, "parameters": [0, 355, 1]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac亚丝娜，今天我们在现实里……做了以后，我们在游戏里也……"]}, {"code": 213, "indent": 7, "parameters": [4, 1, true]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈微笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac嗯，好啊……"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 213, "indent": 7, "parameters": [4, 8, true]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈苦笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac可是，桐人君你不会累吗？"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 213, "indent": 7, "parameters": [-1, 3, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac我在调研RATH情报的时候，听说了一个传闻，"]}, {"code": 401, "indent": 7, "parameters": ["\\dac这次更新FOG还会上线性爱辅助系统，体力增幅就是基本的功能。"]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac从技术研究的角度，想要试一试。"]}, {"code": 213, "indent": 7, "parameters": [4, 6, false]}, {"code": 205, "indent": 7, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 7, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac原来只是为了关心游戏技术啊……"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 205, "indent": 7, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 7, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}]}, {"code": 213, "indent": 7, "parameters": [-1, 12, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac不是！！！想和亚丝娜做爱的心情也是千真万确的！"]}, {"code": 401, "indent": 7, "parameters": ["\\dac但我怕你觉得我太好色了……"]}, {"code": 213, "indent": 7, "parameters": [4, 4, true]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈微笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac呵呵呵，好啦好啦，我知道的。其实，桐人君……"]}, {"code": 401, "indent": 7, "parameters": ["\\dac你更色一点，我也很喜欢~"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 213, "indent": 7, "parameters": [-1, 11, true]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 119, "indent": 7, "parameters": ["重复对话"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 119, "indent": 6, "parameters": ["对话开头-FOG"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 402, "indent": 5, "parameters": [3, "返回"]}, {"code": 119, "indent": 6, "parameters": ["对话开头"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 404, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 402, "indent": 4, "parameters": [2, "谈论最近的事情"]}, {"code": 118, "indent": 5, "parameters": ["对话开头-最近"]}, {"code": 102, "indent": 5, "parameters": [["京子转变 ", "关于出国", "结婚计划", "返回"], 1, -1, 1, 1]}, {"code": 402, "indent": 5, "parameters": [0, "京子转变 "]}, {"code": 122, "indent": 6, "parameters": [123, 123, 0, 0, 8]}, {"code": 355, "indent": 6, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 6, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 6, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 6, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 6, "parameters": ["} else {"]}, {"code": 655, "indent": 6, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 6, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 6, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 6, "parameters": ["}"]}, {"code": 111, "indent": 6, "parameters": [0, 355, 1]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac阿姨最近给我的感觉，怎么说呢，好像如沐春风的感觉……"]}, {"code": 401, "indent": 7, "parameters": ["\\dac是不是形容得夸张了一点？"]}, {"code": 213, "indent": 7, "parameters": [4, 3, false]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈得意", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac我觉得还蛮贴切的，妈妈最近确实开朗了好多。"]}, {"code": 401, "indent": 7, "parameters": ["\\dac不仅是对我们交往的态度缓和了，各种方面都是。"]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac比如，以前总是绷着个脸，最近却常挂着笑。"]}, {"code": 401, "indent": 7, "parameters": ["\\dac以前都不怎么跟我交流，最近却常常和我闲聊。"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac还有，我之前不是说虽然妈妈爸爸很相爱，"]}, {"code": 401, "indent": 7, "parameters": ["\\dac但因为沟通不畅所以关系有些紧张吗？"]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac我最近发现妈妈联系爸爸的次数多得离谱，语气也特别温柔，"]}, {"code": 401, "indent": 7, "parameters": ["\\dac简直像热恋的少女……"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 213, "indent": 7, "parameters": [-1, 6, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac呃，你这样形容自己的妈妈不太合适吧~"]}, {"code": 213, "indent": 7, "parameters": [4, 3, true]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈微笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac和你说不要紧啦，不能让妈妈听见倒是真的。"]}, {"code": 401, "indent": 7, "parameters": ["\\dac这也不是坏事，我希望妈妈能一直保持这个状态。"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 213, "indent": 7, "parameters": [-1, 2, false]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac原因呢？总不能是真恋爱了吧。"]}, {"code": 213, "indent": 7, "parameters": [4, 7, false]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac说什么呢！应该……应该不会。"]}, {"code": 401, "indent": 7, "parameters": ["\\dac我猜，可能是换了一个新的心理医生收获了奇效吧~ "]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 213, "indent": 7, "parameters": [-1, 3, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac我也希望如此，这样我们的婚事也就没有阻碍了。"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 119, "indent": 7, "parameters": ["重复对话"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 119, "indent": 6, "parameters": ["对话开头-最近"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 402, "indent": 5, "parameters": [1, "关于出国"]}, {"code": 122, "indent": 6, "parameters": [123, 123, 0, 0, 9]}, {"code": 355, "indent": 6, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 6, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 6, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 6, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 6, "parameters": ["} else {"]}, {"code": 655, "indent": 6, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 6, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 6, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 6, "parameters": ["}"]}, {"code": 111, "indent": 6, "parameters": [0, 355, 1]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac如果一切顺利，年底我们就能去美国了。"]}, {"code": 205, "indent": 7, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 7, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈安心", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac以妈妈现在的态度，说不定，我们不用去美国也是可以的。"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 205, "indent": 7, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 7, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac嗯。但那个研究所的机会我觉得还是很难得，想要争取一下。"]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac我明白。我会全力支持你的。"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 213, "indent": 7, "parameters": [-1, 3, true]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 119, "indent": 7, "parameters": ["重复对话"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 119, "indent": 6, "parameters": ["对话开头-最近"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 402, "indent": 5, "parameters": [2, "结婚计划"]}, {"code": 122, "indent": 6, "parameters": [123, 123, 0, 0, 10]}, {"code": 355, "indent": 6, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 6, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 6, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 6, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 6, "parameters": ["} else {"]}, {"code": 655, "indent": 6, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 6, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 6, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 6, "parameters": ["}"]}, {"code": 111, "indent": 6, "parameters": [0, 355, 1]}, {"code": 213, "indent": 7, "parameters": [-1, 3, false]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac在拉斯维加斯，只要交钱登记就能领到结婚证明。"]}, {"code": 401, "indent": 7, "parameters": ["\\dac我们去了美国，就先去那里结婚吧！"]}, {"code": 213, "indent": 7, "parameters": [4, 1, true]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈回应", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac桐人君，我以前都觉得你是开玩笑的，你原来真的是这么想的啊！"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac嗯，是啊，有什么问题吗？"]}, {"code": 401, "indent": 7, "parameters": ["\\dac你不愿意嫁给我？"]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac当然不是啦！但你可能不明白，女孩子对婚礼都是有特别的憧憬的。"]}, {"code": 401, "indent": 7, "parameters": ["\\dac我希望我们的婚礼浪漫美好，而不是一个简陋的证明书。"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 213, "indent": 7, "parameters": [-1, 1, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac你说的有道理。我之前考虑不周。"]}, {"code": 401, "indent": 7, "parameters": ["\\dac莉兹总是说我直男，真的是说得对。我要深刻反省检讨！"]}, {"code": 213, "indent": 7, "parameters": [4, 6, false]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈岔开", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac也不至于啦！"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 205, "indent": 7, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 7, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac亚丝娜，我保证，一定会给你一个完美的婚礼的！"]}, {"code": 213, "indent": 7, "parameters": [4, 11, true]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈感动", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac桐人君，我相信你。"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 231, "indent": 7, "parameters": [2, "明日奈转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 7, "parameters": [135]}, {"code": 232, "indent": 7, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac不过在婚礼之前，我们可能要先订婚。这是结城家的传统。"]}, {"code": 401, "indent": 7, "parameters": ["\\dac按现在这样一切顺利进展的势头，可能不久爸爸就会安排订婚仪式了。"]}, {"code": 235, "indent": 7, "parameters": [2]}, {"code": 213, "indent": 7, "parameters": [-1, 12, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac哇！哇哦……"]}, {"code": 213, "indent": 7, "parameters": [-1, 3, true]}, {"code": 101, "indent": 7, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 7, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 7, "parameters": ["\\dac说实话，感觉既期待，又有点紧张。嘿嘿嘿~"]}, {"code": 213, "indent": 7, "parameters": [4, 3, true]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 411, "indent": 6, "parameters": []}, {"code": 119, "indent": 7, "parameters": ["重复对话"]}, {"code": 0, "indent": 7, "parameters": []}, {"code": 412, "indent": 6, "parameters": []}, {"code": 119, "indent": 6, "parameters": ["对话开头-最近"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 402, "indent": 5, "parameters": [3, "返回"]}, {"code": 119, "indent": 6, "parameters": ["对话开头"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 404, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 402, "indent": 4, "parameters": [3, "返回"]}, {"code": 119, "indent": 5, "parameters": ["开头"]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 404, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [2, "做爱"]}, {"code": 118, "indent": 4, "parameters": ["做爱"]}, {"code": 356, "indent": 4, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 101, "indent": 4, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 4, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 4, "parameters": ["\\dac\\c[210](要结束休息开始…正事吗……？)"]}, {"code": 102, "indent": 4, "parameters": [["是", "否"], 1, -1, 1, 1]}, {"code": 402, "indent": 4, "parameters": [0, "是"]}, {"code": 117, "indent": 5, "parameters": [257]}, {"code": 0, "indent": 5, "parameters": []}, {"code": 402, "indent": 4, "parameters": [1, "否"]}, {"code": 111, "indent": 5, "parameters": [0, 356, 1]}, {"code": 356, "indent": 6, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 119, "indent": 6, "parameters": ["开头"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 411, "indent": 5, "parameters": []}, {"code": 118, "indent": 6, "parameters": ["取消"]}, {"code": 101, "indent": 6, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 6, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 6, "parameters": ["\\dac\\c[210](再休息一下吧……)"]}, {"code": 0, "indent": 6, "parameters": []}, {"code": 412, "indent": 5, "parameters": []}, {"code": 0, "indent": 5, "parameters": []}, {"code": 404, "indent": 4, "parameters": []}, {"code": 0, "indent": 4, "parameters": []}, {"code": 402, "indent": 3, "parameters": [3, "取消"]}, {"code": 119, "indent": 4, "parameters": ["取消"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 404, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [-1, 8, false]}, {"code": 119, "indent": 3, "parameters": ["做爱"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 121, "indent": 0, "parameters": [71, 71, 1]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 44, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 528}, "directionFix": false, "image": {"tileId": 0, "characterName": "亚丝娜私服像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 28, "y": 24}, {"id": 5, "name": "EV005料理2", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 484}, "directionFix": true, "image": {"tileId": 0, "characterName": "!food4", "direction": 2, "pattern": 0, "characterIndex": 5}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-6,12]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 527}, "directionFix": true, "image": {"tileId": 0, "characterName": "!food4", "direction": 8, "pattern": 2, "characterIndex": 5}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-6,-6]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 29, "y": 22}, {"id": 6, "name": "EV006床光圈", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 33}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,-12]"]}, {"code": 102, "indent": 0, "parameters": [["登录游戏", "  取消"], 1, 0, 2, 1]}, {"code": 402, "indent": 0, "parameters": [0, "登录游戏"]}, {"code": 117, "indent": 1, "parameters": [127]}, {"code": 117, "indent": 1, "parameters": [152]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "  取消"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 76, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 71, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 9}, {"id": 7, "name": "EV007床", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 73, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 33}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$桐人房间床", "direction": 8, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 74, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 33}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$桐人房间床", "direction": 6, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 75, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 33}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$桐人房间床", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 9}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 39}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 614, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac 要回医院吗？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], -1, -1, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 241, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 249, "indent": 2, "parameters": [{"name": "Inn", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 121, "indent": 2, "parameters": [298, 298, 0]}, {"code": 201, "indent": 2, "parameters": [0, 363, 16, 15, 6, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 19, "indent": null}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 143, 11, 4, 2, 0]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 78, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 327}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在不是出门的时候。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 86, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 327}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 613, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac现在不是出门的时候。"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 97, 0, 6, 0]}, {"code": 111, "indent": 1, "parameters": [4, 24, 0]}, {"code": 122, "indent": 2, "parameters": [95, 95, 0, 0, 2]}, {"code": 355, "indent": 2, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 2, "parameters": [2, "明日奈沉思", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 2, "parameters": [135]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210](现在不应该离开。)"]}, {"code": 235, "indent": 2, "parameters": [2]}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 10, "y": 20}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 39}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 614, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac 要回医院吗？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], -1, -1, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 241, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 249, "indent": 2, "parameters": [{"name": "Inn", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 121, "indent": 2, "parameters": [298, 298, 0]}, {"code": 201, "indent": 2, "parameters": [0, 363, 16, 15, 6, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 19, "indent": null}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 143, 11, 4, 2, 0]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 78, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 327}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在不是出门的时候。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 86, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 327}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 613, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac现在不是出门的时候。"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 97, 0, 6, 0]}, {"code": 111, "indent": 1, "parameters": [4, 24, 0]}, {"code": 122, "indent": 2, "parameters": [95, 95, 0, 0, 2]}, {"code": 355, "indent": 2, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 2, "parameters": [2, "明日奈沉思", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 2, "parameters": [135]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210](现在不应该离开。)"]}, {"code": 235, "indent": 2, "parameters": [2]}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 9, "y": 20}, {"id": 10, "name": "电脑", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 65, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!笔记本", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,40]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 6}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 243}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 213, "indent": 0, "parameters": [19, 10, true]}, {"code": 250, "indent": 0, "parameters": [{"name": "闹钟", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [19, 1, true]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [19, 3, false]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊~~~ 懒觉睡得真爽。 "]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [19, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜的未读信息……！"]}, {"code": 213, "indent": 0, "parameters": [19, 8, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [19, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 昨晚就给我发信息了啊……今天早上也是。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 赶紧回一个过去吧。 "]}, {"code": 250, "indent": 0, "parameters": [{"name": "KOK_Systemsounds_CursorMove_05", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [19, 8, true]}, {"code": 250, "indent": 0, "parameters": [{"name": "微信新消息-ipone-短视频音效-微信提醒_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [19, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac秒回了！"]}, {"code": 213, "indent": 0, "parameters": [19, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac在会谈啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不过她没生气真是太好了。"]}, {"code": 250, "indent": 0, "parameters": [{"name": "KOK_Systemsounds_CursorMove_05", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "微信新消息-ipone-短视频音效-微信提醒_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [19, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈哈，原来在开小差~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac电影嘛，上次亚丝娜说我功课做得不足来着……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac今天反正有空，我可以去繁华街的影碟店看看。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac问问她们感兴趣的类型。"]}, {"code": 250, "indent": 0, "parameters": [{"name": "KOK_Systemsounds_CursorMove_05", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 250, "indent": 0, "parameters": [{"name": "微信新消息-ipone-短视频音效-微信提醒_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呃……这有什么保密的？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac难道……"]}, {"code": 250, "indent": 0, "parameters": [{"name": "KOK_Systemsounds_CursorMove_05", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [19, 2, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "微信新消息-ipone-短视频音效-微信提醒_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [19, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac连标点符号都没打，看来是很突然了……"]}, {"code": 250, "indent": 0, "parameters": [{"name": "KOK_Systemsounds_CursorMove_05", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac起床吧。"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 42, "parameters": [255], "indent": null}, {"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还是去找小直蹭饭好了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac先给她打个电话。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "通话中-嘟-综艺短视频-烘托等待_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 250, "indent": 0, "parameters": [{"name": "通话中-嘟-综艺短视频-烘托等待_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 250, "indent": 0, "parameters": [{"name": "接电话(Phone Pick Up)_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac小直，是我，你在咖啡厅吗？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac哥哥！嗯呢，我在。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我过来一起吃午饭，方便吗？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac当然方便！哥哥，听你的声音……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你是刚起床吧？昨天是不是熬夜干什么坏事了？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [-1, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没！怎么会！不过我确实是刚起床。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac昨天晚上就是打游戏搞晚了一点而已。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac游戏里还不是可以做坏事……\\}嫖妓什么的……\\{"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但没关系，不管哥哥做什么，我都会包容的。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈哈，谢谢你，小直。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但我真的没做坏事"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac好啦好啦，还是快点过来吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我想快点见到哥哥。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，我现在就出发。"]}, {"code": 250, "indent": 0, "parameters": [{"name": "電話が切れるツーツー", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([135, 29, 'A'], true);"]}, {"code": 121, "indent": 0, "parameters": [43, 49, 0]}, {"code": 122, "indent": 0, "parameters": [80, 80, 0, 4, "\"去咖啡厅找直叶吃饭\""]}, {"code": 122, "indent": 0, "parameters": [79, 79, 0, 4, "\"银座\""]}, {"code": 121, "indent": 0, "parameters": [73, 73, 0]}, {"code": 121, "indent": 0, "parameters": [74, 74, 1]}, {"code": 121, "indent": 0, "parameters": [75, 75, 1]}, {"code": 121, "indent": 0, "parameters": [76, 76, 0]}, {"code": 121, "indent": 0, "parameters": [71, 71, 1]}, {"code": 121, "indent": 0, "parameters": [56, 56, 0]}, {"code": 121, "indent": 0, "parameters": [59, 59, 0]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 244]}, {"code": 122, "indent": 0, "parameters": [76, 76, 0, 0, 104]}, {"code": 121, "indent": 0, "parameters": [73, 73, 0]}, {"code": 356, "indent": 0, "parameters": [">信息面板L : 显示选项 : 选项[20]"]}, {"code": 356, "indent": 0, "parameters": [">信息面板L : 选中选项 : 选项[20]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 244}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 2}, {"id": 12, "name": "EV013未知BUG修复", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 243}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 117, "indent": 0, "parameters": [141]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 248}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 121, "indent": 0, "parameters": [76, 76, 1]}, {"code": 121, "indent": 0, "parameters": [71, 71, 1]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 252}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 3, "y": 1}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 39}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 614, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac 要回医院吗？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], -1, -1, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 241, "indent": 2, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 249, "indent": 2, "parameters": [{"name": "Inn", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 121, "indent": 2, "parameters": [298, 298, 0]}, {"code": 201, "indent": 2, "parameters": [0, 363, 16, 15, 6, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 19, "indent": null}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 143, 11, 4, 2, 0]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 78, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 327}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在不是出门的时候。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 86, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 327}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 613, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac现在不是出门的时候。"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 97, 0, 6, 0]}, {"code": 111, "indent": 1, "parameters": [4, 24, 0]}, {"code": 122, "indent": 2, "parameters": [95, 95, 0, 0, 2]}, {"code": 355, "indent": 2, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 2, "parameters": [2, "明日奈沉思", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 2, "parameters": [135]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210](现在不应该离开。)"]}, {"code": 235, "indent": 2, "parameters": [2]}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 11, "y": 20}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 288}, "directionFix": true, "image": {"tileId": 0, "characterName": "工口2", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[24,-12]"]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 526, 0]}, {"code": 355, "indent": 1, "parameters": ["$gamePlayer.followers().follower(0).turnTowardCharacter($gameMap.event(this.eventId()));"]}, {"code": 122, "indent": 1, "parameters": [123, 123, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 1, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 1, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 1, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 1, "parameters": ["} else {"]}, {"code": 655, "indent": 1, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 1, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 1, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 111, "indent": 1, "parameters": [0, 355, 1]}, {"code": 117, "indent": 2, "parameters": [259]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 213, "indent": 2, "parameters": [-1, 3, true]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 21, "y": 7}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 324}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 129, "indent": 0, "parameters": [1, 0, false]}, {"code": 129, "indent": 0, "parameters": [4, 1, false]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 203, "indent": 0, "parameters": [16, 0, 22, 8, 2]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.target(6,2,1);"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-153, -153, -153, 0], 60, false]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 117, "indent": 0, "parameters": [141]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 223, "indent": 0, "parameters": [[-51, -51, -51, 0], 60, false]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 16, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [19, 1, false]}, {"code": 121, "indent": 0, "parameters": [78, 78, 0]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 15, false]}, {"code": 225, "indent": 0, "parameters": [7, 5, 15, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唔啊！"]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.restore(60);"]}, {"code": 213, "indent": 0, "parameters": [19, 12, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "2000_Village1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac小直！你吓死我了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你怎么在这？"]}, {"code": 213, "indent": 0, "parameters": [16, 7, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 40, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 18, "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "直叶苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["直叶.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac昨天说了我要来给哥哥做饭的呀，你不会忘了吧？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊，是有这么一回事。我的确是忘了，抱歉。"]}, {"code": 213, "indent": 0, "parameters": [16, 4, false]}, {"code": 231, "indent": 0, "parameters": [2, "直叶温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["直叶.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哥哥真是的~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac这有什么好抱歉的！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 2, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac诶，这里怎么有个玩偶？"]}, {"code": 213, "indent": 0, "parameters": [16, 11, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "直叶微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["直叶.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是送给哥哥的礼物啦~ 这个玩偶也是我的替身……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我不在的时候，就由它，帮我守望着哥哥！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac原来如此。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac谢谢你，小直。我会好好珍惜的！"]}, {"code": 213, "indent": 0, "parameters": [16, 3, false]}, {"code": 231, "indent": 0, "parameters": [2, "直叶通用", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["直叶.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哥哥一天没吃东西，快饿死了吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac饭已经弄好了，我现在去热一下，哥哥去客厅等我吧。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 325]}, {"code": 201, "indent": 0, "parameters": [0, 252, 20, 16, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 325}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 117, "indent": 0, "parameters": [143]}, {"code": 241, "indent": 0, "parameters": [{"name": "2000_Village1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 203, "indent": 0, "parameters": [16, 0, 18, 16, 6]}, {"code": 203, "indent": 0, "parameters": [18, 0, 20, 17, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 2, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac小直你刚才为啥站在我的床边啊？"]}, {"code": 213, "indent": 0, "parameters": [16, 4, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "直叶微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["直叶.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac因为哥哥的睡颜很可爱啊~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 11, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊？"]}, {"code": 213, "indent": 0, "parameters": [16, 3, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "直叶苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["直叶.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac开玩笑的啦~ 其实是我正好在旁边做清洁，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac听到哥哥机器系统提示音，就好奇过来看看。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哥哥不会以为我守在你旁边几个小时，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac就这么一动不动盯着你看吧？我又不是变态~ "]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 11, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 16, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈哈，那怎么会呢？"]}, {"code": 213, "indent": 0, "parameters": [16, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "直叶转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["直叶.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哥哥，你慢慢吃。我感觉有些困，先回家休息了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我以后有空就过来。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯嗯，没问题。"]}, {"code": 213, "indent": 0, "parameters": [16, 4, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [25], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [25], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 326]}, {"code": 249, "indent": 0, "parameters": [{"name": "LNSM_ME06_Sleep2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 0, "parameters": [0, 252, 8, 16, 4, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 326}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 245, "indent": 0, "parameters": [{"name": "浴室冲水", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 246, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [81, 81, 0, 0, 10]}, {"code": 117, "indent": 0, "parameters": [116]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac洗碗结束~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac给亚丝娜打个电话吧。"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Phone", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Phone", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "接电话(Phone Pick Up)_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac喂，你好。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac拘谨的男声:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac请问，是桐谷和人吗？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你是？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac拘谨的男声:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac叫我铃木就好。我是市场二课的，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac课长把带你的任务交给我了，以后还请多多关照。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac铃木:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac入职的学习材料已经发到你的邮箱了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac晚上可以先熟悉一下，"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac感谢铃木前辈，我一定会好好学习的！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac铃木:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac小伙子很精神，让我想到当年的自己……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不过桐谷君你肯定会比我有出息的！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [-1, 11, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac前辈谬赞了！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac铃木:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac跟我没必要客气！那就先这样。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac拜拜，桐人君，明天公司见。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 34, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "電話が切れるツーツー", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac感觉是个好人。先去看看邮件吧。"]}, {"code": 111, "indent": 0, "parameters": [0, 303, 0]}, {"code": 117, "indent": 1, "parameters": [212]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 327]}, {"code": 122, "indent": 0, "parameters": [80, 80, 0, 4, "\"在电脑上的电子邮件中查看入职学习资料\""]}, {"code": 122, "indent": 0, "parameters": [79, 79, 0, 4, "\"亚丝娜的公寓\""]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 327}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 466}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 121, "indent": 0, "parameters": [47, 47, 0]}, {"code": 203, "indent": 0, "parameters": [18, 0, 20, 17, 0]}, {"code": 203, "indent": 0, "parameters": [16, 0, 18, 16, 6]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 45, "parameters": ["$gamePlayer.requestBalloon(3);"], "indent": null}, {"code": 15, "parameters": [30], "indent": 0}, {"code": 45, "parameters": ["$gameMap.event(16).requestBalloon(4);"], "indent": 0}, {"code": 15, "parameters": [30], "indent": 0}, {"code": 15, "parameters": [30], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gamePlayer.requestBalloon(3);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(16).requestBalloon(4);"], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "A", 1]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 285, 14, 8, 6, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 466}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac说起小直，最近生活上真是多亏了她。\\.\\."]}, {"code": 401, "indent": 0, "parameters": ["\\dac和阿姨的约定并没有因为关系的缓和而废除，\\.\\."]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜工作日不能来我的公寓。\\|"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac小直的定期到访，不仅让我的生活井井有条，\\.\\."]}, {"code": 401, "indent": 0, "parameters": ["\\dac对我的口腹之欲也是大有脾益。\\.\\."]}, {"code": 401, "indent": 0, "parameters": ["\\dac公司的简餐虽然不难吃，但每天吃也遭不住。\\|"]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 467}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 482}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "Instrumental-Divorce in Ghana", "volume": 20, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [10, "人妻亚丝娜", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [11, "人妻亚丝娜惊讶", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [12, "人妻亚丝娜失望", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [10, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac欢迎回家~"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜，你这是…？"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那么，阿纳塔……"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你现在是要吃饭？洗澡？还是……"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac瓦~塔~西~"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](虽然想直接做爱，但感觉肚子有点饿了，这种状态估计很难让亚丝娜满意……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，那个，我们还是先吃饭吧。"]}, {"code": 232, "indent": 0, "parameters": [11, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac诶……\\|"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 232, "indent": 0, "parameters": [12, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 235, "indent": 0, "parameters": [10]}, {"code": 235, "indent": 0, "parameters": [11]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac诶…好吧……"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 249, "indent": 0, "parameters": [{"name": "LNSM_ME06_Sleep2", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [12, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 484]}, {"code": 201, "indent": 0, "parameters": [0, 252, 20, 16, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 484}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 284, "indent": 0, "parameters": ["!公寓房间-夜晚", false, false, 0, 0]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-夜晚光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[27]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[桐人亚丝娜待机]"]}, {"code": 121, "indent": 0, "parameters": [87, 87, 1]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 203, "indent": 0, "parameters": [4, 0, 18, 16, 2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}, {"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [5, 0, 20, 17, 0]}, {"code": 203, "indent": 0, "parameters": [18, 0, 18, 17, 2]}, {"code": 356, "indent": 0, "parameters": ["NF_REM_NPC 1"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 111, "indent": 0, "parameters": [0, 265, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 33, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 213, "indent": 1, "parameters": [4, 8, false]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 213, "indent": 1, "parameters": [-1, 4, true]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac好饱好饱，一本满足！"]}, {"code": 213, "indent": 1, "parameters": [4, 6, false]}, {"code": 205, "indent": 1, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 122, "indent": 1, "parameters": [95, 95, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 1, "parameters": [2, "明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人君，真是每次都这么夸张。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac但你能喜欢，我蛮开心的。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac刚吃完饭剧烈运动对身体不好，我们先休息一下吧。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 108, "indent": 1, "parameters": ["对话数组"]}, {"code": 356, "indent": 1, "parameters": ["SetArray 122 0 0"]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 485]}, {"code": 121, "indent": 1, "parameters": [78, 78, 0]}, {"code": 121, "indent": 1, "parameters": [77, 77, 1]}, {"code": 117, "indent": 1, "parameters": [154]}, {"code": 213, "indent": 1, "parameters": [-1, 3, true]}, {"code": 205, "indent": 1, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 205, "indent": 1, "parameters": [6, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 203, "indent": 1, "parameters": [4, 0, 18, 16, 6]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "Instrumental-Divorce in Ghana", "volume": 20, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 117, "indent": 1, "parameters": [257]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 485}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 486}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 0, "parameters": [100, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[27]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[桐人亚丝娜待机]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[100] : 设置动画序列 : 动画序列[27]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[100] : 播放简单状态元集合 : 集合[桐人亚丝娜舌吻]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[100] : 等待动画序列加载完成"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君……随时都可以喔……"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 35 \\c[8]\\}扶我上去！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯……"]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 35 \\c[8]找死吧！摸我屁股？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君，你这样一直盯着我看……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac感觉有点害羞……"]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 28 \\c[8]我不是故意的……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac抱歉，可我忍不住……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你真美……"]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 35 \\c[8]哼！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac真是的……不过今天，确实是特别的呢……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac时隔多日，我们终于再次合为一体~\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac随时……都可以的喔……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](怎么还是和早上一样……明明那么兴奋，却硬不起来……为什么啊？)"]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 28 \\c[8]他们开始做了吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君……？"]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 35 \\c[8]别吵我，废物。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac抱歉，可能太久没做，有点紧张……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac状态还是不太好。"]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 28 \\c[8]是……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没关系的，慢慢来……不要有太大的压力。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](简直像手淫过度一样……可我这段时间都没有过啊！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君，来……"]}, {"code": 111, "indent": 0, "parameters": [0, 265, 1]}, {"code": 117, "indent": 1, "parameters": [122]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 245, "indent": 0, "parameters": [{"name": "Honeydrop_kiss1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啾噜……唔呣……嗯……"]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 28 \\c[8]姐，你怎么下来了？看到什么破防了？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](和刚才的吻感觉完全不一样……！)"]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 35 \\c[8]我破防？！我只是觉得无聊！死肥猪！我要回去了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](亚丝娜的舌头好积极……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还有她的体香……)"]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 28 \\c[8]嗯嗯，姐你慢走~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](感觉小腹越来越热了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac鸡鸡慢慢硬起来了！)"]}, {"code": 232, "indent": 0, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜，谢谢你！我没问题了！"]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 28 \\c[8]妈的！处女婊子！看你还能嚣张几天！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac真是的！这谢什么呀~\\i[90]"]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 28 \\c[8]这个空调机箱应该撑得住我的体重吧……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那我要上了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，进来吧……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac阿~纳~塔~\\i[90]"]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 28 \\c[8]可恶！这个称谓明明是属于我的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](终于又要和亚丝娜合为一体了……)"]}, {"code": 111, "indent": 0, "parameters": [0, 265, 1]}, {"code": 117, "indent": 1, "parameters": [122]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "P_singlepiston06", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 Honeydrop_o_aegisilent 25 100 0"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[桐人亚丝娜慢]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊，嗯嗯……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没，没事吧……长时间没做，我现在抽插，会不会觉得疼？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要不要我先停下来？"]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 28 \\c[8]就你那个小毛虫，还疼？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不用，不疼……"]}, {"code": 111, "indent": 0, "parameters": [0, 265, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 255 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](不仅是不疼……)"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜，我好舒服啊……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你感觉怎么样？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯……我觉得鸡鸡……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac好温暖呢……"]}, {"code": 111, "indent": 0, "parameters": [0, 265, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 255 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](桐人的下面是不是变大了一些？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac可是……)"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac只是温暖吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜……也觉得，舒服吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac诶……啊，是的……当然了……"]}, {"code": 111, "indent": 0, "parameters": [0, 265, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 255 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](并没有…什么感觉……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac怎么回事……)"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 28 \\c[8]明显就是不舒服的意思吧！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](太爽了……感觉，坚持不了多久……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈啊，哈啊……"]}, {"code": 111, "indent": 0, "parameters": [0, 265, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 255 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](加大力气……多多感受鸡鸡，就可以……)"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊啊……突然夹得好紧……"]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 28 \\c[8]可恶可恶可恶！明明是属于我的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](这说明亚丝娜真的也很有感觉呢……！啊，呜……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但这样的话，要……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊，嗯……哈啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac抱歉，亚丝娜……！"]}, {"code": 111, "indent": 0, "parameters": [0, 265, 0]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 255 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](难道，他已经要……我都还没什么感觉呢……)"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊，桐人……嗯嗯……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不行了…！！！"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[桐人亚丝娜早泄]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": ["PushGab 28 \\c[8]傻逼早泄男……"]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精音Bタイプ中出し⑤（どぴゅ！びゅるっ！）", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [80]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 235, "indent": 0, "parameters": [100]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 111, "indent": 0, "parameters": [0, 265, 0]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 1, 82]}, {"code": 121, "indent": 1, "parameters": [265, 265, 1]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [298, 298, 1]}, {"code": 201, "indent": 1, "parameters": [0, 81, 9, 10, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 487]}, {"code": 117, "indent": 1, "parameters": [131]}, {"code": 201, "indent": 1, "parameters": [0, 252, 20, 16, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 487}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 121, "indent": 0, "parameters": [71, 71, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [22, 0, 19, 20, 0]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [18, 0, 20, 17, 0]}, {"code": 203, "indent": 0, "parameters": [5, 0, 20, 17, 0]}, {"code": 203, "indent": 0, "parameters": [4, 0, 19, 16, 6]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[0,-21] : 时间[1]"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}, {"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}, {"code": 41, "parameters": ["亚丝娜裸", 0], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜裸", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["桐人裸_闭眼", 2], "indent": null}, {"code": 41, "parameters": ["桐人裸", 1], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人裸_闭眼", 2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人裸", 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [-1, 12, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈啊，哈啊……亚丝娜:有……爽到吗……"]}, {"code": 213, "indent": 0, "parameters": [4, 6, false]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 6]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈掩饰", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，我也，高潮了…真的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还是蛮舒服的。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那就太好了……但我感觉刚才…冲刺得太快……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在有点晕晕的……稍微，稍微休息一下……"]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，你休息吧……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊，好幸福……"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我也，好幸福……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["桐人裸_闭眼", 2], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人裸_闭眼", 2], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 45, "parameters": ["$gamePlayer.requestBalloon(10);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gamePlayer.requestBalloon(10);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呼…唔……呼……唔……呼噜……"]}, {"code": 213, "indent": 0, "parameters": [4, 7, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈沉思", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这就，结束了么……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [4, 1, true]}, {"code": 213, "indent": 0, "parameters": [4, 12, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈焦急脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没！我才没有觉得不满呢……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 2, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈沉默脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac只是，小穴还痒痒的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac等晚上回去以后……用小玩具吧。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈心事脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我爱你。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [22, 8, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [71, 71, 1]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[0,0] : 时间[1]"]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 488]}, {"code": 201, "indent": 0, "parameters": [0, 252, 20, 16, 4, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 488}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 121, "indent": 0, "parameters": [71, 71, 0]}, {"code": 203, "indent": 0, "parameters": [4, 0, 20, 15, 6]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": 0}, {"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["同人学校服", 0], "indent": null}, {"code": 41, "parameters": ["桐人裸", 1], "indent": 0}, {"code": 41, "parameters": ["桐人裸_闭眼", 2], "indent": null}, {"code": 17, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["同人学校服", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人裸", 1], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人裸_闭眼", 2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 45, "parameters": ["$gamePlayer.requestBalloon(10);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gamePlayer.requestBalloon(10);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac温柔的女声:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君，桐人君……"]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, false]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君，在这里睡觉可是会腰疼的哦。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["桐人裸", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人裸", 1], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [-1, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜，我又睡着了吗？抱歉！"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 14, "parameters": [-1, 0], "indent": null}, {"code": 41, "parameters": ["桐人裸", 0], "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [-1, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人裸", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈理解", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没事，也没睡很久，但挺沉的，呼吸很重。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac看来今天真是累到了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 11, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac惭愧惭愧，刚才想要好好表现一下，结果……"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君刚才很棒的，我非常舒服~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac真的？"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈苦笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那就好，嘿嘿。"]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈失望", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那个，桐人君……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你睡觉的时候，妈妈打电话问我什么时候回去。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac什么？阿姨生气了没有？没有说什么吧？"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈回应", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我跟她说我们在吃法餐，上菜太慢所以弄晚了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦，那就好，她没说什么了吧……？"]}, {"code": 213, "indent": 0, "parameters": [4, 6, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈叹气", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不过没哄到她。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 12, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 13, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊？！"]}, {"code": 213, "indent": 0, "parameters": [4, 3, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈嘲弄", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈哈，瞧你吓的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但她也没生气，只是提醒我不能太晚。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦哦。"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈安心", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac所以我现在要回去啦。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我送你。"]}, {"code": 213, "indent": 0, "parameters": [4, 4, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈通用", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，我们走吧。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 4, true]}, {"code": 250, "indent": 0, "parameters": [{"name": "Phone", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [4, 2, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Phone", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "接电话(Phone Pick Up)_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](爱丽丝小姐？)"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君，在忙吗？方不方便说话？十万火急！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac稍等！"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜，等我一下，我接个电话。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [25], "indent": null}, {"code": 19, "indent": null}, {"code": 15, "parameters": [120], "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [25], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [120], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你说吧。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac紧急行动，你现在可以上线吗？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac已经开服了吗？还没到公告的时间吧？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac FOG的更新实际已经完成了，只是暂时没有向普通玩家开放，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我用权限可以让你提前登录。"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac原来如此，可是必须现在吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我现在……"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac很抱歉，桐人君，必须现在，必须是你。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我们接到线报，FOG系统一直是SAO归还者进行了特殊标记，原因不明。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac而这次更新，错误地给SAO核心攻略组成员赋予了管理员权限。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac只要能把握住这个机会，我们就可以接入组织的核心数据库，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac取得他们关键的犯罪证据！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [-1, 9, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac明白了。但警方中应该也不乏攻略组成员吧，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我觉得这种重要任务，似乎还是官方亲自下场比较好。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你说得对，但时间不够。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac正常来说，这种重大BUG一被发现就会被立刻修复。是我们在RATH的卧底，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac暂时截住了层报，但最多12小时。我提警力调动的申请根本来不及！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac其实我也考虑过克莱因，但我真的不放心他去办。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac所以只能依靠你了，拜托了，桐人君！ "]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac好吧。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac另外，我得到消息，组织正在实施营救须乡的计划，他现在的律师团里，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可能就有一个是组织委派的。这次的行动，也可能涉及到他。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac什么？！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac所以务必谨慎行动，如果打草惊蛇，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac警方可能被律师质疑，对须乡的定罪不利。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我明白了。"]}, {"code": 250, "indent": 0, "parameters": [{"name": "電話が切れるツーツー", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜，我临时有很重要的事情要处理。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac所以不能送你回家了。抱歉！"]}, {"code": 213, "indent": 0, "parameters": [4, 6, false]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈担忧", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac诶？是什么事？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在还不能跟你解释，但请你相信我。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac等一切结束以后，我会向你全部说明的。"]}, {"code": 213, "indent": 0, "parameters": [4, 7, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈心事", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，我知道了，我相信你，桐人君。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那我先走吧……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈欲言又止", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我爱你……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我也爱你！"]}, {"code": 213, "indent": 0, "parameters": [4, 8, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 37, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 213, "indent": 0, "parameters": [-1, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜真好~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我也赶紧上线吧！"]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 249, "indent": 0, "parameters": [{"name": "Inn", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 121, "indent": 0, "parameters": [71, 71, 1]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 489]}, {"code": 121, "indent": 0, "parameters": [58, 58, 1]}, {"code": 121, "indent": 0, "parameters": [59, 59, 1]}, {"code": 121, "indent": 0, "parameters": [60, 60, 0]}, {"code": 121, "indent": 0, "parameters": [21, 21, 0]}, {"code": 121, "indent": 0, "parameters": [78, 78, 1]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 117, "indent": 0, "parameters": [152]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 489}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 522}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 117, "indent": 0, "parameters": [143]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["桐人裸", 6], "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人裸", 6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 245, "indent": 0, "parameters": [{"name": "浴室冲水", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [127]}, {"code": 213, "indent": 0, "parameters": [-1, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac刚才那一下后劲真大，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac吃饭洗澡以后还是感觉不行……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac昨晚没睡的困意也来攻击我，应该是撑不到亚丝娜过来了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac先去睡吧……"]}, {"code": 249, "indent": 0, "parameters": [{"name": "Inn", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 523]}, {"code": 117, "indent": 0, "parameters": [154]}, {"code": 117, "indent": 0, "parameters": [23]}, {"code": 129, "indent": 0, "parameters": [24, 0, false]}, {"code": 129, "indent": 0, "parameters": [1, 1, false]}, {"code": 121, "indent": 0, "parameters": [298, 298, 0]}, {"code": 201, "indent": 0, "parameters": [0, 299, 0, 13, 6, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 523}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 524}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}, {"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [22, 0, 21, 8, 6]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 28 1"]}, {"code": 121, "indent": 0, "parameters": [86, 86, 0]}, {"code": 203, "indent": 0, "parameters": [24, 0, 22, 9, 0]}, {"code": 117, "indent": 0, "parameters": [141]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 525}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 19, "y": 2}, {"id": 16, "name": "EV016直叶", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 324}, "directionFix": false, "image": {"tileId": 0, "characterName": "直叶", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 326}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 47, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 466}, "directionFix": false, "image": {"tileId": 0, "characterName": "直叶", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 47, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 527}, "directionFix": false, "image": {"tileId": 0, "characterName": "直叶约会行走图", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 47, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 640}, "directionFix": false, "image": {"tileId": 0, "characterName": "直叶", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 644, 2]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 643, 1]}, {"code": 111, "indent": 2, "parameters": [0, 407, 0]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac \\c[210](刚才和小直做了那样的事情，现在根本没办法和她说话……)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 205, "indent": 3, "parameters": [16, {"list": [{"code": 36, "indent": null}, {"code": 34, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 18, "indent": null}]}, {"code": 122, "indent": 3, "parameters": [133, 133, 0, 0, 3]}, {"code": 355, "indent": 3, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 3, "parameters": [2, "直叶温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [168]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac 哥哥，再等我一下，饭菜马上就好了。"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac 嗯嗯。"]}, {"code": 205, "indent": 3, "parameters": [16, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 33, "indent": null}, {"code": 35, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 35, "indent": null}]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 29, "y": 24}, {"id": 17, "name": "EV017电脑光圈", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 146, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 60}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-6,-12]"]}, {"code": 117, "indent": 0, "parameters": [56]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 146, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 327}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-6,-12]"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 121, "indent": 0, "parameters": [65, 65, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "KOK_Systemsounds_MenuOpen_01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 102, "indent": 0, "parameters": [["查看电子邮件", "查看爱巢博客", "    取消"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "查看电子邮件"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 60, false]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 213, "indent": 1, "parameters": [-1, 8, false]}, {"code": 230, "indent": 1, "parameters": [45]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 117, "indent": 1, "parameters": [16]}, {"code": 121, "indent": 1, "parameters": [71, 71, 0]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 328]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 251, 15, 8, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "查看爱巢博客"]}, {"code": 117, "indent": 1, "parameters": [33]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac虽然已经被设置私密，但不知为何，博客内容出现在了眼前……"]}, {"code": 356, "indent": 1, "parameters": [">信息面板K : 打开面板"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 111, "indent": 1, "parameters": [0, 296, 0]}, {"code": 121, "indent": 2, "parameters": [296, 296, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 121, "indent": 2, "parameters": [65, 65, 1]}, {"code": 121, "indent": 2, "parameters": [295, 295, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "    取消"]}, {"code": 121, "indent": 1, "parameters": [65, 65, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 146, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 327}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-6,-12]"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 121, "indent": 0, "parameters": [65, 65, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "KOK_Systemsounds_MenuOpen_01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 121, "indent": 0, "parameters": [71, 71, 0]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 328]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 251, 15, 8, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 328}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 146, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 328}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-6,-12]"]}, {"code": 117, "indent": 0, "parameters": [56]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 71, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 7}, {"id": 18, "name": "EV018料理", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 325}, "directionFix": true, "image": {"tileId": 0, "characterName": "!food1", "direction": 4, "pattern": 1, "characterIndex": 4}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-6,12]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 326}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 466}, "directionFix": true, "image": {"tileId": 0, "characterName": "!food1", "direction": 4, "pattern": 1, "characterIndex": 4}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-6,12]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 467}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 484}, "directionFix": true, "image": {"tileId": 0, "characterName": "!food4", "direction": 2, "pattern": 0, "characterIndex": 5}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[6,12]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 527}, "directionFix": true, "image": {"tileId": 0, "characterName": "!food4", "direction": 8, "pattern": 2, "characterIndex": 5}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,-6]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 29, "y": 23}, {"id": 19, "name": "019床头气泡", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 8}, {"id": 20, "name": "EV020", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 77, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 329}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "闹钟", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 121, "indent": 0, "parameters": [77, 77, 1]}, {"code": 121, "indent": 0, "parameters": [78, 78, 1]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [19, 1, true]}, {"code": 117, "indent": 0, "parameters": [145]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 42, "parameters": [255], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 117, "indent": 0, "parameters": [143]}, {"code": 241, "indent": 0, "parameters": [{"name": "魔王魂 ループ  アコースティック39", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](去学校吧……)"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": 0}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 213, "indent": 0, "parameters": [-1, 9, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊！不对！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac应该是去RECT。"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac幸好幸好！如果搞错去了学校，导致第一天上班就迟到，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac昨天对亚丝娜的承诺就成笑话了。"]}, {"code": 122, "indent": 0, "parameters": [77, 77, 0, 2, 3, 11]}, {"code": 117, "indent": 0, "parameters": [110]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 77, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 389}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 121, "indent": 0, "parameters": [78, 78, 1]}, {"code": 117, "indent": 0, "parameters": [141]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [19, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}不要！亚丝娜！你不能让他内射！\\."]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.target(6,2,15);"]}, {"code": 117, "indent": 0, "parameters": [145]}, {"code": 213, "indent": 0, "parameters": [19, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不要啊！！！\\|"]}, {"code": 213, "indent": 0, "parameters": [19, 2, true]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.restore(90);"]}, {"code": 250, "indent": 0, "parameters": [{"name": "闹钟", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [19, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呼……"]}, {"code": 213, "indent": 0, "parameters": [19, 9, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac原来是梦啊…太好了！"]}, {"code": 213, "indent": 0, "parameters": [19, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唉，我都在乱想什么，居然做那种梦！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我的亚丝娜，永远不会变成那个样子！"]}, {"code": 213, "indent": 0, "parameters": [19, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不过说起来，那个时候确实也多亏了猪田……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这家伙，虽然有时有点奇怪，但总体来说还算听话可靠。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac之前把亚丝娜的心情问题归罪于他，不太公平。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac大概是因为抓鸽子那次以后，我对他总有些莫名的敌意吧。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在想来，感觉也确实没有什么必要。"]}, {"code": 213, "indent": 0, "parameters": [19, 11, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗨~ 只是梦而已，我怎么还正儿八经地思考起来了？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac赶紧起床吧！"]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 42, "parameters": [255], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [143]}, {"code": 122, "indent": 0, "parameters": [100, 100, 1, 0, 1]}, {"code": 117, "indent": 0, "parameters": [154]}, {"code": 121, "indent": 0, "parameters": [77, 77, 1]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 77, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 419}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "闹钟", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 121, "indent": 0, "parameters": [77, 77, 1]}, {"code": 121, "indent": 0, "parameters": [78, 78, 1]}, {"code": 117, "indent": 0, "parameters": [141]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [19, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唔……"]}, {"code": 117, "indent": 0, "parameters": [145]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac起床吧。"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 42, "parameters": [255], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 117, "indent": 0, "parameters": [143]}, {"code": 241, "indent": 0, "parameters": [{"name": "魔王魂 ループ  アコースティック39", "volume": 20, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [-1, 9, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac给亚丝娜打个电话，商量一下今天的安排。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [100, "手机", 0, 0, 100, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [99, "亚丝娜发信中", 0, 0, 100, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 0, "parameters": [98, "亚丝娜通话中", 0, 0, 100, 0, 100, 100, 255, 0]}, {"code": 250, "indent": 0, "parameters": [{"name": "通话中-嘟-综艺短视频-烘托等待_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 250, "indent": 0, "parameters": [{"name": "通话中-嘟-综艺短视频-烘托等待_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 250, "indent": 0, "parameters": [{"name": "接电话(Phone Pick Up)_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜，怎么样？起床了吗？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君，我一个小时之前就已经起床了。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这么早？！"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac妈妈早上回来给我布置了一堆要写的报告，现在又出门了。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这样……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那今天你都要写这些报告了吗？"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac很遗憾，确实如此。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac今天不能陪你了，对不起。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac别这么说，这也是没办法的事。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但我晚上应该还是可以上线的，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac只要我保持现在的效率的话。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不幸中的万幸。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我先不打扰你干活了，晚上见。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，晚上见。"]}, {"code": 250, "indent": 0, "parameters": [{"name": "電話が切れるツーツー", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [30], "indent": 0}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 235, "indent": 0, "parameters": [100]}, {"code": 235, "indent": 0, "parameters": [98]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac难得的周末，又要工作……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac阿姨未免太严格了。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac出门随便逛逛吧。"]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 420]}, {"code": 117, "indent": 0, "parameters": [154]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 77, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 459}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 设置声音 : 默认声音"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dDMS[慢]\\dac2026年6月22日 早"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dDMS[慢]\\dac桐人的公寓"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 关闭声音"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 默认模式"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [141]}, {"code": 250, "indent": 0, "parameters": [{"name": "闹钟", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 111, "indent": 0, "parameters": [0, 56, 1]}, {"code": 111, "indent": 1, "parameters": [0, 57, 1]}, {"code": 129, "indent": 2, "parameters": [1, 1, false]}, {"code": 129, "indent": 2, "parameters": [14, 0, false]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [24]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [19, 1, true]}, {"code": 117, "indent": 0, "parameters": [145]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 251, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Blow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 40, "indent": null}, {"code": 42, "parameters": [255], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 117, "indent": 0, "parameters": [143]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 460]}, {"code": 117, "indent": 0, "parameters": [154]}, {"code": 121, "indent": 0, "parameters": [77, 77, 1]}, {"code": 117, "indent": 0, "parameters": [184]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 77, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 467}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[27]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜早安口交]"]}, {"code": 111, "indent": 0, "parameters": [0, 271, 0]}, {"code": 111, "indent": 1, "parameters": [0, 300, 1]}, {"code": 119, "indent": 2, "parameters": ["跳到CG"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 121, "indent": 0, "parameters": [44, 44, 0]}, {"code": 121, "indent": 0, "parameters": [71, 71, 0]}, {"code": 203, "indent": 0, "parameters": [4, 0, 20, 9, 6]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 41, "parameters": ["桐人裸", 6], "indent": null}, {"code": 41, "parameters": ["桐人裸", 3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人裸", 6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人裸", 3], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 111, "indent": 0, "parameters": [0, 271, 1]}, {"code": 356, "indent": 1, "parameters": [">对话文字响声 : 设置声音 : 默认声音"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dDMS[慢]\\dac2026年6月27日 早"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dDMS[慢]\\dac桐人的公寓"]}, {"code": 356, "indent": 1, "parameters": [">对话文字响声 : 关闭声音"]}, {"code": 356, "indent": 1, "parameters": [">对话文字速度 : 修改模式 : 默认模式"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [141]}, {"code": 241, "indent": 0, "parameters": [{"name": "SMOOTH03s", "volume": 15, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<温柔的女声>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}\\}桐人君……"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<温柔的女声>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}已经早上喽……"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<温柔的女声>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac大懒虫，起床啦~"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [19, 1, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [145]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [19, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯？啊？亚丝娜？几点了？"]}, {"code": 213, "indent": 0, "parameters": [4, 6, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac已经十点多了，我们不是约好了九点在服装店门口见的吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我都和莉兹聊了好一会儿了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [19, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唔……抱歉抱歉，我也不知道怎么就没能起来……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我现在……"]}, {"code": 213, "indent": 0, "parameters": [19, 6, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [141]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊，还是好困……"]}, {"code": 213, "indent": 0, "parameters": [4, 7, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈焦急", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嚤~ 大大大懒虫！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [19, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac稍微等我一下，缓一下，就一下……"]}, {"code": 213, "indent": 0, "parameters": [4, 9, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈得意", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嘻嘻~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我知道一个方法能快速帮你恢复活力！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "volume": 90, "pitch": 70, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 111, "indent": 0, "parameters": [0, 271, 0]}, {"code": 111, "indent": 1, "parameters": [0, 300, 1]}, {"code": 118, "indent": 2, "parameters": ["跳到CG"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 223, "indent": 2, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘4", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 117, "indent": 0, "parameters": [122]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈呣~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊~~~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](叫醒口交？！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不会吧！！！我不是在做梦吧！！！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜！哦……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](忍不住发出了羞耻的声音！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这也不是第一次了，还这么狼狈怎么行？！之后必须忍住！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](但好困难，太舒服了吧！感觉鸡鸡快要融化了……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac明明上个月还那么嫌弃，现在居然这么熟练了~ )"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](不愧是血盟骑士团的才女副团长，闪光的亚丝娜啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是不是也说明……果然她对我的爱越来越深了！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呣啾~啾噜~滋溜~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](想要射了！！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](但是奇怪…！明明蛋蛋有种紧绷感，但却射不出……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可是快感还在继续！好刺激……！不行！必须忍住声音！！！)"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 10, true]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 40, "pitch": 100, "pan": 0}]}, {"code": 111, "indent": 0, "parameters": [0, 271, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [77, 77, 1]}, {"code": 121, "indent": 1, "parameters": [271, 271, 1]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 1, 82]}, {"code": 201, "indent": 1, "parameters": [0, 81, 6, 10, 2, 0]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [143]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[3] : 像素偏移[24,-3] : 时间[1]"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[24,0] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [3, 0, 21, 9, 0]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[0,0] : 时间[60]"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 34, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [3, 2, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜？你怎么停下来了？"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈欲言又止", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac刚才……真的舒服吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac一开始还有点反应，怎么后来就……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [3, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](完蛋！过于忍耐快感，让亚丝娜误会了！)"]}, {"code": 213, "indent": 0, "parameters": [3, 12, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[3] : 像素偏移[0,0] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 17, "indent": null}, {"code": 35, "indent": null}, {"code": 41, "parameters": ["桐人裸", 6], "indent": 0}, {"code": 14, "parameters": [0, -1], "indent": null}, {"code": 36, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人裸", 6], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, -1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 2, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac当然舒服啦！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我是担心我反应太大，被你笑话，实际上我忍得很辛苦呢！"]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈掩饰", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这样吗……？可是……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我含着舔了很久，你也没有…完全硬起来……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [3, 8, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](这个也确实奇怪……明明那么舒服，却一直半软半硬的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac就好像……自慰过度一样……可我昨晚也没有啊！)"]}, {"code": 213, "indent": 0, "parameters": [3, 6, false]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac真的很舒服的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac大概只是状态不好……"]}, {"code": 213, "indent": 0, "parameters": [3, 7, false]}, {"code": 213, "indent": 0, "parameters": [4, 11, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈沉默脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}也许是昨晚自慰过了，桐人君也是健全的男生，毕竟我们也好久没做了……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [4, 3, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈苦笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac舒服就好……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那…桐人君，你恢复精神了吗？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [3, 3, false]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯！多亏了刚才叫醒口交，我现在完全精神满满了！"]}, {"code": 213, "indent": 0, "parameters": [3, 9, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊，阿姨要求我们纯洁交际的……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac刚才我们算违规了吧……"]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君，我好好想过这个事情了。妈妈让我们纯洁交际的目的，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac肯定是担心意外怀孕。只要我们注意避孕，就不算违规。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这样吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可是……"]}, {"code": 213, "indent": 0, "parameters": [4, 7, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈心事脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君，你不会是真的对我失去兴趣了吧……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [3, 12, false]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac当然不会啦！又能像以前那样和你在现实里鱼水交融，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我开心都来不及呢！我们晚上就去酒店！"]}, {"code": 213, "indent": 0, "parameters": [3, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac时间不早了，现在先去约会的第一站，商业街。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac早饭就在咖啡馆吃~"]}, {"code": 213, "indent": 0, "parameters": [4, 8, false]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 40, "indent": null}, {"code": 42, "parameters": [255], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 121, "indent": 0, "parameters": [77, 77, 1]}, {"code": 121, "indent": 0, "parameters": [71, 71, 1]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 468]}, {"code": 117, "indent": 0, "parameters": [131]}, {"code": 117, "indent": 0, "parameters": [96]}, {"code": 117, "indent": 0, "parameters": [91]}, {"code": 117, "indent": 0, "parameters": [154]}, {"code": 121, "indent": 0, "parameters": [87, 87, 0]}, {"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 24 1"]}, {"code": 201, "indent": 0, "parameters": [0, 299, 11, 4, 2, 0]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 77, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 533}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 设置声音 : 默认声音"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 关闭声音"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 默认模式"]}, {"code": 203, "indent": 0, "parameters": [4, 0, 20, 9, 6]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜OL", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜OL", 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [141]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 121, "indent": 0, "parameters": [71, 71, 0]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人君……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 起床啦~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [19, 1, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [145]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 稍等，马上起来！"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 40, "indent": null}, {"code": 42, "parameters": [255], "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 29, "parameters": [2], "indent": null}, {"code": 2, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 121, "indent": 0, "parameters": [76, 76, 0]}, {"code": 121, "indent": 0, "parameters": [73, 73, 0]}, {"code": 121, "indent": 0, "parameters": [74, 74, 1]}, {"code": 121, "indent": 0, "parameters": [75, 75, 1]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈通用", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可能要抽血，就没给你带早饭。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 检查完以后，医院应该会提供营养餐饮。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们走吧~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯，早去早回。"]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 玩家 : 移动到 : 相对坐标[0,0]"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 37, "indent": null}, {"code": 3, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 82 1"]}, {"code": 121, "indent": 0, "parameters": [77, 77, 1]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 534]}, {"code": 117, "indent": 0, "parameters": [154]}, {"code": 121, "indent": 0, "parameters": [96, 96, 0]}, {"code": 121, "indent": 0, "parameters": [373, 373, 0]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 23, "y": 9}, {"id": 21, "name": "EV018空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 288}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 526, 0]}, {"code": 355, "indent": 1, "parameters": ["$gamePlayer.followers().follower(0).turnTowardCharacter($gameMap.event(this.eventId()));"]}, {"code": 122, "indent": 1, "parameters": [123, 123, 0, 0, 2]}, {"code": 355, "indent": 1, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 1, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 1, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 1, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 1, "parameters": ["} else {"]}, {"code": 655, "indent": 1, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 1, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 1, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 1, "parameters": ["}"]}, {"code": 111, "indent": 1, "parameters": [0, 355, 1]}, {"code": 117, "indent": 2, "parameters": [259]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 213, "indent": 2, "parameters": [-1, 3, true]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 22, "y": 7}, {"id": 22, "name": "EV022猪田", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 43, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 43, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 524}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头居家", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 28, "y": 22}, {"id": 23, "name": "EV023烹饪料理", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!food4", "direction": 8, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 29, "y": 21}, {"id": 24, "name": "EV024触发点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 524}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["NF_REM_NPC 1"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈心事", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac睡得很熟……没有发烧。"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "明日奈沉思", 0, 0, 310, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](但表情似乎有些难受的样子……)"]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(22).requestBalloon(8);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(22).requestBalloon(8);"], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac看来确实像莉兹说的，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac游戏里的冲击影响到了现实身体……"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈无奈", 0, 0, 310, 20, 100, 100, 255, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 0, 0, 25, true]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](唉……刚才也不该对莉兹那个态度。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但是当时的心情……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [22, 4, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[22] : 像素偏移[36,0] : 时间[60]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 241, "indent": 0, "parameters": [{"name": "MAP_不気味・緊張感", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [22, 12, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[22] : 像素偏移[0,0] : 时间[10]"]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 13, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{猪田！！！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 5, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你刚才干什么？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [22, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嘿嘿，学姐，别生气嘛！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我就是想……"]}, {"code": 213, "indent": 0, "parameters": [-1, 7, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈凛然脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你在想什么？今天早上已经任由你胡作非为了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在还不老实！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [22, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac怎么是我胡作非为…？！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你还记得当时说的话嘛……"]}, {"code": 213, "indent": 0, "parameters": [-1, 11, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈掩饰", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那只是……顺着当时气氛说的一些瞎话……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你应该懂的。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [22, 3, false]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不是吧？学姐明明也很享受！"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 213, "indent": 0, "parameters": [22, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在也确认了学长没什么大碍，不是正好……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac就像在厨房那次，肯定很刺激的！嘿嘿~"]}, {"code": 213, "indent": 0, "parameters": [-1, 5, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪！田！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [22, 12, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": 0}, {"code": 37, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在可不是游戏！！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [197, 197, 0, 3, 5, 22, 0]}, {"code": 111, "indent": 1, "parameters": [1, 197, 0, 20, 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [-1, 5, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我最近是不是太纵容你了？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [22, 7, false]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [197, 197, 0, 3, 5, 22, 0]}, {"code": 111, "indent": 1, "parameters": [1, 197, 0, 19, 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可是，学姐……"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 3, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈凛然脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你先回去吧！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [22, 5, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac好吧……"]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 213, "indent": 0, "parameters": [22, 8, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac对不起，刚才是我一时糊涂，以后再也不会这样了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac希望学姐消气以后能原谅我……"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 33, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 1], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唉……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 249, "indent": 0, "parameters": [{"name": "LNSM_ME06_Sleep2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜私服像素人", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac半个小时后……"]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 525]}, {"code": 201, "indent": 0, "parameters": [0, 252, 8, 15, 4, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 526}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 203, "indent": 0, "parameters": [0, 0, 29, 0, 0]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [-1, 7, true]}, {"code": 122, "indent": 0, "parameters": [133, 133, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "直叶沉思", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac好像很不舒服的样子，游戏里的事情怎么会弄成这样？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哥哥……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚……"]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 231, "indent": 0, "parameters": [2, "直叶担忧", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜……！"]}, {"code": 213, "indent": 0, "parameters": [-1, 5, true]}, {"code": 231, "indent": 0, "parameters": [2, "直叶不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](亚丝娜亚丝娜亚丝娜！心里就只有亚丝娜！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac如果你知道她的真实嘴脸……！)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 9, true]}, {"code": 231, "indent": 0, "parameters": [2, "直叶凛然", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](不！不行！绝对不能让哥哥知道！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在哥哥过于看重亚丝娜了，真相会让他痛不欲生的！)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜…我的…亚丝娜……"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "直叶严肃", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](我要想办法隐蔽地解决这个问题……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.target(6,1.2,15);"]}, {"code": 213, "indent": 0, "parameters": [19, 5, false]}, {"code": 117, "indent": 0, "parameters": [145]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac须乡你这混蛋！放开我的亚丝娜！！！"]}, {"code": 355, "indent": 0, "parameters": ["Galv.ZOOM.restore(90);"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [19, 2, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呃……小直？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我这是……"]}, {"code": 231, "indent": 0, "parameters": [2, "直叶温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哥哥，做噩梦了吗？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 36, "indent": null}, {"code": 34, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [141]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [19, 8, true]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 117, "indent": 0, "parameters": [145]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊，是……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac做梦。做了噩梦。"]}, {"code": 231, "indent": 0, "parameters": [2, "直叶微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac梦一般都是反的，哥哥不用放在心上。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，小直你说得对。"]}, {"code": 231, "indent": 0, "parameters": [2, "直叶担忧2", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哥哥，我听亚丝娜说，你在游戏里出了意外，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在好些了吗？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [19, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac睡了一觉感觉好很多了。"]}, {"code": 231, "indent": 0, "parameters": [2, "直叶苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac真的吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哥哥千万不要逞强啊！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [19, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac其实本来也没什么，不用担心。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac让我再缓一会儿，马上就起来……"]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [198, 198, 0, 3, 5, 4, 1]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 9, 0]}, {"code": 111, "indent": 2, "parameters": [6, 4, 6]}, {"code": 113, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [4, 3, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人你醒啦~ 正好，料理也弄好了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac一起过来吃吧。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 249, "indent": 0, "parameters": [{"name": "LNSM_ME06_Sleep2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 527]}, {"code": 201, "indent": 0, "parameters": [0, 252, 20, 16, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 643}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac 进入房间等直叶吗？"]}, {"code": 102, "indent": 0, "parameters": [["是", "否"], 1, 0, 2, 1]}, {"code": 402, "indent": 0, "parameters": [0, "是"]}, {"code": 119, "indent": 1, "parameters": ["开始事件"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "否"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["开始事件"]}, {"code": 117, "indent": 0, "parameters": [47]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 44, "parameters": [{"name": "Phone", "volume": 90, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "Phone", "volume": 90, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 111, "indent": 0, "parameters": [0, 409, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯？亚丝娜回电话了！"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 33, "indent": null}]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 251, "indent": 1, "parameters": []}, {"code": 231, "indent": 1, "parameters": [100, "手机", 0, 0, -240, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [97, "亚丝娜通话中", 0, 0, -240, 0, 100, 100, 255, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "接电话(Phone Pick Up)_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人君，抱歉，刚才真是太困了。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜的电话？"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 33, "indent": null}]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 251, "indent": 1, "parameters": []}, {"code": 231, "indent": 1, "parameters": [100, "手机", 0, 0, -240, 0, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [97, "亚丝娜通话中", 0, 0, -240, 0, 100, 100, 255, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "接电话(Phone Pick Up)_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人君，你已经出院了吧？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 抱歉，我昨天晚上睡得不是太好，所以才刚起床。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [48]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没事没事，我也经常睡懒觉。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还记得在SAO的时候，我在树下午睡，你来找我，结果就在我身旁睡着了。"]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嚤~ 又提起这件让人羞耻的事情……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那时候我们还没在一起嘛，在陌生男生身旁睡着，实在是太丢人了！"]}, {"code": 213, "indent": 0, "parameters": [-1, 4, true]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但我觉得很可爱呀！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唉，好想见你，不只是FOG，还有现实中……"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[10]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 50 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我也想……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊！对了！桐人君，我们直接视频不就好了吗？"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">对话框变形器 : 切换形状 : 变形样式[1]"]}, {"code": 356, "indent": 0, "parameters": [">对话框滤镜 : 全部 : 噪点滤镜 : 0 : 1"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对哟！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你等我一下，我手机的摄像头有点问题，去电脑上弄。"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Cancel2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 0, "parameters": [100]}, {"code": 235, "indent": 0, "parameters": [97]}, {"code": 203, "indent": 0, "parameters": [0, 0, 0, 0, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [17, 2, 27, 0, 0]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 644]}, {"code": 117, "indent": 0, "parameters": [154]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 29, "y": 20}, {"id": 25, "name": "EV025", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 525}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 356, "indent": 0, "parameters": ["SetArray 122 0 0"]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 批量事件[8,9,13] : 移动到 : 位置[29,0]"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 203, "indent": 0, "parameters": [4, 0, 8, 15, 4]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [23, 0, 7, 15, 0]}, {"code": 203, "indent": 0, "parameters": [26, 0, 7, 14, 0]}, {"code": 129, "indent": 0, "parameters": [41, 0, false]}, {"code": 129, "indent": 0, "parameters": [24, 1, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 42, "parameters": [0], "indent": 0}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [22, {"list": [{"code": 37, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 0, "parameters": [86, 86, 0]}, {"code": 203, "indent": 0, "parameters": [24, 0, 22, 9, 0]}, {"code": 117, "indent": 0, "parameters": [141]}, {"code": 121, "indent": 0, "parameters": [71, 71, 0]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "- <PERSON> Tender Feeling", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 250, "indent": 0, "parameters": [{"name": "门玲", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 250, "indent": 0, "parameters": [{"name": "门玲", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈回应", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac来啦，稍等！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 34, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 34, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [4, 8, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [25]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 玩家 : 移动显现 : 时间[30] : 方向角度[0] : 移动距离[0]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac小直，你好啊~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你怎么来了？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [133, 133, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "直叶苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜…姐姐……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你好。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我早上起来以后有点不好的预感，担心哥哥是不是出了状况，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac给他打电话也没接，所以过来看看。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [4, 3, false]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈岔开", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不愧是兄妹连心……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人现在确实状态不好。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "直叶惊讶", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哥哥到底什么情况？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈沉思", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可能是游戏里的一些事情对他产生了些负面效果，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac具体我也不是很清楚。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 231, "indent": 0, "parameters": [2, "直叶嘲弄", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}你也不清楚啊……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [4, 2, true]}, {"code": 231, "indent": 0, "parameters": [2, "直叶岔开", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜姐姐，你怎么没有陪着哥哥呢？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈通用", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人睡觉了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我正在做午饭，马上就好，你先进去看他吧。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 33, "indent": null}, {"code": 35, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 7, false]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "直叶不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}真是称职的女友呢……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 526]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 526}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 527}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 203, "indent": 0, "parameters": [4, 0, 18, 16, 2]}, {"code": 203, "indent": 0, "parameters": [16, 0, 21, 16, 4]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [18, 0, 18, 17, 0]}, {"code": 203, "indent": 0, "parameters": [5, 0, 20, 17, 0]}, {"code": 203, "indent": 0, "parameters": [23, 0, 29, 0, 0]}, {"code": 203, "indent": 0, "parameters": [26, 0, 29, 0, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 42, "parameters": [255], "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 33, "indent": null}, {"code": 38, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 129, "indent": 0, "parameters": [1, 0, false]}, {"code": 129, "indent": 0, "parameters": [41, 1, false]}, {"code": 121, "indent": 0, "parameters": [86, 86, 0]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 117, "indent": 0, "parameters": [143]}, {"code": 121, "indent": 0, "parameters": [71, 71, 0]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [24]}, {"code": 241, "indent": 0, "parameters": [{"name": "- <PERSON> Tender Feeling", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac小直，你真的不吃一点吗？亚丝娜的手艺非常棒哦！"]}, {"code": 213, "indent": 0, "parameters": [16, 7, true]}, {"code": 122, "indent": 0, "parameters": [133, 133, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "直叶苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我确实不饿。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还是说回你的病情……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哥哥，虽然现在暂时没事，但还是多休息为好。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac都是因为游戏受的伤，今天你就别上线了吧……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 34, "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 2]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人君，我也觉得你该好好休息的。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这算什么受伤啊！你们都太夸大其词了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我没事的。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac主要还是结衣的事，小直你也知道的，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac已经耽误了很久，我不想再等了。"]}, {"code": 213, "indent": 0, "parameters": [16, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "直叶苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac行，既然哥哥已经决定了，我也不多说什么了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac亚丝娜，你也跟我一起去公会吧？"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，吃完饭我帮你把家务做一下就回去上线。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac小直呢，你怎么安排？"]}, {"code": 231, "indent": 0, "parameters": [2, "直叶温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我等会帮亚丝娜姐姐做完家务就回去。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哥哥玩游戏也不要玩太长的时间哦~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈哈，好，知道啦~"]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 249, "indent": 0, "parameters": [{"name": "Inn", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 60, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [16, 8, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 121, "indent": 0, "parameters": [71, 71, 1]}, {"code": 121, "indent": 0, "parameters": [86, 86, 1]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 528]}, {"code": 117, "indent": 0, "parameters": [154]}, {"code": 117, "indent": 0, "parameters": [16]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 117, "indent": 0, "parameters": [152]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 528}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 2}, {"id": 26, "name": "EV026炊烟", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!$fsm_Object04n", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,48]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 28, "y": 21}, {"id": 27, "name": "EV027光圈", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 146, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 613}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-6,-12]"]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 771, 0]}, {"code": 117, "indent": 1, "parameters": [522]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 681, 0]}, {"code": 117, "indent": 1, "parameters": [357]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 644, 0]}, {"code": 117, "indent": 1, "parameters": [349]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 613, 0]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 121, "indent": 1, "parameters": [65, 65, 0]}, {"code": 250, "indent": 1, "parameters": [{"name": "KOK_Systemsounds_MenuOpen_01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 浅草那个项目的资料文件，我记得应该是放在E盘的……"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 213, "indent": 1, "parameters": [-1, 9, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 有了！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 给铃木前辈发过去吧。"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 250, "indent": 1, "parameters": [{"name": "sys_maou02", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 213, "indent": 1, "parameters": [-1, 3, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac OK，搞定。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 赶紧回医院吧，免得护士担心。"]}, {"code": 121, "indent": 1, "parameters": [65, 65, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 614]}, {"code": 121, "indent": 1, "parameters": [86, 86, 1]}, {"code": 203, "indent": 1, "parameters": [0, 0, 29, 19, 0]}, {"code": 203, "indent": 1, "parameters": [17, 0, 15, 7, 0]}, {"code": 117, "indent": 1, "parameters": [154]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 29, "y": 19}, {"id": 28, "name": "EV028", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 613}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 640, 0]}, {"code": 121, "indent": 1, "parameters": [86, 86, 0]}, {"code": 117, "indent": 1, "parameters": [143]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 613, 0]}, {"code": 117, "indent": 1, "parameters": [143]}, {"code": 203, "indent": 1, "parameters": [17, 0, 29, 24, 0]}, {"code": 203, "indent": 1, "parameters": [27, 0, 15, 7, 0]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 16, "y": 1}, {"id": 29, "name": "EV029", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 640}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 356, "indent": 0, "parameters": ["NF_REM_NPC 1"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["同人学校服", 1], "indent": null}, {"code": 41, "parameters": ["同人学校服", 0], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["同人学校服", 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["同人学校服", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [16, 0, 10, 19, 8]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 117, "indent": 0, "parameters": [172]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 回家的感觉真好~"]}, {"code": 122, "indent": 0, "parameters": [133, 133, 0, 0, 3]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "直叶温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哥哥，你今天怎么安排？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 上午休息一会，然后下午上线FOG吧。你呢？"]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "直叶理解", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 下午要去帮朋友处理点事情。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我中午给哥哥做个午饭，一起吃完再走。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯嗯，好呀。我先去洗个澡。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [16, 9, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "直叶感动", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哥哥等一下！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [16, 11, true]}, {"code": 231, "indent": 0, "parameters": [2, "直叶转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你从医院回来，需要好好清洁一下。所以……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [16, 4, false]}, {"code": 231, "indent": 0, "parameters": [2, "直叶微笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我来帮你洗吧！擦背之类的。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诶诶！？不太好吧……"]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 37, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "直叶焦急脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们是兄妹呀！有什么关系？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 小时候我们就经常一起洗呢！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [16, 7, true]}, {"code": 231, "indent": 0, "parameters": [2, "直叶叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哥哥是嫌弃我吗？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 122, "indent": 0, "parameters": [134, 134, 0, 0, 45]}, {"code": 102, "indent": 0, "parameters": [["\\dac 慌乱否定（\\c[28]变态度\\c[0]:\\c[18]\\v[134]\\c[0]） ", "\\dac 坦然解释"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "\\dac 慌乱否定（\\c[28]变态度\\c[0]:\\c[18]\\v[134]\\c[0]） "]}, {"code": 111, "indent": 1, "parameters": [1, 74, 1, 134, 4]}, {"code": 225, "indent": 2, "parameters": [5, 9, 15, false]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 0, 68], 15, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[28]变态度\\c[101]不足"]}, {"code": 119, "indent": 2, "parameters": ["非变态"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 250, "indent": 2, "parameters": [{"name": "Poison", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 2, "parameters": [[68, 255, 68, 170], 60, true]}, {"code": 111, "indent": 2, "parameters": [0, 407, 1]}, {"code": 122, "indent": 3, "parameters": [74, 74, 1, 0, 1]}, {"code": 356, "indent": 3, "parameters": ["addLog 变态度↑\\c[28]1"]}, {"code": 121, "indent": 3, "parameters": [407, 407, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 213, "indent": 2, "parameters": [-1, 12, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 当然不是……"]}, {"code": 213, "indent": 2, "parameters": [16, 4, false]}, {"code": 205, "indent": 2, "parameters": [16, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 2, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 231, "indent": 2, "parameters": [2, "直叶嘲弄脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 2, "parameters": [168]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 那就赶紧脱衣服吧！"]}, {"code": 235, "indent": 2, "parameters": [2]}, {"code": 213, "indent": 2, "parameters": [-1, 11, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 我，我，我知道了……"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 就一起洗吧。我自己脱衣服……"]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 223, "indent": 2, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 356, "indent": 2, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 356, "indent": 2, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 122, "indent": 2, "parameters": [100, 100, 0, 0, 641]}, {"code": 201, "indent": 2, "parameters": [0, 252, 9, 5, 8, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\dac 坦然解释"]}, {"code": 118, "indent": 1, "parameters": ["非变态"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 41, "parameters": ["同人学校服", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["同人学校服", 1], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 41, "parameters": ["同人学校服", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["同人学校服", 0], "indent": null}]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 小直，这和嫌弃没有关系。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 以前一起洗澡，是因为都还小。而现在我们都已经成年了。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你的心情我非常理解，其实我也是一样的。因为前段时间的误会，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我们疏远了，所以想各种方面都回到小时候的状态。对吧？"]}, {"code": 213, "indent": 1, "parameters": [16, 8, true]}, {"code": 231, "indent": 1, "parameters": [2, "直叶失落脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [168]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 可能是一部分原因吧……"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但是，现在的小直是一个多么有魅力的女性，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你自己可能根本没有概念。"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 如果一起洗澡，我都没有把握不去偷看！"]}, {"code": 205, "indent": 1, "parameters": [16, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 231, "indent": 1, "parameters": [2, "直叶为难", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [168]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哥哥，我不在意的！"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [-1, 6, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 小直，你现在还没谈男朋友，有些事情不懂。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我作为哥哥，也是过来人，你听我的就是了……"]}, {"code": 213, "indent": 1, "parameters": [16, 7, false]}, {"code": 205, "indent": 1, "parameters": [16, {"list": [{"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 231, "indent": 1, "parameters": [2, "直叶苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [168]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我已经大概明白了。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 以后会按哥哥说的，保持交往的尺度。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 205, "indent": 1, "parameters": [16, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 抱歉，小直……"]}, {"code": 205, "indent": 1, "parameters": [16, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 一本正经地说了这么多让你尴尬地话……"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 等你以后就会明白哥哥的苦心了。"]}, {"code": 213, "indent": 1, "parameters": [16, 6, true]}, {"code": 231, "indent": 1, "parameters": [2, "直叶嘲弄", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [168]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哥哥说得一脸愧疚的样子呀？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 是担心我生气吗？不会的啦~ 只是一点小事。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [-1, 3, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 那就好！"]}, {"code": 213, "indent": 1, "parameters": [16, 7, true]}, {"code": 231, "indent": 1, "parameters": [2, "直叶不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [168]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac \\}欲速则不达呀……"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [-1, 2, true]}, {"code": 213, "indent": 1, "parameters": [16, 3, true]}, {"code": 231, "indent": 1, "parameters": [2, "直叶温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [168]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哥哥去洗澡吧！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我去做一些料理的准备。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 642]}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 201, "indent": 1, "parameters": [0, 252, 11, 8, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 640}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 事件[16] : 移动显现 : 时间[10] : 方向角度[0] : 移动距离[0]"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 641}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[8]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直叶浴室擦背]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["桐人裸", 6], "indent": null}, {"code": 19, "indent": null}, {"code": 45, "parameters": ["$gamePlayer.setPriorityType(0);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["桐人裸", 6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gamePlayer.setPriorityType(0);"], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[16] : 像素偏移[0,-18] : 时间[1]"]}, {"code": 203, "indent": 0, "parameters": [16, 0, 9, 6, 8]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 33, "indent": null}, {"code": 41, "parameters": ["直叶现实裸", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["直叶现实裸", 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 241, "indent": 0, "parameters": [{"name": "Just-Saying", "volume": 20, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 245, "indent": 0, "parameters": [{"name": "1-HandjobLow", "volume": 25, "pitch": 50, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 水滴 15 100 0"]}, {"code": 213, "indent": 0, "parameters": [-1, 15, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](胸部……都碰到后背了……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊，那个，小直……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还是让我自己洗吧……"]}, {"code": 213, "indent": 0, "parameters": [16, 3, true]}, {"code": 122, "indent": 0, "parameters": [133, 133, 0, 0, 4]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "直叶安心脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没关系的，没关系的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 以前哥哥不是也帮我擦过背嘛~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 11, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那你，你也靠太近了啦……"]}, {"code": 213, "indent": 0, "parameters": [16, 9, true]}, {"code": 231, "indent": 0, "parameters": [2, "直叶温柔脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 话说回来，哥哥还是这么瘦呢，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不多吃一点可不行噢。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 有吗……？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊，小直，你这样，我会有点痒啦……"]}, {"code": 213, "indent": 0, "parameters": [16, 4, true]}, {"code": 231, "indent": 0, "parameters": [2, "直叶苦笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 搓背就是这样的啊，哥哥不好好配合的话，只会洗得更久！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [16, 1, true]}, {"code": 231, "indent": 0, "parameters": [2, "直叶担忧脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诶？这个是……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 12, true]}, {"code": 108, "indent": 0, "parameters": ["进入CG"]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊，那个，真是抱歉……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我毕竟也是男生……"]}, {"code": 213, "indent": 0, "parameters": [16, 3, true]}, {"code": 231, "indent": 0, "parameters": [2, "直叶嘲弄脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [168]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘛~ 我才是该道歉的那个啦。不过既然事已至此，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我也没办法了呢……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 诶……！？"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 250, "indent": 0, "parameters": [{"name": "fst_sand_light_walk_001", "volume": 10, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[16] : 像素偏移[0,0] : 时间[1]"]}, {"code": 121, "indent": 0, "parameters": [431, 431, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": false, "switch1Id": 431, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 641}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[8]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[直叶浴室手交]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 245, "indent": 0, "parameters": [{"name": "1-HandjobLow", "volume": 25, "pitch": 60, "pan": 0}]}, {"code": 241, "indent": 0, "parameters": [{"name": "Just-Saying", "volume": 20, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 水滴 15 100 0"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 小，小直，你在做什么啊……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 要是一直这样子的话不光没法洗身体，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且哥哥也会很难受吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不行，这、这个……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们，我们是兄妹……这种事情不可以！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没关系啦，这点事情在表兄妹之间根本不算什么，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且是我自愿帮哥哥做的！哥哥不用顾虑，只要享受就好！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊，啊，啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘶，真的不行……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 小直……快……快住手…我……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但是哥哥的小鸡鸡可不是这么想的哦。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不会是因为我的身体所以才变得这么兴奋了吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人"]}, {"code": 401, "indent": 0, "parameters": ["\\dac :我……！不是的，我没有那种想法，这只是生理现象！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对呀，这是自然生理反应，有什么好羞耻的？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 总之只要放心交给我就好，绝对会让哥哥变得舒服起来的。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊……啊啊，呼啊……唔……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过怎么感觉……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哥哥的鸡鸡比之前更大了？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我，我不知道……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 会觉得…恶心吗……？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么会？不管是大是小，只要是哥哥，我都喜欢！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 让我用行动证明吧~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呀啊啊……啊……咕唔……慢一点……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](小直为什么会这么熟练……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呼呼，哥哥的声音听上去好像很享受的样子呢。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 被我用手套弄肉棒真的有这么舒服吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我已经要……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还不行噢~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 欸……？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么了哥哥？刚才不是要我慢一点吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](每次快要不行的时候，她都会慢下来或者改变刺激的位置，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 让我不那么想射……感觉节奏完全被她掌握了一样……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那，还想要吗？"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯？"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 想……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这样的话……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯啊啊啊，嗯咕……嗯，嗯嗯嗯嗯！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哥哥这样子身体一颤一颤的真可爱~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊，哈啊…！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呐，哥哥，龟头这里是不是很舒服啊？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 手指每次划过这里的时候，整根小鸡鸡都在抖个不停呢。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊，小直……哈啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯，我在哦。不用担心，什么也不用害怕。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊，呀啊……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哥哥的小鸡鸡感觉在抖动呢，是想要射出来吗？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈啊……嗯……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯嗯，射出来吧，哥哥射精的样子我会好好看着的~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯，嗯啊……要射了……！"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 30, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[直叶浴室手交射精]"]}, {"code": 108, "indent": 0, "parameters": ["总图像张数90，总帧数360"]}, {"code": 230, "indent": 0, "parameters": [108]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精1", "volume": 45, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [108]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精1", "volume": 45, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [100, "705-公寓-直叶浴室侍奉", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 60, false]}, {"code": 232, "indent": 0, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, true]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊，出来了呢……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 竟然射了这么多出来……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊，哈啊，哈啊……哈啊啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哥哥这次射得不少呢！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 把住院期间积攒的全部射出来，一定感觉很舒服吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是……是很舒服……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那么，下次，让我再这样帮哥哥舒服吧~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 可是……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 放心好了，只要你不说，我不说，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这就永远只是属于我们兄妹的……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直叶:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 小~ 秘~ 密~ "]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 41, "parameters": ["同人学校服", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["同人学校服", 0], "indent": null}]}, {"code": 241, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 235, "indent": 0, "parameters": [100]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 121, "indent": 0, "parameters": [431, 431, 1]}, {"code": 111, "indent": 0, "parameters": [0, 909, 0]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 1, 82]}, {"code": 201, "indent": 1, "parameters": [0, 83, 8, 11, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "LNSM_ME06_Sleep2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 642]}, {"code": 201, "indent": 1, "parameters": [0, 252, 10, 15, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 642}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 356, "indent": 0, "parameters": ["NF_REM_NPC 1"]}, {"code": 117, "indent": 0, "parameters": [143]}, {"code": 121, "indent": 0, "parameters": [71, 71, 0]}, {"code": 203, "indent": 0, "parameters": [16, 0, 8, 15, 6]}, {"code": 205, "indent": 0, "parameters": [16, {"list": [{"code": 38, "indent": null}, {"code": 41, "parameters": ["直叶", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["直叶", 1], "indent": null}]}, {"code": 111, "indent": 0, "parameters": [0, 407, 0]}, {"code": 205, "indent": 1, "parameters": [16, {"list": [{"code": 17, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 33, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 205, "indent": 1, "parameters": [16, {"list": [{"code": 17, "indent": null}, {"code": 33, "indent": null}, {"code": 35, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 203, "indent": 0, "parameters": [23, 0, 7, 15, 0]}, {"code": 203, "indent": 0, "parameters": [26, 0, 7, 14, 0]}, {"code": 111, "indent": 0, "parameters": [0, 407, 0]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 41, "parameters": ["同人学校服", 1], "indent": null}, {"code": 41, "parameters": ["同人学校服", 0], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["同人学校服", 1], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["同人学校服", 0], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 241, "indent": 1, "parameters": [{"name": "- Everyday Life", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 213, "indent": 1, "parameters": [-1, 11, true]}, {"code": 213, "indent": 1, "parameters": [16, 4, true]}, {"code": 205, "indent": 1, "parameters": [16, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 205, "indent": 1, "parameters": [16, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 122, "indent": 1, "parameters": [133, 133, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 1, "parameters": [2, "直叶嘲弄脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [168]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<直叶>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 从刚才洗完澡以后，哥哥就一直盯着我，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我可都要不好意思啦~"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [-1, 12, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊！抱歉抱歉！"]}, {"code": 213, "indent": 1, "parameters": [16, 3, false]}, {"code": 205, "indent": 1, "parameters": [16, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 17, "indent": null}, {"code": 33, "indent": null}, {"code": 35, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null}]}, {"code": 213, "indent": 1, "parameters": [-1, 7, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 唉……"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "浴室冲水", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 41, "parameters": ["同人学校服", 1], "indent": null}, {"code": 41, "parameters": ["同人学校服", 0], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["同人学校服", 1], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 41, "parameters": ["同人学校服", 0], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 250, "indent": 1, "parameters": [{"name": "Equip3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [45]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 1, "parameters": [2, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 1, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 去房间等小直做好午饭吧。"]}, {"code": 117, "indent": 0, "parameters": [128]}, {"code": 121, "indent": 0, "parameters": [71, 71, 1]}, {"code": 121, "indent": 0, "parameters": [86, 86, 0]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 643]}, {"code": 117, "indent": 0, "parameters": [154]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 39, "indent": null}, {"code": 36, "indent": null}, {"code": 33, "indent": null}, {"code": 19, "indent": null}, {"code": 35, "indent": null}, {"code": 41, "parameters": ["!Flame", 7], "indent": null}, {"code": 40, "indent": null}, {"code": 45, "parameters": ["$gameMap.event(24).setPriorityType(1);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["!Flame", 7], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(24).setPriorityType(1);"], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [24, 0, 19, 13, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 643}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 645}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-白天光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 全图事件 : 立即归位"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [16, 0, 18, 16, 6]}, {"code": 203, "indent": 0, "parameters": [18, 0, 18, 17, 0]}, {"code": 203, "indent": 0, "parameters": [5, 0, 20, 17, 0]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 249, "indent": 0, "parameters": [{"name": "LNSM_ME20_Fanfare5", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [16, 4, true]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 全图事件 : 立即归位"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 39, "indent": null}, {"code": 29, "parameters": [6], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [6], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 117, "indent": 0, "parameters": [145]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 213, "indent": 0, "parameters": [19, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](登录游戏吧。)"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip1", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 117, "indent": 0, "parameters": [142]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "KOK_Systemsounds_MenuOpen_05", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 180, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 42, "parameters": [0], "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "KOK_Systemsounds_MenuOpen_01", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 0, "parameters": [86, 86, 1]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 646]}, {"code": 121, "indent": 0, "parameters": [298, 298, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [0, 364, 5, 17, 8, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 646}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 770}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 252 2 公寓房间-夜晚光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 252 3 公寓房间-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 245, "indent": 0, "parameters": [{"name": "浴室冲水", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [90]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 视频联系一下亚丝娜，确认今天的具体安排。"]}, {"code": 117, "indent": 0, "parameters": [122]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [17, 2, 27, 0, 0]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 771]}, {"code": 117, "indent": 0, "parameters": [128]}, {"code": 117, "indent": 0, "parameters": [131]}, {"code": 117, "indent": 0, "parameters": [154]}, {"code": 117, "indent": 0, "parameters": [144]}, {"code": 121, "indent": 0, "parameters": [86, 86, 0]}, {"code": 121, "indent": 0, "parameters": [71, 71, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 771}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 2}, null]}