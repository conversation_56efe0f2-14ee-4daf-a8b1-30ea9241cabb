{"autoplayBgm": false, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 30, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "!亚丝娜家一楼-夜晚", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 44, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 18, 18, 18, 18, 18, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 16, 16, 16, 16, 16, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 18, 18, 18, 18, 18, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 16, 16, 16, 16, 16, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 18, 17, 17, 17, 17, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 16, 16, 16, 15, 15, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 15, 15, 15, 15, 15, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 15, 15, 15, 15, 15, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 16, 16, 15, 15, 15, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 16, 16, 15, 15, 15, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 0, 0, 0, 20, 20, 20, 20, 20, 20, 20, 20, 0, 0, 0, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 0, 0, 0, 20, 20, 20, 20, 20, 20, 20, 20, 0, 0, 0, 5, 5, 5, 5, 5, 5, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 19, 20, 20, 20, 19, 20, 20, 20, 0, 0, 0, 5, 5, 0, 0, 0, 5, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 19, 19, 19, 19, 19, 19, 19, 19, 20, 0, 0, 5, 5, 0, 0, 0, 5, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 20, 19, 19, 19, 19, 19, 19, 19, 20, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 5, 0, 0, 0, 19, 19, 19, 20, 20, 20, 19, 19, 19, 11, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 19, 19, 19, 19, 19, 19, 19, 19, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 19, 19, 19, 19, 19, 19, 19, 19, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 21, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 351 2 亚丝娜家一楼-夜晚光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 351 3 亚丝娜家一楼-夜晚阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 18, "y": 7}, {"id": 2, "name": "浴室门", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 2, "characterIndex": 5}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 28, "y": 7}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door6", "direction": 2, "pattern": 2, "characterIndex": 2}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 8, "y": 12}, {"id": 4, "name": "EV004亚丝娜", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 44, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "亚丝娜 居家服", "direction": 4, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 43, "y": 28}, {"id": 5, "name": "EV005京子", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 48, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "京子短发", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 42, "y": 29}, {"id": 6, "name": "EV006猪田", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 43, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "猪头裸套套(1)", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 43, "y": 29}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 260, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "ERO_Dungeon1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["LAYER 351 2 亚丝娜家一楼-夜晚光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 351 3 亚丝娜家一楼-夜晚阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 121, "indent": 0, "parameters": [43, 43, 0]}, {"code": 121, "indent": 0, "parameters": [44, 44, 0]}, {"code": 203, "indent": 0, "parameters": [4, 0, 14, 21, 2]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[0,6] : 时间[1]"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}, {"code": 41, "parameters": ["亚丝娜 居家服", 1], "indent": null}, {"code": 41, "parameters": ["亚丝娜vr眼罩", 0], "indent": null}, {"code": 16, "indent": null}, {"code": 45, "parameters": ["$gameMap.event(4).setPriorityType(0);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜vr眼罩", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(4).setPriorityType(0);"], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [6, 0, 14, 21, 8]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,12] : 时间[1]"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 19, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [9, 0, 18, 19, 2]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-68, -68, 0, 68], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [6, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac虽然带着套子，虽然是迷奸，但再次肏到母猪老婆的真人……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这份满足感，啧啧，爽啊！一发完全不过瘾！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不过没多的套了……"]}, {"code": 213, "indent": 0, "parameters": [6, 7, true]}, {"code": 213, "indent": 0, "parameters": [6, 9, true]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗨！谁规定这玩意只能用一次的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac带着旧的继续用，不就完事了？"]}, {"code": 213, "indent": 0, "parameters": [6, 3, true]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[-9,12] : 时间[15]"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[-9,18] : 时间[15]"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [6, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯？"]}, {"code": 213, "indent": 0, "parameters": [6, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不是吧……怎么红成这样？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac妈的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac估计是刚才冲刺的时候太用力了……"]}, {"code": 213, "indent": 0, "parameters": [6, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这咋搞呀？再操一次的话，估计会肿的吧……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac如果母猪报警，我岂不是要进去捡肥皂了……"]}, {"code": 213, "indent": 0, "parameters": [6, 5, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac操！操！操！六万块钱只干了一发，真尼玛亏！\\|"]}, {"code": 401, "indent": 0, "parameters": ["\\dac早知道就再买些消肿的药水备着了……"]}, {"code": 213, "indent": 0, "parameters": [6, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唉，只能见好就收了。"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,0] : 时间[1]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip3", "volume": 90, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip2", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜 居家服", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 1], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [6, 0, 21, 20, 8]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 41, "parameters": ["猪田倒地图", 0], "indent": null}, {"code": 41, "parameters": ["猪头居家", 0], "indent": 0}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["猪田倒地图", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["猪头居家", 0], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [6, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac送几条内裤喽，就当作你没服侍好老公的补偿呗~"]}, {"code": 250, "indent": 0, "parameters": [{"name": "LNSM_SE08_Sense8", "volume": 50, "pitch": 100, "pan": -40}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [6, 1, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 34, "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](下线的声音？！没睡着？！)"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 16, "indent": null}, {"code": 41, "parameters": ["猪田倒地图", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["猪田倒地图", 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [55]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 事件[9] : 标准弹跳 : 时间[15] : 高度[42]"]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 250, "indent": 0, "parameters": [{"name": "Equip1", "volume": 50, "pitch": 100, "pan": -40}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈失落脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["明日奈白衬衫.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唔…空调是开着的呀，为什么这么热…？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但又感觉…特别放松…也特别困……"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "明日奈沉默脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["明日奈白衬衫.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac真的不行了…澡明天再洗吧……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 45, "parameters": ["$gameMap.event(4).requestBalloon(10);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(4).requestBalloon(10);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [6, 8, true]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 41, "parameters": ["猪头居家", 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["猪头居家", 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [6, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我操！吓死了！"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 1, "indent": 0}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [6, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac安眠药现在才他妈生效吗？之前如果母猪提前下线……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac狗的！真是后怕！"]}, {"code": 213, "indent": 0, "parameters": [6, 8, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [6, 5, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我日你八辈祖宗！婊子养的奸商！！！"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 1]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([329, 1, 'A'], true);"]}, {"code": 201, "indent": 0, "parameters": [0, 329, 19, 7, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 260, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 351 2 亚丝娜家一楼-夜晚光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 351 3 亚丝娜家一楼-白天阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 121, "indent": 0, "parameters": [43, 43, 0]}, {"code": 121, "indent": 0, "parameters": [44, 44, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [4, 0, 14, 21, 0]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[0,6] : 时间[1]"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 41, "parameters": ["亚丝娜 居家服", 1], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["亚丝娜 居家服", 1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [5, 0, 28, 8, 2]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 41, "parameters": ["京子短发", 2], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["京子短发", 2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [6, 0, 27, 18, 2]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 41, "parameters": ["猪头居家", 0], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["猪头居家", 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [8, 0, 27, 19, 0]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [6, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊呸呸呸！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这酒也太难喝了！"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [1, 0], "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [1, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,-24] : 时间[30]"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "浴室冲水", "volume": 50, "pitch": 150, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 50, "pitch": 150, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "吞口水", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac咕噜……"]}, {"code": 213, "indent": 0, "parameters": [5, 3, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "京子微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈哈哈哈，不能喝酒还要逞强，你要笑死我~ "]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 11, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啥逞强！你这酒有问题啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac红酒不应该是甜的吗？"]}, {"code": 213, "indent": 0, "parameters": [5, 6, true]}, {"code": 231, "indent": 0, "parameters": [2, "京子叹气", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是干红……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 4, false]}, {"code": 231, "indent": 0, "parameters": [2, "京子羞辱", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呵呵，年龄上已经成人了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac行为举止就是个小毛孩~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}不过，有点可爱~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 5, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我毛孩？我不成熟？你扯淡！！！"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,0] : 时间[9]"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [-1, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [-1, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac有些时候我都觉得自己成熟得可怕！！！"]}, {"code": 213, "indent": 0, "parameters": [5, 3, false]}, {"code": 231, "indent": 0, "parameters": [2, "京子嘲弄", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哎~呀~~~\\i[90] 主人消消气~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac母狗倒想看看你怎么展示男性雄风的~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": 0}, {"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [6, 7, true]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 1, "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac臭母狗！你别嚣张我跟你说，等会肏得你下不了床…！"]}, {"code": 213, "indent": 0, "parameters": [6, 9, true]}, {"code": 213, "indent": 0, "parameters": [6, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呃……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我套子用完了，你家里有的吧？"]}, {"code": 213, "indent": 0, "parameters": [5, 11, true]}, {"code": 231, "indent": 0, "parameters": [2, "京子转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不用了。我刚才去厕所就是给那里放入液体避孕套的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你这坏蛋不是不喜欢带套的感觉嘛……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac液体避孕套？还有这种东西！牛逼！"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我老婆对我真好~"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [5, 11, true]}, {"code": 231, "indent": 0, "parameters": [2, "京子别扭脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哼~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "京子通常脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我去看看女儿的情况，你先去卧室等我。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac上楼第一个房间就是。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 8, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Switch1", "volume": 50, "pitch": 150, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["LAYER 351 3 亚丝娜家一楼-夜晚阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 223, "indent": 0, "parameters": [[-68, -68, 0, 68], 5, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 1, "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 1, "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 45, "parameters": ["$gameMap.event(5).requestBalloon(8);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(5).requestBalloon(8);"], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [6, 9, true]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": 0}, {"code": 2, "indent": 0}, {"code": 45, "parameters": ["$gameMap.event(6).requestBalloon(3);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 14, "parameters": [-2, 0], "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(6).requestBalloon(3);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [-2, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[-30,0] : 时间[20]"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [5, 1, true]}, {"code": 231, "indent": 0, "parameters": [2, "京子惊讶脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你、你怎么来了？！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac想干什么？！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我们就在这里做吧~"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[-12,0] : 时间[10]"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[18,0] : 时间[5]"]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 212, "indent": 0, "parameters": [6, 159, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[0,0] : 时间[10]"]}, {"code": 213, "indent": 0, "parameters": [6, 12, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 34, "indent": null}, {"code": 19, "indent": null}, {"code": 35, "indent": null}, {"code": 14, "parameters": [0, 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [2]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[0,0] : 时间[10]"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 34, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 13, "indent": null}, {"code": 13, "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[0,21] : 时间[5]"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 33, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 212, "indent": 0, "parameters": [6, 159, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[0,0] : 时间[10]"]}, {"code": 213, "indent": 0, "parameters": [5, 5, true]}, {"code": 231, "indent": 0, "parameters": [2, "京子说教脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你疯了？！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 5, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac是你发神经才对吧！刚才差点撞死我！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我不就是说在这里做而已……"]}, {"code": 213, "indent": 0, "parameters": [6, 9, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦嚯！我懂了！"]}, {"code": 213, "indent": 0, "parameters": [6, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](呵呵，正好利用这个机会拿捏她一波~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我真是个天才！)"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你女儿虽然或多或少清楚你私生活不是很检点，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你也不太在乎。但乱搞的对象是女儿的同校同学……"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还是之前被你说成是变态罪犯的家伙，情况就又不同了吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你是不是担心她彻底瞧不起你，再也不认你这个妈了？"]}, {"code": 213, "indent": 0, "parameters": [5, 5, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "京子生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{你……！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "京子斥责脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我俩的事，别给我扯上明日奈！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 3, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 14, "parameters": [1, -1], "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [1, -1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呵呵，刚才还说我是小毛孩，现在知道怕了？"]}, {"code": 213, "indent": 0, "parameters": [5, 5, true]}, {"code": 231, "indent": 0, "parameters": [2, "京子轻蔑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac怕？我呸！你以为我怕你吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac配合你玩玩，叫你两声主人，就有点不知道天高地厚了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [2], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "京子固执", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你的全部信息我早就调查清楚了！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 8, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 29, "parameters": [2], "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [2], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "京子冷漠", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac父母都是无业流民，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac整天只知道打柏青哥和赌马。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac母亲娘家还算是名门望族，但宗家和你们早就断绝来往。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac只有分家可怜你们，定期给些施舍，你们全家才勉强过活。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "京子轻蔑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还有你之前诈唬我的所谓黑料……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不过是社团底层混混在八卦杂志上搜刮来的小道消息。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 1, true]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 231, "indent": 0, "parameters": [2, "京子羞辱", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac存在电脑里所谓最后的底牌我也看了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac根本就是一些加密AV而已。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 6, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你…你都知道啦？那你还……"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "京子得意", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哼，不拆穿你不过是因为我一时兴起，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac陪你玩玩需要释放压力罢了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac虽然只是个无能的小胖子，不过倒是有些特长。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 11, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "京子嘲弄脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}而且…人也单纯，还算有点体贴……\\{"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}至少比起那些没心没肺的小白脸好一些……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "京子逞强脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac所以，你如果能继续安分守己，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我不介意还是像之前那样陪你过家家。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 3, true]}, {"code": 231, "indent": 0, "parameters": [2, "京子微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但如果你敢把明日奈牵扯进来……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不排除你会出个意外事故什么的~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 12, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](这颠婆…！操操操！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac尿都要吓出来了！冷静冷静！)"]}, {"code": 213, "indent": 0, "parameters": [5, 4, true]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[24,0] : 时间[15]"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "京子愉快", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac刚才是不是吓到你了。我是关心则乱。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac明日奈对我来说非常重要，你也理解我一下，不要拿她开玩笑……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "京子羞辱", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac就像我刚才说的，我们回复到吵架前的状态，好吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我们上楼继续我们刚才的游戏吧~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 356, "indent": 0, "parameters": [">持续动作 : 事件[6] : 立即终止动作"]}, {"code": 213, "indent": 0, "parameters": [6, 5, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](妈的，真不愧是管理大企业的，这一手PUA玩的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac打一巴掌给个蜜枣，还问甜不甜。)"]}, {"code": 213, "indent": 0, "parameters": [6, 8, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](但这个时候我如果认怂，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac以后她嘴上叫我主人，实际是我成她的玩具了……)"]}, {"code": 213, "indent": 0, "parameters": [6, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](必须赌一把！)"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 18, "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[5] : 像素偏移[0,0] : 时间[15]"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呵呵呵呵，哈哈哈哈！"]}, {"code": 231, "indent": 0, "parameters": [2, "京子质疑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你笑什么？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac被你看扁了啊，老婆~"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac自始至终，我接近你、威胁你、侵犯你的，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac目的，只有一个……"]}, {"code": 213, "indent": 0, "parameters": [5, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我看到你第一眼就想占有你！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac所以才有了后来一系列你觉得幼稚的行动！"]}, {"code": 213, "indent": 0, "parameters": [5, 11, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac刚才提到明日奈，不是我想把她扯进来，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac更不是我对这种发育不完全的小丫头有什么非分之想……"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac也都是因为你！我爱你！！！"]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [5, 12, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 231, "indent": 0, "parameters": [2, "京子惊讶脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac瞎…瞎说什么？！我这种老太婆……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你你你个小屁孩，懂什么爱……？！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 3, true]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac在你眼里我是很不成熟，我也知道我自己就是个loser，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但我也是个男人，我想让自己的女人幸福的心情有什么错？！"]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac上次在酒店门口遇到明日奈，你那个惊慌失措的样子，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你记得吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我们闲聊的时候，你无数次向我抱怨把她带坏的黑衣小子，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你记得吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac每次只要你的压力一大，就去为难你非常重要的明日奈，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你记得吧？"]}, {"code": 213, "indent": 0, "parameters": [5, 8, true]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac明日奈根本就是你的心病……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我想帮你！想让你幸福！"]}, {"code": 213, "indent": 0, "parameters": [5, 11, true]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我不懂什么心理学，也不知道怎么调和亲子关系……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但我有个自己的蠢办法：压力大，射出来就好了！"]}, {"code": 213, "indent": 0, "parameters": [5, 7, false]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 15, "parameters": [25], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [25], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "京子生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac什么歪理！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac歪理不歪理试过才知道！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac之前在学校不也很刺激吗？"]}, {"code": 213, "indent": 0, "parameters": [5, 11, false]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "京子转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac一般般吧……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [6, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[6] : 像素偏移[-36,0] : 时间[15]"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [5, 1, true]}, {"code": 231, "indent": 0, "parameters": [2, "京子微惊脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](力气好大！我被他控制住了！)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 3, true]}, {"code": 231, "indent": 0, "parameters": [2, "京子嘲弄脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["京子内衣.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](没错，所以…我完全没办法反抗他……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [6, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac听我的，好老婆，试一试！"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 3]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 260, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, 6, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, 5, 1]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 29, 1]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 14, 1]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 2, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 2, "parameters": [{"code": 1, "indent": null}]}, {"code": 205, "indent": 2, "parameters": [5, {"list": [{"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 2, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 18, "indent": null}]}, {"code": 123, "indent": 2, "parameters": ["A", 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 260, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">持续动作 : 事件[6] : 左右震动 : 持续时间[60] : 周期[6] : 震动幅度[1]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 260, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "Instrumental-Mothers-Daughter", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "a01", "volume": 10, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[20]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[京子床前正常位慢]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [36], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 50, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [44], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 50, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [32], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [36], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 50, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [44], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 50, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [32], "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦哦……哒咩~\\i[90] "]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊~ \\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac怎么样！刺激吧？过瘾吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊…小声……小声点！嗯啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](这死小鬼真的是不管不顾！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac幸好我的明日奈属于睡着后很难醒的类型……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac放心吧，你和我靠的近才觉得大声，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac实际上我的动作和声音都比平时轻柔多了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](而且安眠药都已经生效了，怎么可能会醒？)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac谁，谁知道你……？哦哦~\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呵，别人不知道就不知道吧，你不知道可就怪了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我反正也是感觉到，现在你下面的小嘴夹得特别紧呢~ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac一点都不像……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac生过女儿的样子！该不会学姐根本不是你亲生的吧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac什……哈啊，哈——嗯呜，你这混蛋……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不是混蛋哦，是主人！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你不听话，我就把她弄醒！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不要！主人……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊啊啊，啊，啊~~~\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这才对嘛，现在回答我的问题，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac明日奈是不是你亲生的？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac当然是啊……啊嗯……\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我十月怀胎…生下来的…嗯…\\i[90] 我的…宝贝女儿……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那你的屄怎么还这么紧？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac有…有在做凯格尔……啊啊~\\i[90] "]}, {"code": 401, "indent": 0, "parameters": ["\\dac平时都有做……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啧啧，不愧是女强人，真是自律！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac之前那么多小白脸都没把你肏松~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac他们…他们只是脸好看…啊~\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac大小，不行……比你差远了……啊嗯嗯……\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac难怪你现在不去找他们了……"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[京子床前正常位快]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "ピストングチュ音多め（低速）", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 a10 10 100 20"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嘿嘿，已经离不开我了吧？是不是？母狗！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac是…是的……我离不开你了，主人……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊啊嗯嗯！\\i[90]\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac说的好！那你，你接着说，既然是亲生的，又这么看重她……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可为什么总是虐待明日奈？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我没有……啊，呀……！我只是……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊啊…想让她…更优秀……啊，啊啊嗯……！！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac借口！借口！你们这种家长我最知道的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac明明自己做不到，却对儿女提一大堆不切实际的要求！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯？什么……你，你说什么…啊嗯！\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊，啊嗯……！\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我又不是你们开的小号？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac凭什么要过你们认为正确的人生？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](什么…？是在说他自己的事吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但明日奈…好像也说过相似的话……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac发现我达不到要求了，就又像垃圾一样把我给丢掉！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac妈的！越想越气！我要好好教育你！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac给我起来！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你…！你要干什么？！"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 床前后入位"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 245, "indent": 0, "parameters": [{"name": "a05", "volume": 10, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[京子床前后入慢]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [32], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 50, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [36], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 50, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [36], "indent": null}, {"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 50, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [4], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [32], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 50, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [36], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 50, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [36], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）１回", "volume": 50, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯呜……！主人…哈……哦…太激烈……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦齁！\\i[90] 啊啊啊……\\i[90]\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac就是要激烈！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你悔改吧！"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, -72, -101, 120, 120, 255, 0, 10, true]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 10, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我操！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac儿女不是父母的小号！"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, -72, -101, 120, 120, 255, 0, 10, true]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 10, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我操！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac儿女不是父母的出气筒！"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, -72, -101, 120, 120, 255, 0, 10, true]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 10, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我操！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac儿女不是父母的木偶！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不是…啊嗯嗯…我没，那种……"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, -72, -101, 120, 120, 255, 0, 10, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还犟嘴！看我不操翻你！"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac三十分钟后……"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 60, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈啊，哈，啊嗯…！不行了……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac知道厉害了吧？你这个不懂女儿心情的母狗！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac和我那对老不死爹妈是一样的，根本不配当父母！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呜呜…不要，不要再说了…哈啊嗯……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac求你…主人……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac好！我不说！你说！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac说！说你不配当明日奈的妈妈！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呜呜呜…我不配……啊啊！\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我不配当明日奈的妈妈……哦齁齁齁齁齁~\\i[90]\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac贱母狗，你简直是白活了这么久！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你真该重头好好学学……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没错…没错，没错！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac骚女儿，叫爸爸！ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac诶？！啊啊啊~\\i[90]"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[京子床前后入快]"]}, {"code": 245, "indent": 0, "parameters": [{"name": "ピストングチュ音多め（中速）", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 a02 10 100 20"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac因为你根本不配当一个母亲！你应该当女儿才对！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac所以让爸爸来好好教育你！快！叫爸爸！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac爸爸！爸爸！呜呜……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊啊啊！这太激烈了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac求爸爸教你怎么做人！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac教我！啊！啊啊嗯嗯……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac女儿都听爸爸的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你永远记住了！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我是你的老公，是你的主人，是你的爸爸！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你以后什么都要听我的！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你的一切都是我的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，啊哇，我…啊，是你的，啊嗯……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac明天，就去跟明日奈道歉！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac什…什么…啊，啊，啊嗯嗯……！\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac有错就要认，挨打要立正！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你最近给她派那么多活，难道不是在虐待她？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呜呜……嗯，爸爸，啊哇，女儿…错了啊，啊嗯……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还干涉她的人际交往，是不是在侵犯她的自由？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈，啊…啊，啊嗯…！\\i[90] 对不起……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈啊，爸爸，啊啊啊……\\i[90]\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你看你个骚样！提到明日奈就这么兴奋吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不会是对着自己女儿发情了吧？"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 225, "indent": 0, "parameters": [8, 7, 15, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不……没有！哈啊，啊啊啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呵呵，夹得更紧了，我看就是！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你这个喜欢乱伦的骚女儿！和爸爸乱伦爽不爽？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不行…这，不要说……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊~\\i[90]\\i[90]\\i[90] 太刺激了……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac喜不喜欢爸爸操你？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac过不过瘾啊？！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈，哈啊…哈啊啊啊…哈……"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, -180, -101, 150, 150, 255, 0, 10, true]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 10, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你说话啊！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊…啊啊嗯……！！\\i[90]\\i[90]\\i[90]"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈，啊…啊，啊嗯…！\\i[90]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac母狗脑子好像短路，呵呵~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊！这骚逼这爽啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唉！不好！大意了，要射了……！"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[京子床前后入射精]"]}, {"code": 230, "indent": 0, "parameters": [200]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精-01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 242, "indent": 0, "parameters": [4]}, {"code": 230, "indent": 0, "parameters": [165]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 15, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 121, "indent": 0, "parameters": [260, 260, 1]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 40, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 117, "indent": 0, "parameters": [91]}, {"code": 201, "indent": 0, "parameters": [0, 82, 18, 13, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 23, "y": 18}, {"id": 8, "name": "EV008酒杯", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!food1", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 42, "y": 28}, {"id": 9, "name": "EV009钥匙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 0, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 4, "moveType": 0, "priorityType": 0, "stepAnime": true, "through": true, "trigger": 0, "walkAnime": true}], "x": 43, "y": 27}, null, null, null, null, null, null, null]}