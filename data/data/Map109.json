{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 22, "note": "", "parallaxLoopX": true, "parallaxLoopY": true, "parallaxName": "!影像店大厅", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 1, "width": 29, "data": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 5, 5, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 5, 5, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 5, 5, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 5, 5, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 109 2 影像店大厅-光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 109 3 影像店大厅-阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 298, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 121, "indent": 0, "parameters": [298, 298, 1]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 9, "y": 3}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 725, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 317, 25, 24, 0, 0]}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 500, 4]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 495, 1]}, {"code": 111, "indent": 2, "parameters": [0, 361, 1]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.followers().follower(0).turnTowardPlayer();"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac学姐……"]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.turnTowardCharacter($gamePlayer.followers().follower(0));"]}, {"code": 213, "indent": 3, "parameters": [-1, 2, true]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(3);"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac那个…你可不可以在外面等我一下……"]}, {"code": 401, "indent": 3, "parameters": ["\\dac我想…买几个影片回去看……可以吗？"]}, {"code": 213, "indent": 3, "parameters": [-1, 11, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac我在旁边你不好意思挑选对吧？"]}, {"code": 401, "indent": 3, "parameters": ["\\dac这也是男生的正常需求啦，没关系的。"]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(4);"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac学姐最好了！"]}, {"code": 221, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [361, 361, 0]}, {"code": 356, "indent": 3, "parameters": ["NF_REM_NPC 1"]}, {"code": 355, "indent": 3, "parameters": ["$gameSelfSwitches.setValue([317, 1, 'A'], true);"]}, {"code": 201, "indent": 3, "parameters": [0, 317, 11, 9, 2, 0]}, {"code": 119, "indent": 3, "parameters": ["结束"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 317, 9, 10, 2, 0]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 86, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](现在不应该离开……)"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 17}, {"id": 3, "name": "EV003影棚入口", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 8, "pattern": 1, "characterIndex": 7}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 500, 4]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 495, 1]}, {"code": 111, "indent": 2, "parameters": [0, 362, 1]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.followers().follower(0).turnTowardPlayer();"]}, {"code": 213, "indent": 3, "parameters": [-1, 8, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 205, "indent": 3, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 3, "parameters": [4, 6, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<影像店老板>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac美女，里面是影棚，不可以进去啊。"]}, {"code": 205, "indent": 3, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 17, "indent": null}]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.followers().follower(0).setDirection(4);"]}, {"code": 213, "indent": 3, "parameters": [-1, 11, true]}, {"code": 122, "indent": 3, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 3, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac嗯，我知道了。"]}, {"code": 401, "indent": 3, "parameters": ["\\dac就在门口看一眼可以吗？"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 213, "indent": 3, "parameters": [4, 3, true]}, {"code": 205, "indent": 3, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 3, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<影像店老板>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac还是对AV拍摄有兴趣对吧！"]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.followers().follower(0).turnTowardPlayer();"]}, {"code": 205, "indent": 3, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 19, "indent": null}]}, {"code": 250, "indent": 3, "parameters": [{"name": "帘子", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 3, "parameters": [10]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈默认", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac\\c[210](是个白人男优……)"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 213, "indent": 3, "parameters": [-1, 3, false]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈感动", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac\\c[210](我就知道肯定不可能是桐人！)"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 213, "indent": 3, "parameters": [4, 4, false]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<影像店老板>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac美女，怎么样，AV拍摄也不吓人吧？"]}, {"code": 401, "indent": 3, "parameters": ["\\dac要不要还是来我这里登记一下？"]}, {"code": 205, "indent": 3, "parameters": [-1, {"list": [{"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 2, "indent": null}]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac老板，谢谢你的好意，但我还是要再考虑一下。"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<影像店老板>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac行，不勉强你。"]}, {"code": 401, "indent": 3, "parameters": ["\\dac但我这里随时欢迎你过来！"]}, {"code": 205, "indent": 3, "parameters": [4, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 3, "parameters": [{"code": 16, "indent": null}]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(7);"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac学姐……你不会真的对拍AV产生兴趣了吧？"]}, {"code": 401, "indent": 3, "parameters": ["\\dac那个那个…我不想…你被别的男人看……"]}, {"code": 213, "indent": 3, "parameters": [-1, 12, false]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.turnTowardCharacter($gamePlayer.followers().follower(0));"]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac怎么可能感兴趣……！你在说什么？！"]}, {"code": 401, "indent": 3, "parameters": ["\\dac我只是确认桐人在不在里面！"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 213, "indent": 3, "parameters": [-1, 5, false]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac就算是游戏，在一群陌生人的面前做爱什么的……"]}, {"code": 401, "indent": 3, "parameters": ["\\dac我是绝对接受不了的！"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(4);"]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac嘿嘿~ 那就好，那就好！"]}, {"code": 213, "indent": 3, "parameters": [-1, 11, true]}, {"code": 121, "indent": 3, "parameters": [362, 362, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [-1, 8, false]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈失望", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac\\c[210](不要再看了，免得又被猪…老板误会……)"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 119, "indent": 1, "parameters": ["结束"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 250, "indent": 0, "parameters": [{"name": "帘子", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 110, 11, 15, 8, 0]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 19, "y": 8}, {"id": 4, "name": "EV004影像店老板", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "akunin3", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 500, 4]}, {"code": 111, "indent": 1, "parameters": [1, 100, 0, 495, 1]}, {"code": 111, "indent": 2, "parameters": [0, 361, 0]}, {"code": 111, "indent": 3, "parameters": [2, "A", 1]}, {"code": 101, "indent": 4, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 4, "parameters": ["\\nc<影像店老板>"]}, {"code": 401, "indent": 4, "parameters": ["\\dac\\}我是说在哪见过呢，确实和那部作品很像……"]}, {"code": 213, "indent": 4, "parameters": [-1, 2, true]}, {"code": 123, "indent": 4, "parameters": ["A", 0]}, {"code": 119, "indent": 4, "parameters": ["结束"]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影像店老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac办个会员吧！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac积分高了之后，可以看到更新更劲爆的影片哦~"]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 719}, "directionFix": false, "image": {"tileId": 0, "characterName": "akunin3", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 7}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).setDirection(8);"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 1, true]}, {"code": 213, "indent": 0, "parameters": [4, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影像店老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac美女，你是来面试AV女优的吧！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac欢迎欢迎！"]}, {"code": 356, "indent": 0, "parameters": ["PushGab 27 \\}\\c[8]老逼登!我现在都舍不得给别人看!你他妈还想拍AV?!"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(5);"]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 121, "indent": 0, "parameters": [369, 369, 0]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈苦笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac当然不是！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我只不过……随便看看。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影像店老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac美女，再考虑一下嘛！以你的身材相貌，肯定是顶格的付费……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊不！我愿意自掏腰包额外给你加钱！"]}, {"code": 213, "indent": 0, "parameters": [-1, 7, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG明日奈凛然脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 0, "parameters": [135]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac真的不用了！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [4, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影像店老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊，是嘛…可惜了。"]}, {"code": 213, "indent": 0, "parameters": [4, 9, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影像店老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}不过，这个女娃似乎在哪里看到过……"]}, {"code": 213, "indent": 0, "parameters": [4, 8, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 121, "indent": 0, "parameters": [359, 359, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 359, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 515}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 720}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [12, {"list": [{"code": 44, "parameters": [{"name": "KOK_Systemsounds_ItemSelect_06", "volume": 90, "pitch": 100, "pan": 0}], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "KOK_Systemsounds_ItemSelect_06", "volume": 90, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 事件[12] : 弹性放大出现(不透明) : 时间[20] : 比例溢出[0.1] : 中心锚点[0.5,1.0]"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 721]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 721}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 722}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 724}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": ["LAYER 109 2 影像店大厅-光 0 0 255 5 0 0 1"]}, {"code": 356, "indent": 0, "parameters": ["LAYER 109 3 影像店大厅-阴影 0 0 255 4 0 0 0"]}, {"code": 356, "indent": 0, "parameters": ["LAYER REFRESH"]}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 117, "indent": 0, "parameters": [24]}, {"code": 205, "indent": 0, "parameters": [3, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [14, 0, 19, 9, 8]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 设置声音 : 默认声音"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dDMS[慢]\\dac 五分钟后……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 1]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dDMS[慢]\\dac 地下街-影像店"]}, {"code": 356, "indent": 0, "parameters": [">对话文字速度 : 修改模式 : 默认模式"]}, {"code": 356, "indent": 0, "parameters": [">对话文字响声 : 关闭声音"]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影像店老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 让你久等了，给。"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [17, 0, 9, 8, 0]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [17, 0, 0, 3, 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac获得了\\c[103]《カノジョよりも彼女のお母さんが性癖にぶっ刺さり、勃起しすぎてチ〇ポが痛い。》\\c[0]!"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [4, 9, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影像店老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊，对了！小姑娘，你如果化个妆打扮一下，应该还是挺好看的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 老夫不会看走眼。怎么样，要不要考虑拍个片子试试？"]}, {"code": 213, "indent": 0, "parameters": [-1, 7, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 111, "indent": 0, "parameters": [0, 437, 0]}, {"code": 205, "indent": 1, "parameters": [14, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 122, "indent": 1, "parameters": [131, 131, 0, 0, 21]}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 2"]}, {"code": 213, "indent": 1, "parameters": [14, 3, false]}, {"code": 231, "indent": 1, "parameters": [2, "FOG诗乃嘲弄", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [167]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 1, "parameters": [4, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 老鬼，这个事我刚才已经跟她谈过了。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你不用多嘴。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [4, 12, false]}, {"code": 205, "indent": 1, "parameters": [4, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<影像店老板>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哦哦，好的好的。"]}, {"code": 205, "indent": 1, "parameters": [4, {"list": [{"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 1, "parameters": [14, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 231, "indent": 1, "parameters": [2, "FOG诗乃放松", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [167]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 中途遇到搞不定的情况，随时给我发信号。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯，感谢。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 恕我拒绝，断无可能。"]}, {"code": 213, "indent": 1, "parameters": [4, 7, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<影像店老板>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 唉，遗憾。"]}, {"code": 205, "indent": 1, "parameters": [4, {"list": [{"code": 4, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 725]}, {"code": 121, "indent": 0, "parameters": [86, 86, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 725}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 3}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-12,-12]"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).turnTowardPlayer();"]}, {"code": 111, "indent": 0, "parameters": [0, 360, 1]}, {"code": 117, "indent": 1, "parameters": [239]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [2, "A", 1]}, {"code": 231, "indent": 1, "parameters": [1, "AV莉法", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 213, "indent": 1, "parameters": [-1, 7, false]}, {"code": 122, "indent": 1, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈意外脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](兄妹题材…？！)"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](居然有乱伦的影片！好恶心！)"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 355, "indent": 1, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(3);"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac诶……这个女的…！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac啊！没什么没什么！"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [1, "AV莉法", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 719}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-12,-12]"]}, {"code": 122, "indent": 0, "parameters": [123, 123, 0, 0, 101]}, {"code": 355, "indent": 0, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 0, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 0, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 0, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 355, 1]}, {"code": 231, "indent": 1, "parameters": [1, "AV莉法", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 213, "indent": 1, "parameters": [-1, 1, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac \\c[210](兄妹题材，而且这个女优好眼熟……)"]}, {"code": 356, "indent": 1, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 122, "indent": 1, "parameters": [134, 134, 0, 0, 40]}, {"code": 102, "indent": 1, "parameters": [["\\dac 购买（\\c[28]变态度\\c[0]:\\c[18]\\v[134]\\c[0]） ", "\\dac 放下"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "\\dac 购买（\\c[28]变态度\\c[0]:\\c[18]\\v[134]\\c[0]） "]}, {"code": 111, "indent": 2, "parameters": [1, 74, 1, 134, 4]}, {"code": 225, "indent": 3, "parameters": [5, 9, 15, false]}, {"code": 224, "indent": 3, "parameters": [[255, 0, 0, 68], 15, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 401, "indent": 3, "parameters": ["\\dac \\c[28]变态度\\c[101]不足"]}, {"code": 119, "indent": 3, "parameters": ["非变态"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 250, "indent": 3, "parameters": [{"name": "Poison", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 3, "parameters": [[68, 255, 68, 170], 60, true]}, {"code": 122, "indent": 3, "parameters": [74, 74, 1, 0, 1]}, {"code": 356, "indent": 3, "parameters": ["addLog 变态度↑\\c[28]1"]}, {"code": 213, "indent": 3, "parameters": [-1, 11, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac \\c[210](只是觉得女优好看而已，并不是因为题材……)"]}, {"code": 250, "indent": 3, "parameters": [{"name": "Shop", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 123, "indent": 3, "parameters": ["A", 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\dac 放下"]}, {"code": 118, "indent": 2, "parameters": ["非变态"]}, {"code": 111, "indent": 2, "parameters": [0, 431, 0]}, {"code": 213, "indent": 3, "parameters": [-1, 11, false]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac \\c[210](\\dac 我在想什么？！"]}, {"code": 401, "indent": 3, "parameters": ["\\dac 和直叶一起洗澡以后，头脑都有点不正常了……)"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [-1, 8, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 356, "indent": 1, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [1, "AV莉法", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 13}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[12,-12]"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).turnTowardPlayer();"]}, {"code": 111, "indent": 0, "parameters": [0, 360, 1]}, {"code": 117, "indent": 1, "parameters": [239]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 361, 1]}, {"code": 111, "indent": 1, "parameters": [2, "A", 1]}, {"code": 231, "indent": 2, "parameters": [1, "AV亚丝娜", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 213, "indent": 2, "parameters": [-1, 1, false]}, {"code": 122, "indent": 2, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 2, "parameters": ["$gameTemp.img = 2"]}, {"code": 121, "indent": 2, "parameters": [369, 369, 0]}, {"code": 231, "indent": 2, "parameters": [2, "fog亚丝娜犹豫脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 2, "parameters": [135]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\c[210](这个AV女优…！"]}, {"code": 401, "indent": 2, "parameters": ["\\dac怎么头发和服装都和我好像……？)"]}, {"code": 235, "indent": 2, "parameters": [2]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(12);"]}, {"code": 356, "indent": 2, "parameters": ["PushGab 27 \\}\\c[8]完蛋!不会要暴露了吧?!"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 213, "indent": 2, "parameters": [-1, 8, true]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(9);"]}, {"code": 230, "indent": 2, "parameters": [60]}, {"code": 356, "indent": 2, "parameters": ["ForceGabClear"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac学姐，你怎么一直盯着这个看？"]}, {"code": 401, "indent": 2, "parameters": ["\\dac原来你喜欢凌辱题材啊！"]}, {"code": 235, "indent": 2, "parameters": [1]}, {"code": 213, "indent": 2, "parameters": [-1, 11, false]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.turnTowardCharacter($gamePlayer.followers().follower(0));"]}, {"code": 231, "indent": 2, "parameters": [2, "FOG明日奈new", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 2, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 2, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 2, "parameters": ["FOG明日奈气鼓鼓脸红.png"]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac才！没！有！"]}, {"code": 235, "indent": 2, "parameters": [2]}, {"code": 355, "indent": 2, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(3);"]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [1, "AV亚丝娜", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 235, "indent": 2, "parameters": [1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "B", 1]}, {"code": 111, "indent": 2, "parameters": [2, "A", 0]}, {"code": 231, "indent": 3, "parameters": [1, "AV迷奸", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 213, "indent": 3, "parameters": [-1, 1, false]}, {"code": 122, "indent": 3, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 3, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈欲言又止", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac\\c[210](不是之前那部作品了，被猪田买走了吗？"]}, {"code": 401, "indent": 3, "parameters": ["\\dac难道是因为那个女优很像我……)"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 213, "indent": 3, "parameters": [-1, 11, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac怎么啦，学姐？"]}, {"code": 213, "indent": 3, "parameters": [-1, 12, false]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈转移话题", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac没、没什么……"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 235, "indent": 3, "parameters": [1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 231, "indent": 3, "parameters": [1, "AV迷奸", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 213, "indent": 3, "parameters": [-1, 5, false]}, {"code": 122, "indent": 3, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 3, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 3, "parameters": [2, "FOG明日奈严肃脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [135]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac\\c[210](迷奸？这不是鼓励犯罪嘛！)"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 235, "indent": 3, "parameters": [1]}, {"code": 355, "indent": 3, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(3);"]}, {"code": 356, "indent": 3, "parameters": ["PushGab 27 \\}\\c[8]幸好我提前买走了，差点暴露~"]}, {"code": 230, "indent": 3, "parameters": [60]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 123, "indent": 2, "parameters": ["B", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 231, "indent": 2, "parameters": [1, "AV迷奸", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 101, "indent": 2, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 235, "indent": 2, "parameters": [1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 719}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[12,-12]"]}, {"code": 122, "indent": 0, "parameters": [123, 123, 0, 0, 102]}, {"code": 355, "indent": 0, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 0, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 0, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 0, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 355, 1]}, {"code": 231, "indent": 1, "parameters": [1, "AV迷奸", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 213, "indent": 1, "parameters": [-1, 8, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac \\c[210](迷奸啊，和充气娃娃有什么区别？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 搞不懂喜欢这种题材的家伙。)"]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [1, "AV迷奸", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 16, "y": 13}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-12,-12]"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).turnTowardPlayer();"]}, {"code": 111, "indent": 0, "parameters": [0, 360, 1]}, {"code": 117, "indent": 1, "parameters": [239]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [2, "A", 1]}, {"code": 231, "indent": 1, "parameters": [1, "AV逆寝取", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 213, "indent": 1, "parameters": [-1, 11, false]}, {"code": 122, "indent": 1, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈心事脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](逆寝取……男方出轨的影片。)"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 355, "indent": 1, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(9);"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac这部片我看过！男的交往久了，对女朋友失去新鲜感，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac就和她闺蜜搞在一起了。男人都是喜新厌旧的，也蛮正常……"]}, {"code": 355, "indent": 1, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(12);"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<猪田>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不过我是例外！"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [1, "AV逆寝取", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 719}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[-12,-12]"]}, {"code": 122, "indent": 0, "parameters": [123, 123, 0, 0, 103]}, {"code": 355, "indent": 0, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 0, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 0, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 0, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 355, 1]}, {"code": 231, "indent": 1, "parameters": [1, "AV逆寝取", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 213, "indent": 1, "parameters": [-1, 6, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac \\c[210](女孩子主导的寝取吗？这种事应该不可能吧。)"]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [1, "AV逆寝取", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 19, "y": 13}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 495}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[12,-12]"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).turnTowardPlayer();"]}, {"code": 111, "indent": 0, "parameters": [0, 360, 1]}, {"code": 117, "indent": 1, "parameters": [239]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [2, "A", 1]}, {"code": 231, "indent": 1, "parameters": [1, "AV精灵母女", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 213, "indent": 1, "parameters": [-1, 11, false]}, {"code": 122, "indent": 1, "parameters": [95, 95, 0, 0, 51]}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 1, "parameters": [2, "FOG明日奈生气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [135]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<亚丝娜>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](母女一起？！变态……！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac变态！！！)"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 355, "indent": 1, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(4);"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [1, "AV精灵母女", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 719}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[12,-12]"]}, {"code": 122, "indent": 0, "parameters": [123, 123, 0, 0, 104]}, {"code": 355, "indent": 0, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 0, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 0, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 0, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 355, 1]}, {"code": 231, "indent": 1, "parameters": [1, "AV精灵母女", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 213, "indent": 1, "parameters": [-1, 7, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac \\c[210](母女一起啊……变态又尴尬。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 而且女儿已经成年了，母亲不都是大婶了吗？居然也有人喜欢。)"]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 231, "indent": 1, "parameters": [1, "AV精灵母女", 0, 0, -150, -100, 100, 100, 255, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 20, "y": 13}, {"id": 10, "name": "EV010触发点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 719, 0]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 203, "indent": 1, "parameters": [13, 0, 28, 21, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你好，我是来帮舞厅门卫拿东西的。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 他说，你这里有个新电影，今天答应要给他的。"]}, {"code": 213, "indent": 1, "parameters": [4, 1, false]}, {"code": 205, "indent": 1, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 1], "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<影像店老板>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 舞厅门卫？杜克那家伙？我答应给他新片子？什么时候的事？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 现在我的忘性这么大了吗？"]}, {"code": 213, "indent": 1, "parameters": [4, 8, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<影像店老板>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不过既然他这么说了，万一真是我答应过的……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 他可能会来找我麻烦。"]}, {"code": 213, "indent": 1, "parameters": [4, 7, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<影像店老板>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 新的片子还有些后期工作需要弄。小姑娘，稍微等老夫一下。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你可以随便逛逛，或者坐在沙发上休息也行。"]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 720]}, {"code": 117, "indent": 1, "parameters": [154]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 117, "indent": 1, "parameters": [6]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 1, "y": 0}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 719, 0]}, {"code": 203, "indent": 1, "parameters": [3, 0, 28, 0, 0]}, {"code": 205, "indent": 1, "parameters": [3, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 203, "indent": 1, "parameters": [4, 0, 9, 6, 0]}, {"code": 203, "indent": 1, "parameters": [13, 0, 9, 6, 0]}, {"code": 203, "indent": 1, "parameters": [14, 0, 19, 9, 8]}, {"code": 205, "indent": 1, "parameters": [14, {"list": [{"code": 35, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 203, "indent": 1, "parameters": [10, 0, 9, 8, 0]}, {"code": 203, "indent": 1, "parameters": [12, 0, 9, 8, 0]}, {"code": 205, "indent": 1, "parameters": [12, {"list": [{"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 203, "indent": 1, "parameters": [16, 0, 7, 14, 0]}, {"code": 203, "indent": 1, "parameters": [15, 0, 19, 9, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 9, "y": 1}, {"id": 12, "name": "EV012水晶球", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 719}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Other1", "direction": 4, "pattern": 0, "characterIndex": 6}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,-12]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 2}, {"id": 13, "name": "EV013任务气泡", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,-48]"]}, {"code": 108, "indent": 0, "parameters": ["indicator : Quest_A : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 5"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 0}, {"id": 14, "name": "EV014诗乃", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "诗乃fog像素", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 117, "indent": 0, "parameters": [5]}, {"code": 111, "indent": 0, "parameters": [0, 437, 1]}, {"code": 121, "indent": 1, "parameters": [437, 437, 0]}, {"code": 213, "indent": 1, "parameters": [0, 6, true]}, {"code": 122, "indent": 1, "parameters": [131, 131, 0, 0, 21]}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 1, "parameters": [2, "FOG诗乃叹气", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [167]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 桐人，你来早了一些吧？离约定好的时间还有十几分钟呢。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你怎么知道是我……？"]}, {"code": 213, "indent": 1, "parameters": [0, 3, true]}, {"code": 231, "indent": 1, "parameters": [2, "FOG诗乃放松", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [167]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你踏入地下街的那一刻，我就知道了。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你在这里做什么？"]}, {"code": 213, "indent": 1, "parameters": [14, 7, true]}, {"code": 231, "indent": 1, "parameters": [2, "FOG诗乃不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [167]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我不能有自己的消遣吗？"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 反正不会影响约定好的事情，不用担心。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [14, 9, true]}, {"code": 231, "indent": 1, "parameters": [2, "FOG诗乃嘲弄", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [167]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 既然现在时间还有多的，你有没有兴趣……"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 205, "indent": 1, "parameters": [14, {"list": [{"code": 36, "indent": null}, {"code": 25, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 25, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 213, "indent": 1, "parameters": [14, 1, false]}, {"code": 205, "indent": 1, "parameters": [14, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 231, "indent": 1, "parameters": [2, "FOG诗乃震惊", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [167]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 咦……？竟然穿女装……？"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 231, "indent": 1, "parameters": [2, "FOG诗乃理解", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [167]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 没想到你有这样的爱好。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这个样子，完全看不出是男生。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [-1, 7, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 为了任务没办法，我也不想的。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我一进入地下街你不是就知道了吗？故作惊讶让我难堪。"]}, {"code": 231, "indent": 1, "parameters": [2, "FOG诗乃挖苦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [167]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 只是侦测到你的信号，哪会关心你的奇装异服……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不说这个了，过来看看。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 111, "indent": 1, "parameters": [6, 14, 2]}, {"code": 205, "indent": 2, "parameters": [14, {"list": [{"code": 17, "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 13, "indent": null}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 205, "indent": 2, "parameters": [14, {"list": [{"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 13, "indent": null}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 213, "indent": 1, "parameters": [-1, 2, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 什么啊……"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 12, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 122, "indent": 1, "parameters": [184, 184, 0, 0, 5]}, {"code": 117, "indent": 1, "parameters": [119]}, {"code": 213, "indent": 1, "parameters": [-1, 1, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这不是AV拍摄现场吗？"]}, {"code": 213, "indent": 1, "parameters": [14, 3, true]}, {"code": 231, "indent": 1, "parameters": [2, "FOG诗乃岔开", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [167]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 是哦，我就是这部片子的制片人。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 今天为了配合你的任务，所以把拍摄时间提前了。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 45, "parameters": ["$gamePlayer.turnTowardCharacter($gameMap.event(14));"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 45, "parameters": ["$gamePlayer.turnTowardCharacter($gameMap.event(14));"], "indent": null}]}, {"code": 213, "indent": 1, "parameters": [-1, 6, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你居然当了AV制片人……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 游戏里的AV行业这么火爆吗？"]}, {"code": 231, "indent": 1, "parameters": [2, "FOG诗乃得意", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [167]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 游戏能做到现实里无法实现的演出，场景、外形、服装都可以随意调整。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 说是宅男们的造梦工程也不为过。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [14, 4, false]}, {"code": 231, "indent": 1, "parameters": [2, "FOG诗乃玩弄脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [167]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 那么……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你有没有兴趣试试？"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 231, "indent": 1, "parameters": [2, "FOG诗乃岔开脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 1, "parameters": [167]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 虽然今天应该是来不及了，以后空闲的时候可以安排你参演哦~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不仅能有各种有趣的体验，如果销量好报酬也是很高的。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 356, "indent": 1, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 122, "indent": 1, "parameters": [134, 134, 0, 0, 50]}, {"code": 102, "indent": 1, "parameters": [["\\dac 不置可否（\\c[28]变态度\\c[0]:\\c[18]\\v[134]\\c[0]） ", "\\dac 断然拒绝"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "\\dac 不置可否（\\c[28]变态度\\c[0]:\\c[18]\\v[134]\\c[0]） "]}, {"code": 111, "indent": 2, "parameters": [1, 74, 1, 134, 4]}, {"code": 225, "indent": 3, "parameters": [5, 9, 15, false]}, {"code": 224, "indent": 3, "parameters": [[255, 0, 0, 68], 15, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": [""]}, {"code": 401, "indent": 3, "parameters": ["\\dac \\c[28]变态度\\c[101]不足"]}, {"code": 119, "indent": 3, "parameters": ["非变态"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 250, "indent": 3, "parameters": [{"name": "Poison", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 3, "parameters": [[68, 255, 68, 170], 60, true]}, {"code": 122, "indent": 3, "parameters": [74, 74, 1, 0, 1]}, {"code": 356, "indent": 3, "parameters": ["addLog 变态度↑\\c[28]1"]}, {"code": 213, "indent": 3, "parameters": [-1, 11, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac 以后的事以后再说吧。我想先专心任务。"]}, {"code": 231, "indent": 3, "parameters": [2, "FOG诗乃体谅脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [167]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac 行啊，以后有想法了随时找我。"]}, {"code": 401, "indent": 3, "parameters": ["\\dac 这个行当，男优可是一直很紧缺的。"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 231, "indent": 3, "parameters": [2, "FOG诗乃爱慕", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [167]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac 不过你女装演女主也不是不可以。"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 205, "indent": 3, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 3, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 13, "indent": null}]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac 这就算了！"]}, {"code": 213, "indent": 3, "parameters": [0, 3, true]}, {"code": 231, "indent": 3, "parameters": [2, "FOG诗乃玩弄脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 3, "parameters": [167]}, {"code": 232, "indent": 3, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac 呵呵~"]}, {"code": 235, "indent": 3, "parameters": [2]}, {"code": 122, "indent": 3, "parameters": [161, 161, 1, 0, 1]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "\\dac 断然拒绝"]}, {"code": 118, "indent": 2, "parameters": ["非变态"]}, {"code": 205, "indent": 2, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 2, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 13, "indent": null}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 绝对不要。"]}, {"code": 213, "indent": 2, "parameters": [0, 8, true]}, {"code": 231, "indent": 2, "parameters": [2, "FOG诗乃挖苦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 117, "indent": 2, "parameters": [167]}, {"code": 232, "indent": 2, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\nc<诗乃>"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 行吧。以后改变想法了，随时找我。"]}, {"code": 235, "indent": 2, "parameters": [2]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 205, "indent": 1, "parameters": [14, {"list": [{"code": 12, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 15, "parameters": [10], "indent": 0}, {"code": 35, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac \\c[210](还是别去打扰她的监制工作了……)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 0, "y": 2}, {"id": 15, "name": "EV015气泡", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,-48]"]}, {"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 0, "y": 1}, {"id": 16, "name": "EV016光圈", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 721}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,12]"]}, {"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac是否在沙发上等待？"]}, {"code": 102, "indent": 0, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "是"]}, {"code": 205, "indent": 1, "parameters": [0, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 356, "indent": 1, "parameters": [">行走图额外位置偏移量 : 玩家 : 像素偏移[12,12] : 时间[9]"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 35, "indent": null}, {"code": 14, "parameters": [-1, 0], "indent": null}, {"code": 36, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 35, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 14, "parameters": [-1, 0], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 36, "indent": null}]}, {"code": 249, "indent": 1, "parameters": [{"name": "LNSM_ME06_Sleep2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 60, false]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 230, "indent": 1, "parameters": [45]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": [">行走图额外位置偏移量 : 玩家 : 像素偏移[0,0] : 时间[1]"]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 722]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 201, "indent": 1, "parameters": [0, 317, 35, 17, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "否"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 1, "y": 1}, null]}