{"autoplayBgm": true, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "S - かおりとぬくもり", "pan": 0, "pitch": 100, "volume": 25}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 21, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 50, "width": 30, "data": [2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2674, 0, 0, 0, 0, 0, 0, 0, 2673, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 2672, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3859, 3857, 3857, 3857, 3869, 0, 3867, 3857, 3857, 3857, 3861, 0, 0, 3859, 3857, 3857, 3857, 3869, 0, 3867, 3857, 3857, 3857, 3861, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 0, 0, 0, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3868, 0, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 0, 0, 0, 0, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 0, 0, 0, 0, 0, 0, 0, 3866, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3866, 0, 0, 3866, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3866, 0, 0, 0, 0, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 3868, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3868, 0, 0, 3868, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3866, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 0, 0, 0, 0, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3866, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3866, 0, 0, 3866, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 3856, 0, 3819, 3809, 3809, 3809, 3809, 3809, 3821, 0, 3856, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3856, 0, 0, 0, 0, 0, 0, 3865, 3857, 3857, 3857, 3869, 0, 3867, 3857, 3857, 3857, 3863, 0, 0, 3865, 3857, 3857, 3857, 3869, 0, 3867, 3857, 3857, 3857, 3863, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 350, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 498, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 79, 15, 17, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 14, "y": 20}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "cd0e1ea5c1e9f788a68a5d8b80dbc67e_dc958a83f3ccff2bda34ce8460313038", "direction": 2, "pattern": 1, "characterIndex": 7}, "list": [{"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 79, 15, 17, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}], "x": 15, "y": 20}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹现实", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 莉兹"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 19}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子短发", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 爱心 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 10"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\>屈服：25%"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子短发", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 京子"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 19}, {"id": 5, "name": "EV005", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [91]}, {"code": 121, "indent": 0, "parameters": [21, 21, 1]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 40, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 42, "parameters": [255], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 117, "indent": 0, "parameters": [11]}, {"code": 356, "indent": 0, "parameters": ["ForceGabClear"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[6] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[7] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[8] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[9] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[10] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[12] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[14] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[15] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[16] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[17] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[18] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[19] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[20] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[21] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[22] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[23] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[24] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[25] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[26] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[27] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[28] : 强制刷新文本"]}, {"code": 356, "indent": 0, "parameters": [">事件漂浮文字 : 事件[29] : 强制刷新文本"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 11, "y": 0}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 85}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo莉兹战斗像素人", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6帐内的两人"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 5, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 5 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 5]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 5 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 85}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo莉兹战斗像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6帐内的两人"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关野营地帐篷内莉兹拷问猪田的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [218, 218, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 1, true]}, {"code": 201, "indent": 1, "parameters": [0, 118, 17, 9, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 231, "indent": 1, "parameters": [1, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 241, "indent": 1, "parameters": [{"name": "夜晚森林篝火", "volume": 55, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "bob_chikan01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[16]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[莉兹足交1]"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嘶~学..学姐,这..这和说好的奖励不..不对吧?嘶~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哈?你这头死肥猪居然对我的好闺蜜干了那么过分的事,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac现在居然还想从我这里得到奖励?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac怎..怎么会,学..学姐你骗我!我明明告诉你了所有的事情。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac所以我这不是在给你我理解的奖励嘛?像你这种死肥猪,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac能被我这种美女用鞋子帮你撸，你就应该感恩戴德了吧?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac至..至少请拖掉鞋子..刮的我好疼，嘶~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac你这死肥猪居然幻想我帮你足交嘛,也不撒泡尿泡泡镜子,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac必须还得给你个肥猪一点教训才行。"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[莉兹足交2]"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 44, "parameters": [{"name": "Blow1", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [140], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 44, "parameters": [{"name": "Blow1", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [140], "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哎哟~疼,学..学姐..轻一点..轻一点..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac臭肥猪,你的小鸡鸡看起来还挺大嘛,还是男人都是你这个水平吗?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嘶~我..我不知道...别踩了，学..学姐..要..要断了，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac真的要断了，好疼啊~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不会吧..对不起..对不起，我不知道会这么疼~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac你这肥猪以为我会这么说吗?笨蛋,我可不是亚丝娜,这里可是游戏，就是真断了,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac也没啥影响吧,当初肯定就是这样装可怜接近的亚丝娜和桐人那个笨蛋对不对。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](这婊子心理变态吧！)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哎哟,没..没有,好疼,真的都是意外..学..学姐我没骗你,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac而且铁链勒的我好痛,能不能不要拴着铁链..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac死肥猪居然还在给我装,你那点破心思我可是早就知道了,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac这铁链可是为了保护我的安全,毕竟为了干这种事可是要互相"]}, {"code": 401, "indent": 1, "parameters": ["\\dac解开限制,谁知道你个肥猪还有什么东西隐瞒着。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac没..没有了..学姐我知道的..真..真的全部告诉你了...嘶~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac那么死肥猪想要亚丝娜她彻底成为你的女人吗?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac诶..亚丝娜学姐对我很好...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac还给我装,给我说心里话!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哎哟,我...我说..我想亚丝娜学姐成为我的女人,我想狠狠的操"]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜学姐，我...我想让亚丝娜学姐成为我的专属母狗..."]}, {"code": 401, "indent": 1, "parameters": ["\\dac天天和她配种，让她给我生一窝小猪..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac这才像话,记住了死肥猪你以后就是我的奴隶,我会帮你"]}, {"code": 401, "indent": 1, "parameters": ["\\dac想办法把亚丝娜变成你的女人,以后的事听我的指挥，凭你的"]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪脑根本完全成不了事。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac诶..学...学姐愿意帮我吗?难道是因为学姐喜欢那个桐人吗?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac死肥猪只要乖乖听我的命令就行，身为奴隶还敢猜主人的想法,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac看样子是惩罚还不太够,如果接下来10分钟还不射精给我看的话,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我倒是想看看如果在游戏里把这种鸡巴踩断了会发生什么呢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 10...10分钟，不..不行啊学..学姐这样我只有..哎哟!疼的感觉,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac完全没有任何快感..哎哟~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哼,谁管你。"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 232, "indent": 1, "parameters": [1, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 242, "indent": 1, "parameters": [1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [1], "indent": null}]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 85}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo莉兹战斗像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6帐内的两人"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关野营地帐篷内莉兹拷问猪田的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [218, 218, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 1, true]}, {"code": 201, "indent": 1, "parameters": [0, 118, 17, 9, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 231, "indent": 1, "parameters": [1, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 241, "indent": 1, "parameters": [{"name": "夜晚森林篝火", "volume": 55, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "bob_chikan01", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[16]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[莉兹足交1]"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嘶~学..学姐,这..这和说好的奖励不..不对吧?嘶~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哈?你这头死肥猪居然对我的好闺蜜干了那么过分的事,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac现在居然还想从我这里得到奖励?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac怎..怎么会,学..学姐你骗我!我明明告诉你了所有的事情。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac所以我这不是在给你我理解的奖励嘛?像你这种死肥猪,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac能被我这种美女用鞋子帮你撸，你就应该感恩戴德了吧?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac至..至少请拖掉鞋子..刮的我好疼，嘶~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac你这死肥猪居然幻想我帮你足交嘛,也不撒泡尿泡泡镜子,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac必须还得给你个肥猪一点教训才行。"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[莉兹足交2]"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 44, "parameters": [{"name": "Blow1", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}, {"code": 15, "parameters": [140], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 44, "parameters": [{"name": "Blow1", "volume": 35, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [140], "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哎哟~疼,学..学姐..轻一点..轻一点..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac臭肥猪,你的小鸡鸡看起来还挺大嘛,还是男人都是你这个水平吗?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac嘶~我..我不知道...别踩了，学..学姐..要..要断了，"]}, {"code": 401, "indent": 1, "parameters": ["\\dac真的要断了，好疼啊~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac不会吧..对不起..对不起，我不知道会这么疼~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac你这肥猪以为我会这么说吗?笨蛋,我可不是亚丝娜,这里可是游戏，就是真断了,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac也没啥影响吧,当初肯定就是这样装可怜接近的亚丝娜和桐人那个笨蛋对不对。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](这婊子心理变态吧！)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哎哟,没..没有,好疼,真的都是意外..学..学姐我没骗你,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac而且铁链勒的我好痛,能不能不要拴着铁链..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac死肥猪居然还在给我装,你那点破心思我可是早就知道了,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac这铁链可是为了保护我的安全,毕竟为了干这种事可是要互相"]}, {"code": 401, "indent": 1, "parameters": ["\\dac解开限制,谁知道你个肥猪还有什么东西隐瞒着。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac没..没有了..学姐我知道的..真..真的全部告诉你了...嘶~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac那么死肥猪想要亚丝娜她彻底成为你的女人吗?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac诶..亚丝娜学姐对我很好...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac还给我装,给我说心里话!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哎哟,我...我说..我想亚丝娜学姐成为我的女人,我想狠狠的操"]}, {"code": 401, "indent": 1, "parameters": ["\\dac亚丝娜学姐，我...我想让亚丝娜学姐成为我的专属母狗..."]}, {"code": 401, "indent": 1, "parameters": ["\\dac天天和她配种，让她给我生一窝小猪..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac这才像话,记住了死肥猪你以后就是我的奴隶,我会帮你"]}, {"code": 401, "indent": 1, "parameters": ["\\dac想办法把亚丝娜变成你的女人,以后的事听我的指挥，凭你的"]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪脑根本完全成不了事。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac诶..学...学姐愿意帮我吗?难道是因为学姐喜欢那个桐人吗?"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac死肥猪只要乖乖听我的命令就行，身为奴隶还敢猜主人的想法,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac看样子是惩罚还不太够,如果接下来10分钟还不射精给我看的话,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac我倒是想看看如果在游戏里把这种鸡巴踩断了会发生什么呢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 10...10分钟，不..不行啊学..学姐这样我只有..哎哟!疼的感觉,"]}, {"code": 401, "indent": 1, "parameters": ["\\dac完全没有任何快感..哎哟~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac哼,谁管你。"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 232, "indent": 1, "parameters": [1, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 242, "indent": 1, "parameters": [1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [1], "indent": null}]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 16}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 137, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 69}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子双马尾", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/5公寓偷窥(一)"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 10, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 10 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 10]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 10 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 137, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子双马尾", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/5公寓偷窥(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关公寓偷窥(一)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [7, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 121, "indent": 1, "parameters": [209, 209, 0]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 201, "indent": 1, "parameters": [0, 115, 12, 16, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 241, "indent": 1, "parameters": [{"name": "夜晚", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[13]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[猪头房间京子后背位慢]"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯…嗯…呜…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}一不注意,都已经差不多2点了啊,再干两回就差不多休息吧。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}还…还要继续吗？你这家伙…到底…呜…."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}吵死了，便器就要有个便器的样子，反正明天也是周六。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊…嗯…前面说得好好的让我回家，结果走了半路又让人家过来…而且玩完游戏嗯~~突然又...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}我临时改的主意,反正你也回去没什么事,玩完FOG后积攒的东西没地方发泄可不行呢~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}还有…下次不要在隔壁那家伙面前…."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}…我知道我知道….放心吧…他不会…."]}, {"code": 356, "indent": 1, "parameters": ["PushGab 7 隔着墙果然声音太小听不太清在说一些什么..."]}, {"code": 230, "indent": 1, "parameters": [100]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呼~呼~不过肏穿着这种衣服的体验感确实相当的不错~比之前的可带劲多了，来臭母狗把头给我抬起来~"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[猪头房间京子后背位快]"]}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストンタイプB（グチュ音多め）（中くらい）", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 床摇2 30 100 0"]}, {"code": 356, "indent": 1, "parameters": ["play_bgs3 aya_bgv012 5 100 0"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯啊，啊，噢~~"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 7 这么激烈...简直和野兽一样....."]}, {"code": 230, "indent": 1, "parameters": [100]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}突然就进入状态了不是嘛，你这受虐狂~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊…哈啊…嗯啊~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}果然对待抖M就得粗暴一点，再多叫给我听听！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呜啊，我…才没有…噢噢噢~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}真是不坦率的母猪啊，还在逞强。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}来了，要给你注入新鲜的种汁了，给我心怀感激的接下来吧！"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[猪头房间京子后背位中出]"]}, {"code": 230, "indent": 1, "parameters": [80]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs3"]}, {"code": 250, "indent": 1, "parameters": [{"name": "射精音Bタイプ外出し①（勢いよくびゅるるるぅ～）", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 250, "indent": 1, "parameters": [{"name": "射精音Bタイプ外出し①（勢いよくびゅるるるぅ～）", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 137, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子双马尾", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/5公寓偷窥(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关公寓偷窥(一)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [7, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 121, "indent": 1, "parameters": [209, 209, 0]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 201, "indent": 1, "parameters": [0, 115, 12, 16, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 241, "indent": 1, "parameters": [{"name": "夜晚", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[13]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[猪头房间京子后背位慢]"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯…嗯…呜…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}一不注意,都已经差不多2点了啊,再干两回就差不多休息吧。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}还…还要继续吗？你这家伙…到底…呜…."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}吵死了，便器就要有个便器的样子，反正明天也是周六。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊…嗯…前面说得好好的让我回家，结果走了半路又让人家过来…而且玩完游戏嗯~~突然又...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}我临时改的主意,反正你也回去没什么事,玩完FOG后积攒的东西没地方发泄可不行呢~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}还有…下次不要在隔壁那家伙面前…."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}…我知道我知道….放心吧…他不会…."]}, {"code": 356, "indent": 1, "parameters": ["PushGab 7 隔着墙果然声音太小听不太清在说一些什么..."]}, {"code": 230, "indent": 1, "parameters": [100]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呼~呼~不过肏穿着这种衣服的体验感确实相当的不错~比之前的可带劲多了，来臭母狗把头给我抬起来~"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[猪头房间京子后背位快]"]}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストンタイプB（グチュ音多め）（中くらい）", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 床摇2 30 100 0"]}, {"code": 356, "indent": 1, "parameters": ["play_bgs3 aya_bgv012 5 100 0"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯啊，啊，噢~~"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 7 这么激烈...简直和野兽一样....."]}, {"code": 230, "indent": 1, "parameters": [100]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}突然就进入状态了不是嘛，你这受虐狂~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊…哈啊…嗯啊~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}果然对待抖M就得粗暴一点，再多叫给我听听！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呜啊，我…才没有…噢噢噢~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}真是不坦率的母猪啊，还在逞强。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}来了，要给你注入新鲜的种汁了，给我心怀感激的接下来吧！"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[猪头房间京子后背位中出]"]}, {"code": 230, "indent": 1, "parameters": [80]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs3"]}, {"code": 250, "indent": 1, "parameters": [{"name": "射精音Bタイプ外出し①（勢いよくびゅるるるぅ～）", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 250, "indent": 1, "parameters": [{"name": "射精音Bタイプ外出し①（勢いよくびゅるるるぅ～）", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子双马尾", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/5公寓偷窥(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关公寓偷窥(一)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [7, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 121, "indent": 1, "parameters": [209, 209, 0]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 201, "indent": 1, "parameters": [0, 115, 12, 16, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 241, "indent": 1, "parameters": [{"name": "夜晚", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[13]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[猪头房间京子后背位慢]"]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯…嗯…呜…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}一不注意,都已经差不多2点了啊,再干两回就差不多休息吧。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}还…还要继续吗？你这家伙…到底…呜…."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}吵死了，便器就要有个便器的样子，反正明天也是周六。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊…嗯…前面说得好好的让我回家，结果走了半路又让人家过来…而且玩完游戏嗯~~突然又...."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}我临时改的主意,反正你也回去没什么事,玩完FOG后积攒的东西没地方发泄可不行呢~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}还有…下次不要在隔壁那家伙面前…."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}…我知道我知道….放心吧…他不会…."]}, {"code": 356, "indent": 1, "parameters": ["PushGab 7 隔着墙果然声音太小听不太清在说一些什么..."]}, {"code": 230, "indent": 1, "parameters": [100]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呼~呼~不过肏穿着这种衣服的体验感确实相当的不错~比之前的可带劲多了，来臭母狗把头给我抬起来~"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[猪头房间京子后背位快]"]}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストンタイプB（グチュ音多め）（中くらい）", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 床摇2 30 100 0"]}, {"code": 356, "indent": 1, "parameters": ["play_bgs3 aya_bgv012 5 100 0"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯啊，啊，噢~~"]}, {"code": 356, "indent": 1, "parameters": ["PushGab 7 这么激烈...简直和野兽一样....."]}, {"code": 230, "indent": 1, "parameters": [100]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}突然就进入状态了不是嘛，你这受虐狂~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊…哈啊…嗯啊~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}果然对待抖M就得粗暴一点，再多叫给我听听！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac异常成熟的女子音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呜啊，我…才没有…噢噢噢~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}真是不坦率的母猪啊，还在逞强。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}来了，要给你注入新鲜的种汁了，给我心怀感激的接下来吧！"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[猪头房间京子后背位中出]"]}, {"code": 230, "indent": 1, "parameters": [80]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs3"]}, {"code": 250, "indent": 1, "parameters": [{"name": "射精音Bタイプ外出し①（勢いよくびゅるるるぅ～）", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 250, "indent": 1, "parameters": [{"name": "射精音Bタイプ外出し①（勢いよくびゅるるるぅ～）", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [160]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 241, "indent": 1, "parameters": [{"name": "", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 16}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 144, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 80}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子双马尾", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6公寓偷窥(二)"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 10, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 10 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 10]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 10 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 144, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子双马尾", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6公寓偷窥(二)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关公寓偷窥(二)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [7, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 121, "indent": 1, "parameters": [210, 210, 0]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 201, "indent": 1, "parameters": [0, 115, 12, 16, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）（中くらい）", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 床摇2 30 100 0"]}, {"code": 356, "indent": 1, "parameters": ["play_bgs3 aya_bgv012 5 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 5 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[13]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[公寓墙壁后入偷窥]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊...哈啊..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}屁股给我在夹紧一点,你这骚母狗。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}啊..嗯...拜托今天,真的让我回去吧,昨天不都陪你一天了..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}昨天是昨天,今天我还没爽够呢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}那至少…戴上…今天是危险的日子…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}是嘛，那可真是个好消息~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\} …你这…家伙…呜~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嘿嘿，这个姿势不错吧，肉体之间的碰撞声，不觉得很色情嘛。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}…没…觉得，快点完事…."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}还在嘴硬,决定了今天至少要给你中出三次,才会放你回去。"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs3"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 144, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子双马尾", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6公寓偷窥(二)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关公寓偷窥(二)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [7, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 121, "indent": 1, "parameters": [210, 210, 0]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 201, "indent": 1, "parameters": [0, 115, 12, 16, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）（中くらい）", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 床摇2 30 100 0"]}, {"code": 356, "indent": 1, "parameters": ["play_bgs3 aya_bgv012 5 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 5 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[13]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[公寓墙壁后入偷窥]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊...哈啊..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}屁股给我在夹紧一点,你这骚母狗。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}啊..嗯...拜托今天,真的让我回去吧,昨天不都陪你一天了..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}昨天是昨天,今天我还没爽够呢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}那至少…戴上…今天是危险的日子…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}是嘛，那可真是个好消息~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\} …你这…家伙…呜~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嘿嘿，这个姿势不错吧，肉体之间的碰撞声，不觉得很色情嘛。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}…没…觉得，快点完事…."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}还在嘴硬,决定了今天至少要给你中出三次,才会放你回去。"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs3"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子双马尾", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6公寓偷窥(二)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关公寓偷窥(二)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 129, "indent": 1, "parameters": [7, 0, false]}, {"code": 129, "indent": 1, "parameters": [1, 1, false]}, {"code": 121, "indent": 1, "parameters": [210, 210, 0]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 201, "indent": 1, "parameters": [0, 115, 12, 16, 4, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "ピストンタイプA（肉のぶつかる音入り）（中くらい）", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 床摇2 30 100 0"]}, {"code": 356, "indent": 1, "parameters": ["play_bgs3 aya_bgv012 5 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 5 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[13]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[公寓墙壁后入偷窥]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊...哈啊..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}屁股给我在夹紧一点,你这骚母狗。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}啊..嗯...拜托今天,真的让我回去吧,昨天不都陪你一天了..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}昨天是昨天,今天我还没爽够呢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}那至少…戴上…今天是危险的日子…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}是嘛，那可真是个好消息~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\} …你这…家伙…呜~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嘿嘿，这个姿势不错吧，肉体之间的碰撞声，不觉得很色情嘛。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}…没…觉得，快点完事…."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}还在嘴硬,决定了今天至少要给你中出三次,才会放你回去。"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs3"]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 16}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 150, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 80}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子双马尾", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6走廊偷窥(一)"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 5, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 5 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 5]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 5 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 150, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子双马尾", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6走廊偷窥(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关走廊偷窥(一)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [211, 211, 0]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 201, "indent": 1, "parameters": [0, 112, 2, 4, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "口交+娇喘3", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 1, "parameters": [15, 1, true]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 5 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[13]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[窗外口交慢]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}唔呣...嗯..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呼~没错，再含的深一点。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊…这个真的就是最后了，已经没有时间了，我得回去才行。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}知道啦知道啦，含着的时候嘴别说话。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}咕…嗯唔…噗哈…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}舌头也别空着，给我好好舔，\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}不然这样再过一个小时也没法结束噢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}唔，嗯…哈啊…唔….唔…."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}啧，把舌头缠上来都做不好嘛，那就让我来帮帮你。"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[窗外口交快]"]}, {"code": 245, "indent": 1, "parameters": [{"name": "口交+娇喘9", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呜？嗯，嗯…噗…!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊…爽！果然口交至少得做到这样才行。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}咳…咳咳…哈啊…呜…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呼…呼…舒服到忍不住了，像用加热后的自慰杯一样，还不用上润滑液。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呜呜！！嗯！噗哈…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}唔…这个可能要上瘾了也说不定。嘶哈…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯…呜啊…停，一下…呼吸要…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}啊，你说了什么吗？不管了，我要射了，给我全部喝下去。"]}, {"code": 230, "indent": 1, "parameters": [200]}, {"code": 250, "indent": 1, "parameters": [{"name": "射精音・中出し（長い）①", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [180]}, {"code": 251, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 150, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子双马尾", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6走廊偷窥(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关走廊偷窥(一)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [211, 211, 0]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 201, "indent": 1, "parameters": [0, 112, 2, 4, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "口交+娇喘3", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 1, "parameters": [15, 1, true]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 5 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[13]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[窗外口交慢]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}唔呣...嗯..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呼~没错，再含的深一点。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊…这个真的就是最后了，已经没有时间了，我得回去才行。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}知道啦知道啦，含着的时候嘴别说话。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}咕…嗯唔…噗哈…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}舌头也别空着，给我好好舔，\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}不然这样再过一个小时也没法结束噢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}唔，嗯…哈啊…唔….唔…."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}啧，把舌头缠上来都做不好嘛，那就让我来帮帮你。"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[窗外口交快]"]}, {"code": 245, "indent": 1, "parameters": [{"name": "口交+娇喘9", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呜？嗯，嗯…噗…!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊…爽！果然口交至少得做到这样才行。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}咳…咳咳…哈啊…呜…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呼…呼…舒服到忍不住了，像用加热后的自慰杯一样，还不用上润滑液。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呜呜！！嗯！噗哈…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}唔…这个可能要上瘾了也说不定。嘶哈…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯…呜啊…停，一下…呼吸要…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}啊，你说了什么吗？不管了，我要射了，给我全部喝下去。"]}, {"code": 230, "indent": 1, "parameters": [200]}, {"code": 250, "indent": 1, "parameters": [{"name": "射精音・中出し（長い）①", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [180]}, {"code": 251, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子双马尾", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6走廊偷窥(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关走廊偷窥(一)的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [211, 211, 0]}, {"code": 121, "indent": 1, "parameters": [21, 21, 0]}, {"code": 201, "indent": 1, "parameters": [0, 112, 2, 4, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 245, "indent": 1, "parameters": [{"name": "口交+娇喘3", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 1, "parameters": [15, 1, true]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 111, "indent": 1, "parameters": [0, 19, 1]}, {"code": 356, "indent": 2, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 25 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 噪点滤镜 : 5 : 1"]}, {"code": 356, "indent": 2, "parameters": [">图片滤镜 : 图片[99] : 模糊滤镜 : 40 : 1"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[13]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[窗外口交慢]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}唔呣...嗯..."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呼~没错，再含的深一点。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊…这个真的就是最后了，已经没有时间了，我得回去才行。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}知道啦知道啦，含着的时候嘴别说话。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}咕…嗯唔…噗哈…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}舌头也别空着，给我好好舔，\\{"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}不然这样再过一个小时也没法结束噢。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}唔，嗯…哈啊…唔….唔…."]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}啧，把舌头缠上来都做不好嘛，那就让我来帮帮你。"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[窗外口交快]"]}, {"code": 245, "indent": 1, "parameters": [{"name": "口交+娇喘9", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呜？嗯，嗯…噗…!"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}哈啊…爽！果然口交至少得做到这样才行。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}咳…咳咳…哈啊…呜…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呼…呼…舒服到忍不住了，像用加热后的自慰杯一样，还不用上润滑液。"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}呜呜！！嗯！噗哈…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}唔…这个可能要上瘾了也说不定。嘶哈…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac有点熟悉的猪田女友声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}嗯…呜啊…停，一下…呼吸要…"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田的声音:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\}啊，你说了什么吗？不管了，我要射了，给我全部喝下去。"]}, {"code": 230, "indent": 1, "parameters": [200]}, {"code": 250, "indent": 1, "parameters": [{"name": "射精音・中出し（長い）①", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [180]}, {"code": 251, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 356, "indent": 1, "parameters": [">对话框滤镜 : 全部 : 模糊滤镜 : 0 : 1"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 16}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 85}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo莉兹战斗像素人", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6帐前的两人"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 5, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 5 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 5]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 5 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 85}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo莉兹战斗像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6帐前的两人"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关野营地帐篷前莉兹试探猪田的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [214, 214, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 1, true]}, {"code": 201, "indent": 1, "parameters": [0, 118, 17, 9, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 85}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo莉兹战斗像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/6帐前的两人"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关野营地帐篷前莉兹试探猪田的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [214, 214, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 1, true]}, {"code": 201, "indent": 1, "parameters": [0, 118, 17, 9, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 16}, {"id": 11, "name": "模板", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 0}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 145}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子短发", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/9说教"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 5, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 5 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 5]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 5 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子短发", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/9说教"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关京子说教亚丝娜的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [222, 222, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 128, 28, 19, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [222, 222, 0]}, {"code": 231, "indent": 1, "parameters": [100, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 241, "indent": 1, "parameters": [{"name": "MAP_不気味・緊張感", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯呣~~床上还有明日奈的味道~"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但也只有我不在的时候……她才会睡这张床。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我的小公主……已经不愿意和妈妈一起睡了……"]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "甘い香り", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "handjob_slow", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 夏梅12_アナル喘ぎ通常 20 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[20]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[京子自慰慢]"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯嗯~手指…不够啊……"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊~哦~~死猪头！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哦……竟然让我，沦落到如此悲惨的境地……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我都已经……唔……多少年没有、自己用手……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊~~~~这样做了…………"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不允许…啊~~~我找帅哥…又…呃……不陪我…………"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我累死累活……好不容易、呣啊~~~~提前一天赶回来…………"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac ……死猪头！啊啊~居然说自己有事~~~没空？！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac ……还听到…啊~另一个女人的声音……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac …臭肥猪…哦哦~这么丑居然还花心…唔唔~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你这个小瘪三,我差不多已经把你摸透了~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 等明日奈转学办好以后…看我怎么收拾你……"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 245, "indent": 1, "parameters": [{"name": "handjob_medium", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 夏梅13_アナル喘ぎ激しめ 20 100 0"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[京子自慰快]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 可恶……手指、不够啊~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 如果是那个臭鸡巴…啊~啊啊啊~~~~~~"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 死猪头的臭鸡巴……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呣唔~哦~啊啊啊~~~~"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 死猪头！死猪头！死猪头！\\."]}, {"code": 401, "indent": 1, "parameters": ["\\dac 臭鸡巴！臭鸡巴！臭鸡巴……！"]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 3]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 30, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[京子自慰高潮]"]}, {"code": 230, "indent": 1, "parameters": [200]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 250, "indent": 1, "parameters": [{"name": "03潮吹き音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 355, "indent": 1, "parameters": ["$gameMessage.clear();"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "娇喘弱10", "volume": 15, "pitch": 80, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哼！死猪头…………\\|讨厌死了……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哦~\\|再来一次吧……"]}, {"code": 246, "indent": 1, "parameters": [2]}, {"code": 242, "indent": 1, "parameters": [1]}, {"code": 232, "indent": 1, "parameters": [60, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 121, "indent": 1, "parameters": [222, 222, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子短发", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/9说教"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关京子说教亚丝娜的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [222, 222, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 128, 28, 19, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [222, 222, 0]}, {"code": 231, "indent": 1, "parameters": [100, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 241, "indent": 1, "parameters": [{"name": "MAP_不気味・緊張感", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<京子>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯呣~~床上还有明日奈的味道~"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 但也只有我不在的时候……她才会睡这张床。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我的小公主……已经不愿意和妈妈一起睡了……"]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "甘い香り", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "handjob_slow", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 夏梅12_アナル喘ぎ通常 20 100 0"]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[20]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[京子自慰慢]"]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 嗯嗯~手指…不够啊……"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊~哦~~死猪头！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哦……竟然让我，沦落到如此悲惨的境地……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我都已经……唔……多少年没有、自己用手……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 啊~~~~这样做了…………"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不允许…啊~~~我找帅哥…又…呃……不陪我…………"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我累死累活……好不容易、呣啊~~~~提前一天赶回来…………"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac ……死猪头！啊啊~居然说自己有事~~~没空？！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac ……还听到…啊~另一个女人的声音……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac …臭肥猪…哦哦~这么丑居然还花心…唔唔~~~"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你这个小瘪三,我差不多已经把你摸透了~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 等明日奈转学办好以后…看我怎么收拾你……"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 245, "indent": 1, "parameters": [{"name": "handjob_medium", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["play_bgs2 夏梅13_アナル喘ぎ激しめ 20 100 0"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[京子自慰快]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 可恶……手指、不够啊~"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 如果是那个臭鸡巴…啊~啊啊啊~~~~~~"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 死猪头的臭鸡巴……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 呣唔~哦~啊啊啊~~~~"]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 死猪头！死猪头！死猪头！\\."]}, {"code": 401, "indent": 1, "parameters": ["\\dac 臭鸡巴！臭鸡巴！臭鸡巴……！"]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 3]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 30, false]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[京子自慰高潮]"]}, {"code": 230, "indent": 1, "parameters": [200]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 250, "indent": 1, "parameters": [{"name": "03潮吹き音", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, true]}, {"code": 355, "indent": 1, "parameters": ["$gameMessage.clear();"]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 245, "indent": 1, "parameters": [{"name": "娇喘弱10", "volume": 15, "pitch": 80, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哼！死猪头…………\\|讨厌死了……"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哦~\\|再来一次吧……"]}, {"code": 246, "indent": 1, "parameters": [2]}, {"code": 242, "indent": 1, "parameters": [1]}, {"code": 232, "indent": 1, "parameters": [60, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, true]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 235, "indent": 1, "parameters": [100]}, {"code": 121, "indent": 1, "parameters": [222, 222, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 14}, {"id": 13, "name": "EV013", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 222, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 3}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 京子:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊啊~\\|咿~~~~~\\|哦哦~~~~~~~\\|啊~~~\\|\\|\\.\\.\\."]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呼~呼~呼~去…去了………… \\.\\^"]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 4]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 19, "y": 14}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 130}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹私服像素人", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/9践踏猪脸"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 5, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 5 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 5]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 5 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 130}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹私服像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/9践踏猪脸"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关践踏猪脸的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [165, 165, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 34, 13, 11, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "エースDungeon2", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 245, "indent": 1, "parameters": [{"name": "bob_chikan01", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, true]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[16]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[莉兹踩踏]"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 咿！！！学姐？为什么？！ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](贱人，竟然敢用脚踩我的脸！都是汗，还这么臭！)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 怎么？我好心给你的脸做个SPA，你有意见？！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不不不敢？能和绝世美女的玉足亲密接触，是我的荣幸！ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哼，算你识相！我可是憋了一肚子的火， "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 得让我好好发泄，踩死你们这些臭男人！ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](气死我了，还不能发作……可恶！要踩多久啊？这时间真难熬。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 又要发动想象力了吗？让我熬过以前霸凌时光的神技……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](可是这贱人汗脚的酸臭味，该联想什么才能让心里好受点呢？\\|"]}, {"code": 401, "indent": 1, "parameters": ["\\dac有了！但不知道行不行啊……不管了，闭上眼睛开始想象！)"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 241, "indent": 1, "parameters": [{"name": "エースDungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "bob_chikan01", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[22]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜踩踏幻想]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](哦哦，果然一下就不一样了！)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac幻想中的亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](这是对你出轨的惩罚！)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac幻想中的亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](而且对象还是你的岳母！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你这个畜生老公，看我用汗脚熏死你……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](呜……老婆的脚才不臭呢……好多香汗……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac幻想中的亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](真是变态，被我的黑丝臭脚踩脸居然开心……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我的脚一天没有洗了，真的香？)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](老婆的脚天下第一美味，一辈子都闻不够……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac幻想中的亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](那你愿意舔吗？)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](愿意！)"]}, {"code": 231, "indent": 1, "parameters": [1, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 1, "parameters": [2, "莉兹批评", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["莉兹私服.png"]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 死猪头，你干嘛？ "]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 130}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹私服像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/9践踏猪脸"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关践踏猪脸的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 121, "indent": 1, "parameters": [165, 165, 0]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 34, 13, 11, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 243, "indent": 1, "parameters": []}, {"code": 241, "indent": 1, "parameters": [{"name": "エースDungeon2", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 245, "indent": 1, "parameters": [{"name": "bob_chikan01", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 1, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, true]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[16]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[莉兹踩踏]"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 222, "indent": 1, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 咿！！！学姐？为什么？！ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](贱人，竟然敢用脚踩我的脸！都是汗，还这么臭！)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 怎么？我好心给你的脸做个SPA，你有意见？！"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不不不敢？能和绝世美女的玉足亲密接触，是我的荣幸！ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哼，算你识相！我可是憋了一肚子的火， "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 得让我好好发泄，踩死你们这些臭男人！ "]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](气死我了，还不能发作……可恶！要踩多久啊？这时间真难熬。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 又要发动想象力了吗？让我熬过以前霸凌时光的神技……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](可是这贱人汗脚的酸臭味，该联想什么才能让心里好受点呢？\\|"]}, {"code": 401, "indent": 1, "parameters": ["\\dac有了！但不知道行不行啊……不管了，闭上眼睛开始想象！)"]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 241, "indent": 1, "parameters": [{"name": "エースDungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 1, "parameters": [{"name": "bob_chikan01", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[22]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[亚丝娜踩踏幻想]"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 2, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](哦哦，果然一下就不一样了！)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac幻想中的亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](这是对你出轨的惩罚！)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac幻想中的亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](而且对象还是你的岳母！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你这个畜生老公，看我用汗脚熏死你……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](呜……老婆的脚才不臭呢……好多香汗……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac幻想中的亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](真是变态，被我的黑丝臭脚踩脸居然开心……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 我的脚一天没有洗了，真的香？)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](老婆的脚天下第一美味，一辈子都闻不够……)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac幻想中的亚丝娜:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](那你愿意舔吗？)"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac猪田: "]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[210](愿意！)"]}, {"code": 231, "indent": 1, "parameters": [1, "黑幕", 0, 0, 0, 0, 100, 100, 255, 0]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 356, "indent": 1, "parameters": ["stop_bgs2"]}, {"code": 355, "indent": 1, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 1, "parameters": [2, "莉兹批评", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["莉兹私服.png"]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 死猪头，你干嘛？ "]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 232, "indent": 1, "parameters": [100, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 235, "indent": 1, "parameters": [99]}, {"code": 235, "indent": 1, "parameters": [1]}, {"code": 244, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 16}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 193}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子短发", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11沦陷伊始"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 10, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 10 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 15]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 10 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子短发", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11沦陷伊始"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关京子和猪田在酒店偷情的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件（一）", "仅限事件（二）", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 273, 10, 8, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件（一）"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 201, "indent": 1, "parameters": [0, 273, 10, 8, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "仅限事件（二）"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 6]}, {"code": 201, "indent": 1, "parameters": [0, 273, 19, 7, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子短发", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/11沦陷伊始"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关京子和猪田在酒店偷情的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件（一）", "仅限事件（二）", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 273, 10, 8, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件（一）"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 2]}, {"code": 201, "indent": 1, "parameters": [0, 273, 10, 8, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "仅限事件（二）"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 6]}, {"code": 201, "indent": 1, "parameters": [0, 273, 19, 7, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 14}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 234}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/12按摩演习(一)"]}, {"code": 111, "indent": 0, "parameters": [1, 72, 0, 10, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] 10 \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 0, 10]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] 10 \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 234}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/12按摩演习(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/12莉兹呵猪田进行第一次按摩演习的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [239, 239, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 1, true]}, {"code": 201, "indent": 1, "parameters": [0, 302, 15, 8, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [239, 239, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 3]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 1, true]}, {"code": 201, "indent": 1, "parameters": [0, 302, 15, 8, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 81, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 234}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/12按摩演习(一)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/12莉兹呵猪田进行第一次按摩演习的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [239, 239, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 1, true]}, {"code": 201, "indent": 1, "parameters": [0, 302, 15, 8, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [239, 239, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 3]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 1, true]}, {"code": 201, "indent": 1, "parameters": [0, 302, 15, 8, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 14}, {"id": 17, "name": "EV017", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 304}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/14篝火之夜"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/14莉兹和桐人在篝火旁夜谈的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件（一）", "仅限事件（二）", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [251, 251, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 304]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 1, true]}, {"code": 201, "indent": 1, "parameters": [0, 337, 12, 17, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件（一）"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [251, 251, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 305]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 1, true]}, {"code": 201, "indent": 1, "parameters": [0, 337, 11, 17, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "仅限事件（二）"]}, {"code": 121, "indent": 1, "parameters": [251, 251, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 306]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[-68, -68, 0, 68], 1, true]}, {"code": 201, "indent": 1, "parameters": [0, 337, 11, 17, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [3, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 14}, {"id": 18, "name": "EV018", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 272}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹泳装", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/13进退维谷"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关莉兹被卡在树丛中的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [243, 243, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 272]}, {"code": 201, "indent": 1, "parameters": [0, 336, 11, 13, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [243, 243, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 274]}, {"code": 201, "indent": 1, "parameters": [0, 336, 11, 13, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 14}, {"id": 19, "name": "EV019", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 314}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 8, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/14梦陷囹圄"]}, {"code": 111, "indent": 0, "parameters": [1, 74, 0, 15, 4]}, {"code": 225, "indent": 1, "parameters": [5, 9, 15, false]}, {"code": 224, "indent": 1, "parameters": [[255, 0, 0, 68], 15, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": [""]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[28]变态度\\c[101]不足"]}, {"code": 401, "indent": 1, "parameters": ["\\dac\\c[18]无法解锁此回想"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [186, 186, 0, 0, 15]}, {"code": 111, "indent": 1, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 2, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 2, "parameters": [0, "是"]}, {"code": 122, "indent": 3, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 3, "parameters": [0, 139, true]}, {"code": 123, "indent": 3, "parameters": ["A", 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 402, "indent": 2, "parameters": [1, "否"]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 404, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 234}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/14梦陷囹圄"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关桐人在梦中身体不受控制侵犯莉兹的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [248, 248, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 311]}, {"code": 201, "indent": 1, "parameters": [0, 336, 12, 14, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [248, 248, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 313]}, {"code": 201, "indent": 1, "parameters": [0, 336, 12, 14, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 194, "switch1Valid": true, "switch2Id": 303, "switch2Valid": true, "variableId": 100, "variableValid": false, "variableValue": 234}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/14梦陷囹圄"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关桐人在梦中身体不受控制侵犯莉兹的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [248, 248, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 311]}, {"code": 201, "indent": 1, "parameters": [0, 336, 12, 14, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [248, 248, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 313]}, {"code": 201, "indent": 1, "parameters": [0, 336, 12, 14, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 303, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 234}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/14梦陷囹圄"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关桐人在梦中身体不受控制侵犯莉兹的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [248, 248, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 311]}, {"code": 201, "indent": 1, "parameters": [0, 336, 12, 14, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [248, 248, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 313]}, {"code": 201, "indent": 1, "parameters": [0, 336, 12, 14, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 12}, {"id": 20, "name": "EV020", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 412}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/19按摩演习(二)"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 10]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 234}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/19按摩演习(二)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/19莉兹和猪田进行第二次按摩演习的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [253, 253, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 346, 10, 7, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [253, 253, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 201, "indent": 1, "parameters": [0, 346, 10, 7, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 12}, {"id": 21, "name": "EV021", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 417}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo莉兹浴衣像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/19宾至如归"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/19莉兹给桐人按摩的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [255, 255, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 412]}, {"code": 201, "indent": 1, "parameters": [0, 30, 8, 9, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [255, 255, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 413]}, {"code": 201, "indent": 1, "parameters": [0, 30, 8, 9, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 12}, {"id": 22, "name": "EV022", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 446}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子短发", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/20学园倩影"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 20]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子短发", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/20学园倩影"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关京子和猪田在酒店偷情的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [259, 259, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 239, 12, 9, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [259, 259, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([354, 1, 'A'], true);"]}, {"code": 201, "indent": 1, "parameters": [0, 354, 37, 26, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子短发", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/20学园倩影"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关京子和猪田在酒店偷情的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [259, 259, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 201, "indent": 1, "parameters": [0, 239, 12, 9, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [259, 259, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([354, 1, 'A'], true);"]}, {"code": 201, "indent": 1, "parameters": [0, 354, 37, 26, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 24, "y": 14}, {"id": 23, "name": "EV023", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 457}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子OL", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/21伪三人行"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 20]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子OL", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/21伪三人行"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关京子和猪田在亚丝娜房间做爱的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [260, 260, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([351, 1, 'A'], true);"]}, {"code": 201, "indent": 1, "parameters": [0, 351, 18, 21, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [260, 260, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([351, 1, 'A'], true);"]}, {"code": 201, "indent": 1, "parameters": [0, 351, 18, 21, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 82, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "京子OL", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/21伪三人行"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关京子和猪田在亚丝娜房间做爱的回想,要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 2, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [260, 260, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([351, 1, 'A'], true);"]}, {"code": 201, "indent": 1, "parameters": [0, 351, 18, 21, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 121, "indent": 1, "parameters": [260, 260, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 3]}, {"code": 355, "indent": 1, "parameters": ["$gameSelfSwitches.setValue([351, 1, 'A'], true);"]}, {"code": 201, "indent": 1, "parameters": [0, 351, 18, 21, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 12}, {"id": 24, "name": "EV024", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 417}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo莉兹浴衣像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 6/20裆部防护"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关6/20莉兹和桐人测试护裆的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [262, 262, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 434]}, {"code": 201, "indent": 1, "parameters": [0, 346, 16, 11, 6, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [262, 262, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 435]}, {"code": 201, "indent": 1, "parameters": [0, 346, 9, 6, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 10}, {"id": 25, "name": "EV025", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 412}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹私服像素人", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/2猪突猛进"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 30]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 234}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹私服像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/2猪突猛进"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/2莉兹在医院被猪田强奸的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [278, 278, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 98, 53, 38, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [278, 278, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 98, 52, 38, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 83, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 234}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹私服像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/2猪突猛进"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/2莉兹在医院被猪田强奸的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [278, 278, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 98, 53, 38, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [278, 278, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 1]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 201, "indent": 1, "parameters": [0, 98, 52, 38, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 10}, {"id": 26, "name": "EV026", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 690}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/7心的温度"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 15]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 690}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/7心的温度"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/7莉兹和桐人在游戏中结合的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 121, "indent": 1, "parameters": [907, 907, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 683]}, {"code": 201, "indent": 1, "parameters": [0, 248, 19, 15, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [907, 907, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 684]}, {"code": 201, "indent": 1, "parameters": [0, 248, 19, 15, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 83, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 690}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/7心的温度"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/7莉兹和桐人在游戏中结合的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 121, "indent": 1, "parameters": [907, 907, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 683]}, {"code": 201, "indent": 1, "parameters": [0, 248, 19, 15, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [298, 298, 0]}, {"code": 121, "indent": 1, "parameters": [907, 907, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 684]}, {"code": 201, "indent": 1, "parameters": [0, 248, 19, 15, 8, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 10}, {"id": 27, "name": "EV027", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 694}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 8, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/7冤冤相报"]}, {"code": 122, "indent": 0, "parameters": [186, 186, 0, 0, 5]}, {"code": 111, "indent": 0, "parameters": [1, 72, 1, 186, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，是否花费\\c[113] \\v[186] \\c[0]解锁此事件？"]}, {"code": 102, "indent": 1, "parameters": [["是", "否"], 1, 0, 1, 1]}, {"code": 402, "indent": 1, "parameters": [0, "是"]}, {"code": 122, "indent": 2, "parameters": [72, 72, 2, 1, 186]}, {"code": 212, "indent": 2, "parameters": [0, 139, true]}, {"code": 123, "indent": 2, "parameters": ["A", 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 402, "indent": 1, "parameters": [1, "否"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 404, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac当前不安总量为\\c[113] \\v[72] \\c[0]，此事件需要\\c[113] \\v[186] \\c[0]解锁。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 694}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/7冤冤相报"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/7莉兹报复猪田的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [905, 905, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 367, 91, 37, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [905, 905, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 367, 91, 37, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 83, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 694}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/7冤冤相报"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/7莉兹报复猪田的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [905, 905, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 367, 91, 37, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [905, 905, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 367, 91, 37, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 8}, {"id": 28, "name": "EV028", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 762}, "directionFix": true, "image": {"tileId": 0, "characterName": "fgo莉兹浴衣像素人", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/10性爱木偶"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/10莉兹和桐人在按摩室发生事情的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [914, 914, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 762]}, {"code": 201, "indent": 1, "parameters": [0, 30, 8, 9, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [914, 914, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 765]}, {"code": 201, "indent": 1, "parameters": [0, 30, 8, 9, 0, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 8}, {"id": 29, "name": "EV029", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 471, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 827}, "directionFix": true, "image": {"tileId": 0, "characterName": "莉兹私服像素人", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 108, "indent": 0, "parameters": ["=>事件漂浮文字 : 7/13试用情人"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac这是有关7/13莉兹和桐人在咖啡馆共进晚餐的回想,"]}, {"code": 401, "indent": 0, "parameters": ["\\dac要回顾吗?"]}, {"code": 102, "indent": 0, "parameters": [["完整剧情", "仅限事件", "放弃回顾"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "完整剧情"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [925, 925, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 0]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 826]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 179, 6, 12, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "仅限事件"]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 121, "indent": 1, "parameters": [925, 925, 0]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 122, "indent": 1, "parameters": [82, 82, 0, 1, 100]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 0, 827]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 179, 6, 12, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [2, "放弃回顾"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 8}]}