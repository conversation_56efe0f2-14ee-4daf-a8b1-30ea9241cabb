{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "SF_Concrete", "battleback2Name": "SF_Building1", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 32, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 6, "width": 29, "data": [1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 5922, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5924, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 5922, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5924, 1536, 1536, 5906, 5916, 5900, 5916, 5900, 5916, 5900, 5916, 5913, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 5906, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5913, 1536, 1536, 5920, 6274, 5920, 6274, 5920, 6274, 5920, 6274, 5920, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 5920, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 5920, 1536, 1536, 5920, 6280, 5920, 6280, 5920, 6280, 5920, 6280, 5920, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 5920, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 5920, 1536, 1536, 5920, 1585, 5932, 1585, 5920, 1585, 5932, 1585, 5920, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 5920, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 5920, 1536, 1536, 5920, 1585, 6279, 1585, 5920, 1585, 6279, 1585, 5920, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 5920, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 5920, 1536, 1536, 5920, 1585, 6285, 1585, 5920, 1585, 6285, 1585, 5920, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 5920, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 5920, 1536, 1536, 5920, 1585, 1585, 1585, 5920, 1585, 1585, 1585, 5920, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 5920, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 5920, 1536, 1536, 5920, 1585, 5931, 5921, 5903, 5933, 1585, 5931, 5899, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5914, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 5920, 1536, 1536, 5920, 1585, 6275, 6274, 5920, 6278, 1585, 6275, 5906, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5916, 5913, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 5920, 1536, 1536, 5920, 1585, 6281, 6280, 5920, 6284, 1585, 6281, 5920, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 5920, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 5920, 1536, 1536, 5920, 1585, 1585, 1585, 5920, 1585, 1585, 1585, 5920, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 5920, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 5920, 1536, 1536, 5920, 1585, 1585, 1585, 5920, 1585, 1585, 1585, 5920, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 5920, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 5920, 1536, 1536, 5920, 1585, 5931, 5921, 5927, 1585, 5931, 5921, 5927, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 5920, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 5920, 1536, 1536, 5920, 1585, 6275, 6274, 6278, 1585, 6275, 6274, 6278, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 5920, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 5920, 1536, 1536, 5920, 1585, 6281, 6280, 6284, 1585, 6281, 6280, 6284, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 5929, 5921, 5933, 1625, 5931, 5921, 5921, 5921, 5915, 1536, 1536, 5920, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 6275, 6274, 6278, 1625, 6275, 6274, 6274, 6274, 5920, 1536, 1536, 5920, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 6281, 6280, 6284, 1625, 6281, 6280, 6280, 6280, 5920, 1536, 1536, 5920, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 5920, 1536, 1536, 5905, 5908, 5924, 1585, 5922, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5908, 5924, 1585, 5922, 5908, 5908, 5908, 5908, 5908, 5924, 1585, 5922, 5914, 1536, 1536, 5906, 5916, 5926, 1585, 5928, 5900, 5916, 5916, 5900, 5916, 5916, 5916, 5916, 5916, 5916, 5926, 1585, 5928, 5916, 5900, 5916, 5916, 5916, 5926, 1585, 5928, 5913, 1536, 1536, 5920, 6274, 6278, 1585, 6275, 5920, 6275, 6274, 5920, 6274, 6274, 6274, 6274, 6274, 6274, 6278, 1585, 6275, 6274, 5920, 6274, 6274, 6274, 6278, 1585, 6275, 5920, 1536, 1536, 5920, 6280, 6284, 1585, 6281, 5920, 6280, 6280, 5920, 6280, 6280, 6280, 6280, 6280, 6280, 6284, 1625, 6281, 6280, 5920, 6280, 6280, 6280, 6284, 1585, 6281, 5920, 1536, 1536, 5920, 1585, 1585, 1585, 1585, 5932, 1585, 1585, 5920, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 5920, 1585, 1585, 1585, 1585, 1585, 1585, 5920, 1536, 1536, 5920, 1585, 1585, 1585, 1585, 6279, 1585, 1585, 5920, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 5920, 1585, 1585, 1585, 1585, 1585, 1585, 5920, 1536, 1536, 5920, 1585, 1585, 1585, 1585, 6285, 1585, 1585, 5920, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 5920, 1585, 1585, 1585, 1585, 1585, 1585, 5920, 1536, 1536, 5920, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 5920, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 1625, 5920, 1585, 1585, 1585, 1585, 1585, 1585, 5920, 1536, 1536, 5929, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5919, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5921, 5919, 5921, 5921, 5921, 5921, 5921, 5921, 5927, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3194, 0, 3194, 0, 3194, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3196, 0, 3196, 0, 3196, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 243, 244, 243, 244, 0, 0, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 243, 244, 243, 244, 0, 0, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 243, 244, 243, 244, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 251, 142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 642, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 243, 244, 243, 244, 243, 244, 0, 0, 0, 250, 0, 649, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 251, 610, 0, 0, 0, 0, 0, 0, 243, 244, 243, 244, 243, 244, 0, 0, 130, 252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 319, 0, 319, 0, 319, 0, 0, 0, 0, 0, 161, 0, 161, 0, 161, 0, 161, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 181, 181, 181, 181, 181, 181, 181, 0, 0, 0, 0, 169, 0, 169, 0, 169, 0, 169, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 189, 189, 189, 189, 189, 189, 189, 0, 0, 0, 0, 0, 319, 0, 0, 0, 319, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 129, 132, 129, 132, 0, 249, 131, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 0, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 129, 132, 129, 132, 0, 249, 131, 0, 0, 0, 0, 0, 0, 0, 0, 163, 0, 163, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 0, 153, 0, 0, 0, 179, 0, 0, 0, 0, 0, 626, 626, 0, 171, 0, 171, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 129, 132, 129, 132, 0, 0, 187, 0, 0, 0, 0, 0, 634, 634, 0, 0, 0, 0, 0, 293, 0, 0, 0, 0, 0, 0, 0, 293, 0, 153, 0, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 595, 0, 0, 0, 595, 0, 301, 0, 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 283, 0, 0, 0, 0, 0, 0, 0, 0, 0, 129, 252, 0, 0, 0, 0, 149, 0, 0, 0, 0, 0, 514, 319, 0, 0, 515, 319, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 0, 0, 0, 0, 0, 157, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 211, 0, 259, 260, 261, 0, 259, 260, 261, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 219, 0, 267, 268, 269, 0, 267, 268, 269, 0, 0, 319, 0, 0, 292, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 283, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 0, 0, 0, 0, 0, 620, 0, 139, 140, 0, 319, 0, 0, 0, 0, 300, 319, 0, 140, 139, 0, 319, 0, 706, 0, 0, 0, 0, 181, 0, 0, 180, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 181, 0, 0, 0, 0, 641, 643, 721, 645, 0, 714, 0, 0, 0, 0, 189, 0, 0, 188, 0, 0, 224, 0, 129, 132, 129, 132, 129, 132, 189, 0, 0, 131, 0, 656, 651, 652, 653, 0, 722, 0, 0, 0, 0, 0, 0, 0, 0, 292, 0, 232, 0, 153, 0, 153, 0, 153, 0, 0, 0, 0, 0, 0, 665, 0, 0, 0, 0, 0, 0, 0, 0, 0, 130, 252, 0, 0, 0, 0, 0, 0, 129, 132, 129, 132, 129, 132, 0, 0, 251, 129, 0, 0, 147, 0, 147, 0, 147, 0, 0, 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 153, 0, 153, 0, 153, 0, 0, 0, 0, 153, 0, 0, 147, 0, 147, 0, 147, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 5, 0, 5, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 5, 0, 5, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 5, 0, 5, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 0, 37, 0, 39, 0, 44, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 0, 37, 0, 39, 0, 44, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 0, 37, 0, 39, 0, 44, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 0, 37, 0, 39, 0, 44, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 50, 50, 50, 50, 50, 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 27, 0, 27, 27, 27, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 27, 0, 27, 27, 27, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 27, 27, 27, 27, 27, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 27, 27, 27, 27, 27, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 27, 27, 27, 27, 27, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 27, 27, 27, 27, 27, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 58, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 121, "indent": 0, "parameters": [71, 71, 1]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "Castle1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 58, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 117, "indent": 0, "parameters": [362]}, {"code": 121, "indent": 0, "parameters": [71, 71, 0]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 241, "indent": 0, "parameters": [{"name": "Castle1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 117, "indent": 0, "parameters": [366]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](午休时间到了。)"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": 0}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 230, "indent": 0, "parameters": [25]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 玩家 : 移动到 : 位置[24,26]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, true]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 34, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [80, 80, 0, 4, "\"午休\""]}, {"code": 122, "indent": 0, "parameters": [100, 100, 1, 0, 1]}, {"code": 121, "indent": 0, "parameters": [71, 71, 1]}, {"code": 121, "indent": 0, "parameters": [79, 79, 1]}, {"code": 121, "indent": 0, "parameters": [86, 86, 1]}, {"code": 121, "indent": 0, "parameters": [287, 287, 0]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}], "x": 12, "y": 9}, {"id": 2, "name": "EV002", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 318}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 0, "characterIndex": 5}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "SC_door5", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17, "indent": 0}, {"code": 15, "parameters": [3], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Bell3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [183, 183, 0, 0, 291]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [1, 183, 197, 198, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 79, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 318}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 0, "characterIndex": 5}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在不需要去其他楼层。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 12}, {"id": 3, "name": "EV003", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 318}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 0, "characterIndex": 5}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "SC_door5", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17, "indent": 0}, {"code": 15, "parameters": [3], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Bell3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [183, 183, 0, 0, 291]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [1, 183, 197, 198, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 79, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 318}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 0, "characterIndex": 5}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在不需要去其他楼层。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 12}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 52, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!food1", "direction": 4, "pattern": 0, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 25, "y": 26}, {"id": 5, "name": "EV005周二职员", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 52, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "上班族 (5)", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 117, "indent": 0, "parameters": [354]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 27}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 54, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "成人女性用", "direction": 4, "pattern": 1, "characterIndex": 2}, "list": [{"code": 117, "indent": 0, "parameters": [354]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 26}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door5", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 2, "y": 16}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door5", "direction": 2, "pattern": 0, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](这是女厕所……)"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 6, "y": 16}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](有人。)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 2, "y": 7}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](有人。)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 4, "y": 7}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 111, "indent": 0, "parameters": [1, 100, 0, 846, 0]}, {"code": 117, "indent": 1, "parameters": [542]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 6, "y": 7}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 0, "characterIndex": 1}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 12, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 8, "y": 7}, {"id": 13, "name": "EV013一之濑", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 58, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "上班族 (7)", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](认真工作中，还是不要打扰别人了……)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 56, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 57, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 5}, {"id": 14, "name": "EV014", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 52, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!food1", "direction": 4, "pattern": 0, "characterIndex": 5}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 27}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 318}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 0, "characterIndex": 5}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "SC_door5", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 17, "indent": 0}, {"code": 15, "parameters": [3], "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [3], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 250, "indent": 0, "parameters": [{"name": "Bell3", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 122, "indent": 0, "parameters": [183, 183, 0, 0, 291]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 201, "indent": 0, "parameters": [1, 183, 197, 198, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 79, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 318}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 0, "characterIndex": 5}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac现在不需要去其他楼层。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 12}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 97, 0, 7, 0]}, {"code": 111, "indent": 1, "parameters": [1, 96, 0, 14, 0]}, {"code": 203, "indent": 2, "parameters": [17, 2, 40, 0, 0]}, {"code": 111, "indent": 2, "parameters": [0, 477, 1]}, {"code": 111, "indent": 3, "parameters": [0, 475, 0]}, {"code": 203, "indent": 4, "parameters": [37, 0, 4, 22, 0]}, {"code": 203, "indent": 4, "parameters": [34, 0, 4, 22, 0]}, {"code": 203, "indent": 4, "parameters": [35, 0, 4, 19, 0]}, {"code": 203, "indent": 4, "parameters": [29, 0, 3, 26, 8]}, {"code": 203, "indent": 4, "parameters": [19, 0, 3, 25, 2]}, {"code": 205, "indent": 4, "parameters": [29, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(29).requestBalloon(3);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(19).requestBalloon(4);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 45, "parameters": ["$gameMap.event(29).requestBalloon(3);"], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 45, "parameters": ["$gameMap.event(19).requestBalloon(4);"], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 411, "indent": 3, "parameters": []}, {"code": 203, "indent": 4, "parameters": [13, 0, 11, 2, 0]}, {"code": 203, "indent": 4, "parameters": [9, 2, 11, 0, 0]}, {"code": 203, "indent": 4, "parameters": [37, 0, 4, 6, 0]}, {"code": 203, "indent": 4, "parameters": [34, 0, 4, 8, 0]}, {"code": 203, "indent": 4, "parameters": [35, 0, 2, 9, 0]}, {"code": 203, "indent": 4, "parameters": [29, 0, 4, 5, 2]}, {"code": 203, "indent": 4, "parameters": [19, 0, 4, 6, 8]}, {"code": 205, "indent": 4, "parameters": [29, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(29).requestBalloon(5);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(19).requestBalloon(4);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 45, "parameters": ["$gameMap.event(29).requestBalloon(5);"], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 45, "parameters": ["$gameMap.event(19).requestBalloon(4);"], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 356, "indent": 4, "parameters": ["audiosource bgs 29"]}, {"code": 245, "indent": 4, "parameters": [{"name": "喘ぎ声とピストン　弱", "volume": 35, "pitch": 90, "pan": 0}]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 111, "indent": 3, "parameters": [0, 475, 1]}, {"code": 356, "indent": 4, "parameters": ["audiosource bgs 29"]}, {"code": 245, "indent": 4, "parameters": [{"name": "女性急促呼吸声", "volume": 50, "pitch": 110, "pan": 0}]}, {"code": 203, "indent": 4, "parameters": [9, 2, 11, 0, 0]}, {"code": 203, "indent": 4, "parameters": [37, 0, 4, 6, 0]}, {"code": 203, "indent": 4, "parameters": [29, 0, 4, 5, 2]}, {"code": 203, "indent": 4, "parameters": [19, 0, 4, 6, 8]}, {"code": 205, "indent": 4, "parameters": [29, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(29).requestBalloon(5);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(19).requestBalloon(4);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 45, "parameters": ["$gameMap.event(29).requestBalloon(5);"], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 45, "parameters": ["$gameMap.event(19).requestBalloon(4);"], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 4, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 0, "indent": 4, "parameters": []}, {"code": 412, "indent": 3, "parameters": []}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 96, 0, 13, 0]}, {"code": 203, "indent": 2, "parameters": [21, 2, 30, 0, 0]}, {"code": 203, "indent": 2, "parameters": [23, 2, 33, 0, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 111, "indent": 1, "parameters": [1, 96, 0, 10, 0]}, {"code": 111, "indent": 2, "parameters": [0, 451, 1]}, {"code": 122, "indent": 3, "parameters": [100, 100, 0, 0, 748]}, {"code": 121, "indent": 3, "parameters": [451, 451, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 205, "indent": 2, "parameters": [17, {"list": [{"code": 36, "indent": null}, {"code": 18, "indent": null}, {"code": 35, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 35, "indent": null}]}, {"code": 203, "indent": 2, "parameters": [28, 0, 15, 27, 4]}, {"code": 205, "indent": 2, "parameters": [28, {"list": [{"code": 17, "indent": null}, {"code": 35, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 2, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 35, "indent": null}]}, {"code": 205, "indent": 2, "parameters": [17, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(17).requestBalloon(4);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(28).requestBalloon(4);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 45, "parameters": ["$gameMap.event(17).requestBalloon(4);"], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 45, "parameters": ["$gameMap.event(28).requestBalloon(4);"], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 2, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 205, "indent": 0, "parameters": [17, {"list": [{"code": 15, "parameters": [60], "indent": 0}, {"code": 45, "parameters": ["$gameMap.event(17).requestBalloon(10);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(17).requestBalloon(10);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 214, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 11, "y": 8}, {"id": 17, "name": "EV017铃木", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 58, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "人妻老公", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": false}], "x": 14, "y": 27}, {"id": 18, "name": "EV018", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 54, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "!food4", "direction": 2, "pattern": 0, "characterIndex": 5}, "list": [{"code": 111, "indent": 0, "parameters": [2, "A", 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac \\c[210](餐盘的回收清洁是由行政部处理。"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 放在这里就可以了。)"]}, {"code": 123, "indent": 1, "parameters": ["A", 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 26}, {"id": 19, "name": "EV019市场部长", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "おっさん", "direction": 8, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 27}, {"id": 20, "name": "EV020一课课长", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 51, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "restaurant_staff", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 117, "indent": 0, "parameters": [354]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 26, "y": 14}, {"id": 21, "name": "EV021周一偷懒员工", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 51, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "上班族 (5)", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 117, "indent": 0, "parameters": [354]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 15}, {"id": 22, "name": "EV022", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 55, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "上班族 (5)", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 4, "y": 5}, {"id": 23, "name": "EV023周一摸鱼员工", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 51, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "上班族 (5)", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 117, "indent": 0, "parameters": [354]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 15}, {"id": 24, "name": "EV024", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 53, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "上班族 (5)", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 117, "indent": 0, "parameters": [354]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 58, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 18}, {"id": 25, "name": "EV025", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 25}, "directionFix": true, "image": {"tileId": 0, "characterName": "!door6", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 4, "y": 23}, {"id": 26, "name": "EV026", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [6, -1, 2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac市场二课的业绩记录。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 12, "y": 16}, {"id": 27, "name": "EV027", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [6, -1, 2]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac市场一课的业绩记录。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 16, "y": 16}, {"id": 28, "name": "EV028铃木妻子", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 359}, "directionFix": false, "image": {"tileId": 0, "characterName": "人妻", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 97, 0, 7, 0]}, {"code": 111, "indent": 1, "parameters": [1, 96, 0, 10, 0]}, {"code": 122, "indent": 2, "parameters": [123, 123, 0, 0, 201]}, {"code": 355, "indent": 2, "parameters": ["let array122 = $gameVariables.value(122);"]}, {"code": 655, "indent": 2, "parameters": ["let value123 = $gameVariables.value(123);"]}, {"code": 655, "indent": 2, "parameters": ["if (array122.includes(value123)) {"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, true);"]}, {"code": 655, "indent": 2, "parameters": ["} else {"]}, {"code": 655, "indent": 2, "parameters": ["    array122.push(value123);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameVariables.setValue(122, array122);"]}, {"code": 655, "indent": 2, "parameters": ["    $gameSwitches.setValue(355, false);"]}, {"code": 655, "indent": 2, "parameters": ["}"]}, {"code": 111, "indent": 2, "parameters": [0, 355, 1]}, {"code": 205, "indent": 3, "parameters": [17, {"list": [{"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 213, "indent": 3, "parameters": [17, 4, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<铃木>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac 今天怎么过来了？"]}, {"code": 213, "indent": 3, "parameters": [28, 4, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<人妻>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac 你忘了？下午学校开家长会。"]}, {"code": 401, "indent": 3, "parameters": ["\\dac 我想着顺路，就带了便当来看你。"]}, {"code": 213, "indent": 3, "parameters": [-1, 3, true]}, {"code": 101, "indent": 3, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 3, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 3, "parameters": ["\\dac \\c[210](妻子来给丈夫送爱心便当，真是温馨呀！"]}, {"code": 401, "indent": 3, "parameters": ["\\dac 不要打扰他们的相处时光了。)"]}, {"code": 205, "indent": 3, "parameters": [17, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(17).requestBalloon(4);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(28).requestBalloon(4);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 3, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 45, "parameters": ["$gameMap.event(17).requestBalloon(4);"], "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 45, "parameters": ["$gameMap.event(28).requestBalloon(4);"], "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 3, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 411, "indent": 2, "parameters": []}, {"code": 213, "indent": 3, "parameters": [17, 4, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [28, 4, false]}, {"code": 230, "indent": 3, "parameters": [30]}, {"code": 213, "indent": 3, "parameters": [-1, 3, true]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 119, "indent": 2, "parameters": ["结束"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 118, "indent": 0, "parameters": ["结束"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 11, "y": 1}, {"id": 29, "name": "EV029诗乃", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "诗乃像素", "direction": 6, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 1}, {"id": 30, "name": "EV030特-偷懒员工", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 51, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "上班族 (5)", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 122, "indent": 0, "parameters": [123, 123, 0, 0, 101]}, {"code": 117, "indent": 0, "parameters": [354]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 1}, {"id": 31, "name": "EV031", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 356}, "directionFix": true, "image": {"tileId": 0, "characterName": "", "direction": 8, "pattern": 0, "characterIndex": 7}, "list": [{"code": 117, "indent": 0, "parameters": [5]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac要结束午休吗？"]}, {"code": 102, "indent": 0, "parameters": [["是", "否"], 1, 0, 2, 1]}, {"code": 402, "indent": 0, "parameters": [0, "是"]}, {"code": 117, "indent": 1, "parameters": [157]}, {"code": 249, "indent": 1, "parameters": [{"name": "LNSM_ME06_Sleep2", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": null}, {"code": 15, "parameters": [10], "indent": null}, {"code": 19, "indent": null}, {"code": 33, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 213, "indent": 1, "parameters": [-1, 8, false]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 60, true]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 117, "indent": 1, "parameters": [363]}, {"code": 122, "indent": 1, "parameters": [100, 100, 1, 0, 1]}, {"code": 117, "indent": 1, "parameters": [6]}, {"code": 121, "indent": 1, "parameters": [287, 287, 1]}, {"code": 117, "indent": 1, "parameters": [159]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "否"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 等会儿再开始工作吧……"]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [6]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 71, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 25}, {"id": 32, "name": "EV032", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 356}, "directionFix": true, "image": {"tileId": 0, "characterName": "!Flame", "direction": 8, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,48]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 2, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 71, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 14, "y": 24}, {"id": 33, "name": "EV033特-摸鱼员工", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 51, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "上班族 (5)", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 气泡 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 3"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 122, "indent": 0, "parameters": [123, 123, 0, 0, 101]}, {"code": 117, "indent": 0, "parameters": [354]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 1}, {"id": 34, "name": "EV034触发点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 846, 0]}, {"code": 117, "indent": 1, "parameters": [542]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 15, "y": 1}, {"id": 35, "name": "EV035触发点2", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 182}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [1, 100, 0, 846, 0]}, {"code": 111, "indent": 1, "parameters": [0, 66, 0]}, {"code": 243, "indent": 2, "parameters": []}, {"code": 241, "indent": 2, "parameters": [{"name": "Castle1", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 121, "indent": 2, "parameters": [66, 66, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["ForceGabClear"]}, {"code": 203, "indent": 1, "parameters": [0, 0, 0, 0, 0]}, {"code": 117, "indent": 1, "parameters": [6]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 16, "y": 1}, {"id": 36, "name": "EV036", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 478, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 185, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 355, "indent": 0, "parameters": ["let playerX = $gamePlayer.x;"]}, {"code": 655, "indent": 0, "parameters": ["let playerY = $gamePlayer.y;"]}, {"code": 655, "indent": 0, "parameters": ["// 检查玩家是否在指定位置"]}, {"code": 655, "indent": 0, "parameters": ["if ((playerX === 2 && playerY === 5) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 2 && playerY === 6) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 4 && playerY === 8)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, true); // 设置开关 479 为开"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, false); // 可选：设置开关 479 为关"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 479, 0]}, {"code": 111, "indent": 1, "parameters": [0, 66, 1]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [66, 66, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 478, "switch1Valid": true, "switch2Id": 475, "switch2Valid": true, "variableId": 185, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 4, 0]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 2, 0]}, {"code": 111, "indent": 2, "parameters": [0, 66, 1]}, {"code": 244, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [66, 66, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 13, "y": 7}, {"id": 37, "name": "EV037爱心", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["indicator : 爱心 : 0 : 0"]}, {"code": 108, "indent": 0, "parameters": ["range_indicator : 10"]}, {"code": 108, "indent": 0, "parameters": ["pulse_indicator : 0.008"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 0}, {"id": 38, "name": "EV038", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 478, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 185, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [29, 5, true]}, {"code": 355, "indent": 0, "parameters": ["let playerX = $gamePlayer.x;"]}, {"code": 655, "indent": 0, "parameters": ["let playerY = $gamePlayer.y;"]}, {"code": 655, "indent": 0, "parameters": ["// 检查玩家是否在指定位置"]}, {"code": 655, "indent": 0, "parameters": ["if ((playerX === 2 && playerY === 5) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 2 && playerY === 6) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 4 && playerY === 8)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, true); // 设置开关 479 为开"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, false); // 可选：设置开关 479 为关"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 479, 0]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 203, "indent": 1, "parameters": [35, 0, 2, 9, 0]}, {"code": 111, "indent": 1, "parameters": [0, 66, 1]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [66, 66, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["PushGab 72 \\}废物！管不好…手下！嗯…连个直播都搞不好！"]}, {"code": 122, "indent": 1, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 213, "indent": 0, "parameters": [19, 3, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["let playerX = $gamePlayer.x;"]}, {"code": 655, "indent": 0, "parameters": ["let playerY = $gamePlayer.y;"]}, {"code": 655, "indent": 0, "parameters": ["// 检查玩家是否在指定位置"]}, {"code": 655, "indent": 0, "parameters": ["if ((playerX === 2 && playerY === 5) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 2 && playerY === 6) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 4 && playerY === 8)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, true); // 设置开关 479 为开"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, false); // 可选：设置开关 479 为关"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 479, 0]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 203, "indent": 1, "parameters": [35, 0, 2, 9, 0]}, {"code": 111, "indent": 1, "parameters": [0, 66, 1]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [66, 66, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["PushGab 49 \\}是我…教导无方！我是废物…就该被您调教！"]}, {"code": 122, "indent": 1, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 213, "indent": 0, "parameters": [29, 7, true]}, {"code": 355, "indent": 0, "parameters": ["let playerX = $gamePlayer.x;"]}, {"code": 655, "indent": 0, "parameters": ["let playerY = $gamePlayer.y;"]}, {"code": 655, "indent": 0, "parameters": ["// 检查玩家是否在指定位置"]}, {"code": 655, "indent": 0, "parameters": ["if ((playerX === 2 && playerY === 5) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 2 && playerY === 6) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 4 && playerY === 8)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, true); // 设置开关 479 为开"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, false); // 可选：设置开关 479 为关"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 479, 0]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 203, "indent": 1, "parameters": [35, 0, 2, 9, 0]}, {"code": 111, "indent": 1, "parameters": [0, 66, 1]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [66, 66, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["PushGab 72 \\}想被我调教？哼…嗯…你也配？"]}, {"code": 122, "indent": 1, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 355, "indent": 0, "parameters": ["let playerX = $gamePlayer.x;"]}, {"code": 655, "indent": 0, "parameters": ["let playerY = $gamePlayer.y;"]}, {"code": 655, "indent": 0, "parameters": ["// 检查玩家是否在指定位置"]}, {"code": 655, "indent": 0, "parameters": ["if ((playerX === 2 && playerY === 5) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 2 && playerY === 6) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 4 && playerY === 8)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, true); // 设置开关 479 为开"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, false); // 可选：设置开关 479 为关"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 479, 0]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 203, "indent": 1, "parameters": [35, 0, 2, 9, 0]}, {"code": 111, "indent": 1, "parameters": [0, 66, 1]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [66, 66, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["PushGab 72 \\}不争气的…嗯…废屌！"]}, {"code": 122, "indent": 1, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 213, "indent": 0, "parameters": [19, 3, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["let playerX = $gamePlayer.x;"]}, {"code": 655, "indent": 0, "parameters": ["let playerY = $gamePlayer.y;"]}, {"code": 655, "indent": 0, "parameters": ["// 检查玩家是否在指定位置"]}, {"code": 655, "indent": 0, "parameters": ["if ((playerX === 2 && playerY === 5) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 2 && playerY === 6) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 4 && playerY === 8)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, true); // 设置开关 479 为开"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, false); // 可选：设置开关 479 为关"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 479, 0]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 203, "indent": 1, "parameters": [35, 0, 2, 9, 0]}, {"code": 111, "indent": 1, "parameters": [0, 66, 1]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [66, 66, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["PushGab 49 \\}是，是，随您…骑我！"]}, {"code": 122, "indent": 1, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["let playerX = $gamePlayer.x;"]}, {"code": 655, "indent": 0, "parameters": ["let playerY = $gamePlayer.y;"]}, {"code": 655, "indent": 0, "parameters": ["// 检查玩家是否在指定位置"]}, {"code": 655, "indent": 0, "parameters": ["if ((playerX === 2 && playerY === 5) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 2 && playerY === 6) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 4 && playerY === 8)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, true); // 设置开关 479 为开"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, false); // 可选：设置开关 479 为关"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 479, 0]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 203, "indent": 1, "parameters": [35, 0, 2, 9, 0]}, {"code": 111, "indent": 1, "parameters": [0, 66, 1]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [66, 66, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["PushGab 49 \\}骑得越狠，我…我越知道自己多废！"]}, {"code": 122, "indent": 1, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 213, "indent": 0, "parameters": [29, 5, true]}, {"code": 355, "indent": 0, "parameters": ["let playerX = $gamePlayer.x;"]}, {"code": 655, "indent": 0, "parameters": ["let playerY = $gamePlayer.y;"]}, {"code": 655, "indent": 0, "parameters": ["// 检查玩家是否在指定位置"]}, {"code": 655, "indent": 0, "parameters": ["if ((playerX === 2 && playerY === 5) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 2 && playerY === 6) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 4 && playerY === 8)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, true); // 设置开关 479 为开"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, false); // 可选：设置开关 479 为关"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 479, 0]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 203, "indent": 1, "parameters": [35, 0, 2, 9, 0]}, {"code": 111, "indent": 1, "parameters": [0, 66, 1]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [66, 66, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["PushGab 72 \\}蠢材！哈啊…贱货！"]}, {"code": 122, "indent": 1, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 213, "indent": 0, "parameters": [19, 3, true]}, {"code": 355, "indent": 0, "parameters": ["let playerX = $gamePlayer.x;"]}, {"code": 655, "indent": 0, "parameters": ["let playerY = $gamePlayer.y;"]}, {"code": 655, "indent": 0, "parameters": ["// 检查玩家是否在指定位置"]}, {"code": 655, "indent": 0, "parameters": ["if ((playerX === 2 && playerY === 5) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 2 && playerY === 6) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 4 && playerY === 8)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, true); // 设置开关 479 为开"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, false); // 可选：设置开关 479 为关"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 479, 0]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 203, "indent": 1, "parameters": [35, 0, 2, 9, 0]}, {"code": 111, "indent": 1, "parameters": [0, 66, 1]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [66, 66, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["PushGab 49 \\}我就是个愚蠢的贱货！"]}, {"code": 122, "indent": 1, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 213, "indent": 0, "parameters": [29, 6, true]}, {"code": 355, "indent": 0, "parameters": ["let playerX = $gamePlayer.x;"]}, {"code": 655, "indent": 0, "parameters": ["let playerY = $gamePlayer.y;"]}, {"code": 655, "indent": 0, "parameters": ["// 检查玩家是否在指定位置"]}, {"code": 655, "indent": 0, "parameters": ["if ((playerX === 2 && playerY === 5) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 2 && playerY === 6) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 4 && playerY === 8)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, true); // 设置开关 479 为开"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, false); // 可选：设置开关 479 为关"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 479, 0]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 203, "indent": 1, "parameters": [35, 0, 2, 9, 0]}, {"code": 111, "indent": 1, "parameters": [0, 66, 1]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [66, 66, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["PushGab 72 \\}哈啊…瞧你……这贱样！"]}, {"code": 122, "indent": 1, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 213, "indent": 0, "parameters": [19, 3, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["let playerX = $gamePlayer.x;"]}, {"code": 655, "indent": 0, "parameters": ["let playerY = $gamePlayer.y;"]}, {"code": 655, "indent": 0, "parameters": ["// 检查玩家是否在指定位置"]}, {"code": 655, "indent": 0, "parameters": ["if ((playerX === 2 && playerY === 5) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 2 && playerY === 6) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 4 && playerY === 8)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, true); // 设置开关 479 为开"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, false); // 可选：设置开关 479 为关"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 479, 0]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 203, "indent": 1, "parameters": [35, 0, 2, 9, 0]}, {"code": 111, "indent": 1, "parameters": [0, 66, 1]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [66, 66, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [196, 196, 1, 0, 1]}, {"code": 356, "indent": 1, "parameters": ["PushGab 49 \\}我这种贱狗…只配给您当玩具！"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 213, "indent": 0, "parameters": [29, 5, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["let playerX = $gamePlayer.x;"]}, {"code": 655, "indent": 0, "parameters": ["let playerY = $gamePlayer.y;"]}, {"code": 655, "indent": 0, "parameters": ["// 检查玩家是否在指定位置"]}, {"code": 655, "indent": 0, "parameters": ["if ((playerX === 2 && playerY === 5) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 2 && playerY === 6) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 4 && playerY === 8)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, true); // 设置开关 479 为开"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, false); // 可选：设置开关 479 为关"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 479, 0]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 203, "indent": 1, "parameters": [35, 0, 2, 9, 0]}, {"code": 111, "indent": 1, "parameters": [0, 66, 1]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [66, 66, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [196, 196, 1, 0, 1]}, {"code": 356, "indent": 1, "parameters": ["PushGab 72 \\}嗯…哈啊…你、你连…啊…玩具…都不配！"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 213, "indent": 0, "parameters": [19, 4, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["let playerX = $gamePlayer.x;"]}, {"code": 655, "indent": 0, "parameters": ["let playerY = $gamePlayer.y;"]}, {"code": 655, "indent": 0, "parameters": ["// 检查玩家是否在指定位置"]}, {"code": 655, "indent": 0, "parameters": ["if ((playerX === 2 && playerY === 5) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 2 && playerY === 6) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 4 && playerY === 8)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, true); // 设置开关 479 为开"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, false); // 可选：设置开关 479 为关"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 479, 0]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 203, "indent": 1, "parameters": [35, 0, 2, 9, 0]}, {"code": 111, "indent": 1, "parameters": [0, 66, 1]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [66, 66, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["PushGab 49 \\}女王陛下继续辱骂我！我爽死了！"]}, {"code": 122, "indent": 1, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 213, "indent": 0, "parameters": [29, 5, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["let playerX = $gamePlayer.x;"]}, {"code": 655, "indent": 0, "parameters": ["let playerY = $gamePlayer.y;"]}, {"code": 655, "indent": 0, "parameters": ["// 检查玩家是否在指定位置"]}, {"code": 655, "indent": 0, "parameters": ["if ((playerX === 2 && playerY === 5) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 2 && playerY === 6) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 4 && playerY === 8)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, true); // 设置开关 479 为开"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, false); // 可选：设置开关 479 为关"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 479, 0]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 203, "indent": 1, "parameters": [35, 0, 2, 9, 0]}, {"code": 111, "indent": 1, "parameters": [0, 66, 1]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [66, 66, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 122, "indent": 1, "parameters": [196, 196, 1, 0, 1]}, {"code": 356, "indent": 1, "parameters": ["PushGab 72 \\}你还有脸爽？哼嗯…哈啊……"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 213, "indent": 0, "parameters": [29, 5, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["let playerX = $gamePlayer.x;"]}, {"code": 655, "indent": 0, "parameters": ["let playerY = $gamePlayer.y;"]}, {"code": 655, "indent": 0, "parameters": ["// 检查玩家是否在指定位置"]}, {"code": 655, "indent": 0, "parameters": ["if ((playerX === 2 && playerY === 5) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 2 && playerY === 6) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 4 && playerY === 8)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, true); // 设置开关 479 为开"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, false); // 可选：设置开关 479 为关"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 479, 0]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 203, "indent": 1, "parameters": [35, 0, 2, 9, 0]}, {"code": 111, "indent": 1, "parameters": [0, 66, 1]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [66, 66, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["PushGab 72 \\}不可能给你射的。起来，给我舔！"]}, {"code": 122, "indent": 1, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 213, "indent": 0, "parameters": [19, 4, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["let playerX = $gamePlayer.x;"]}, {"code": 655, "indent": 0, "parameters": ["let playerY = $gamePlayer.y;"]}, {"code": 655, "indent": 0, "parameters": ["// 检查玩家是否在指定位置"]}, {"code": 655, "indent": 0, "parameters": ["if ((playerX === 2 && playerY === 5) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 2 && playerY === 6) || "]}, {"code": 655, "indent": 0, "parameters": ["    (playerX === 4 && playerY === 8)) {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, true); // 设置开关 479 为开"]}, {"code": 655, "indent": 0, "parameters": ["} else {"]}, {"code": 655, "indent": 0, "parameters": ["    $gameSwitches.setValue(479, false); // 可选：设置开关 479 为关"]}, {"code": 655, "indent": 0, "parameters": ["}"]}, {"code": 111, "indent": 0, "parameters": [0, 479, 0]}, {"code": 117, "indent": 1, "parameters": [5]}, {"code": 203, "indent": 1, "parameters": [35, 0, 2, 9, 0]}, {"code": 111, "indent": 1, "parameters": [0, 66, 1]}, {"code": 244, "indent": 2, "parameters": []}, {"code": 121, "indent": 2, "parameters": [66, 66, 0]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 356, "indent": 1, "parameters": ["PushGab 49 \\}喳！这也是奴才的荣幸！"]}, {"code": 122, "indent": 1, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 205, "indent": 0, "parameters": [19, {"list": [{"code": 44, "parameters": [{"name": "bob_cloth1", "volume": 90, "pitch": 100, "pan": 0}], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 44, "parameters": [{"name": "bob_cloth1", "volume": 90, "pitch": 100, "pan": 0}], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": ["audiosource bgs 29"]}, {"code": 245, "indent": 0, "parameters": [{"name": "女性急促呼吸声", "volume": 50, "pitch": 110, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [29, {"list": [{"code": 45, "parameters": ["$gameMap.event(19).requestBalloon(4);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(19).requestBalloon(4);"], "indent": 0}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(19).requestBalloon(4);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(29).requestBalloon(8);"], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 15, "parameters": [60], "indent": null}, {"code": 0}], "repeat": true, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(19).requestBalloon(4);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(19).requestBalloon(4);"], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(19).requestBalloon(4);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(29).requestBalloon(8);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 121, "indent": 0, "parameters": [478, 478, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 478, "switch1Valid": true, "switch2Id": 475, "switch2Valid": true, "variableId": 185, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [19, 4, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 4, 0]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 22, 0]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 203, "indent": 2, "parameters": [35, 0, 4, 19, 0]}, {"code": 111, "indent": 2, "parameters": [0, 66, 1]}, {"code": 244, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [66, 66, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["PushGab 49 \\}女王陛下…滋溜…您的靴子…滋溜…好香~"]}, {"code": 122, "indent": 2, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 4, 0]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 22, 0]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 203, "indent": 2, "parameters": [35, 0, 4, 19, 0]}, {"code": 111, "indent": 2, "parameters": [0, 66, 1]}, {"code": 244, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [66, 66, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["PushGab 72 \\}不错，舌头还算灵活。说，你想要什么恩赐？"]}, {"code": 122, "indent": 2, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 213, "indent": 0, "parameters": [19, 3, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 4, 0]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 22, 0]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 203, "indent": 2, "parameters": [35, 0, 4, 19, 0]}, {"code": 111, "indent": 2, "parameters": [0, 66, 1]}, {"code": 244, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [66, 66, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["PushGab 49 \\}女王陛下，我…我想要您的体味！直接的味道！"]}, {"code": 122, "indent": 2, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 213, "indent": 0, "parameters": [29, 3, true]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 4, 0]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 22, 0]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 203, "indent": 2, "parameters": [35, 0, 4, 19, 0]}, {"code": 111, "indent": 2, "parameters": [0, 66, 1]}, {"code": 244, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [66, 66, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["PushGab 72 \\}哈哈，真是个贪心的奴隶！赏你这个吧。"]}, {"code": 122, "indent": 2, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 213, "indent": 0, "parameters": [19, 15, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 4, 0]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 22, 0]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 203, "indent": 2, "parameters": [35, 0, 4, 19, 0]}, {"code": 111, "indent": 2, "parameters": [0, 66, 1]}, {"code": 244, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [66, 66, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["PushGab 49 \\}您的手套…好香，好幸福，求您再多给我点！"]}, {"code": 122, "indent": 2, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 4, 0]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 22, 0]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 203, "indent": 2, "parameters": [35, 0, 4, 19, 0]}, {"code": 111, "indent": 2, "parameters": [0, 66, 1]}, {"code": 244, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [66, 66, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["PushGab 72 \\}啧，贱狗就是贱，给了甜头还想要更多。"]}, {"code": 122, "indent": 2, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 213, "indent": 0, "parameters": [19, 4, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 4, 0]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 22, 0]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 203, "indent": 2, "parameters": [35, 0, 4, 19, 0]}, {"code": 111, "indent": 2, "parameters": [0, 66, 1]}, {"code": 244, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [66, 66, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["PushGab 49 \\}求您…支配我！赏赐我！我快受不了了！"]}, {"code": 122, "indent": 2, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 4, 0]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 22, 0]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 203, "indent": 2, "parameters": [35, 0, 4, 19, 0]}, {"code": 111, "indent": 2, "parameters": [0, 66, 1]}, {"code": 244, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [66, 66, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["PushGab 72 \\}受不了？我看你是欠踩了！"]}, {"code": 122, "indent": 2, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 213, "indent": 0, "parameters": [19, 4, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 4, 0]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 22, 0]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 203, "indent": 2, "parameters": [35, 0, 4, 19, 0]}, {"code": 111, "indent": 2, "parameters": [0, 66, 1]}, {"code": 244, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [66, 66, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["PushGab 49 \\}呃…啊！求您……再踩重一点~"]}, {"code": 122, "indent": 2, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [180]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 4, 0]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 22, 0]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 203, "indent": 2, "parameters": [35, 0, 4, 19, 0]}, {"code": 111, "indent": 2, "parameters": [0, 66, 1]}, {"code": 244, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [66, 66, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["PushGab 72 \\}哦？还敢提要求？胆子不小嘛！"]}, {"code": 122, "indent": 2, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 213, "indent": 0, "parameters": [19, 4, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 4, 0]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 22, 0]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 203, "indent": 2, "parameters": [35, 0, 4, 19, 0]}, {"code": 111, "indent": 2, "parameters": [0, 66, 1]}, {"code": 244, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [66, 66, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["PushGab 49 \\}啊！啊啊！我想永远被您折磨！"]}, {"code": 122, "indent": 2, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 213, "indent": 0, "parameters": [29, 3, true]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 4, 0]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 22, 0]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 203, "indent": 2, "parameters": [35, 0, 4, 19, 0]}, {"code": 111, "indent": 2, "parameters": [0, 66, 1]}, {"code": 244, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [66, 66, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["PushGab 72 \\}呵呵，你想我就要给吗？奖励结束了。起来！"]}, {"code": 122, "indent": 2, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 4, 0]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 22, 0]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 203, "indent": 2, "parameters": [35, 0, 4, 19, 0]}, {"code": 111, "indent": 2, "parameters": [0, 66, 1]}, {"code": 244, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [66, 66, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["PushGab 72 \\}解锁电脑，我要处理点私事。"]}, {"code": 122, "indent": 2, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 213, "indent": 0, "parameters": [19, 7, false]}, {"code": 205, "indent": 0, "parameters": [19, {"list": [{"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 4, 0]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 22, 0]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 203, "indent": 2, "parameters": [35, 0, 4, 19, 0]}, {"code": 111, "indent": 2, "parameters": [0, 66, 1]}, {"code": 244, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [66, 66, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["PushGab 49 \\}是，女王陛下。那个…能不能把内裤赏给我……"]}, {"code": 122, "indent": 2, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 213, "indent": 0, "parameters": [29, 5, false]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [29, {"list": [{"code": 14, "parameters": [1, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [1, 0], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 3, 5, -1, 1]}, {"code": 111, "indent": 0, "parameters": [1, 197, 0, 4, 0]}, {"code": 111, "indent": 1, "parameters": [1, 198, 0, 22, 0]}, {"code": 117, "indent": 2, "parameters": [5]}, {"code": 203, "indent": 2, "parameters": [35, 0, 4, 19, 0]}, {"code": 111, "indent": 2, "parameters": [0, 66, 1]}, {"code": 244, "indent": 3, "parameters": []}, {"code": 121, "indent": 3, "parameters": [66, 66, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 356, "indent": 2, "parameters": ["PushGab 72 闭嘴！滚回你的狗窝去！"]}, {"code": 122, "indent": 2, "parameters": [196, 196, 1, 0, 1]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 203, "indent": 0, "parameters": [37, 0, 13, 0, 0]}, {"code": 121, "indent": 0, "parameters": [478, 478, 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 13, "y": 8}, {"id": 39, "name": "EV039", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 478, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 196, "variableValid": true, "variableValue": 8}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 111, "indent": 0, "parameters": [0, 475, 0]}, {"code": 117, "indent": 1, "parameters": [120]}, {"code": 213, "indent": 1, "parameters": [-1, 5, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac \\c[210](诗乃这贱女人！居然和别人玩这种情色游戏……)"]}, {"code": 213, "indent": 1, "parameters": [-1, 1, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac \\c[210](等一下…！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 她和我又没关系，干嘛要觉得生气？)"]}, {"code": 213, "indent": 1, "parameters": [-1, 8, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 117, "indent": 1, "parameters": [120]}, {"code": 213, "indent": 1, "parameters": [-1, 11, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac \\c[210](是部长和诗乃吧……玩得真花。)"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 6}, {"id": 40, "name": "EV040铃木2", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 463, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": true, "image": {"tileId": 0, "characterName": "人妻老公", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](午休还要加班，看来今天的工作很忙，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还是不要打扰铃木前辈了……)"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 2}]}