{"autoplayBgm": false, "autoplayBgs": true, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 56, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 66, "width": 32, "data": [1568, 2289, 2386, 1570, 1578, 2832, 2816, 2816, 2816, 2820, 2854, 1554, 2848, 2256, 2264, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 1576, 2289, 2290, 1578, 2859, 2845, 2844, 2844, 2824, 2840, 1564, 1562, 2848, 2256, 2264, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 1561, 2289, 2290, 1561, 1561, 1563, 2907, 2909, 2834, 2854, 1554, 1570, 2848, 2256, 2264, 2856, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 1569, 2385, 2290, 1569, 1569, 1560, 1563, 2859, 2855, 1564, 1562, 1578, 2848, 2256, 2242, 2260, 2276, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 1577, 2289, 2290, 1577, 1577, 1568, 1560, 1561, 1561, 1562, 1570, 2859, 2855, 2256, 2240, 2244, 2278, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2260, 2241, 2242, 2260, 2276, 1576, 1568, 1569, 1569, 1570, 1578, 2907, 2901, 2280, 2248, 2264, 2851, 2845, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2240, 2240, 2240, 2240, 2242, 2276, 1576, 1577, 1577, 1578, 2850, 2852, 2883, 2909, 2256, 2264, 2860, 2906, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2240, 2240, 2240, 2240, 2240, 2264, 2859, 2849, 2838, 2836, 2821, 2854, 2908, 2274, 2241, 2264, 2898, 2867, 2900, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2240, 2240, 2240, 2240, 2240, 2242, 2260, 2276, 2856, 2844, 2854, 2274, 2260, 2241, 2244, 2278, 2880, 2864, 2888, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2242, 2260, 2260, 2260, 2241, 2240, 2244, 2278, 2898, 2865, 2864, 2870, 2909, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2244, 2268, 2278, 2898, 2865, 2868, 2892, 2902, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2244, 2278, 2898, 2884, 2865, 2864, 2888, 2850, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2264, 2862, 2880, 2868, 2892, 2892, 2902, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2264, 2898, 2869, 2902, 2274, 2276, 2859, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2264, 2904, 2902, 2274, 2241, 2242, 2276, 2832, 2816, 2820, 2844, 2844, 2844, 2844, 2844, 2844, 2844, 2828, 2844, 2844, 2844, 2844, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2242, 2260, 2260, 2241, 2240, 2240, 2264, 2856, 2844, 2854, 2274, 2260, 2260, 2260, 2260, 2260, 2276, 2860, 2274, 2260, 2260, 2260, 2240, 2240, 2240, 2244, 2268, 2268, 2248, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2242, 2260, 2260, 2260, 2241, 2240, 2240, 2240, 2240, 2240, 2242, 2260, 2241, 2240, 2240, 2240, 2240, 2244, 2268, 2278, 2898, 2900, 2280, 2248, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2268, 2278, 2858, 2907, 2873, 2888, 2862, 2280, 2248, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2836, 2836, 2819, 2852, 2880, 2866, 2885, 2909, 2256, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2244, 2268, 2248, 2240, 2240, 2244, 2268, 2268, 2268, 2248, 2816, 2816, 2816, 2840, 2904, 2892, 2902, 2274, 2241, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2244, 2268, 2268, 2268, 2248, 2244, 2278, 2858, 2280, 2268, 2268, 2278, 2850, 2836, 2852, 2280, 2816, 2816, 2820, 2846, 2861, 2274, 2261, 2269, 2248, 2244, 2268, 2268, 2248, 2240, 2240, 2240, 2264, 2859, 2839, 2861, 2258, 2278, 2850, 2819, 2836, 2836, 2836, 2836, 2817, 2816, 2818, 2836, 2816, 2820, 2854, 2274, 2261, 2269, 2278, 2858, 2280, 2278, 2859, 2861, 2280, 2248, 2240, 2240, 2242, 2276, 2848, 2275, 2279, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2275, 2269, 2278, 2850, 2836, 2819, 2836, 2852, 2907, 2909, 2858, 2280, 2268, 2248, 2240, 2264, 2848, 2284, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2284, 2850, 2836, 2817, 2816, 2816, 2816, 2818, 2836, 2836, 2819, 2852, 2910, 2256, 2240, 2264, 2857, 2838, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 2280, 2248, 2242, 2276, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2822, 2861, 2256, 2240, 2264, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2274, 2241, 2240, 2264, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2906, 2256, 2240, 2244, 2278, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2908, 2256, 2240, 2264, 2910, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2822, 2861, 2256, 2240, 2242, 2276, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2274, 2241, 2240, 2240, 2264, 2856, 2824, 2820, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2844, 2824, 2816, 2840, 2256, 2240, 2240, 2240, 2242, 2276, 2856, 2854, 2906, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2274, 2276, 2856, 2828, 2854, 2256, 2240, 2240, 2240, 2240, 2242, 2276, 2898, 2867, 2900, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2274, 2241, 2242, 2276, 2860, 2274, 2241, 2240, 2240, 2240, 2240, 2244, 2278, 2880, 2864, 2888, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2274, 2241, 2240, 2240, 2242, 2260, 2241, 2240, 2240, 2240, 2240, 2240, 2264, 2907, 2873, 2868, 2902, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2844, 2854, 2274, 2241, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2242, 2276, 2882, 2902, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2274, 2260, 2241, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2240, 2264, 2908, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2274, 2241, 2240, 2240, 2240, 2244, 2268, 2248, 2240, 2244, 2268, 2248, 2240, 2240, 2240, 2240, 2240, 2242, 2276, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2256, 2240, 2240, 2240, 2244, 2278, 2858, 2280, 2268, 2278, 2858, 2280, 2268, 2248, 2240, 2240, 2240, 2240, 2264, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2274, 2241, 2240, 2240, 2240, 2264, 2859, 2827, 2836, 2836, 2836, 2819, 2836, 2852, 2256, 2240, 2240, 2240, 2240, 2264, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2256, 2240, 2240, 2240, 2240, 2242, 2276, 2832, 2816, 2816, 2816, 2816, 2816, 2840, 2256, 2244, 2268, 2248, 2240, 2264, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2256, 2240, 2240, 2240, 2240, 2240, 2264, 2832, 2816, 2816, 2816, 2816, 2816, 2840, 2280, 2278, 2858, 2256, 2244, 2278, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2854, 2274, 2241, 2240, 2244, 2268, 2268, 2268, 2278, 2832, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2842, 2280, 2278, 2898, 2900, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2260, 2241, 2240, 2244, 2278, 2898, 2884, 2900, 2859, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2822, 2861, 2898, 2869, 2902, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2240, 2240, 2244, 2278, 2899, 2893, 2892, 2894, 2901, 2856, 2844, 2824, 2816, 2816, 2816, 2816, 2816, 2820, 2854, 2898, 2869, 2902, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2268, 2268, 2278, 2907, 2903, 2850, 2836, 2852, 2905, 2886, 2900, 2856, 2824, 2816, 2820, 2844, 2844, 2854, 2898, 2869, 2902, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2852, 2910, 2850, 2836, 2836, 2817, 2816, 2818, 2852, 2904, 2894, 2909, 2856, 2844, 2854, 2898, 2884, 2884, 2869, 2902, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2822, 2849, 2825, 2816, 2816, 2816, 2816, 2816, 2822, 2861, 2274, 2276, 2907, 2897, 2897, 2873, 2864, 2864, 2888, 2859, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2910, 2832, 2816, 2816, 2816, 2816, 2820, 2854, 2274, 2241, 2242, 2260, 2260, 2276, 2904, 2892, 2872, 2870, 2909, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2817, 2816, 2816, 2816, 2816, 2840, 2274, 2241, 2240, 2240, 2240, 2240, 2242, 2260, 2276, 2904, 2902, 2850, 2817, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2280, 2248, 2240, 2240, 2240, 2240, 2240, 2240, 2242, 2276, 2859, 2825, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 2256, 2244, 2268, 2248, 2240, 2240, 2240, 2240, 2242, 2276, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2840, 2280, 2278, 2858, 2280, 2268, 2248, 2240, 2240, 2240, 2264, 2856, 2824, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2836, 2836, 2819, 2836, 2852, 2280, 2248, 2240, 2240, 2242, 2276, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2818, 2852, 2256, 2240, 2240, 2240, 2264, 2832, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 2816, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 0, 0, 0, 0, 0, 0, 0, 0, 0, 372, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 372, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 288, 0, 0, 0, 273, 273, 264, 256, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 298, 0, 0, 0, 272, 265, 299, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 305, 306, 0, 0, 0, 0, 0, 307, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 13, 266, 326, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 315, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 0, 0, 0, 0, 0, 0, 284, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 232, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 374, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 307, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 205, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 305, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 306, 296, 0, 0, 297, 298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 259, 0, 0, 0, 0, 0, 304, 0, 0, 305, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 366, 367, 0, 357, 293, 304, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 356, 364, 364, 364, 364, 0, 0, 364, 373, 301, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 271, 277, 0, 0, 0, 0, 0, 280, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 0, 288, 289, 290, 0, 0, 0, 0, 285, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 296, 297, 298, 0, 0, 0, 0, 293, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 257, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 265, 0, 0, 0, 257, 0, 273, 273, 265, 273, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 273, 0, 257, 0, 265, 0, 0, 0, 273, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 264, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 257, 0, 0, 0, 265, 256, 273, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 272, 257, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 317, 0, 315, 273, 264, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 273, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 273, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 264, 0, 0, 0, 0, 0, 0, 257, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 272, 0, 0, 0, 0, 0, 0, 265, 0, 0, 0, 0, 0, 0, 0, 0, 0, 290, 288, 289, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 273, 0, 0, 0, 0, 0, 0, 0, 296, 297, 298, 0, 297, 298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 306, 304, 305, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 256, 0, 0, 0, 0, 0, 0, 0, 304, 305, 306, 0, 0, 0, 0, 305, 306, 288, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 272, 0, 0, 0, 282, 0, 298, 0, 288, 0, 0, 0, 0, 0, 0, 0, 0, 296, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 282, 288, 289, 290, 0, 306, 0, 296, 0, 0, 0, 0, 0, 0, 0, 0, 304, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 290, 296, 297, 0, 0, 0, 0, 304, 0, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 298, 304, 305, 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 299, 364, 0, 0, 0, 0, 0, 0, 0, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 366, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 297, 298, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 366, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 366, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 298, 284, 364, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 285, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 67, 297, 298, 288, 289, 290, 0, 0, 0, 299, 371, 374, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 67, 304, 305, 306, 296, 297, 298, 280, 281, 282, 307, 294, 371, 372, 372, 374, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 306, 51, 289, 290, 315, 326, 308, 308, 294, 371, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 59, 280, 281, 282, 315, 316, 316, 316, 326, 371, 374, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 288, 289, 290, 0, 0, 0, 273, 258, 294, 371, 374, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 280, 281, 282, 67, 296, 297, 298, 0, 12, 12, 280, 281, 282, 294, 371, 374, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 288, 289, 290, 0, 304, 305, 306, 280, 281, 282, 288, 289, 290, 326, 294, 363, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 195, 0, 0, 0, 0, 296, 297, 298, 21, 0, 0, 0, 288, 289, 290, 296, 297, 298, 315, 318, 363, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 206, 0, 0, 304, 305, 306, 0, 0, 280, 281, 282, 297, 298, 304, 305, 306, 283, 299, 363, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 214, 0, 0, 0, 0, 0, 0, 0, 288, 289, 290, 305, 306, 0, 0, 283, 310, 355, 366, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 205, 0, 0, 0, 0, 0, 0, 0, 296, 297, 298, 0, 0, 0, 283, 310, 355, 366, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 186, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 306, 0, 0, 0, 291, 355, 366, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 204, 0, 0, 0, 216, 216, 0, 0, 0, 0, 0, 0, 0, 0, 299, 371, 374, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 224, 224, 0, 0, 0, 0, 0, 0, 0, 0, 307, 294, 371, 374, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 207, 0, 280, 281, 282, 0, 0, 232, 156, 157, 0, 0, 0, 0, 0, 0, 0, 0, 326, 294, 371, 372, 372, 372, 372, 372, 372, 372, 372, 372, 0, 0, 215, 0, 288, 289, 290, 0, 0, 0, 164, 165, 0, 0, 0, 0, 0, 0, 0, 0, 315, 316, 336, 337, 338, 339, 337, 338, 339, 337, 338, 339, 280, 281, 282, 0, 296, 297, 298, 0, 0, 0, 0, 0, 0, 14, 0, 0, 0, 217, 218, 219, 0, 0, 344, 345, 346, 347, 345, 346, 347, 345, 346, 347, 288, 289, 290, 0, 304, 280, 281, 282, 0, 0, 0, 0, 0, 22, 0, 0, 0, 217, 218, 219, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 296, 297, 280, 281, 282, 288, 289, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 217, 218, 219, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 304, 305, 288, 289, 290, 296, 280, 281, 282, 0, 280, 281, 282, 0, 0, 0, 0, 217, 218, 219, 0, 0, 0, 0, 0, 0, 0, 0, 283, 284, 284, 285, 284, 285, 296, 297, 298, 304, 288, 289, 290, 0, 288, 289, 290, 0, 0, 0, 189, 217, 218, 219, 0, 0, 0, 283, 260, 260, 260, 260, 324, 356, 356, 325, 280, 281, 282, 305, 306, 0, 296, 297, 298, 0, 296, 297, 298, 0, 0, 0, 153, 154, 0, 0, 0, 0, 0, 299, 355, 356, 356, 356, 366, 364, 364, 367, 288, 289, 290, 0, 0, 0, 304, 305, 306, 0, 304, 305, 306, 0, 0, 0, 161, 162, 0, 0, 0, 283, 284, 286, 363, 364, 364, 364, 364, 364, 364, 364, 296, 297, 298, 0, 0, 283, 284, 284, 281, 282, 0, 0, 11, 0, 280, 281, 282, 0, 0, 0, 283, 286, 355, 356, 366, 364, 364, 364, 364, 364, 364, 364, 304, 305, 306, 283, 284, 286, 355, 357, 287, 284, 284, 284, 284, 284, 288, 289, 290, 0, 0, 0, 291, 355, 366, 364, 364, 364, 364, 364, 364, 364, 364, 364, 263, 284, 284, 286, 355, 356, 366, 367, 356, 356, 356, 356, 356, 357, 296, 297, 298, 0, 0, 0, 299, 363, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 356, 356, 356, 356, 366, 364, 364, 364, 364, 364, 364, 364, 364, 365, 304, 305, 306, 0, 0, 0, 291, 363, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 375, 372, 373, 301, 0, 0, 0, 0, 0, 291, 363, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 375, 372, 372, 373, 295, 308, 309, 0, 0, 0, 0, 0, 299, 371, 372, 372, 374, 364, 364, 364, 364, 364, 364, 364, 364, 364, 375, 372, 372, 372, 374, 364, 365, 295, 338, 339, 327, 316, 317, 0, 0, 0, 0, 0, 307, 337, 339, 294, 363, 364, 364, 364, 364, 364, 364, 364, 364, 375, 373, 295, 308, 294, 371, 372, 373, 319, 346, 347, 317, 0, 0, 0, 0, 0, 0, 0, 315, 316, 316, 318, 371, 374, 364, 364, 364, 364, 364, 364, 364, 365, 295, 327, 316, 326, 337, 338, 339, 309, 0, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 307, 294, 363, 364, 364, 364, 364, 364, 364, 364, 365, 319, 280, 281, 282, 345, 346, 347, 317, 0, 288, 289, 290, 0, 0, 0, 0, 0, 280, 281, 282, 0, 315, 318, 363, 364, 364, 364, 364, 364, 364, 364, 365, 301, 288, 289, 290, 0, 272, 11, 0, 0, 296, 297, 298, 0, 0, 0, 0, 0, 288, 289, 290, 0, 0, 291, 363, 364, 364, 364, 364, 364, 364, 364, 365, 301, 296, 297, 298, 0, 0, 0, 0, 0, 304, 305, 306, 0, 14, 0, 0, 0, 296, 297, 298, 0, 0, 299, 371, 372, 374, 364, 364, 364, 364, 364, 365, 301, 304, 305, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 0, 0, 304, 305, 306, 0, 0, 307, 308, 294, 363, 364, 364, 364, 364, 375, 373, 301, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 315, 316, 318, 363, 364, 364, 364, 364, 365, 295, 309, 0, 0, 0, 0, 0, 0, 280, 281, 282, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 291, 363, 364, 364, 364, 364, 365, 319, 317, 0, 0, 195, 0, 0, 186, 288, 289, 280, 281, 282, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 283, 286, 363, 364, 364, 364, 364, 365, 301, 0, 0, 0, 186, 0, 0, 280, 281, 282, 288, 289, 290, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 291, 355, 366, 364, 364, 364, 364, 365, 301, 0, 0, 0, 0, 204, 0, 288, 289, 290, 296, 297, 298, 288, 289, 290, 0, 0, 0, 0, 0, 0, 0, 283, 286, 363, 364, 364, 364, 364, 364, 373, 301, 0, 0, 280, 281, 282, 0, 296, 297, 298, 304, 305, 306, 296, 297, 298, 280, 281, 282, 0, 0, 0, 0, 291, 355, 366, 364, 364, 364, 364, 364, 308, 309, 0, 0, 288, 289, 290, 0, 280, 281, 282, 0, 0, 0, 304, 280, 281, 282, 289, 290, 0, 0, 0, 0, 299, 363, 364, 364, 364, 364, 364, 364, 344, 317, 280, 281, 296, 297, 280, 281, 282, 289, 290, 0, 0, 0, 0, 288, 289, 290, 297, 298, 0, 0, 0, 0, 291, 363, 364, 364, 364, 364, 364, 364, 281, 280, 281, 282, 304, 305, 288, 289, 290, 297, 298, 0, 0, 0, 0, 296, 297, 298, 305, 306, 0, 0, 0, 0, 299, 363, 364, 364, 364, 364, 364, 364, 289, 288, 289, 290, 298, 0, 296, 297, 298, 305, 258, 0, 0, 0, 0, 304, 305, 306, 0, 0, 0, 0, 0, 0, 291, 363, 364, 364, 364, 364, 364, 364, 297, 296, 297, 298, 306, 0, 304, 305, 306, 13, 266, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 283, 324, 366, 364, 364, 364, 364, 364, 364, 305, 304, 305, 306, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 283, 324, 366, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 283, 284, 324, 366, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 283, 324, 356, 366, 364, 364, 364, 364, 364, 364, 364, 364, 364, 284, 284, 285, 0, 0, 0, 288, 289, 290, 0, 0, 0, 0, 0, 0, 0, 0, 0, 283, 324, 366, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 356, 357, 287, 285, 0, 0, 296, 280, 281, 282, 0, 0, 0, 0, 0, 0, 0, 0, 299, 363, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 367, 357, 287, 285, 0, 20, 288, 289, 290, 283, 284, 285, 0, 0, 0, 0, 0, 291, 363, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 367, 357, 287, 284, 284, 284, 284, 284, 324, 356, 325, 284, 285, 0, 0, 0, 299, 363, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 367, 356, 356, 356, 356, 356, 356, 366, 364, 367, 356, 325, 261, 0, 0, 341, 332, 374, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 269, 0, 0, 0, 291, 363, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 364, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 5, 5, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 262}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "MAP_bgm_normal03", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [25, {"list": [{"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 203, "indent": 0, "parameters": [2, 0, 2, 48, 6]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 30, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 19, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(2).requestBalloon(3);"], "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(2).requestBalloon(3);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 102], 30, false]}, {"code": 356, "indent": 0, "parameters": ["infotext : 11 : 1 : 590 : 140 : 28 : 150 : 0 : 1 : 0 : 亚  丝  娜  信  息  更  新"]}, {"code": 122, "indent": 0, "parameters": [58, 58, 2, 0, 5]}, {"code": 356, "indent": 0, "parameters": ["addLog 亚丝娜的情感发生了一点变化...."]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac厚瓣暗红花，花蜜是B级食材，就从它开始吧~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这玩意不像是能吃的样子……"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac虽然比不上亚丝娜，但我好歹也是专家级的采集技能。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你这个只会战斗的家伙，听我的就好了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，行。你去采吧。"]}, {"code": 213, "indent": 0, "parameters": [2, 5, false]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹批评", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac有没有绅士风度啊？哪有让女孩子干粗活的道理？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你来！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac好吧。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [15], "indent": 0}, {"code": 45, "parameters": ["$gameMap.event(2).requestBalloon(3);"], "indent": 0}, {"code": 15, "parameters": [25], "indent": null}, {"code": 17, "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(2).requestBalloon(3);"], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [25], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac也正好利用这个机会练练生活技能。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不过我不是很熟练，可能要多花一点时间。"]}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹坏笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没关系！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不如说越久越好~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹岔开", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呃……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我的意思是，慢工出细活嘛！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不嫌我慢就行。我准备开始了。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([336, 20, 'A'], true);"]}, {"code": 223, "indent": 0, "parameters": [[-17, -17, 0, 17], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [121]}, {"code": 122, "indent": 0, "parameters": [79, 79, 0, 4, "\"露营地\""]}, {"code": 122, "indent": 0, "parameters": [75, 75, 0, 4, "\"树丛\""]}, {"code": 250, "indent": 0, "parameters": [{"name": "Item1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac获得了\\c[103]香甜花蜜\\c[103]!"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 18, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac搞定。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac继续前进吧。"]}, {"code": 213, "indent": 0, "parameters": [2, 3, true]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 玩家 : 移动到 : 相对坐标[0,0]"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 37, "indent": null}, {"code": 2, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 37 1"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 263]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 263}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 264}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 45, "parameters": ["$gamePlayer.followers().follower(0).setDirection(8);"], "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 45, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(1);"], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gamePlayer.followers().follower(0).setDirection(8);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(1);"], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [12, {"list": [{"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [13, {"list": [{"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 265]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 事件[12] : 标准落下 : 时间[25] : 缓冲时间[10] : 高度[96]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Motion-Pop28-1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 事件[13] : 标准落下 : 时间[25] : 缓冲时间[10] : 高度[96]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Motion-Pop28-1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 事件[14] : 标准落下 : 时间[25] : 缓冲时间[10] : 高度[96]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "Motion-Pop28-1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [13, {"list": [{"code": 15, "parameters": [40], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [40], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 15, "parameters": [45], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [45], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [12, {"list": [{"code": 15, "parameters": [35], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [35], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹焦急", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 咦~惹——"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 又是史莱姆！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac史莱姆有什么好怕的？"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(12);"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹苦笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac是不可怕，但是恶心啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac来的路上都是这种东西，所以我才让猪田主战的。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 203, "indent": 0, "parameters": [2, 0, 11, 47, 8]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["NF_REM_NPC 1"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没事，这种低等怪物，我一刀一个。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你等我一下。"]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 3, "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 加油！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 37, "indent": null}, {"code": 29, "parameters": [5], "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [12, 1, false]}, {"code": 212, "indent": 0, "parameters": [12, 6, false]}, {"code": 205, "indent": 0, "parameters": [12, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 34, "indent": null}, {"code": 17, "indent": null}, {"code": 41, "parameters": ["Damage3", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["Damage3", 1], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 15, "parameters": [5], "indent": 0}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [13, 1, false]}, {"code": 212, "indent": 0, "parameters": [13, 6, false]}, {"code": 205, "indent": 0, "parameters": [13, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 34, "indent": null}, {"code": 17, "indent": null}, {"code": 41, "parameters": ["Damage3", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["Damage3", 1], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 2, "indent": null}, {"code": 15, "parameters": [5], "indent": 0}, {"code": 14, "parameters": [3, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [3, 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [14, 1, false]}, {"code": 212, "indent": 0, "parameters": [14, 6, false]}, {"code": 205, "indent": 0, "parameters": [14, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 34, "indent": null}, {"code": 17, "indent": null}, {"code": 41, "parameters": ["Damage3", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["Damage3", 1], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 2, "indent": 0}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 事件[12] : 横向挤扁 : 时间[60] : 横向比例[1.5]"]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 事件[13] : 横向挤扁 : 时间[60] : 横向比例[1.5]"]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 事件[14] : 横向挤扁 : 时间[60] : 横向比例[1.5]"]}, {"code": 122, "indent": 0, "parameters": [199, 199, 0, 0, 0]}, {"code": 117, "indent": 0, "parameters": [206]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 搞定。"]}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹安心", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}跟着你就是有安全感……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 2, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac什么？"]}, {"code": 213, "indent": 0, "parameters": [2, 11, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹微笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没什么没什么，我只是感叹：还得是你，这么快就清场了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 猪田那家伙，打一只都要好久。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 他的属性很低，难为他了。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 以后再有开荒，你还是找我比较好。"]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 230, "indent": 0, "parameters": [45]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹得意", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这可是你自己的说的，下次找你，不许推辞。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac承诺的事情我当然不会反悔。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我们继续收集食材吧~"]}, {"code": 356, "indent": 0, "parameters": [">位置与位移 : 玩家 : 移动到 : 相对坐标[0,0]"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 37, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["NF_ADD_NPC 37 1"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 266]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 266}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 267}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(3);"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).setDirection(8);"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹得意", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac前面红色的那个蘑菇，也是B级食材。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac喂喂，这种颜色，还有白色斑点……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这玩意有毒的吧？"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹岔开", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，但可以通过高级厨艺技能处理掉。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 亚丝娜没问题的。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac原来如此。"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你采集也要注意些，小心中毒。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac中毒了会怎样？"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac局部红肿瘙痒吧……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac别怕，我带着解毒药呢~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那就行。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([336, 3, 'A'], true);"]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([336, 19, 'A'], true);"]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [206]}, {"code": 250, "indent": 0, "parameters": [{"name": "Item1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac获得了\\c[103]白星红伞菇\\c[103]!"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac成功了。也没中毒。"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹嘲弄", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不错嘛，说不定你也有这方面的天赋呢~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac下次我和亚丝娜去采集的时候，你考不考虑也一起来？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac敬谢不敏。"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹叹气", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈哈。英雄所见略同。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac其实我也不是很喜欢采集，但谁叫亚丝娜是我好姐们。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唔…原来是这样。我真是深表惭愧……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac谢谢你啦，弥补了我这个不称职男友的短板~"]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(3);"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 268]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 268}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 270}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [197, 197, 0, 3, 5, -1, 0]}, {"code": 122, "indent": 0, "parameters": [198, 198, 0, 0, 14]}, {"code": 203, "indent": 0, "parameters": [2, 1, 197, 198, 8]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 356, "indent": 0, "parameters": ["NF_REM_NPC 1"]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 38, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 45, "parameters": ["$gameMap.event(2).requestBalloon(2);"], "indent": null}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 45, "parameters": ["$gameMap.event(2).requestBalloon(2);"], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac钓鱼点！"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac喂，莉兹，刚才我们采的几样都是素的，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我觉得可以去钓点鱼烤着吃~"]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹通用", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可以呀~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac完全没问题。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 4, false]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 203, "indent": 0, "parameters": [2, 0, 10, 12, 4]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[2] : 像素偏移[-21,24] : 时间[1]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 271]}, {"code": 201, "indent": 0, "parameters": [0, 336, 9, 13, 6, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 271}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-17, -17, 0, 17], 60, true]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不太懂你诶……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不喜欢采集，但看到钓鱼就两眼放光。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你特别喜欢吃鱼吗？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 11, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嘿嘿，钓鱼可不是为了鱼。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac而是钓的过程，可以放空思绪、平静内心。"]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac听你这么一说解释，我也想学学钓鱼了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 真的？难得有女孩子对钓鱼感兴趣的。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就算是亚丝娜也只是一般般……"]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹通用", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你有额外的钓竿吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我陪你一起钓吧~ "]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 7, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呃，真可惜…… "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 今天只带了一支钓竿。"]}, {"code": 213, "indent": 0, "parameters": [2, 11, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹掩饰", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那，下次有空的时候，你单独教我钓鱼好吗？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [5, 3, false]}, {"code": 111, "indent": 0, "parameters": [0, 131, 0]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 完全没问题！"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 前段时间得到一本钓鱼秘籍，我现在是大师级水准。 "]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 其实我的水平也很一般啦~ "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不过教你入门没问题，你不要嫌弃就好。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹微笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那我们说定喽~ "]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯，一言既出驷马难追。"]}, {"code": 213, "indent": 0, "parameters": [2, 3, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 桐人，我在旁边和你聊天会打扰到你吗？ "]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac完全不会。"]}, {"code": 213, "indent": 0, "parameters": [5, 11, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 对了，周四的事谢谢啦，我欠你一次。"]}, {"code": 213, "indent": 0, "parameters": [2, 2, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹惊讶2", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 欠我？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯。昨天亚丝娜没跟你聊这件事吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 取得传说级食材以后，她开心得语气明显都变了。"]}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹岔开", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那你确实该谢谢我……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [5, 1, false]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [2, 1, false]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 2, "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[2] : 像素偏移[0,24] : 时间[15]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊！有鱼上钩了！"]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[2] : 像素偏移[0,0] : 时间[1]"]}, {"code": 111, "indent": 0, "parameters": [1, 199, 0, 2, 0]}, {"code": 242, "indent": 1, "parameters": [2]}, {"code": 230, "indent": 1, "parameters": [120]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 199, 0, 3, 1]}, {"code": 242, "indent": 1, "parameters": [1]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 272]}, {"code": 201, "indent": 0, "parameters": [0, 336, 11, 13, 6, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 272}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 203, "indent": 0, "parameters": [2, 0, 12, 13, 4]}, {"code": 205, "indent": 0, "parameters": [28, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">地图镜头 : 固定看向 : 事件[31]"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-17, -17, 0, 17], 60, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 241, "indent": 0, "parameters": [{"name": "Micmacs-A-La-Gare-Rapha-l-Beau", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 111, "indent": 0, "parameters": [1, 199, 0, 2, 4]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 钓到的两条鱼，都是稀有品种！开心！"]}, {"code": 122, "indent": 1, "parameters": [31, 31, 1, 0, 1]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [1, 199, 0, 3, 1]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这条鱼真大，不错不错！"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹嘲弄", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那还不谢谢我？ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我的陪伴肯定对你有气运的加成。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 11, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿，谢谢。第一次钓的这么爽！"]}, {"code": 213, "indent": 0, "parameters": [-1, 9, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 啊，不对，应该说是第二爽……"]}, {"code": 213, "indent": 0, "parameters": [2, 2, true]}, {"code": 213, "indent": 0, "parameters": [-1, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 仅次于和亚丝娜在22层钓湖之主那次。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊~ 有点怀念……"]}, {"code": 213, "indent": 0, "parameters": [2, 7, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹凛然", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}\\c[8]哼……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 11, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哈哈，不忆当年勇了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们回去吧。"]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 213, "indent": 0, "parameters": [-1, 2, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 怎么了？ "]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹岔开", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那个…桐人，其实你钓鱼之前，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我就注意到前面的树林有一个A级食材。 "]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 因为不想破坏你钓鱼的兴致就暂时没说。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们把它也采集回去吧。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 6, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac A级？我的技能等级采不了A级啊……"]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹回应", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 让我来吧~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你在这里等我一下就好。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦好。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我顺便可以把钓到的鱼拓印一下。 "]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 4, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 39, "indent": null}, {"code": 37, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 33, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 15, "parameters": [5], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "系统_提示_5", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [28, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 3, false]}, {"code": 111, "indent": 0, "parameters": [0, 243, 1]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 231, "indent": 1, "parameters": [89, "", 0, 0, 346, 400, 100, 100, 255, 0]}, {"code": 356, "indent": 1, "parameters": [">图片动态遮罩板A : 简单透视镜 : 图片[89] : 样式[20]"]}, {"code": 230, "indent": 1, "parameters": [1]}, {"code": 356, "indent": 1, "parameters": [">图片动态遮罩板A : 图片[89] : 启用该插件的动态遮罩板"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[89] : 设置动画序列 : 动画序列[6]"]}, {"code": 356, "indent": 1, "parameters": [">图片动画序列 : 图片[89] : 播放简单状态元集合 : 集合[口交含入]"]}, {"code": 231, "indent": 1, "parameters": [86, "剧情小图背景3", 0, 0, -816, 0, 100, 100, 0, 0]}, {"code": 231, "indent": 1, "parameters": [87, "剧情小图背景1", 0, 0, -29, -7, 100, 100, 255, 0]}, {"code": 231, "indent": 1, "parameters": [88, "剧情小图背景2", 0, 0, 29, 7, 100, 100, 0, 0]}, {"code": 232, "indent": 1, "parameters": [87, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 1, "parameters": [88, 0, 0, 0, 0, 0, 100, 100, 255, 0, 15, false]}, {"code": 232, "indent": 1, "parameters": [89, 0, 0, 0, 375, 407, 100, 100, 255, 0, 30, false]}, {"code": 232, "indent": 1, "parameters": [86, 0, 0, 0, -20, 0, 100, 100, 255, 0, 30, true]}, {"code": 232, "indent": 1, "parameters": [86, 0, 0, 0, 20, 0, 100, 100, 255, 0, 75, false]}, {"code": 232, "indent": 1, "parameters": [87, 0, 0, 0, 58, 14, 100, 100, 0, 0, 105, false]}, {"code": 232, "indent": 1, "parameters": [88, 0, 0, 0, -29, -7, 100, 100, 0, 0, 105, false]}, {"code": 232, "indent": 1, "parameters": [86, 0, 0, 0, 836, 0, 100, 100, 0, 0, 105, false]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 102], 30, false]}, {"code": 356, "indent": 1, "parameters": ["infotext : 11 : 1 : 590 : 140 : 28 : 150 : 0 : 1 : 0 : 亚  丝  娜  信  息  更  新"]}, {"code": 356, "indent": 1, "parameters": ["addLog 亚丝娜的身体发生了些许变化...."]}, {"code": 122, "indent": 1, "parameters": [36, 36, 1, 0, 1]}, {"code": 122, "indent": 1, "parameters": [31, 31, 1, 0, 11]}, {"code": 232, "indent": 1, "parameters": [89, 0, 0, 0, 404, 414, 100, 100, 0, 0, 150, true]}, {"code": 235, "indent": 1, "parameters": [86]}, {"code": 235, "indent": 1, "parameters": [87]}, {"code": 235, "indent": 1, "parameters": [88]}, {"code": 235, "indent": 1, "parameters": [89]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 20, false]}, {"code": 250, "indent": 1, "parameters": [{"name": "Bow1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": ["infotext : 8 : 5 : 240 : 320 : 68 : 270 : 0 : 2 : 1 : 心 境 重 塑"]}, {"code": 230, "indent": 1, "parameters": [15]}, {"code": 356, "indent": 1, "parameters": ["infotext : 10 : 4 : 450 : 510 : 100 : 90 : 10 : 14 : 0 : 守   口   如   瓶"]}, {"code": 230, "indent": 1, "parameters": [90]}, {"code": 250, "indent": 1, "parameters": [{"name": "Up1", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 1, "parameters": [[255, 255, 255, 170], 20, false]}, {"code": 356, "indent": 1, "parameters": ["infotext : 10 : 7 : 450 : 510 : 100 : 90 : 10 : 14 : 0 : 浅   尝   未   止"]}, {"code": 356, "indent": 1, "parameters": [">信息面板A : 隐藏选项 : 选项[24]"]}, {"code": 356, "indent": 1, "parameters": [">信息面板A : 解锁选项 : 选项[25]"]}, {"code": 356, "indent": 1, "parameters": [">信息面板A : 显示选项 : 选项[25]"]}, {"code": 356, "indent": 1, "parameters": [">信息面板A : 选中选项 : 选项[25]"]}, {"code": 356, "indent": 1, "parameters": ["addLog 亚丝娜内心某处似乎产生了微妙的变化..."]}, {"code": 108, "indent": 1, "parameters": ["口交"]}, {"code": 356, "indent": 1, "parameters": [">菜单GIF : GIF[2] : 隐藏"]}, {"code": 356, "indent": 1, "parameters": [">菜单GIF : GIF[1] : 隐藏"]}, {"code": 356, "indent": 1, "parameters": [">菜单GIF : GIF[3] : 隐藏"]}, {"code": 356, "indent": 1, "parameters": [">菜单GIF : GIF[4] : 隐藏"]}, {"code": 356, "indent": 1, "parameters": [">菜单GIF : GIF[5] : 显示"]}, {"code": 112, "indent": 1, "parameters": []}, {"code": 122, "indent": 2, "parameters": [198, 198, 0, 3, 5, 2, 1]}, {"code": 111, "indent": 2, "parameters": [1, 198, 0, 6, 0]}, {"code": 113, "indent": 3, "parameters": []}, {"code": 230, "indent": 3, "parameters": [60]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 413, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 112, "indent": 1, "parameters": []}, {"code": 122, "indent": 2, "parameters": [198, 198, 0, 3, 5, 2, 1]}, {"code": 111, "indent": 2, "parameters": [1, 198, 0, 6, 0]}, {"code": 113, "indent": 3, "parameters": []}, {"code": 230, "indent": 3, "parameters": [120]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 413, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹焦急脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{呀啊！！！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 1, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹？！"]}, {"code": 205, "indent": 0, "parameters": [28, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 273]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 273}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [600]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹焦急脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{桐人，快来，我需要你！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 马上！ "]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 274]}, {"code": 122, "indent": 0, "parameters": [80, 80, 0, 4, "\"去帮助莉兹\""]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 274}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">地图镜头 : 解除固定看向"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[16]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[莉兹树丛诱惑]"]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 HSE_momi 45 70 0"]}, {"code": 111, "indent": 0, "parameters": [0, 243, 0]}, {"code": 111, "indent": 1, "parameters": [0, 300, 1]}, {"code": 241, "indent": 2, "parameters": [{"name": "节奏-INNDRIVE-Oriente", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 莉兹，你怎么搞的？！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 食材在树丛后面，我图省事直接钻过去采，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 结果被卡住了！快点帮忙！好难受啊！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好，稍等！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac \\c[210](莉兹这个姿势……)"]}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 切换为按钮组 : 样式[1]"]}, {"code": 102, "indent": 0, "parameters": [["\\dac 感觉有点色情（\\c[28]变态度\\c[0]:\\c[18]10\\c[0]） ", "\\dac 肯定很难受吧"], 1, 0, 1, 1]}, {"code": 402, "indent": 0, "parameters": [0, "\\dac 感觉有点色情（\\c[28]变态度\\c[0]:\\c[18]10\\c[0]） "]}, {"code": 111, "indent": 1, "parameters": [1, 74, 0, 10, 4]}, {"code": 225, "indent": 2, "parameters": [5, 9, 15, false]}, {"code": 224, "indent": 2, "parameters": [[255, 0, 0, 68], 15, true]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": [""]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[28]变态度\\c[101]不足"]}, {"code": 119, "indent": 2, "parameters": ["非变态"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 411, "indent": 1, "parameters": []}, {"code": 250, "indent": 2, "parameters": [{"name": "Poison", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 2, "parameters": [[68, 255, 68, 170], 60, true]}, {"code": 111, "indent": 2, "parameters": [0, 194, 1]}, {"code": 122, "indent": 3, "parameters": [74, 74, 1, 0, 1]}, {"code": 356, "indent": 3, "parameters": ["addLog 变态度↑\\c[28]1"]}, {"code": 121, "indent": 3, "parameters": [194, 194, 0]}, {"code": 0, "indent": 3, "parameters": []}, {"code": 412, "indent": 2, "parameters": []}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[210](咕嘟…… "]}, {"code": 401, "indent": 2, "parameters": ["\\dac 有点色的感觉…… )"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[210](之前都没注意过，莉兹也是安产型的屁股……"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 好大……)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[210](呃，我在想什么！这可是亚丝娜的闺蜜！"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 不对不对！是不是亚丝娜的闺蜜我都不应该乱想！)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 你还在等什么呀~ 快点来帮我出来嘛~ "]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 马上马上……"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[210](这该怎么帮她啊？"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 因为刚才的胡思乱想，搞得我都不知道怎么下手了。)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[210](硬着头皮上吧！)"]}, {"code": 224, "indent": 2, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 245, "indent": 2, "parameters": [{"name": "", "volume": 35, "pitch": 90, "pan": 0}]}, {"code": 250, "indent": 2, "parameters": [{"name": "うっ……", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 2, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[莉兹树丛诱惑2]"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\{嗯呃！！\\} "]}, {"code": 245, "indent": 2, "parameters": [{"name": "捂嘴娇喘2", "volume": 35, "pitch": 90, "pan": 0}]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac你你你你！你…！！干嘛啊？！！"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 我我我……！"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 我不是故意的！我只是想把你拉出来！"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac谁、谁信啊…？！"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac \\c[210](唉，现在不管怎么解释都只是越描越黑！"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 先救她出来才行！)"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac咳咳…哼、哼……"]}, {"code": 401, "indent": 2, "parameters": ["\\dac你…你…你这色鬼！不过让你看看…也无妨！"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac 算了，不解释了……我先把你弄出来！"]}, {"code": 101, "indent": 2, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 2, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\}反正…反正不是什么…大不了的事情！\\{"]}, {"code": 401, "indent": 2, "parameters": ["\\dac\\}反正…也只是游戏……！\\c[8]反正…是给你看……"]}, {"code": 232, "indent": 2, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 221, "indent": 2, "parameters": []}, {"code": 250, "indent": 2, "parameters": [{"name": "服を脱がせる音1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 402, "indent": 0, "parameters": [1, "\\dac 肯定很难受吧"]}, {"code": 118, "indent": 1, "parameters": ["非变态"]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 莉兹这样，肯定很难受，赶紧把她弄出来吧。"]}, {"code": 232, "indent": 1, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 30, true]}, {"code": 221, "indent": 1, "parameters": []}, {"code": 250, "indent": 1, "parameters": [{"name": "服を脱がせる音1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 哎呦，好痛！一点不知道轻重。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 404, "indent": 0, "parameters": []}, {"code": 356, "indent": 0, "parameters": [">对话选项按钮组 : 恢复为选项窗口"]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 275]}, {"code": 111, "indent": 0, "parameters": [0, 243, 0]}, {"code": 111, "indent": 1, "parameters": [0, 300, 1]}, {"code": 223, "indent": 2, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 121, "indent": 2, "parameters": [243, 243, 1]}, {"code": 122, "indent": 2, "parameters": [100, 100, 0, 1, 82]}, {"code": 201, "indent": 2, "parameters": [0, 82, 8, 15, 2, 0]}, {"code": 119, "indent": 2, "parameters": ["结束事件"]}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 336, 18, 10, 4, 0]}, {"code": 118, "indent": 0, "parameters": ["结束事件"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 275}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 203, "indent": 0, "parameters": [2, 0, 17, 10, 6]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 40, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-34, -34, 0, 34], 60, true]}, {"code": 213, "indent": 0, "parameters": [-1, 12, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 抱歉，刚才那个姿势实在是不好用力。"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹嘲弄", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 算了。毕竟还是你救了我。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 英雄大人，你要小女子怎么报答你呢？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 111, "indent": 0, "parameters": [0, 194, 1]}, {"code": 213, "indent": 1, "parameters": [-1, 3, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这种小事不足挂齿啦。"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 213, "indent": 1, "parameters": [-1, 11, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 这……这种小事不足挂齿啦……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 而且，还搞出那种乌龙……"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹微笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 唔~ 不管怎么说，还是要答谢你的救命之恩的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 以身相许似乎夸张了一点，但让你摸一下屁股还是可以的哦~ "]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [-1, 11, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你就别拿我寻开心了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们赶紧回去吧。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 1, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [10], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [10], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹坏笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 害羞什么，你刚才不就已经摸过了？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还是说，你更愿意摸胸？"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 111, "indent": 0, "parameters": [0, 194, 0]}, {"code": 213, "indent": 1, "parameters": [-1, 12, false]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 那！那是……！"]}, {"code": 213, "indent": 1, "parameters": [2, 3, false]}, {"code": 205, "indent": 1, "parameters": [2, {"list": [{"code": 3, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 231, "indent": 1, "parameters": [2, "FOG莉兹掩饰", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 而且，还脱了我的裤子…… "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你说，如果我添油加醋说给亚丝娜听，她会怎么想？"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [-1, 12, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 千万不要！"]}, {"code": 231, "indent": 1, "parameters": [2, "FOG莉兹微笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 好啦，不逗你了。我们回去吧。"]}, {"code": 205, "indent": 1, "parameters": [2, {"list": [{"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不过……"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 231, "indent": 1, "parameters": [2, "FOG莉兹感动", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你刚才有没有一瞬间……"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 对我心动呢？"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [-1, 11, false]}, {"code": 205, "indent": 1, "parameters": [2, {"list": [{"code": 1, "indent": null}, {"code": 45, "parameters": ["$gameMap.event(2).requestBalloon(3);"], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 45, "parameters": ["$gameMap.event(2).requestBalloon(3);"], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 60, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 213, "indent": 1, "parameters": [-1, 6, true]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 那是为了帮你出来好吧~"]}, {"code": 205, "indent": 1, "parameters": [2, {"list": [{"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 231, "indent": 1, "parameters": [2, "FOG莉兹叹气", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 好啦，不逗你了。我们回去吧。"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [2, 8, true]}, {"code": 205, "indent": 1, "parameters": [2, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 17, "indent": null}]}, {"code": 231, "indent": 1, "parameters": [2, "FOG莉兹感动", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 1, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 1, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 1, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 1, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 不过呢，报答还是要的~"]}, {"code": 235, "indent": 1, "parameters": [2]}, {"code": 213, "indent": 1, "parameters": [2, 11, false]}, {"code": 356, "indent": 1, "parameters": [">行走图额外位置偏移量 : 事件[2] : 像素偏移[30,0] : 时间[5]"]}, {"code": 230, "indent": 1, "parameters": [5]}, {"code": 250, "indent": 1, "parameters": [{"name": "人类音效4-俏皮的啧啧亲嘴声_爱给网_aigei_com", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 1, "parameters": [">行走图额外位置偏移量 : 事件[2] : 像素偏移[0,0] : 时间[10]"]}, {"code": 230, "indent": 1, "parameters": [10]}, {"code": 213, "indent": 1, "parameters": [-1, 1, false]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 15, "parameters": [25], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 15, "parameters": [25], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 16, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 205, "indent": 1, "parameters": [2, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 1, "indent": null}, {"code": 45, "parameters": ["$gameMap.event(2).requestBalloon(3);"], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": 0}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": true, "wait": false}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 45, "parameters": ["$gameMap.event(2).requestBalloon(3);"], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": 0}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 39, "indent": null}]}, {"code": 230, "indent": 1, "parameters": [30]}, {"code": 213, "indent": 1, "parameters": [-1, 7, false]}, {"code": 101, "indent": 1, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 1, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 1, "parameters": ["\\dac 莉兹！ "]}, {"code": 401, "indent": 1, "parameters": ["\\dac 你这家伙又拿我寻开心！"]}, {"code": 223, "indent": 1, "parameters": [[-255, -255, -255, 0], 60, true]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 243, 0]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 121, "indent": 1, "parameters": [243, 243, 1]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 1, 82]}, {"code": 121, "indent": 1, "parameters": [300, 300, 1]}, {"code": 201, "indent": 1, "parameters": [0, 82, 8, 15, 2, 0]}, {"code": 119, "indent": 1, "parameters": ["结束事件"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [80, 80, 0, 4, "\"回去找亚丝娜\""]}, {"code": 201, "indent": 0, "parameters": [0, 337, 26, 10, 4, 0]}, {"code": 118, "indent": 0, "parameters": ["结束事件"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 310}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 26}, {"id": 2, "name": "EV002莉兹", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "莉兹FOG", "direction": 2, "pattern": 1, "characterIndex": 2}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 31, "y": 55}, {"id": 3, "name": "EV003红菇光点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 7, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 29}, {"id": 4, "name": "EV004", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 269}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 270]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 270}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 16}, {"id": 5, "name": "EV005钓鱼桐人", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 271}, "directionFix": false, "image": {"tileId": 0, "characterName": "$nhgo9eu3", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,12]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 272}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 9, "y": 13}, {"id": 6, "name": "EV006", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 268}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 事件[24] : 移动显现 : 时间[20] : 方向角度[180] : 移动距离[48]"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [-1, 8, false]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).setDirection(8);"]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(12);"]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 15, "parameters": [5], "indent": null}, {"code": 36, "indent": null}, {"code": 16, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 36, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 356, "indent": 0, "parameters": [">显现动作 : 事件[25] : 移动显现 : 时间[25] : 方向角度[90] : 移动距离[144]"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 117, "indent": 0, "parameters": [206]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [4]}, {"code": 212, "indent": 0, "parameters": [24, 7, false]}, {"code": 213, "indent": 0, "parameters": [24, 1, false]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [24, {"list": [{"code": 34, "indent": null}, {"code": 17, "indent": null}, {"code": 41, "parameters": ["Damage3", 1], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 41, "parameters": ["Damage3", 1], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [3]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 事件[25] : 移动消失 : 时间[10] : 方向角度[270] : 移动距离[24]"]}, {"code": 230, "indent": 0, "parameters": [55]}, {"code": 356, "indent": 0, "parameters": [">消失动作 : 事件[24] : 横向挤扁 : 时间[60] : 横向比例[1.5]"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$gamePlayer.followers().follower(0).requestBalloon(4);"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 269]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 269}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 24}, {"id": 7, "name": "EV007", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 6, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这个可以吃吗？"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹回应", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac算是可以吧……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac一种特殊的香料来着，不是所有人都能接受。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可以先弄回去。让亚丝娜来决定用不用。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([336, 8, 'A'], true);"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [206]}, {"code": 250, "indent": 0, "parameters": [{"name": "Item1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac获得了\\c[103]蓬松香草\\c[103]!"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 20}, {"id": 8, "name": "EV008", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,48]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 23, "y": 19}, {"id": 9, "name": "EV009", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 19, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 4}, {"id": 10, "name": "EV010", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 1, "y": 5}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!fsm_Waterobject03", "direction": 4, "pattern": 1, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}], "x": 2, "y": 5}, {"id": 12, "name": "EV012史莱姆", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 265}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 268}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 42}, {"id": 13, "name": "EV013史莱姆", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 265}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 268}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 41}, {"id": 14, "name": "EV014史莱姆", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 265}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 268}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 41}, {"id": 15, "name": "EV015", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 15, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 45}, {"id": 16, "name": "EV016", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 266}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 267]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 267}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 20, "y": 32}, {"id": 17, "name": "EV017", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 266}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 267]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 267}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 32}, {"id": 18, "name": "EV018", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 266}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 267]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 267}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 22, "y": 32}, {"id": 19, "name": "EV019红伞菇", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,48]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 28}, {"id": 20, "name": "EV020霸王花光点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,48]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 5, "y": 44}, {"id": 21, "name": "EV021灰矮菇", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 2, "characterName": "", "characterIndex": 0, "direction": 2, "pattern": 0}, "list": [{"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这个口蘑看着也像是食材的样子，采集一点？"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹温柔", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这个是D级食材，所以我才没要你采。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不过……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹微笑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac多多益善，采吧采吧~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嗯。"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 355, "indent": 0, "parameters": ["$gameSelfSwitches.setValue([336, 22, 'A'], true);"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 117, "indent": 0, "parameters": [206]}, {"code": 250, "indent": 0, "parameters": [{"name": "Item1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": [""]}, {"code": 401, "indent": 0, "parameters": ["\\dac获得了\\c[103]灰矮菇\\c[103]!"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 43}, {"id": 22, "name": "EV022灰矮菇光点", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Flame", "direction": 2, "pattern": 0, "characterIndex": 7}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,48]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 21, "y": 42}, {"id": 23, "name": "EV023", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 263}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 264]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 264}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 10, "y": 47}, {"id": 24, "name": "EV024史莱姆", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 268}, "directionFix": false, "image": {"tileId": 0, "characterName": "Monster", "direction": 6, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图滤镜 : 纯色滤镜 : 纯红 : 155"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 269}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 22}, {"id": 25, "name": "EV025桐人虚影", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 268}, "directionFix": true, "image": {"tileId": 0, "characterName": "桐人", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图滤镜 : 模糊滤镜 : 15"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 6, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": true, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 270}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 21}, {"id": 26, "name": "EV026", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 269}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 270]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 270}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 18, "y": 16}, {"id": 27, "name": "EV027", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 269}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 270]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 270}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 19, "y": 16}, {"id": 28, "name": "EV028电子屏", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 272}, "directionFix": false, "image": {"tileId": 0, "characterName": "$悬浮屏幕", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,-24]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 274}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 311}, "directionFix": false, "image": {"tileId": 0, "characterName": "$悬浮屏幕", "direction": 2, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,-24]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 312}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 12, "y": 15}, {"id": 29, "name": "EV029", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 273}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">地图镜头 : 解除固定看向"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 274]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 274}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 312}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">地图镜头 : 解除固定看向"]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [5], "indent": null}, {"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [5], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 313]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 15, "y": 12}, {"id": 30, "name": "EV030", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 273}, "directionFix": false, "image": {"tileId": 0, "characterName": "$莉兹森林卡住", "direction": 2, "pattern": 2, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,4]"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 275}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 311}, "directionFix": false, "image": {"tileId": 0, "characterName": "$莉兹森林卡住", "direction": 2, "pattern": 2, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,4]"]}, {"code": 205, "indent": 0, "parameters": [0, {"list": [{"code": 15, "parameters": [30], "indent": null}, {"code": 17, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 314}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 5}, {"id": 31, "name": "EV031", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 13, "y": 17}, {"id": 32, "name": "EV032空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 8, "y": 46}, {"id": 33, "name": "EV015空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 6, "pattern": 1, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 44}, {"id": 34, "name": "EV015空气墙", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "", "direction": 6, "pattern": 1, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 7, "y": 45}, {"id": 35, "name": "EV035", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 311}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">地图镜头 : 固定看向 : 事件[31]"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[-17, -17, 0, 17], 60, false]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 241, "indent": 0, "parameters": [{"name": "Micmacs-A-La-Gare-Rapha-l-Beau", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [-1, 3, true]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹焦急脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{呀啊！！！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [-1, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹？！"]}, {"code": 205, "indent": 0, "parameters": [28, {"list": [{"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 213, "indent": 0, "parameters": [-1, 2, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这一幕怎么有种似曾相识的感觉……？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不想那么多了，先去帮忙吧。"]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 312]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 312}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [600]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG莉兹焦急脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["FOG莉兹泳装.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<莉兹>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{桐人，快来，我需要你！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<桐人>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 马上！ "]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 313]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 313}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 1, true]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[16]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[莉兹树丛诱惑]"]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 HSE_momi 45 70 0"]}, {"code": 111, "indent": 0, "parameters": [0, 248, 0]}, {"code": 111, "indent": 1, "parameters": [0, 300, 1]}, {"code": 241, "indent": 2, "parameters": [{"name": "节奏-INNDRIVE-Oriente", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 30, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你这是……？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac别楞着了，卡着好难受，快来帮我！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac好，好……"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 35, "pitch": 90, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "うっ……", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[莉兹树丛诱惑2]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{嗯呃！！\\} "]}, {"code": 245, "indent": 0, "parameters": [{"name": "捂嘴娇喘2", "volume": 35, "pitch": 90, "pan": 0}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊？你干嘛？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我、我…我……我不小心的……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac鬼才信你！但还是先帮我出来再说……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](莉兹的屁股……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](我再瞎想些什么？！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac得赶紧帮她出来才行……)"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 245, "indent": 0, "parameters": [{"name": "啪啪啪1", "volume": 50, "pitch": 100, "pan": 0}]}, {"code": 356, "indent": 0, "parameters": ["play_bgs2 さくら16_NTRSEX喘ぎ激しめ 25 100 0"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[桐人梦境莉兹本番]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{哦~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\{啊啊啊！！桐人！你混蛋！啊！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac对不起莉兹！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我不知道怎么了…突然没办法控制自己了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac停！你快拔出来！快点！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我也很想停！但是……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呜呜……我的第一次！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你是处女…？"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac废话！我当然是！呃啊……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac对不起！真的很对不起！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac对不起有什么用？我的处女……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我留给猪田君的最宝贵的处女！啊……你给我还来！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹，我……我以后一定会补偿你的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac什么补偿也没有用！啊……你这个猥琐闷骚的家伙，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac平时…装作正经的样子……啊…没想到是这种色鬼！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我不是啊！啊！为什么就是停不下来！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac人渣！变态！秃头！败类！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹…唔…如果能让你心里好受一点，你就继续骂吧……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac强奸犯！乘人之危！亚丝娜真是瞎了眼了！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac对，你骂得都对！我对不起你，也对不起亚丝娜！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我要告诉亚丝娜……哦…嗯啊……让她甩了你！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不行！这个不行！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac求你了莉兹，我真的是控制不了身体才这样的！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我知道这个错误无法挽回，但我不能和亚丝娜分开！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那比杀了我还难受！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac谁管你！贱人！白痴！傻逼！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac唔唔…莉兹，我快要……！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac什么？！不行不行不行！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac快拔出来！我今天是危险期……"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 255], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[梦境莉兹本番中出]"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac莉兹:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac混蛋…！\\.\\."]}, {"code": 401, "indent": 0, "parameters": ["\\dac呃呃…哈…哈啊…嗬咿……\\|\\|\\.\\.\\.\\^"]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 111, "indent": 1, "parameters": [2, "A", 1]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 242, "indent": 0, "parameters": [1]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 0, 0, 60, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac桐人:"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哈啊…哈啊……意识…\\."]}, {"code": 401, "indent": 0, "parameters": ["\\dac在消失……\\."]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 111, "indent": 0, "parameters": [0, 248, 0]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 121, "indent": 1, "parameters": [248, 248, 1]}, {"code": 122, "indent": 1, "parameters": [100, 100, 0, 1, 82]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 40, "indent": null}, {"code": 42, "parameters": [255], "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 201, "indent": 1, "parameters": [0, 82, 5, 13, 2, 0]}, {"code": 119, "indent": 1, "parameters": ["结束事件"]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 249, "indent": 0, "parameters": [{"name": "Mystery", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 122, "indent": 0, "parameters": [100, 100, 0, 0, 314]}, {"code": 201, "indent": 0, "parameters": [0, 290, 58, 12, 2, 0]}, {"code": 118, "indent": 0, "parameters": ["结束事件"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 230, "indent": 0, "parameters": [30]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 250, "indent": 0, "parameters": [{"name": "あえぎ1", "volume": 100, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [170]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精音・中出し（短い）①", "volume": 85, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [10]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 100, "variableValid": true, "variableValue": 314}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 11, "y": 15}, null]}