{"autoplayBgm": false, "autoplayBgs": false, "battleback1Name": "", "battleback2Name": "", "bgm": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "bgs": {"name": "", "pan": 0, "pitch": 100, "volume": 90}, "disableDashing": false, "displayName": "", "encounterList": [], "encounterStep": 30, "height": 30, "note": "", "parallaxLoopX": false, "parallaxLoopY": false, "parallaxName": "", "parallaxShow": true, "parallaxSx": 0, "parallaxSy": 0, "scrollType": 0, "specifyBattleback": false, "tilesetId": 6, "width": 23, "data": [1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 6883, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6870, 6868, 6869, 6881, 6881, 6881, 6881, 6881, 6870, 6868, 6884, 1536, 1536, 6880, 7186, 7186, 7186, 7190, 7239, 7187, 7186, 7186, 7186, 6888, 6860, 6886, 7234, 7234, 7234, 7234, 7234, 6864, 6848, 6872, 1536, 1536, 6880, 7192, 7192, 7192, 7196, 7245, 7193, 7192, 7192, 7192, 7235, 6880, 7238, 7240, 7240, 7240, 7240, 7240, 6864, 6848, 6872, 1536, 1536, 6880, 1602, 2370, 2356, 2356, 2356, 2356, 2356, 2372, 1602, 7241, 6880, 7244, 1604, 1604, 1604, 1604, 1604, 6864, 6848, 6872, 1536, 1536, 6880, 1602, 2352, 2336, 2336, 2336, 2336, 2336, 2360, 1602, 1602, 6880, 1604, 1604, 1604, 1604, 1604, 1604, 6864, 6848, 6872, 1536, 1536, 6880, 1602, 2352, 2336, 2336, 2336, 2336, 2336, 2360, 1602, 6882, 6851, 6884, 1604, 1604, 1604, 1604, 1604, 6864, 6848, 6872, 1536, 1536, 6880, 1602, 2352, 2336, 2336, 2336, 2336, 2336, 2360, 1602, 6864, 6848, 6872, 1604, 1604, 1604, 1604, 1604, 6864, 6848, 6872, 1536, 1536, 6880, 1602, 2376, 2364, 2364, 2364, 2364, 2364, 2374, 1602, 6864, 6848, 6872, 1604, 1604, 1604, 1604, 1604, 6864, 6848, 6872, 1536, 1536, 6867, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6861, 6876, 6878, 6881, 6893, 1604, 6891, 6881, 6877, 6876, 6873, 1536, 1536, 6880, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6274, 6880, 7234, 7234, 7234, 7238, 1604, 7235, 7234, 7234, 7234, 6880, 1536, 1536, 6880, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6280, 6880, 7240, 7240, 7240, 7244, 1604, 7241, 7240, 7240, 7240, 6880, 1536, 1536, 6880, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 6880, 1604, 4098, 4084, 4084, 4084, 4084, 4084, 4100, 1604, 6880, 1536, 1536, 6880, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 6880, 1604, 4080, 4064, 4064, 4064, 4064, 4064, 4088, 1604, 6880, 1536, 1536, 6880, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 6880, 1604, 4080, 4064, 4064, 4064, 4064, 4064, 4088, 1604, 6880, 1536, 1536, 6880, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 1585, 6880, 1604, 4080, 4064, 4064, 4064, 4064, 4064, 4088, 1604, 6880, 1536, 1536, 6867, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6875, 1604, 4080, 4064, 4064, 4064, 4064, 4064, 4088, 1604, 6880, 1536, 1536, 6880, 7234, 7234, 7234, 7234, 7234, 7234, 7234, 7234, 7234, 6880, 1604, 4080, 4064, 4064, 4064, 4064, 4064, 4088, 1604, 6880, 1536, 1536, 6880, 7240, 7240, 7240, 7240, 7240, 7240, 7240, 7240, 7240, 6880, 1604, 4104, 4092, 4092, 4092, 4092, 4092, 4102, 1604, 6880, 1536, 1536, 6880, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 6880, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 6880, 1536, 1536, 6880, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 6880, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 1604, 6880, 1536, 1536, 6867, 6881, 6881, 6881, 6893, 1604, 6891, 6881, 6881, 6881, 6879, 6881, 6881, 6881, 6893, 1604, 6891, 6871, 6881, 6881, 6887, 1536, 1536, 6880, 7234, 7234, 7234, 7238, 1604, 7235, 7234, 7234, 7234, 7234, 7234, 7234, 7234, 7238, 1604, 7235, 6880, 1536, 1536, 1536, 1536, 1536, 6880, 7240, 7240, 7240, 7244, 1604, 7241, 7240, 7240, 7240, 7240, 7240, 7240, 7240, 7244, 1604, 7241, 6880, 1536, 1536, 1536, 1536, 1536, 6880, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 4146, 4132, 4148, 6880, 1536, 1536, 1536, 1536, 1536, 6880, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 1590, 4152, 4140, 4150, 6880, 1536, 1536, 1536, 1536, 1536, 6889, 6885, 1590, 6883, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6881, 6887, 1536, 1536, 1536, 1536, 1536, 1536, 6889, 6881, 6887, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 1536, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3570, 3556, 3572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3576, 3564, 3574, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 431, 0, 0, 0, 0, 0, 0, 0, 431, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 787, 0, 0, 0, 0, 0, 0, 0, 0, 0, 787, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 795, 0, 0, 0, 0, 0, 0, 0, 0, 0, 795, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 232, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 789, 790, 791, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 797, 798, 799, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 370, 371, 407, 0, 368, 0, 369, 0, 0, 0, 415, 0, 0, 0, 0, 0, 0, 0, 415, 0, 0, 0, 0, 498, 499, 0, 500, 501, 502, 503, 354, 180, 0, 423, 198, 0, 0, 0, 0, 0, 197, 423, 0, 0, 0, 0, 506, 507, 0, 508, 509, 510, 511, 362, 188, 0, 295, 206, 0, 0, 860, 0, 0, 205, 295, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 303, 0, 0, 0, 868, 0, 0, 0, 303, 0, 0, 0, 0, 376, 378, 377, 379, 0, 243, 244, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 856, 0, 0, 0, 0, 0, 857, 0, 0, 0, 0, 0, 0, 0, 0, 0, 467, 0, 0, 0, 0, 0, 0, 864, 0, 0, 0, 0, 0, 865, 0, 0, 0, 0, 0, 466, 0, 0, 0, 475, 0, 0, 0, 466, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 474, 0, 0, 0, 0, 0, 0, 0, 474, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 873, 0, 873, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 881, 0, 881, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 19, 19, 19, 19, 19, 19, 19, 19, 0, 0, 0, 15, 15, 15, 15, 15, 0, 0, 0, 0, 0, 0, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 0, 15, 15, 15, 15, 15, 15, 0, 0, 0, 0, 0, 0, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 0, 15, 15, 15, 15, 15, 15, 0, 0, 0, 0, 0, 0, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 11, 15, 15, 15, 15, 15, 15, 0, 0, 0, 0, 0, 0, 19, 19, 19, 19, 19, 19, 19, 19, 19, 0, 0, 0, 15, 15, 15, 15, 15, 0, 0, 0, 0, 0, 0, 19, 19, 19, 19, 19, 19, 19, 19, 19, 0, 0, 0, 15, 15, 15, 15, 15, 0, 0, 0, 0, 0, 0, 19, 19, 19, 19, 19, 19, 19, 19, 19, 0, 0, 0, 15, 15, 15, 15, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 17, 17, 17, 17, 17, 17, 17, 17, 17, 0, 0, 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 17, 17, 17, 17, 17, 17, 17, 17, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 17, 17, 17, 17, 17, 17, 17, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 17, 17, 17, 17, 17, 17, 17, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 17, 17, 17, 17, 17, 17, 17, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 17, 17, 17, 17, 17, 17, 17, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 17, 17, 17, 17, 17, 17, 17, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 17, 17, 17, 17, 17, 17, 17, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 17, 17, 17, 17, 17, 17, 17, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 17, 17, 17, 17, 17, 17, 17, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "events": [null, {"id": 1, "name": "EV001", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "催眠过程中", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, false]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 19, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [15], "indent": null}, {"code": 4, "indent": 0}, {"code": 4, "indent": 0}, {"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [25]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 1]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 小美人，先等老朽一下下， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 在谈话的时候点一些熏香，可以让双方都放松一下哟。 "]}, {"code": 213, "indent": 0, "parameters": [4, 8, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝默认", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](这家伙可能要使用催眠熏香了。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但我已经提前特训过，希望能够对抗它的效果。)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 112, "indent": 0, "parameters": []}, {"code": 122, "indent": 1, "parameters": [197, 197, 0, 3, 5, 2, 0]}, {"code": 111, "indent": 1, "parameters": [1, 197, 0, 15, 0]}, {"code": 113, "indent": 2, "parameters": []}, {"code": 0, "indent": 2, "parameters": []}, {"code": 412, "indent": 1, "parameters": []}, {"code": 0, "indent": 1, "parameters": []}, {"code": 413, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 2]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘻嘻嘻，好了，但是……"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 麻烦先看一下这个。"]}, {"code": 212, "indent": 0, "parameters": [4, 142, false]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [4, 1, true]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝绝顶", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呃……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 3]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 42, "parameters": [150], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [150], "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[7] : 像素偏移[18,0] : 时间[30]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 213, "indent": 0, "parameters": [7, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 还是FOG里催眠方便，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 一键启动，不用引导个半天。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 直接进入主题吧。"]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[7] : 像素偏移[0,0] : 时间[20]"]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 3, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 听得到我的声音吗？记得这个声音吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你完全不会怀疑的声音。"]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 3"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠疑惑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac是的。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 好的。上前一步。"]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 4, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 发生了意外情况，让你很紧张吧？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 放轻松，让你的思绪回到出发前……"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠疑惑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我……回到了出发以前……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 警卫室……"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 3, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac没错，我是你信任的同伴，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我们正在推演可能发生的情形。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 2, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac而你刚刚经历的场景，妓院老板的突然出现、妓女等级考试，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac都是我们推测出的意外情况。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那么，你会怎么去应对呢？"]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我个人觉得可以牺牲一点色相，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac和妓院老板边做爱边套他的情报，是不错的选择。"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠疑惑", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不，桐人君，我不太赞同。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 首先做爱应该和喜欢的人进行，其次这样做的必要性要不高。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板未见得会有什么情报。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且我们这次的目的只是取得关键材料。没必要节外生枝。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我会想办法把升级测试的事情糊弄过去。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 或者找机会装作被冒犯到，直接发脾气走人。 "]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 并不算太意外的回答。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 虽然改进了装置，但抵抗还是很强。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 看来即使FOG里，也要沿用现实的思路了。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我赞同你的大部分观点。但你忽略了一个重要的因素。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你忘记之前已经中催眠了吗？妓院老板肯定会利用这点来操控你。 "]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠回应", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是，我确实被熏香催眠了，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但效果只是强制发情和感度提高。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且我已经使用自慰练习来对抗催眠的效果。我可以忍耐。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac No No No。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你忽略了一点。这是妓院，是敌人的主场。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 如果他增加催眠熏香的浓度，就能操控你的行为。"]}, {"code": 213, "indent": 0, "parameters": [4, 1, true]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠理解", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯，桐人君，你果然心思缜密。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠得意", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不过也没有关系，最坏的结果，就和你暴露时的方案一样，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我会利用权限让我的人物强制死亡，触发回城。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [2, 1, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 原来还有这种权限……"]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个作为最终手段没问题。但可能引发敌人的怀疑。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 如果不是退无可退，不应该选择这个方案。"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠服从", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac赞同。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 很好。我们复盘一下。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你会被妓院老板的强力催眠熏香影响， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 行为上不受自己的控制。 "]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但为了不被怀疑，只要不强迫你性交， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你都不应该使用权限脱困。 "]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 1, "indent": null}, {"code": 3, "indent": null}, {"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac另外，刚才和你讨论的并不是什么桐人， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而是你最信任的福克西。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[2] : 像素偏移[0,-24] : 时间[15]"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我说得对吧？ "]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠理解", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是的。我和福克西推演了潜入时可能遇到的情况。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我会被催眠，行为不受控制。但只要不做爱，我都会忍耐。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[2] : 像素偏移[0,0] : 时间[15]"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac很好。我数到三……"]}, {"code": 213, "indent": 0, "parameters": [2, 9, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不，等一下……我应该试着给她增加一个状态切换提示词，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 或许让我在现实中也能快速将她催眠。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝，听我说……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 福克西是值得信任的，和福克西商讨计划是必要的。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但为了保密，每次谈论机密前，福克西都会对你说一个暗号。 "]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 福克西说得对。 "]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 4, "indent": null}, {"code": 2, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 以后，不管是FOG还是现实中，当听到福克西说：“婊子骑士”， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你都会进入案情讨论模式，也就是现在的状态。明白了吗？"]}, {"code": 231, "indent": 0, "parameters": [3, "FOG爱丽丝催眠理解", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务催眠.png"]}, {"code": 232, "indent": 0, "parameters": [3, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝明白了。"]}, {"code": 235, "indent": 0, "parameters": [3]}, {"code": 213, "indent": 0, "parameters": [2, 4, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac好极了。我现在数到三，你会解除催眠，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 并从过去的警卫局，回到现时的妓院。"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 19, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 一、二、三……"]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac艾莉儿小姐，你还在听吗？"]}, {"code": 213, "indent": 0, "parameters": [4, 1, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝苦笑脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 213, "indent": 0, "parameters": [4, 12, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac呃，不好意思，我好像走神了一下。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 麻烦您再说一次。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 2, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我刚才是问，你没有觉得身体很热，想要脱衣服？"]}, {"code": 213, "indent": 0, "parameters": [4, 3, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝得意", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](多亏了自慰训练，现在并没有什么感觉。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但如果他怀疑，可能会影响桐人君的行动，还是装作中招了吧。)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝嘲弄", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 确实有些热，但脱衣服就不用了。那个，老板……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 就像刚才那个客人说的，我的技术很差的，完全不用考试。"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 私底下跟你解释清楚，就不会伤害到您的面子了。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 那么，请恕我失陪。 "]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不要着急。"]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 艾莉儿小姐想走，就说明老朽招待不周哦~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 首先，就是熏香的味道还不够，让我增加一些浓度吧。 "]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 19, "indent": null}, {"code": 15, "parameters": [15], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [15], "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 2, "indent": null}, {"code": 19, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝惊讶", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](他要增加浓度…… "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 果然，被桐……被福克西说中了。)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [4, 7, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝叹气脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](我的身体很快就会被他控制的。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但我必须要忍耐，为了任务，不到万不得已……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 4]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 213, "indent": 0, "parameters": [4, 8, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 艾莉儿小姐，现在的味道更香了齁。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 不知道这个味道有没有让你改变主意呢？做个实验吧~ "]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 3, "indent": null}, {"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac嘻嘻嘻，请你原地跳一下，然后站住别动。"]}, {"code": 213, "indent": 0, "parameters": [4, 2, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝惊讶2", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我为什么要……"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 14, "parameters": [0, 0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [0, 0], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 1, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝惊讶脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac怎么会这样？身体擅自就……！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac你对我做了什么……？!"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝妥协", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](身体真的被控制住了！虽然有心理准备， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但真正体验的时候，还是会感到恐惧……)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝不悦", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac怎么可能会屈服……！"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 3, "indent": null}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 17, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘻嘻嘻，这点手段都没有，怎么在地下街混啊。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 你继续尝试挣扎吧，看有没有用~ "]}, {"code": 250, "indent": 0, "parameters": [{"name": "drink_maou17", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [60], "indent": null}, {"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [60], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "drink_maou17", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [2, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嗯~ 美酒配佳人~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 妙！妙！ "]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 16, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [1], "indent": null}, {"code": 13, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [1], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 13, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 213, "indent": 0, "parameters": [2, 1, true]}, {"code": 230, "indent": 0, "parameters": [5]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}这娘们越来越合我的口味了……"]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 美人你放心，我是生意人，又不是黑社会。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 我不会强迫你和我性交的，只是给你一些专业上的指导而已~ "]}, {"code": 213, "indent": 0, "parameters": [4, 1, false]}, {"code": 123, "indent": 0, "parameters": ["A", 1]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝安心", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](不会强迫我性交……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 太好了，那就没问题了。)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac今天的话，就选口交好了~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac过来，跪在我面前。"]}, {"code": 213, "indent": 0, "parameters": [4, 8, false]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝妥协", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](口交……用嘴巴吸吮男性生殖器的下作行为……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 但比起被侵犯，这个不算什么。爱丽丝，忍耐！)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [3], "indent": null}, {"code": 3, "indent": 0}, {"code": 3, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 4, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [3], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 4, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [150]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 5]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": true, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 356, "indent": 0, "parameters": [">持续动作 : 事件[4] : 左右震动 : 持续时间[60] : 周期[15] : 震动幅度[1]"]}, {"code": 230, "indent": 0, "parameters": [60]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 241, "indent": 0, "parameters": [{"name": "催眠H事件被动", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘5", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 231, "indent": 0, "parameters": [99, "", 0, 0, 0, 0, 100, 100, 0, 0]}, {"code": 232, "indent": 0, "parameters": [99, 0, 0, 0, 0, 0, 100, 100, 255, 0, 1, false]}, {"code": 230, "indent": 0, "parameters": [1]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 设置动画序列 : 动画序列[14]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 等待动画序列加载完成"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[妓院催眠口交慢]"]}, {"code": 230, "indent": 0, "parameters": [30]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac虽然的确不够熟练，但是……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦哦哦，有够爽啊~ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](生来头一次，在这么近的距离看男人的肉棒……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 竟然有这么大的……男人的肉棒……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210] (这么大的东西……含进嘴巴里，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 感觉下巴都快要脱臼了……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊啊，是的，是的，用嘴唇包住牙齿……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac视线保持朝向老朽这边，你做得很好！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac (这也太臭了……脑子都要被熏晕了…)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210] (不过比起当初警校被恶搞时喝过的香菜汁，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我还可以忍受……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac吸吮的同时，嘴巴里的舌头也不可以偷懒~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac越来越上道了哟，艾莉儿小姐~ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210] (这杂碎真让人不爽……唔唔……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 为了任务，必须忍耐！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 嘿嘿嘿~ 送分题的部分结束了哦~ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac接下来自己思考能让老朽变得开心的方法吧~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210] (这个混蛋，摆出一副主人的样子……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我才不是你的奴隶！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](既然现在必须要曲意逢迎……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac那就看我……)"]}, {"code": 245, "indent": 0, "parameters": [{"name": "口交+娇喘6", "volume": 25, "pitch": 100, "pan": 0}]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[妓院催眠口交快]"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210] (\\{吸死你！！！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 哦！！！真积极啊~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 没错没错！很有精神！！！哦哦！！！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不能因为加速了就让舌头偷懒哦~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac还是要好好品尝味道才行~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 要像是在给龟头按摩一样， "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 在吸吮的同时用舌头搅拌~ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210] (我可不是为了讨好你才……但是……"]}, {"code": 401, "indent": 0, "parameters": ["\\dac这样也好，可以快点结束……)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac啊啊啊~~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac老朽都不用教，你就知道怎么做会让男人开心啊！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac艾莉儿小姐，就知道你有当婊子的天赋~ "]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210] (做婊子？！的天赋？！开什么玩笑？！！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac即使不用催眠，以老朽的魅力全力进攻，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 应该也能轻易让你堕落的吧~"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac 爱丽丝: "]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210] (放屁！)"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac哦，等……不太妙！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac股间感觉快要！"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\dac妓院老板(影狐):"]}, {"code": 401, "indent": 0, "parameters": ["\\dac我操！！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac给老子全吞下去！哦哦哦！"]}, {"code": 224, "indent": 0, "parameters": [[255, 255, 255, 170], 10, false]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放简单状态元集合 : 集合[空白]"]}, {"code": 356, "indent": 0, "parameters": [">图片动画序列 : 图片[99] : 播放动作 : 动作元[妓院催眠口交射精]"]}, {"code": 250, "indent": 0, "parameters": [{"name": "口内射精B", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 245, "indent": 0, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精-02", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精-02", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [120]}, {"code": 250, "indent": 0, "parameters": [{"name": "射精-02", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 0, "parameters": [100]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 251, "indent": 0, "parameters": []}, {"code": 235, "indent": 0, "parameters": [99]}, {"code": 356, "indent": 0, "parameters": ["stop_bgs2"]}, {"code": 111, "indent": 0, "parameters": [0, 300, 1]}, {"code": 223, "indent": 1, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 205, "indent": 1, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 40, "indent": null}, {"code": 34, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 1, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 1, "parameters": [236, 236, 1]}, {"code": 201, "indent": 1, "parameters": [0, 84, 8, 15, 2, 0]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 411, "indent": 0, "parameters": []}, {"code": 203, "indent": 1, "parameters": [2, 0, 16, 14, 2]}, {"code": 203, "indent": 1, "parameters": [4, 0, 16, 17, 8]}, {"code": 205, "indent": 1, "parameters": [2, {"list": [{"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 1, "parameters": [{"code": 33, "indent": null}]}, {"code": 122, "indent": 1, "parameters": [200, 200, 0, 0, 6]}, {"code": 245, "indent": 1, "parameters": [{"name": "過呼吸03", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 245, "indent": 1, "parameters": [{"name": "", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 230, "indent": 1, "parameters": [60]}, {"code": 0, "indent": 1, "parameters": []}, {"code": 412, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 241, "indent": 0, "parameters": [{"name": "Dungeon1", "volume": 35, "pitch": 100, "pan": 0}]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 60, true]}, {"code": 213, "indent": 0, "parameters": [2, 3, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac远超预期，远超预期！"]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 7]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 15, "parameters": [145], "indent": null}, {"code": 18, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [145], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18, "indent": null}]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 34, "indent": null}, {"code": 15, "parameters": [30], "indent": null}, {"code": 14, "parameters": [1, 0], "indent": null}, {"code": 3, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 17, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [30], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 14, "parameters": [1, 0], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17, "indent": null}]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac刚签的，热乎的，A级妓女证。"]}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac但你在老朽心里，妥妥的S级。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac可惜老朽一个人说了不算，你知道的，S级需要委员会表决。"]}, {"code": 213, "indent": 0, "parameters": [2, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac愚蠢的民主制度，只会埋没人才，哈哈~"]}, {"code": 401, "indent": 0, "parameters": ["\\dac所以，除了这张破纸之外，今天给你另一个选项……"]}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac当老朽的情妇。只用伺候老朽一个，"]}, {"code": 401, "indent": 0, "parameters": ["\\dac而且保证比你现在赚得多。考虑一下？"]}, {"code": 213, "indent": 0, "parameters": [4, 5, false]}, {"code": 355, "indent": 0, "parameters": ["$gameTemp.img = 2"]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝不悦脸红", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac不，不用了。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 213, "indent": 0, "parameters": [2, 7, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 是老朽刚才的手段把你吓到了吧？ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 催眠什么的，只是为了不必要的麻烦，平时也不怎么用。 "]}, {"code": 213, "indent": 0, "parameters": [2, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<妓院老板(影狐)>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 而且刚才不是射完就给你解开了吗？"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以不用害怕~ "]}, {"code": 213, "indent": 0, "parameters": [4, 8, true]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝凛然", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac老板你误会。催眠强制，和捆绑拘禁……差、不、多。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac当作情趣，我不反对……"]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\c[210](对于这些践踏女性尊严的事，仅仅反对怎么够？！"]}, {"code": 401, "indent": 0, "parameters": ["\\dac必须绳之以法！！！)"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝斥责", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 只是我单纯喜欢当妓女的体验而已。"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 所以谢谢老板好意，但容我拒绝。"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 3, "indent": null}, {"code": 33, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 3, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 33, "indent": null}]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[24,0] : 时间[7]"]}, {"code": 230, "indent": 0, "parameters": [7]}, {"code": 250, "indent": 0, "parameters": [{"name": "Book1", "volume": 90, "pitch": 100, "pan": 0}]}, {"code": 213, "indent": 0, "parameters": [2, 1, false]}, {"code": 356, "indent": 0, "parameters": [">行走图额外位置偏移量 : 事件[4] : 像素偏移[0,0] : 时间[8]"]}, {"code": 230, "indent": 0, "parameters": [8]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 34, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝鄙夷", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 这个A级证，我就心怀感激地收下了。 "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 老板再见。 "]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 123, "indent": 0, "parameters": ["B", 0]}, {"code": 231, "indent": 0, "parameters": [2, "FOG爱丽丝凛然", 0, 0, 250, 20, 100, 100, 0, 0]}, {"code": 355, "indent": 0, "parameters": ["CBR-画像合成"]}, {"code": 655, "indent": 0, "parameters": ["<明日奈温柔.png>"]}, {"code": 655, "indent": 0, "parameters": ["fgo爱丽丝任务.png"]}, {"code": 232, "indent": 0, "parameters": [2, 0, 0, 0, 310, 20, 100, 100, 255, 0, 25, true]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<爱丽丝>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac\\}再也不见~"]}, {"code": 235, "indent": 0, "parameters": [2]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 2, "indent": 0}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 213, "indent": 0, "parameters": [2, 8, true]}, {"code": 230, "indent": 0, "parameters": [15]}, {"code": 203, "indent": 0, "parameters": [7, 0, 16, 18, 2]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 40, "indent": null}, {"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 212, "indent": 0, "parameters": [7, 158, false]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 8]}, {"code": 205, "indent": 0, "parameters": [2, {"list": [{"code": 15, "parameters": [4], "indent": null}, {"code": 42, "parameters": [205], "indent": null}, {"code": 15, "parameters": [4], "indent": 0}, {"code": 42, "parameters": [155], "indent": null}, {"code": 15, "parameters": [4], "indent": 0}, {"code": 42, "parameters": [105], "indent": null}, {"code": 15, "parameters": [4], "indent": 0}, {"code": 42, "parameters": [55], "indent": null}, {"code": 15, "parameters": [4], "indent": 0}, {"code": 42, "parameters": [0], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": false}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [205], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [155], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [105], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [55], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [0], "indent": null}]}, {"code": 230, "indent": 0, "parameters": [20]}, {"code": 205, "indent": 0, "parameters": [7, {"list": [{"code": 15, "parameters": [4], "indent": 0}, {"code": 42, "parameters": [55], "indent": 0}, {"code": 15, "parameters": [4], "indent": 0}, {"code": 42, "parameters": [105], "indent": 0}, {"code": 15, "parameters": [4], "indent": 0}, {"code": 42, "parameters": [155], "indent": 0}, {"code": 15, "parameters": [4], "indent": 0}, {"code": 42, "parameters": [205], "indent": 0}, {"code": 15, "parameters": [4], "indent": 0}, {"code": 42, "parameters": [255], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [55], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [105], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [155], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [205], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [4], "indent": 0}]}, {"code": 505, "indent": 0, "parameters": [{"code": 42, "parameters": [255], "indent": null}]}, {"code": 213, "indent": 0, "parameters": [7, 3, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac 为了脱身，居然说自己喜欢当妓女？ "]}, {"code": 401, "indent": 0, "parameters": ["\\dac 呵呵呵呵…… "]}, {"code": 213, "indent": 0, "parameters": [7, 4, false]}, {"code": 101, "indent": 0, "parameters": ["", 0, 1, 2]}, {"code": 401, "indent": 0, "parameters": ["\\nc<影狐斯卡利>"]}, {"code": 401, "indent": 0, "parameters": ["\\dac有趣。"]}, {"code": 223, "indent": 0, "parameters": [[-255, -255, -255, 0], 30, true]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 223, "indent": 0, "parameters": [[0, 0, 0, 0], 1, true]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 38, "indent": null}, {"code": 40, "indent": null}, {"code": 34, "indent": null}, {"code": 29, "parameters": [4], "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 40, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 34, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 122, "indent": 0, "parameters": [200, 200, 0, 0, 0]}, {"code": 121, "indent": 0, "parameters": [236, 236, 1]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 201, "indent": 0, "parameters": [0, 84, 8, 15, 2, 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 3, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "B", "selfSwitchValid": true, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 7}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 29, "parameters": [4], "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 1, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 29, "parameters": [4], "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 12, "indent": null}, {"code": 12}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 205, "indent": 0, "parameters": [8, {"list": [{"code": 16, "indent": null}, {"code": 38, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 16, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 38, "indent": null}]}, {"code": 213, "indent": 0, "parameters": [4, 5, false]}, {"code": 205, "indent": 0, "parameters": [4, {"list": [{"code": 12, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 2, "indent": null}, {"code": 1, "indent": null}, {"code": 39, "indent": null}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 2, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 1, "indent": null}]}, {"code": 505, "indent": 0, "parameters": [{"code": 39, "indent": null}]}, {"code": 123, "indent": 0, "parameters": ["B", 1]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 4, "walkAnime": true}], "x": 14, "y": 22}, {"id": 2, "name": "EV002老板", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": false, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "NPC00", "direction": 8, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true}], "x": 16, "y": 22}, {"id": 3, "name": "EV003紫烟", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 2}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$fsm_Object04", "direction": 6, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图滤镜 : 纯色滤镜 : 紫色 : 255"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,48]"]}, {"code": 123, "indent": 0, "parameters": ["A", 0]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 4}, "directionFix": false, "image": {"tileId": 0, "characterName": "!$fsm_Object04", "direction": 4, "pattern": 1, "characterIndex": 0}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图滤镜 : 纯色滤镜 : 紫色 : 155"]}, {"code": 108, "indent": 0, "parameters": ["=>行走图额外位置偏移量 : 像素偏移[0,33]"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": true, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 15}, {"id": 4, "name": "EV004爱丽丝", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 1}, "directionFix": false, "image": {"tileId": 0, "characterName": "fgo爱丽丝任务像素人", "direction": 8, "pattern": 1, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": true, "trigger": 0, "walkAnime": true}], "x": 16, "y": 23}, {"id": 5, "name": "EV005门", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!Door3", "direction": 2, "pattern": 1, "characterIndex": 6}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [5, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 99, 15, 15, 8, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 16, "y": 12}, null, {"id": 7, "name": "EV007影狐", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 3}, "directionFix": true, "image": {"tileId": 0, "characterName": "福克西（影狐斯卡利）", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图滤镜 : 模糊滤镜 : 15"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 5}, "directionFix": false, "image": {"characterIndex": 0, "characterName": "", "direction": 2, "pattern": 0, "tileId": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 0, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 8}, "directionFix": true, "image": {"tileId": 0, "characterName": "福克西（影狐斯卡利）", "direction": 2, "pattern": 1, "characterIndex": 1}, "list": [{"code": 108, "indent": 0, "parameters": ["=>行走图滤镜 : 模糊滤镜 : 0"]}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 17}, {"id": 8, "name": "EV008门", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 250, "indent": 0, "parameters": [{"name": "Open1", "volume": 30, "pitch": 100, "pan": 0}]}, {"code": 205, "indent": 0, "parameters": [9, {"list": [{"code": 17}, {"code": 15, "parameters": [3]}, {"code": 18}, {"code": 15, "parameters": [3]}, {"code": 19}, {"code": 37}, {"code": 0}], "repeat": false, "skippable": false, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 17}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 18}]}, {"code": 505, "indent": 0, "parameters": [{"code": 15, "parameters": [3]}]}, {"code": 505, "indent": 0, "parameters": [{"code": 19}]}, {"code": 505, "indent": 0, "parameters": [{"code": 37}]}, {"code": 205, "indent": 0, "parameters": [-1, {"list": [{"code": 12}, {"code": 0}], "repeat": false, "skippable": true, "wait": true}]}, {"code": 505, "indent": 0, "parameters": [{"code": 12}]}, {"code": 250, "indent": 0, "parameters": [{"name": "Move1", "pan": 0, "pitch": 100, "volume": 90}]}, {"code": 221, "indent": 0, "parameters": []}, {"code": 201, "indent": 0, "parameters": [0, 99, 15, 15, 8, 0]}, {"code": 222, "indent": 0, "parameters": []}, {"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 16, "y": 24}, {"id": 9, "name": "EV009门", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!door4", "direction": 2, "pattern": 1, "characterIndex": 3}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 5, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 1, "walkAnime": true}], "x": 6, "y": 24}, {"id": 10, "name": "EV010香炉", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!光源関係1", "direction": 8, "pattern": 0, "characterIndex": 6}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 15, "y": 16}, {"id": 11, "name": "EV011", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "!food1", "direction": 4, "pattern": 0, "characterIndex": 0}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 17, "y": 15}, {"id": 12, "name": "EV012", "note": "", "pages": [{"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 1, "switch1Valid": false, "switch2Id": 1, "switch2Valid": false, "variableId": 1, "variableValid": false, "variableValue": 0}, "directionFix": false, "image": {"tileId": 0, "characterName": "xd3aykrx", "direction": 2, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 6}, "directionFix": false, "image": {"tileId": 0, "characterName": "xd3aykrx", "direction": 6, "pattern": 1, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}, {"conditions": {"actorId": 1, "actorValid": false, "itemId": 1, "itemValid": false, "selfSwitchCh": "A", "selfSwitchValid": false, "switch1Id": 236, "switch1Valid": true, "switch2Id": 1, "switch2Valid": false, "variableId": 200, "variableValid": true, "variableValue": 7}, "directionFix": false, "image": {"tileId": 0, "characterName": "xd3aykrx", "direction": 2, "pattern": 0, "characterIndex": 4}, "list": [{"code": 0, "indent": 0, "parameters": []}], "moveFrequency": 3, "moveRoute": {"list": [{"code": 0, "parameters": []}], "repeat": true, "skippable": false, "wait": false}, "moveSpeed": 3, "moveType": 0, "priorityType": 1, "stepAnime": false, "through": false, "trigger": 0, "walkAnime": true}], "x": 16, "y": 15}]}